{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue?vue&type=style&index=0&id=0e44e783&scoped=true&lang=css", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue", "mtime": 1754278483446}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753339847609}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753339890164}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753339854740}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNjM2NmZTIgMTAwJSk7DQogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsNCn0NCg0KLyog6aG16Z2i5aS06YOo5qC35byPICovDQoucGFnZS1oZWFkZXIgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KfQ0KDQoucGFnZS10aXRsZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoucGFnZS10aXRsZSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC1zaXplOiAyOHB4Ow0KfQ0KDQoucGFnZS1kZXNjcmlwdGlvbiBwIHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luOiAwOw0KfQ0KDQovKiDlop7lvLrljaHniYfmoLflvI8gKi8NCi5lbmhhbmNlZC1jYXJkIHsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCn0NCg0KLmVuaGFuY2VkLWNhcmQ6aG92ZXIgew0KICBib3gtc2hhZG93OiAwIDhweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xMik7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCn0NCg0KLyog5Y2h54mH5aS06YOo5qC35byPICovDQouY2FyZC1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi5oZWFkZXItbGVmdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTJweDsNCn0NCg0KLmhlYWRlci1sZWZ0IGkgew0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KfQ0KDQouaGVhZGVyLXRpdGxlIHsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgY29sb3I6ICMyYzNlNTA7DQp9DQoNCi5tYXRlcmlhbC1pbmRpY2F0b3Igew0KICBtYXJnaW4tbGVmdDogMTBweDsNCn0NCg0KLml0ZW0tY291bnQtYmFkZ2Ugew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KfQ0KDQouaGVhZGVyLXJpZ2h0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5oZWFkZXItcmlnaHQgLmVsLWJ1dHRvbiB7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmV4cG9ydC1jb21wbGV0ZS1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY3ZTVmIDAlLCAjZmViNDdiIDEwMCUpOw0KICBib3JkZXI6IG5vbmU7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmV4cG9ydC1jb21wbGV0ZS1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmViNDdiIDAlLCAjZmY3ZTVmIDEwMCUpOw0KfQ0KDQovKiDmkJzntKLljLrln5/moLflvI8gKi8NCi5zZWFyY2gtc2VjdGlvbiB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnNlYXJjaC1mb3JtIC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5zZWFyY2gtZm9ybSAuZWwtYXV0b2NvbXBsZXRlIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQouc2VhcmNoLWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlcjogbm9uZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQoucmVzZXQtYnRuIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiDooajmoLzlrrnlmajmoLflvI8gKi8NCi50YWJsZS1jb250YWluZXIgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA1KTsNCn0NCg0KLyog5aKe5by66KGo5qC85qC35byPICovDQouZW5oYW5jZWQtdGFibGUgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB0aCB7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50Ow0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2JvZHkgdHI6aG92ZXIgPiB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZmYgIWltcG9ydGFudDsNCn0NCg0KLmVuaGFuY2VkLXRhYmxlIC5jdXJyZW50LXJvdyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmYgIWltcG9ydGFudDsNCn0NCg0KLmVuaGFuY2VkLXRhYmxlIC5jdXJyZW50LXJvdzpob3ZlciA+IHRkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2U2ZjdmZiAhaW1wb3J0YW50Ow0KfQ0KDQovKiDooajmoLzljZXlhYPmoLzmoLflvI8gKi8NCi5pbmRleC1udW1iZXIgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5tYXRlcmlhbC1uYW1lLWNlbGwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLm1hdGVyaWFsLWljb24gew0KICBjb2xvcjogIzY3QzIzQTsNCn0NCg0KLm1hdGVyaWFsLW5hbWUgew0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLnN1cHBsaWVyLWNlbGwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLnN1cHBsaWVyLWljb24gew0KICBjb2xvcjogI0U2QTIzQzsNCn0NCg0KLmRlc2NyaXB0aW9uLXRleHQgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxM3B4Ow0KfQ0KDQouZW1wdHktZGF0YSB7DQogIGNvbG9yOiAjQzBDNENDOw0KICBmb250LXN0eWxlOiBpdGFsaWM7DQp9DQoNCi51c2VyLWluZm8sIC50aW1lLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDVweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQoudXNlci1pbmZvIGksIC50aW1lLWluZm8gaSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouYXR0YWNobWVudC1idG4gew0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmF0dGFjaG1lbnQtYnRuOmhvdmVyIHsNCiAgY29sb3I6ICM2NmIxZmY7DQp9DQoNCi8qIOaTjeS9nOaMiemSruagt+W8jyAqLw0KLmFjdGlvbi1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiA4cHg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouZWRpdC1idG4gew0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmVkaXQtYnRuOmhvdmVyIHsNCiAgY29sb3I6ICM2NmIxZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmY7DQp9DQoNCi5kZWxldGUtYnRuIHsNCiAgY29sb3I6ICNGNTZDNkM7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5kZWxldGUtYnRuOmhvdmVyIHsNCiAgY29sb3I6ICNmNzg5ODk7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZWYwZjA7DQp9DQoNCi8qIOmAmueUqOagt+W8jyAqLw0KLmNsZWFyZml4OmJlZm9yZSwNCi5jbGVhcmZpeDphZnRlciB7DQogIGRpc3BsYXk6IHRhYmxlOw0KICBjb250ZW50OiAiIjsNCn0NCg0KLmNsZWFyZml4OmFmdGVyIHsNCiAgY2xlYXI6IGJvdGg7DQp9DQoNCi5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1yaWdodDogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5lbC11cGxvYWRfX3RpcCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDEycHg7DQogIG1hcmdpbi10b3A6IDdweDsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLmFwcC1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDEwcHg7DQogIH0NCg0KICAucGFnZS1oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAuaGVhZGVyLXJpZ2h0IHsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogIH0NCg0KICAuc2VhcmNoLWZvcm0gew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIH0NCg0KICAuc2VhcmNoLWZvcm0gLmVsLWZvcm0taXRlbSB7DQogICAgbWFyZ2luLXJpZ2h0OiAwOw0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQoNCi8qIOe7n+S4gOaMiemSruagt+W8jyAqLw0KLmVsLWJ1dHRvbi0tcHJpbWFyeSB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5lbC1idXR0b24tLXN1Y2Nlc3Mgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNTZhYjJmIDAlLCAjYThlNmNmIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtYnV0dG9uLS1pbmZvIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzE3YTJiOCAwJSwgIzEzODQ5NiAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0td2FybmluZyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIgMCUsICNmNTU3NmMgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5lbC1idXR0b24tLWRhbmdlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiNmIgMCUsICNlZTVhMjQgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+kDA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/config", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-setting\"></i>\r\n        <span>材料及工艺参数配置</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理材料信息、工艺参数组和参数明细的三层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击材料行查看工艺参数组，点击参数组行查看参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 材料信息表格 -->\r\n    <el-card class=\"material-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-box\"></i>\r\n          <span class=\"header-title\">材料信息管理</span>\r\n          <el-badge :value=\"materialTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddMaterial\">\r\n            <span>新增材料</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"materialMultiple\" @click=\"handleBatchDeleteMaterial\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportComplete\" class=\"export-complete-btn\">\r\n            <span>整体导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 材料查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"materialQueryParams\" ref=\"materialQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialName\"\r\n              :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n              placeholder=\"请输入材料名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handleMaterialNameSelect\"\r\n              @focus=\"handleMaterialNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-search\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.supplierName\"\r\n              :fetch-suggestions=\"querySupplierSuggestions\"\r\n              placeholder=\"请输入供应商名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleSupplierFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-office-building\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialModel\"\r\n              :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n              placeholder=\"请输入材料型号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleMaterialModelFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-goods\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleMaterialQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetMaterialQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"materialLoading\"\r\n          :data=\"materialList\"\r\n          @row-click=\"handleMaterialClick\"\r\n          @selection-change=\"handleMaterialSelectionChange\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getMaterialRowClassName\"\r\n          ref=\"materialTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载材料数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (materialQueryParams.pageNum - 1) * materialQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"材料名称\" min-width=\"140\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"material-name-cell\">\r\n                <i class=\"el-icon-box material-icon\"></i>\r\n                <span class=\"material-name\">{{ scope.row.materialName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"supplierName\" label=\"供应商\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"supplier-cell\">\r\n                <i class=\"el-icon-office-building supplier-icon\"></i>\r\n                <span>{{ scope.row.supplierName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialModel\" label=\"材料型号\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"info\" v-if=\"scope.row.materialModel\">{{ scope.row.materialModel }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialDescription\" label=\"材料描述\" min-width=\"180\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.materialDescription\" class=\"description-text\">{{ scope.row.materialDescription }}</span>\r\n              <span v-else class=\"empty-data\">暂无描述</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditMaterial(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteMaterial(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"materialTotal > 0\"\r\n        :total=\"materialTotal\"\r\n        :page.sync=\"materialQueryParams.pageNum\"\r\n        :limit.sync=\"materialQueryParams.pageSize\"\r\n        @pagination=\"getMaterialList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 工艺参数组表格 -->\r\n    <el-card class=\"param-group-card enhanced-card\" style=\"margin-bottom: 20px;\" v-show=\"currentMaterial\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-setting\"></i>\r\n          <span class=\"header-title\">工艺参数组</span>\r\n          <div class=\"material-indicator\" v-if=\"currentMaterial\">\r\n            <el-tag type=\"success\" size=\"small\">\r\n              <i class=\"el-icon-box\"></i>\r\n              {{ currentMaterial.materialName }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"paramGroupTotal\" class=\"item-count-badge\" type=\"success\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>新增参数组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"paramGroupMultiple || !currentMaterial\" @click=\"handleBatchDeleteParamGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"paramGroupQueryParams\" ref=\"paramGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.processType\"\r\n              :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n              placeholder=\"请输入工艺类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleProcessTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-setting\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.paramNumber\"\r\n              :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n              placeholder=\"请输入参数编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNumberFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-tickets\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleParamGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetParamGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table\r\n        v-loading=\"paramGroupLoading\"\r\n        :data=\"paramGroupList\"\r\n        @row-click=\"handleParamGroupClick\"\r\n        @selection-change=\"handleParamGroupSelectionChange\"\r\n        highlight-current-row\r\n        style=\"width: 100%\"\r\n        :row-class-name=\"getParamGroupRowClassName\"\r\n        ref=\"paramGroupTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column prop=\"processType\" label=\"工艺类型\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column prop=\"paramNumber\" label=\"参数编号\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" />\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\" />\r\n        <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click.stop=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditParamGroup(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click.stop=\"handleDeleteParamGroup(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"paramGroupTotal > 0\"\r\n        :total=\"paramGroupTotal\"\r\n        :page.sync=\"paramGroupQueryParams.pageNum\"\r\n        :limit.sync=\"paramGroupQueryParams.pageSize\"\r\n        @pagination=\"getParamGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 参数明细表格 -->\r\n    <el-card class=\"box-card\" v-show=\"currentParamGroup\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 16px;\">参数明细 - {{ currentParamGroup ? currentParamGroup.paramNumber : '' }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddParamItem\" :disabled=\"!currentParamGroup\">新增</el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" :disabled=\"paramItemMultiple || !currentParamGroup\" @click=\"handleBatchDeleteParamItem\">批量删除</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExportParamItem\" :disabled=\"!currentParamGroup\">导出</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数明细查询条件 -->\r\n      <el-form :model=\"paramItemQueryParams\" ref=\"paramItemQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" style=\"margin-bottom: 15px;\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-autocomplete\r\n            v-model=\"paramItemQueryParams.paramName\"\r\n            :fetch-suggestions=\"queryParamNameSuggestions\"\r\n            placeholder=\"请输入参数名称\"\r\n            clearable\r\n            style=\"width: 200px;\"\r\n            @focus=\"handleParamNameFocus\"\r\n            :trigger-on-focus=\"true\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"参数单位\" prop=\"unit\">\r\n          <el-autocomplete\r\n            v-model=\"paramItemQueryParams.unit\"\r\n            :fetch-suggestions=\"queryUnitSuggestions\"\r\n            placeholder=\"请输入参数单位\"\r\n            clearable\r\n            style=\"width: 200px;\"\r\n            @focus=\"handleUnitFocus\"\r\n            :trigger-on-focus=\"true\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleParamItemQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetParamItemQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <el-table\r\n        v-loading=\"paramItemLoading\"\r\n        :data=\"paramItemList\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleParamItemSelectionChange\"\r\n        @row-click=\"handleParamItemRowClick\"\r\n        ref=\"paramItemTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip />\r\n        <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.paramValue !== null ? scope.row.paramValue : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n        <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" />\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\" />\r\n        <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click.stop=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleEditParamItem(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click=\"handleDeleteParamItem(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"paramItemTotal > 0\"\r\n        :total=\"paramItemTotal\"\r\n        :page.sync=\"paramItemQueryParams.pageNum\"\r\n        :limit.sync=\"paramItemQueryParams.pageSize\"\r\n        @pagination=\"getParamItemList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 材料信息对话框 -->\r\n    <el-dialog :title=\"materialTitle\" :visible.sync=\"materialOpen\" width=\"800px\" append-to-body v-drag>\r\n      <el-form ref=\"materialForm\" :model=\"materialForm\" :rules=\"materialRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n              <el-input v-model=\"materialForm.materialName\" placeholder=\"请输入材料名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"supplierName\">\r\n              <el-input v-model=\"materialForm.supplierName\" placeholder=\"请输入供应商名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n              <el-input v-model=\"materialForm.materialModel\" placeholder=\"请输入材料型号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"材料描述\">\r\n          <el-input v-model=\"materialForm.materialDescription\" type=\"textarea\" placeholder=\"请输入材料描述\" :rows=\"3\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"materialUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"materialFileList\"\r\n            :on-success=\"handleMaterialUploadSuccess\"\r\n            :on-remove=\"handleMaterialFileRemove\"\r\n            :before-upload=\"beforeMaterialUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"materialForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitMaterialForm\">确 定</el-button>\r\n        <el-button @click=\"cancelMaterial\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工艺参数组对话框 -->\r\n    <el-dialog :title=\"paramGroupTitle\" :visible.sync=\"paramGroupOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramGroupForm\" :model=\"paramGroupForm\" :rules=\"paramGroupRules\" label-width=\"100px\">\r\n        <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n          <el-input v-model=\"paramGroupForm.processType\" placeholder=\"请输入工艺类型\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n          <el-input v-model=\"paramGroupForm.paramNumber\" placeholder=\"请输入参数编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramGroupUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramGroupFileList\"\r\n            :on-success=\"handleParamGroupUploadSuccess\"\r\n            :on-remove=\"handleParamGroupFileRemove\"\r\n            :before-upload=\"beforeParamGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数明细对话框 -->\r\n    <el-dialog :title=\"paramItemTitle\" :visible.sync=\"paramItemOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramItemForm\" :model=\"paramItemForm\" :rules=\"paramItemRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"paramItemForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"paramItemForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"paramItemForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramItemUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramItemFileList\"\r\n            :on-success=\"handleParamItemUploadSuccess\"\r\n            :on-remove=\"handleParamItemFileRemove\"\r\n            :before-upload=\"beforeParamItemUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramItemForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamItemForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamItem\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial,\r\n  exportMaterial, getMaterialOptions\r\n} from \"@/api/material/material\";\r\nimport {\r\n  listProcessParamGroup, getProcessParamGroup, delProcessParamGroup,\r\n  addProcessParamGroup, updateProcessParamGroup, listByMaterialId,\r\n  exportProcessParamGroup, getProcessParamGroupOptions, exportCompleteData\r\n} from \"@/api/material/processParamGroup\";\r\nimport {\r\n  listProcessParamItem, getProcessParamItem, delProcessParamItem,\r\n  addProcessParamItem, updateProcessParamItem, listByGroupId,\r\n  exportProcessParamItem, getProcessParamItemOptions\r\n} from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\n\r\nexport default {\r\n  name: \"MaterialConfig\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n\r\n      // 材料相关\r\n      materialLoading: true,\r\n      materialList: [],\r\n      materialTotal: 0,\r\n      currentMaterial: null,\r\n      materialOpen: false,\r\n      materialTitle: \"\",\r\n      materialForm: {},\r\n      materialFileList: [],\r\n      materialIds: [],\r\n      materialSingle: true,\r\n      materialMultiple: true,\r\n      materialQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null\r\n      },\r\n      materialRules: {\r\n        materialName: [\r\n          { required: true, message: \"材料名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 工艺参数组相关\r\n      paramGroupLoading: false,\r\n      paramGroupList: [],\r\n      paramGroupTotal: 0,\r\n      currentParamGroup: null,\r\n      paramGroupOpen: false,\r\n      paramGroupTitle: \"\",\r\n      paramGroupForm: {},\r\n      paramGroupFileList: [],\r\n      paramGroupIds: [],\r\n      paramGroupSingle: true,\r\n      paramGroupMultiple: true,\r\n      paramGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null\r\n      },\r\n      paramGroupRules: {\r\n        processType: [\r\n          { required: true, message: \"工艺类型不能为空\", trigger: \"blur\" }\r\n        ],\r\n        paramNumber: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 参数明细相关\r\n      paramItemLoading: false,\r\n      paramItemList: [],\r\n      paramItemTotal: 0,\r\n      paramItemOpen: false,\r\n      paramItemTitle: \"\",\r\n      paramItemForm: {},\r\n      paramItemFileList: [],\r\n      paramItemIds: [],\r\n      paramItemSingle: true,\r\n      paramItemMultiple: true,\r\n      paramItemQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        groupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      paramItemRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      materialNameSuggestions: [],\r\n      supplierSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      paramNumberSuggestions: [],\r\n      unitSuggestions: []\r\n    };\r\n  },\r\n  created() {\r\n    this.getMaterialList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数编号建议\r\n      getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数名称建议\r\n      getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n        this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数单位建议\r\n      getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n        this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商搜索建议 */\r\n    querySupplierSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数名称搜索建议 */\r\n    /** 参数名称搜索建议 */\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数单位搜索建议 */\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料名称选择事件 */\r\n    handleMaterialNameSelect(item) {\r\n      this.materialQueryParams.materialName = item.value;\r\n      // 移除自动搜索，让用户手动点击搜索按钮\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      // 重新加载材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商焦点事件 */\r\n    handleSupplierFocus() {\r\n      // 重新加载供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      // 重新加载材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      // 基于当前选中的材料加载工艺类型建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取工艺类型选项\r\n        const processTypes = [...new Set(this.paramGroupList.map(item => item.processType).filter(Boolean))];\r\n        this.processTypeSuggestions = processTypes.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有工艺类型\r\n        getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n          this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      // 基于当前选中的材料加载参数编号建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取参数编号选项\r\n        const paramNumbers = [...new Set(this.paramGroupList.map(item => item.paramNumber).filter(Boolean))];\r\n        this.paramNumberSuggestions = paramNumbers.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有参数编号\r\n        getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n          this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数名称焦点事件 */\r\n    handleParamNameFocus() {\r\n      // 基于当前选中的参数组加载参数名称建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数名称选项\r\n        const paramNames = [...new Set(this.paramItemList.map(item => item.paramName).filter(Boolean))];\r\n        this.paramNameSuggestions = paramNames.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数名称\r\n        getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数单位焦点事件 */\r\n    handleUnitFocus() {\r\n      // 基于当前选中的参数组加载参数单位建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数单位选项\r\n        const units = [...new Set(this.paramItemList.map(item => item.unit).filter(Boolean))];\r\n        this.unitSuggestions = units.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数单位\r\n        getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      // 如果已经是数组，直接返回\r\n      if (Array.isArray(attachments)) {\r\n        return attachments;\r\n      }\r\n\r\n      // 如果是字符串，按逗号分割并转换为文件对象\r\n      if (typeof attachments === 'string') {\r\n        return attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n          const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n          return {\r\n            name: fileName,\r\n            url: url.trim(),\r\n            uid: Date.now() + index,\r\n            status: 'success'\r\n          };\r\n        });\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 查询材料列表 */\r\n    getMaterialList() {\r\n      this.materialLoading = true;\r\n      listMaterial(this.materialQueryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.materialTotal = response.total;\r\n        this.materialLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 材料查询 */\r\n    handleMaterialQuery() {\r\n      this.materialQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n    },\r\n\r\n    /** 重置材料查询 */\r\n    resetMaterialQuery() {\r\n      this.resetForm(\"materialQueryForm\");\r\n      this.handleMaterialQuery();\r\n    },\r\n\r\n    /** 材料行点击事件 */\r\n    handleMaterialClick(row) {\r\n      this.currentMaterial = row;\r\n      this.paramGroupQueryParams.materialId = row.materialId;\r\n      this.getParamGroupList();\r\n      this.paramItemList = [];\r\n      this.paramItemTotal = 0;\r\n      this.currentParamGroup = null;\r\n      // 同时选中该行\r\n      this.$refs.materialTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 材料选择变化事件 */\r\n    handleMaterialSelectionChange(selection) {\r\n      this.materialIds = selection.map(item => item.materialId);\r\n      this.materialSingle = selection.length !== 1;\r\n      this.materialMultiple = !selection.length;\r\n    },\r\n\r\n    /** 材料行样式 */\r\n    getMaterialRowClassName({row, rowIndex}) {\r\n      if (this.currentMaterial && row.materialId === this.currentMaterial.materialId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询工艺参数组列表 */\r\n    getParamGroupList() {\r\n      if (!this.currentMaterial) return;\r\n      this.paramGroupLoading = true;\r\n      listProcessParamGroup(this.paramGroupQueryParams).then(response => {\r\n        this.paramGroupList = response.rows;\r\n        this.paramGroupTotal = response.total;\r\n        this.paramGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数组查询 */\r\n    handleParamGroupQuery() {\r\n      this.paramGroupQueryParams.pageNum = 1;\r\n      this.getParamGroupList();\r\n    },\r\n\r\n    /** 重置参数组查询 */\r\n    resetParamGroupQuery() {\r\n      this.resetForm(\"paramGroupQueryForm\");\r\n      this.handleParamGroupQuery();\r\n    },\r\n\r\n    /** 工艺参数组行点击事件 */\r\n    handleParamGroupClick(row) {\r\n      this.currentParamGroup = row;\r\n      this.paramItemQueryParams.groupId = row.groupId;\r\n      this.getParamItemList();\r\n      // 同时选中该行\r\n      this.$refs.paramGroupTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数组选择变化事件 */\r\n    handleParamGroupSelectionChange(selection) {\r\n      this.paramGroupIds = selection.map(item => item.groupId);\r\n      this.paramGroupSingle = selection.length !== 1;\r\n      this.paramGroupMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数明细行点击事件 */\r\n    handleParamItemRowClick(row) {\r\n      this.$refs.paramItemTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数明细选择变化事件 */\r\n    handleParamItemSelectionChange(selection) {\r\n      this.paramItemIds = selection.map(item => item.itemId);\r\n      this.paramItemSingle = selection.length !== 1;\r\n      this.paramItemMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数组行样式 */\r\n    getParamGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentParamGroup && row.groupId === this.currentParamGroup.groupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询参数明细列表 */\r\n    getParamItemList() {\r\n      if (!this.currentParamGroup) return;\r\n      this.paramItemLoading = true;\r\n      listProcessParamItem(this.paramItemQueryParams).then(response => {\r\n        this.paramItemList = response.rows;\r\n        this.paramItemTotal = response.total;\r\n        this.paramItemLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数明细查询 */\r\n    handleParamItemQuery() {\r\n      this.paramItemQueryParams.pageNum = 1;\r\n      this.getParamItemList();\r\n    },\r\n\r\n    /** 重置参数明细查询 */\r\n    resetParamItemQuery() {\r\n      this.resetForm(\"paramItemQueryForm\");\r\n      this.handleParamItemQuery();\r\n    },\r\n\r\n    /** 新增材料 */\r\n    handleAddMaterial() {\r\n      this.resetMaterialForm();\r\n      this.materialOpen = true;\r\n      this.materialTitle = \"添加材料信息\";\r\n    },\r\n\r\n    /** 修改材料 */\r\n    handleEditMaterial(row) {\r\n      this.resetMaterialForm();\r\n      const materialId = row.materialId;\r\n      getMaterial(materialId).then(response => {\r\n        this.materialForm = response.data;\r\n        // 处理附件数据\r\n        this.materialFileList = this.parseAttachments(response.data.attachments);\r\n        this.materialOpen = true;\r\n        this.materialTitle = \"修改材料信息\";\r\n      });\r\n    },\r\n\r\n    /** 提交材料表单 */\r\n    submitMaterialForm() {\r\n      this.$refs[\"materialForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.materialForm.attachments = this.materialFileList.length > 0\r\n            ? this.materialFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.materialForm.materialId != null) {\r\n            // 更新操作，设置更新人\r\n            this.materialForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.materialForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.materialForm.materialId != null) {\r\n            updateMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          } else {\r\n            addMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消材料操作 */\r\n    cancelMaterial() {\r\n      this.materialOpen = false;\r\n      this.resetMaterialForm();\r\n    },\r\n\r\n    /** 重置材料表单 */\r\n    resetMaterialForm() {\r\n      this.materialForm = {\r\n        materialId: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        materialDescription: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.materialFileList = [];\r\n      this.resetForm(\"materialForm\");\r\n    },\r\n\r\n    /** 删除材料 */\r\n    handleDeleteMaterial(row) {\r\n      const materialIds = row.materialId;\r\n      this.$modal.confirm('是否确认删除材料\"' + row.materialName + '\"？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除材料 */\r\n    handleBatchDeleteMaterial() {\r\n      const materialIds = this.materialIds;\r\n      this.$modal.confirm('是否确认删除选中的' + materialIds.length + '条材料数据？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出材料 */\r\n    handleExportMaterial() {\r\n      this.download('material/material/export', {\r\n        ...this.materialQueryParams\r\n      }, `material_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 整体导出 */\r\n    handleExportComplete() {\r\n      this.$modal.loading(\"正在导出数据，请稍候...\");\r\n      this.download('material/material/exportComplete', {\r\n        ...this.materialQueryParams\r\n      }, `complete_data_${new Date().getTime()}.xlsx`).then(() => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgSuccess(\"导出成功\");\r\n      }).catch(error => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(\"导出失败: \" + (error.message || \"未知错误\"));\r\n      });\r\n    },\r\n\r\n    /** 新增工艺参数组 */\r\n    handleAddParamGroup() {\r\n      this.resetParamGroupForm();\r\n      this.paramGroupForm.materialId = this.currentMaterial.materialId;\r\n      this.paramGroupOpen = true;\r\n      this.paramGroupTitle = \"添加工艺参数组\";\r\n    },\r\n\r\n    /** 修改工艺参数组 */\r\n    handleEditParamGroup(row) {\r\n      this.resetParamGroupForm();\r\n      const groupId = row.groupId;\r\n      getProcessParamGroup(groupId).then(response => {\r\n        this.paramGroupForm = response.data;\r\n        // 处理附件数据\r\n        this.paramGroupFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramGroupOpen = true;\r\n        this.paramGroupTitle = \"修改工艺参数组\";\r\n      });\r\n    },\r\n\r\n    /** 提交工艺参数组表单 */\r\n    submitParamGroupForm() {\r\n      this.$refs[\"paramGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramGroupForm.attachments = this.paramGroupFileList.length > 0\r\n            ? this.paramGroupFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramGroupForm.groupId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramGroupForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramGroupForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramGroupForm.groupId != null) {\r\n            updateProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          } else {\r\n            addProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消工艺参数组操作 */\r\n    cancelParamGroup() {\r\n      this.paramGroupOpen = false;\r\n      this.resetParamGroupForm();\r\n    },\r\n\r\n    /** 重置工艺参数组表单 */\r\n    resetParamGroupForm() {\r\n      this.paramGroupForm = {\r\n        groupId: null,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramGroupFileList = [];\r\n      this.resetForm(\"paramGroupForm\");\r\n    },\r\n\r\n    /** 删除工艺参数组 */\r\n    handleDeleteParamGroup(row) {\r\n      const groupIds = row.groupId;\r\n      this.$modal.confirm('是否确认删除参数组\"' + row.paramNumber + '\"？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数组 */\r\n    handleBatchDeleteParamGroup() {\r\n      const groupIds = this.paramGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + groupIds.length + '条参数组数据？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出工艺参数组 */\r\n    handleExportParamGroup() {\r\n      this.download('material/processParamGroup/export', {\r\n        ...this.paramGroupQueryParams\r\n      }, `param_group_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 新增参数明细 */\r\n    handleAddParamItem() {\r\n      this.resetParamItemForm();\r\n      this.paramItemForm.groupId = this.currentParamGroup.groupId;\r\n      this.paramItemOpen = true;\r\n      this.paramItemTitle = \"添加参数明细\";\r\n    },\r\n\r\n    /** 修改参数明细 */\r\n    handleEditParamItem(row) {\r\n      this.resetParamItemForm();\r\n      const itemId = row.itemId;\r\n      getProcessParamItem(itemId).then(response => {\r\n        this.paramItemForm = response.data;\r\n        // 处理附件数据\r\n        this.paramItemFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramItemOpen = true;\r\n        this.paramItemTitle = \"修改参数明细\";\r\n      });\r\n    },\r\n\r\n    /** 提交参数明细表单 */\r\n    submitParamItemForm() {\r\n      this.$refs[\"paramItemForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramItemForm.attachments = this.paramItemFileList.length > 0\r\n            ? this.paramItemFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramItemForm.itemId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramItemForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramItemForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramItemForm.itemId != null) {\r\n            updateProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          } else {\r\n            addProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消参数明细操作 */\r\n    cancelParamItem() {\r\n      this.paramItemOpen = false;\r\n      this.resetParamItemForm();\r\n    },\r\n\r\n    /** 重置参数明细表单 */\r\n    resetParamItemForm() {\r\n      this.paramItemForm = {\r\n        itemId: null,\r\n        groupId: null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramItemFileList = [];\r\n      this.resetForm(\"paramItemForm\");\r\n    },\r\n\r\n    /** 删除参数明细 */\r\n    handleDeleteParamItem(row) {\r\n      const itemIds = row.itemId;\r\n      this.$modal.confirm('是否确认删除参数\"' + row.paramName + '\"？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数明细 */\r\n    handleBatchDeleteParamItem() {\r\n      const itemIds = this.paramItemIds;\r\n      this.$modal.confirm('是否确认删除选中的' + itemIds.length + '条参数明细数据？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出参数明细 */\r\n    handleExportParamItem() {\r\n      this.download('material/processParamItem/export', {\r\n        ...this.paramItemQueryParams\r\n      }, `param_item_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 材料附件上传成功 */\r\n    handleMaterialUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 材料附件移除 */\r\n    handleMaterialFileRemove(file, fileList) {\r\n      console.log('材料附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组，并且正确处理空数组的情况\r\n      if (Array.isArray(fileList)) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.materialFileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 材料附件上传前检查 */\r\n    beforeMaterialUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数组附件上传成功 */\r\n    handleParamGroupUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramGroupFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数组附件移除 */\r\n    handleParamGroupFileRemove(file, fileList) {\r\n      this.paramGroupFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数组附件上传前检查 */\r\n    beforeParamGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数明细附件上传成功 */\r\n    handleParamItemUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramItemFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数明细附件移除 */\r\n    handleParamItemFileRemove(file, fileList) {\r\n      this.paramItemFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数明细附件上传前检查 */\r\n    beforeParamItemUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          // 如果是逗号分隔的URL字符串，转换为对象数组\r\n          if (attachments.includes(',') || (attachments && !attachments.startsWith('['))) {\r\n            this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: url.trim()\r\n              };\r\n            });\r\n          } else {\r\n            // 尝试解析JSON格式\r\n            this.attachmentList = JSON.parse(attachments || '[]');\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments;\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      } catch (e) {\r\n        // 如果JSON解析失败，尝试作为逗号分隔的字符串处理\r\n        if (typeof attachments === 'string' && attachments.trim()) {\r\n          this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n            const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: url.trim()\r\n            };\r\n          });\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      }\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '0 B';\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.material-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-complete-btn {\r\n  background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.export-complete-btn:hover {\r\n  background: linear-gradient(135deg, #feb47b 0%, #ff7e5f 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.material-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.material-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.material-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.supplier-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.supplier-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.description-text {\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}