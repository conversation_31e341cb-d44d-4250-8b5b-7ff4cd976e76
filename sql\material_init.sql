-- 材料管理系统完整初始化脚本
-- 请在执行前确保数据库已创建

-- 1. 执行数据库表创建
SOURCE material_tables.sql;

-- 2. 执行菜单权限配置
SOURCE material_menu.sql;

-- 3. 添加材料导入权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料导入', (SELECT menu_id FROM sys_menu WHERE menu_name = '材料及参数配置' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '材料管理' AND parent_id = 0)), 6, '', '', '', 1, 0, 'F', '0', '0', 'material:material:import', '#', 'admin', NOW(), '', NULL, '');

-- 4. 给admin角色分配导入权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms = 'material:material:import';

-- 5. 验证数据
SELECT '数据库表检查' as check_type, 
       CASE WHEN COUNT(*) = 5 THEN '✓ 所有表已创建' ELSE '✗ 表创建不完整' END as result
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('materials', 'process_param_groups', 'process_param_items', 'test_plans', 'test_results');

SELECT '菜单权限检查' as check_type,
       CASE WHEN COUNT(*) >= 15 THEN '✓ 菜单权限已配置' ELSE '✗ 菜单权限配置不完整' END as result
FROM sys_menu 
WHERE menu_name LIKE '%材料%' OR menu_name LIKE '%参数%';

SELECT '测试数据检查' as check_type,
       CONCAT('✓ 材料数据: ', COUNT(*), ' 条') as result
FROM materials;

-- 完成提示
SELECT '初始化完成' as status, '请重启应用服务器以加载新的权限配置' as message;
