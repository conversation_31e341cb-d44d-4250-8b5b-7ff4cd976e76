<template>
  <div class="app-container">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-data-line"></i>
        <span>趋势对比分析</span>
      </div>
      <div class="page-description">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <p>📈 多维度数据趋势对比分析，支持材料性能、供应商质量等多种对比维度</p>
          <el-button type="text" @click="showUsageGuide = true" style="color: #409EFF;">
            <i class="el-icon-question"></i>
            <span>使用指南</span>
          </el-button>
        </div>
        <el-alert
          title="使用提示：选择对比维度 → 配置筛选条件 → 生成图表分析"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px;">
        </el-alert>
      </div>
    </div>

    <!-- 使用指南卡片 -->
    <el-card class="usage-guide-card enhanced-card" style="margin-bottom: 20px;" v-if="showUsageGuide">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-info"></i>
          <span class="header-title">📊 趋势对比分析使用指南</span>
        </div>
        <div class="header-right">
          <el-button type="text" @click="showUsageGuide = false" class="close-guide-btn">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="guide-item">
            <h4>🎯 对比维度说明</h4>
            <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.6;">
              <li><strong>材料对比：</strong>按材料名称分组，计算每种材料的供应商数据和测试数据平均值</li>
              <li><strong>供应商对比：</strong>按供应商分组，计算准确率（供应商数据与测试数据的偏差）</li>
              <li><strong>参数编号对比：</strong>按参数组分组，展示工艺参数明细和测试结果统计</li>
              <li><strong>工艺类型对比：</strong>按工艺类型分组，计算稳定性（测试数据标准差）</li>
              <li><strong>时间趋势：</strong>按日期分组，展示测试数据随时间的变化趋势</li>
            </ul>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="guide-item">
            <h4>📊 数据来源详解</h4>
            <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.6;">
              <li><strong>供应商平均值：</strong>选中项目下所有测试记录的供应商数据平均值</li>
              <li><strong>测试平均值：</strong>选中项目下所有测试记录的实际测试值平均值</li>
              <li><strong>准确率：</strong>100% - |供应商平均值 - 测试平均值| / 供应商平均值 × 100%</li>
              <li><strong>稳定性：</strong>基于测试数据标准差计算，数值越小越稳定</li>
              <li><strong>数据量：</strong>参与计算的测试记录总数</li>
              <li><strong>参数明细：</strong>来自工艺参数配置中的具体参数项</li>
            </ul>
          </div>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px;">
        <el-col :span="24">
          <div class="guide-item">
            <h4>💡 使用建议</h4>
            <p style="margin: 10px 0; line-height: 1.6; color: #606266;">
              1. 选择对比维度 → 2. 配置筛选条件（支持多选） → 3. 点击"生成图表"分析 → 4. 切换图表类型查看不同视角 → 5. 查看详细数据表获取具体数值
            </p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="trend-analysis-card enhanced-card">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-data-line"></i>
          <span class="header-title">数据趋势对比分析</span>
        </div>
        <div class="header-right">
          <el-button type="info" icon="el-icon-question" size="small" @click="showUsageGuide = !showUsageGuide" class="guide-btn">
            <span>使用指南</span>
          </el-button>
          <el-button type="primary" icon="el-icon-refresh" size="small" @click="refreshChart" class="refresh-btn">
            <span>刷新数据</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="exportChart" class="export-btn">
            <span>导出图表</span>
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px" style="margin-bottom: 20px;">
        <el-form-item label="对比维度" prop="compareType">
          <el-select v-model="queryParams.compareType" placeholder="请选择对比维度" style="width: 250px;" clearable @change="handleCompareTypeChange">
            <el-option label="📊 材料性能对比" value="material">
              <span style="float: left">📊 材料性能对比</span>
              <span style="float: right; color: #8492a6; font-size: 12px">比较不同材料性能</span>
            </el-option>
            <el-option label="🏭 供应商数据对比" value="supplier">
              <span style="float: left">🏭 供应商数据对比</span>
              <span style="float: right; color: #8492a6; font-size: 12px">比较供应商质量</span>
            </el-option>
            <el-option label="🔢 参数编号对比" value="paramNumber">
              <span style="float: left">🔢 参数编号对比</span>
              <span style="float: right; color: #8492a6; font-size: 12px">比较不同参数值</span>
            </el-option>
            <el-option label="⚙️ 工艺类型对比" value="processType">
              <span style="float: left">⚙️ 工艺类型对比</span>
              <span style="float: right; color: #8492a6; font-size: 12px">比较工艺效果</span>
            </el-option>
            <el-option label="📈 时间趋势分析" value="timeTrend">
              <span style="float: left">📈 时间趋势分析</span>
              <span style="float: right; color: #8492a6; font-size: 12px">查看时间变化</span>
            </el-option>
            <el-option label="⚖️ 供应商vs测试值" value="supplierVsTest">
              <span style="float: left">⚖️ 供应商vs测试值</span>
              <span style="float: right; color: #8492a6; font-size: 12px">对比数据差异</span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 材料性能对比 -->
        <el-form-item label="选择材料" prop="materialNames" v-if="queryParams.compareType === 'material'">
          <el-select
            v-model="queryParams.materialNames"
            multiple
            placeholder="请选择要对比的材料"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="material in materialOptions"
              :key="material.materialId"
              :label="material.materialName + ' (' + material.supplierName + ')'"
              :value="material.materialId"
            />
          </el-select>
        </el-form-item>

        <!-- 供应商数据对比 -->
        <el-form-item label="选择供应商" prop="supplierNames" v-if="queryParams.compareType === 'supplier'">
          <el-select
            v-model="queryParams.supplierNames"
            multiple
            placeholder="请选择要对比的供应商"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="supplier in supplierOptions"
              :key="supplier"
              :label="supplier"
              :value="supplier"
            />
          </el-select>
        </el-form-item>

        <!-- 参数编号对比 -->
        <el-form-item label="选择参数" prop="paramNumbers" v-if="queryParams.compareType === 'paramNumber'">
          <el-select
            v-model="queryParams.paramNumbers"
            multiple
            placeholder="请选择要对比的参数编号"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="param in paramNumberOptions"
              :key="param.groupId"
              :label="param.paramNumber + ' - ' + param.materialName"
              :value="param.groupId"
            />
          </el-select>
        </el-form-item>

        <!-- 工艺类型对比 -->
        <el-form-item label="选择工艺" prop="processTypes" v-if="queryParams.compareType === 'processType'">
          <el-select
            v-model="queryParams.processTypes"
            multiple
            placeholder="请选择要对比的工艺类型"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="type in processTypeOptions"
              :key="type"
              :label="type"
              :value="type"
            />
          </el-select>
        </el-form-item>

        <!-- 时间趋势分析 -->
        <el-form-item label="时间范围" prop="dateRange" v-if="queryParams.compareType === 'timeTrend'">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 300px;"
            clearable
          />
        </el-form-item>

        <!-- 供应商vs测试值对比 -->
        <el-form-item label="选择参数" prop="compareParam" v-if="queryParams.compareType === 'supplierVsTest'">
          <el-select
            v-model="queryParams.compareParam"
            placeholder="请选择要对比的参数"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="param in paramNumberOptions"
              :key="param.groupId"
              :label="param.paramNumber + ' - ' + param.materialName"
              :value="param.groupId"
            />
          </el-select>
        </el-form-item>



        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" :loading="loading">生成对比图表</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置条件</el-button>
        </el-form-item>
      </el-form>

      <!-- 图表类型选择 -->
      <el-row style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-radio-group v-model="chartType" @change="handleChartTypeChange">
            <el-radio-button label="line">折线图</el-radio-button>
            <el-radio-button label="bar">柱状图</el-radio-button>
            <el-radio-button label="scatter">散点图</el-radio-button>
            <el-radio-button label="radar">雷达图</el-radio-button>
            <el-radio-button label="heatmap">热力图</el-radio-button>
          </el-radio-group>
          <el-button-group style="margin-left: 20px;">
            <el-button size="small" @click="toggleDataTable">{{ showDataTable ? '隐藏' : '显示' }}数据表</el-button>
            <el-button size="small" @click="toggleProjectDetails" :disabled="selectedParamDetails.length === 0">
              {{ showProjectDetails ? '隐藏' : '显示' }}项目详情
            </el-button>
            <el-button size="small" @click="toggleFullscreen">全屏显示</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </el-card>

    <!-- 参数详情信息卡片 -->
    <el-card v-if="selectedParamDetails.length > 0 && showProjectDetails" class="box-card param-details-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold; font-size: 14px;">📋 选中项目详情信息</span>
        <el-tag size="mini" type="info" style="margin-left: 10px;">{{ selectedParamDetails.length }}项</el-tag>
        <el-button type="text" @click="showProjectDetails = false" style="float: right; color: #909399;">
          <i class="el-icon-close"></i>
        </el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="8" v-for="(detail, index) in selectedParamDetails" :key="index">
          <el-card class="param-detail-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span style="font-weight: bold; color: #409EFF;">
                <i class="el-icon-data-line"></i>
                {{ detail.paramNumber || detail.name }}
              </span>
              <el-tag size="mini" type="success" style="float: right;" v-if="detail.testCount">
                {{ detail.testCount }}次测试
              </el-tag>
            </div>

            <!-- 基本信息 -->
            <div class="detail-section">
              <div class="section-title">
                <i class="el-icon-info"></i>
                基本信息
              </div>
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="材料名称" v-if="detail.materialName">
                  <el-tag type="primary" size="mini">{{ detail.materialName }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="供应商" v-if="detail.supplierName">
                  <el-tag type="success" size="mini">{{ detail.supplierName }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="材料型号" v-if="detail.materialModel">
                  <span>{{ detail.materialModel }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="工艺类型" v-if="detail.processType">
                  <el-tag type="warning" size="mini">{{ detail.processType }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="性能类型" v-if="detail.performanceType">
                  <el-tag type="info" size="mini">{{ detail.performanceType }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="参数编号" v-if="detail.paramNumber">
                  <span>{{ detail.paramNumber }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="数据量" v-if="detail.dataCount !== undefined">
                  <el-tag type="danger" size="mini">{{ detail.dataCount }}条</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="创建时间" v-if="detail.createTime">
                  <span>{{ detail.createTime }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 统计信息 -->
            <div class="detail-section" v-if="detail.statistics">
              <div class="section-title">
                <i class="el-icon-data-analysis"></i>
                统计信息
              </div>
              <el-row :gutter="10">
                <el-col :span="12" v-if="detail.statistics.avgValue !== undefined">
                  <div class="stat-item">
                    <div class="stat-label">平均值</div>
                    <div class="stat-value">{{ formatNumber(detail.statistics.avgValue) }}</div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="detail.statistics.maxValue !== undefined">
                  <div class="stat-item">
                    <div class="stat-label">最大值</div>
                    <div class="stat-value">{{ formatNumber(detail.statistics.maxValue) }}</div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="detail.statistics.minValue !== undefined">
                  <div class="stat-item">
                    <div class="stat-label">最小值</div>
                    <div class="stat-value">{{ formatNumber(detail.statistics.minValue) }}</div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="detail.statistics.stdDev !== undefined">
                  <div class="stat-item">
                    <div class="stat-label">标准差</div>
                    <div class="stat-value">{{ formatNumber(detail.statistics.stdDev) }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 参数明细 -->
            <div class="detail-section" v-if="detail.mainParams && detail.mainParams.length > 0">
              <div class="section-title">
                <i class="el-icon-menu"></i>
                参数明细
                <el-tag size="mini" type="info" style="margin-left: 5px;">{{ detail.mainParams.length }}个</el-tag>
              </div>
              <div class="params-container">
                <el-tooltip
                  v-for="param in detail.mainParams"
                  :key="param.paramName"
                  :content="`${param.paramName}: ${param.paramValue || 'N/A'} ${param.unit || ''}`"
                  placement="top"
                >
                  <el-tag
                    size="mini"
                    :type="getParamTagType(param)"
                    style="margin-right: 5px; margin-bottom: 3px; cursor: pointer;"
                    @click="showParamDetail(param)"
                  >
                    {{ param.paramName }}
                    <span v-if="param.paramValue" style="margin-left: 3px; opacity: 0.8;">
                      ({{ formatNumber(param.paramValue) }})
                    </span>
                  </el-tag>
                </el-tooltip>
              </div>
            </div>

            <!-- 测试方案信息 -->
            <div class="detail-section" v-if="detail.testPlanInfo">
              <div class="section-title">
                <i class="el-icon-document"></i>
                测试方案
              </div>
              <div class="detail-item">
                <label>方案编号：</label>
                <span class="detail-value">{{ detail.testPlanInfo.planCode }}</span>
              </div>
              <div class="detail-item" v-if="detail.testPlanInfo.testEquipment">
                <label>测试设备：</label>
                <span class="detail-value">{{ detail.testPlanInfo.testEquipment }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold; font-size: 14px;">{{ chartTitle }}</span>
        <div style="float: right;">
          <el-tooltip content="图表说明" placement="top">
            <el-button type="text" icon="el-icon-question" @click="showChartHelp" />
          </el-tooltip>
        </div>
      </div>

      <div v-loading="chartLoading" element-loading-text="正在生成图表...">
        <div
          ref="chart"
          :style="{ height: chartHeight + 'px', width: '100%' }"
          v-show="!showDataTable"
        ></div>

        <!-- 数据表格 -->
        <el-table
          v-show="showDataTable"
          :data="chartData"
          style="width: 100%"
          :max-height="chartHeight"
        >
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope" v-if="column.prop === 'paramDetails'">
              <span v-if="scope.row.paramDetails && scope.row.paramDetails.length > 0">
                <el-tag
                  v-for="(param, index) in scope.row.paramDetails"
                  :key="index"
                  size="mini"
                  type="info"
                  style="margin-right: 5px; margin-bottom: 2px;"
                >
                  {{ param.paramName }}: {{ param.paramValue }}{{ param.unit }}
                </el-tag>
              </span>
              <span v-else style="color: #909399;">暂无参数</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" style="margin-top: 20px;" v-if="statisticsData.length > 0">
      <el-col :span="6" v-for="(stat, index) in statisticsData" :key="index">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-title">{{ stat.title }}</div>
            <div class="statistics-value">{{ stat.value }}</div>
            <div class="statistics-desc">{{ stat.description }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表说明对话框 -->
    <el-dialog title="📊 图表说明" :visible.sync="helpDialogVisible" width="700px" append-to-body>
      <div class="chart-help-content">
        <h4>🎯 图表类型说明：</h4>
        <ul>
          <li><strong>📈 折线图：</strong>适用于展示数据随时间或其他连续变量的变化趋势，清晰显示数据走向</li>
          <li><strong>📊 柱状图：</strong>适用于比较不同类别之间的数值大小，直观对比差异</li>
          <li><strong>🔵 散点图：</strong>适用于展示两个变量之间的相关关系，发现数据规律</li>
          <li><strong>🕸️ 雷达图：</strong>适用于多维度数据的综合对比，全面评估性能</li>
          <li><strong>🌡️ 热力图：</strong>适用于展示数据的分布密度和相关性，识别热点区域</li>
        </ul>

        <h4>🔍 对比维度说明：</h4>
        <ul>
          <li><strong>📊 材料性能对比：</strong>比较不同材料的性能表现，识别最优材料</li>
          <li><strong>🏭 供应商数据对比：</strong>比较不同供应商材料的质量差异，评估供应商可靠性</li>
          <li><strong>🔢 参数编号对比：</strong>比较不同参数编号下的测试值趋势，分析参数影响</li>
          <li><strong>⚙️ 工艺类型对比：</strong>比较不同工艺类型的效果，优化工艺流程</li>
          <li><strong>📈 时间趋势分析：</strong>展示测试数据随时间的变化规律，预测发展趋势</li>
          <li><strong>⚖️ 供应商vs测试值：</strong>对比供应商提供数据与实际测试结果的差异</li>
        </ul>

        <h4>💡 使用技巧：</h4>
        <ul>
          <li>将鼠标悬停在图表数据点上可查看详细信息和参数明细</li>
          <li>点击参数标签可查看该参数的详细信息</li>
          <li>使用"显示数据表"功能可查看原始数据</li>
          <li>选择合适的图表类型能更好地展示数据特征</li>
          <li>多选对比项目可进行横向比较分析</li>
        </ul>
      </div>
    </el-dialog>

    <!-- 参数详情对话框 -->
    <el-dialog
      title="📋 参数详细信息"
      :visible.sync="paramDetailDialogVisible"
      width="500px"
      append-to-body
      v-if="currentParamDetail"
    >
      <div class="param-detail-content">
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="参数名称">
            <el-tag type="primary">{{ currentParamDetail.paramName }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="参数数值">
            <span style="font-weight: bold; color: #67C23A;">
              {{ formatNumber(currentParamDetail.paramValue) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="参数单位" v-if="currentParamDetail.unit">
            {{ currentParamDetail.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="数据类型">
            {{ typeof currentParamDetail.paramValue === 'number' ? '数值型' : '文本型' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" v-if="currentParamDetail.createTime">
            {{ currentParamDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" v-if="currentParamDetail.updateTime">
            {{ currentParamDetail.updateTime }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentParamDetail.remark" style="margin-top: 15px;">
          <h4 style="color: #409EFF; margin-bottom: 8px;">📝 备注信息：</h4>
          <p style="background: #f5f7fa; padding: 10px; border-radius: 4px; margin: 0;">
            {{ currentParamDetail.remark }}
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { listTestResult, getTestResultOptions } from "@/api/material/testResult";
import { listMaterial } from "@/api/material/material";
import { listProcessParamGroup } from "@/api/material/processParamGroup";
import { listProcessParamItem } from "@/api/material/processParamItem";

export default {
  name: "MaterialTrend",
  data() {
    return {
      // 加载状态
      loading: false,
      // 图表实例
      chart: null,
      // 图表类型
      chartType: 'line',
      // 图表高度
      chartHeight: 400,
      // 图表标题
      chartTitle: '数据趋势对比分析',
      // 图表加载状态
      chartLoading: false,
      // 是否显示数据表
      showDataTable: false,
      // 是否全屏
      isFullscreen: false,

      // 查询参数
      queryParams: {
        compareType: 'material',
        paramNumbers: [],
        materialNames: [],
        supplierNames: [],
        processTypes: [],
        dateRange: null,
        compareParam: null
      },

      // 选项数据
      paramNumberOptions: [],
      materialOptions: [],
      supplierOptions: [],
      processTypeOptions: [],

      // 图表数据
      chartData: [],
      tableColumns: [],

      // 参数详情
      selectedParamDetails: [],

      // 统计数据
      statisticsData: [],

      // 帮助对话框
      helpDialogVisible: false,

      // 使用指南显示状态
      showUsageGuide: false,

      // 项目详情显示状态
      showProjectDetails: false,

      // 参数详情对话框
      paramDetailDialogVisible: false,
      currentParamDetail: null
    };
  },
  mounted() {
    this.initChart();
    this.loadOptions();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    /** 初始化图表 */
    initChart() {
      this.chart = echarts.init(this.$refs.chart);

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize();
        }
      });
    },

    /** 加载选项数据 */
    async loadOptions() {
      try {
        // 加载参数编号选项
        const paramResponse = await listProcessParamGroup({});
        this.paramNumberOptions = paramResponse.rows || [];

        // 加载材料选项
        const materialResponse = await listMaterial({});
        this.materialOptions = materialResponse.rows || [];

        // 加载供应商选项
        const supplierResponse = await getTestResultOptions({ type: 'supplierName' });
        this.supplierOptions = supplierResponse.data || [];

        // 加载工艺类型选项
        const processResponse = await getTestResultOptions({ type: 'processType' });
        this.processTypeOptions = processResponse.data || [];

      } catch (error) {
        console.error('加载选项数据失败：', error);
        this.$modal.msgError('加载选项数据失败');
      }
    },

    /** 对比类型改变 */
    handleCompareTypeChange(value) {
      // 重置相关参数
      this.queryParams.paramNumbers = [];
      this.queryParams.materialNames = [];
      this.queryParams.supplierNames = [];
      this.queryParams.processTypes = [];
      this.queryParams.dateRange = null;
      this.queryParams.compareParam = null;

      // 清空选中的参数详情和图表数据
      this.selectedParamDetails = [];
      this.chartData = [];
      this.statisticsData = [];

      // 更新图表标题
      this.updateChartTitle();
    },

    /** 更新图表标题 */
    updateChartTitle() {
      const typeMap = {
        'paramNumber': '参数编号对比分析',
        'material': '材料性能对比分析',
        'supplier': '供应商质量对比分析',
        'processType': '工艺类型效果对比分析',
        'timeTrend': '时间趋势对比分析'
      };
      this.chartTitle = typeMap[this.queryParams.compareType] || '对比分析图';
    },

    /** 图表类型改变 */
    handleChartTypeChange(type) {
      this.chartType = type;
      if (this.chartData.length > 0) {
        this.renderChart();
      }
    },

    /** 查询数据 */
    async handleQuery() {
      if (!this.validateQuery()) {
        return;
      }

      this.loading = true;
      this.chartLoading = true;

      try {
        // 根据对比类型获取不同的数据
        let chartData = [];
        let paramDetails = [];

        switch (this.queryParams.compareType) {
          case 'material':
            chartData = await this.getMaterialCompareData();
            break;
          case 'supplier':
            chartData = await this.getSupplierCompareData();
            break;
          case 'paramNumber':
            chartData = await this.getParamNumberCompareData();
            break;
          case 'processType':
            chartData = await this.getProcessTypeCompareData();
            break;
          case 'timeTrend':
            chartData = await this.getTimeTrendData();
            break;
          case 'supplierVsTest':
            chartData = await this.getSupplierVsTestData();
            break;
        }

        this.chartData = chartData;
        this.updateTableColumns();
        this.renderChart();

        // 更新选中参数详情
        this.updateSelectedParamDetails();

      } catch (error) {
        console.error('获取对比数据失败：', error);
        this.$modal.msgError('获取对比数据失败');
      } finally {
        this.loading = false;
        this.chartLoading = false;
      }
    },

    /** 验证查询条件 */
    validateQuery() {
      const { compareType } = this.queryParams;

      if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length === 0) {
        this.$message.warning('请选择至少一个参数编号');
        return false;
      }

      if (compareType === 'material' && this.queryParams.materialNames.length === 0) {
        this.$message.warning('请选择至少一个材料');
        return false;
      }

      if (compareType === 'supplier' && this.queryParams.supplierNames.length === 0) {
        this.$message.warning('请选择至少一个供应商');
        return false;
      }

      if (compareType === 'processType' && this.queryParams.processTypes.length === 0) {
        this.$message.warning('请选择至少一个工艺类型');
        return false;
      }

      if (compareType === 'timeTrend' && !this.queryParams.dateRange) {
        this.$message.warning('请选择时间范围');
        return false;
      }

      if (compareType === 'supplierVsTest' && !this.queryParams.compareParam) {
        this.$message.warning('请选择要对比的参数');
        return false;
      }

      return true;
    },

    /** 获取材料对比数据 */
    async getMaterialCompareData() {
      const materialIds = this.queryParams.materialNames || [];
      const compareData = [];

      if (materialIds.length === 0) {
        return compareData;
      }

      for (const materialId of materialIds) {
        try {
          // 通过材料ID查找对应的参数组，然后查找测试结果
          const paramGroupResponse = await listProcessParamGroup({
            materialId: materialId,
            pageNum: 1,
            pageSize: 1000
          });

          const paramGroups = paramGroupResponse.rows || [];
          const material = this.materialOptions.find(m => m.materialId === materialId);

          let allSupplierValues = [];
          let allTestValues = [];

          // 遍历该材料的所有参数组，获取测试结果
          for (const group of paramGroups) {
            const testResponse = await listTestResult({
              groupId: group.groupId,
              pageNum: 1,
              pageSize: 1000
            });

            const testResults = testResponse.rows || [];
            const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));
            const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));

            allSupplierValues = allSupplierValues.concat(supplierValues);
            allTestValues = allTestValues.concat(testValues);
          }

          compareData.push({
            name: material ? material.materialName : `材料${materialId}`,
            supplier: material ? material.supplierName : '',
            supplierAvg: allSupplierValues.length > 0 ? (allSupplierValues.reduce((a, b) => a + b, 0) / allSupplierValues.length).toFixed(2) : 0,
            testAvg: allTestValues.length > 0 ? (allTestValues.reduce((a, b) => a + b, 0) / allTestValues.length).toFixed(2) : 0,
            supplierMax: allSupplierValues.length > 0 ? Math.max(...allSupplierValues).toFixed(2) : 0,
            testMax: allTestValues.length > 0 ? Math.max(...allTestValues).toFixed(2) : 0,
            supplierMin: allSupplierValues.length > 0 ? Math.min(...allSupplierValues).toFixed(2) : 0,
            testMin: allTestValues.length > 0 ? Math.min(...allTestValues).toFixed(2) : 0,
            dataCount: allTestValues.length
          });
        } catch (error) {
          console.error(`获取材料${materialId}数据失败：`, error);
        }
      }

      return compareData;
    },

    /** 获取供应商对比数据 */
    async getSupplierCompareData() {
      const suppliers = this.queryParams.supplierNames || [];
      const compareData = [];

      if (suppliers.length === 0) {
        return compareData;
      }

      for (const supplier of suppliers) {
        try {
          const response = await listTestResult({
            supplierName: supplier,
            pageNum: 1,
            pageSize: 1000
          });

          const testResults = response.rows || [];
          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));
          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));

          compareData.push({
            name: supplier,
            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,
            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,
            accuracy: supplierValues.length > 0 && testValues.length > 0 ?
              (100 - Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -
              (testValues.reduce((a, b) => a + b, 0) / testValues.length)) /
              (testValues.reduce((a, b) => a + b, 0) / testValues.length) * 100).toFixed(2) : 0,
            dataCount: testResults.length
          });
        } catch (error) {
          console.error(`获取供应商${supplier}数据失败：`, error);
        }
      }

      return compareData;
    },

    /** 获取参数编号对比数据 */
    async getParamNumberCompareData() {
      const paramGroupIds = this.queryParams.paramNumbers || [];
      const compareData = [];

      if (paramGroupIds.length === 0) {
        return compareData;
      }

      for (const groupId of paramGroupIds) {
        try {
          // 获取测试结果数据
          const response = await listTestResult({
            groupId: groupId,
            pageNum: 1,
            pageSize: 1000
          });

          // 获取参数明细数据
          const paramItemResponse = await listProcessParamItem({
            groupId: groupId,
            pageNum: 1,
            pageSize: 1000
          });

          const paramGroup = this.paramNumberOptions.find(p => p.groupId === groupId);
          const testResults = response.rows || [];
          const paramItems = paramItemResponse.rows || [];

          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));
          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));

          // 格式化参数明细信息
          const paramDetails = paramItems.map(item => ({
            paramName: item.paramName || 'N/A',
            paramValue: item.paramValue !== null && item.paramValue !== undefined ?
              String(item.paramValue) : 'N/A',
            unit: item.unit || ''
          }));

          compareData.push({
            name: paramGroup ? paramGroup.paramNumber : `参数${groupId}`,
            material: paramGroup ? paramGroup.materialName : '',
            processType: paramGroup ? paramGroup.processType : '',
            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,
            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,
            deviation: supplierValues.length > 0 && testValues.length > 0 ?
              Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -
              (testValues.reduce((a, b) => a + b, 0) / testValues.length)).toFixed(2) : 0,
            dataCount: testResults.length,
            paramDetails: paramDetails, // 添加参数明细信息
            groupId: groupId // 保存groupId用于后续使用
          });
        } catch (error) {
          console.error(`获取参数组${groupId}数据失败：`, error);
        }
      }

      return compareData;
    },

    /** 获取工艺类型对比数据 */
    async getProcessTypeCompareData() {
      const processTypes = this.queryParams.processTypes || [];
      const compareData = [];

      if (processTypes.length === 0) {
        return compareData;
      }

      for (const processType of processTypes) {
        try {
          const response = await listTestResult({
            processType: processType,
            pageNum: 1,
            pageSize: 1000
          });

          const testResults = response.rows || [];
          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));
          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));

          compareData.push({
            name: processType,
            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,
            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,
            stability: testValues.length > 1 ? this.calculateStandardDeviation(testValues).toFixed(2) : 0,
            dataCount: testResults.length
          });
        } catch (error) {
          console.error(`获取工艺类型${processType}数据失败：`, error);
        }
      }

      return compareData;
    },

    /** 获取时间趋势数据 */
    async getTimeTrendData() {
      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {
        return [];
      }

      try {
        const [startDate, endDate] = this.queryParams.dateRange;
        const response = await listTestResult({
          startDate: startDate,
          endDate: endDate,
          pageNum: 1,
          pageSize: 1000
        });

        const testResults = response.rows || [];
        const trendData = [];

        // 按日期分组
        const dateGroups = {};
        testResults.forEach(result => {
          const date = result.createTime ? result.createTime.split(' ')[0] : '';
          if (date && !dateGroups[date]) {
            dateGroups[date] = [];
          }
          if (date) {
            dateGroups[date].push(result);
          }
        });

        // 计算每日平均值
        Object.keys(dateGroups).sort().forEach(date => {
          const dayResults = dateGroups[date];
          const testValues = dayResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));

          trendData.push({
            date: date,
            avgValue: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,
            count: dayResults.length
          });
        });

        return trendData;
      } catch (error) {
        console.error('获取时间趋势数据失败：', error);
        return [];
      }
    },

    /** 获取供应商vs测试值对比数据 */
    async getSupplierVsTestData() {
      const groupId = this.queryParams.compareParam;

      if (!groupId) {
        return [];
      }

      try {
        const response = await listTestResult({
          groupId: groupId,
          pageNum: 1,
          pageSize: 1000
        });

        const testResults = response.rows || [];
        const compareData = testResults.map(result => ({
          name: result.materialName || '未知材料',
          supplier: result.supplierName || '未知供应商',
          supplierValue: parseFloat(result.supplierDatasheetVal) || 0,
          testValue: parseFloat(result.testValue) || 0,
          difference: Math.abs((parseFloat(result.supplierDatasheetVal) || 0) - (parseFloat(result.testValue) || 0)).toFixed(2),
          createTime: result.createTime
        }));

        return compareData;
      } catch (error) {
        console.error('获取供应商vs测试值数据失败：', error);
        return [];
      }
    },

    /** 计算标准差 */
    calculateStandardDeviation(values) {
      const avg = values.reduce((a, b) => a + b, 0) / values.length;
      const squareDiffs = values.map(value => Math.pow(value - avg, 2));
      const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
      return Math.sqrt(avgSquareDiff);
    },

    /** 格式化参数明细显示 */
    formatParamDetails(row, column, cellValue) {
      if (!cellValue || !Array.isArray(cellValue)) {
        return '暂无参数';
      }

      return cellValue.map(param => {
        let text = param.paramName + ': ' + param.paramValue;
        if (param.unit) {
          text += ' ' + param.unit;
        }
        return text;
      }).join('; ');
    },

    /** 更新表格列 */
    updateTableColumns() {
      const { compareType } = this.queryParams;

      switch (compareType) {
        case 'material':
          this.tableColumns = [
            { prop: 'name', label: '材料名称', width: 150 },
            { prop: 'supplier', label: '供应商', width: 120 },
            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },
            { prop: 'testAvg', label: '测试平均值', width: 120 },
            { prop: 'dataCount', label: '数据量', width: 80 }
          ];
          break;
        case 'supplier':
          this.tableColumns = [
            { prop: 'name', label: '供应商', width: 150 },
            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },
            { prop: 'testAvg', label: '测试平均值', width: 120 },
            { prop: 'accuracy', label: '准确率(%)', width: 100 },
            { prop: 'dataCount', label: '数据量', width: 80 }
          ];
          break;
        case 'paramNumber':
          this.tableColumns = [
            { prop: 'name', label: '参数编号', width: 120 },
            { prop: 'material', label: '材料名称', width: 120 },
            { prop: 'processType', label: '工艺类型', width: 100 },
            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },
            { prop: 'testAvg', label: '测试平均值', width: 120 },
            { prop: 'deviation', label: '偏差', width: 80 },
            { prop: 'paramDetails', label: '参数明细', width: 200 }
          ];
          break;
        case 'processType':
          this.tableColumns = [
            { prop: 'name', label: '工艺类型', width: 150 },
            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },
            { prop: 'testAvg', label: '测试平均值', width: 120 },
            { prop: 'stability', label: '稳定性', width: 100 },
            { prop: 'dataCount', label: '数据量', width: 80 }
          ];
          break;
        case 'timeTrend':
          this.tableColumns = [
            { prop: 'date', label: '日期', width: 120 },
            { prop: 'avgValue', label: '平均值', width: 100 },
            { prop: 'count', label: '数据量', width: 80 }
          ];
          break;
        case 'supplierVsTest':
          this.tableColumns = [
            { prop: 'name', label: '材料名称', width: 150 },
            { prop: 'supplier', label: '供应商', width: 120 },
            { prop: 'supplierValue', label: '供应商值', width: 100 },
            { prop: 'testValue', label: '测试值', width: 100 },
            { prop: 'difference', label: '差值', width: 80 }
          ];
          break;
      }
    },

    /** 渲染图表 */
    renderChart() {
      if (!this.chart || this.chartData.length === 0) {
        return;
      }

      let option = {};

      switch (this.chartType) {
        case 'line':
          option = this.getLineChartOption();
          break;
        case 'bar':
          option = this.getBarChartOption();
          break;
        case 'scatter':
          option = this.getScatterChartOption();
          break;
        case 'radar':
          option = this.getRadarChartOption();
          break;
        case 'heatmap':
          option = this.getHeatmapChartOption();
          break;
      }

      this.chart.setOption(option, true);
    },

    /** 获取折线图配置 */
    getLineChartOption() {
      // 根据对比类型生成不同的图表配置
      const { compareType } = this.queryParams;

      if (compareType === 'timeTrend') {
        // 时间趋势图
        return {
          title: {
            text: this.chartTitle,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              let result = params[0].name + '<br/>';
              params.forEach(param => {
                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
              });
              return result;
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.chartData.map(item => item.date)
          },
          yAxis: {
            type: 'value',
            name: '平均值'
          },
          series: [{
            name: '平均值',
            type: 'line',
            data: this.chartData.map(item => item.avgValue),
            smooth: true,
            symbol: 'circle',
            symbolSize: 6
          }]
        };
      } else {
        // 其他对比类型的折线图
        const self = this;

        return {
          title: {
            text: this.chartTitle,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(50, 50, 50, 0.95)',
            borderColor: '#409EFF',
            borderWidth: 1,
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); border-radius: 8px; padding: 12px;',
            formatter: function(params) {
              const dataIndex = params[0].dataIndex;
              const currentData = self.chartData[dataIndex];

              let result = `<div style="font-size: 14px; font-weight: bold; color: #409EFF; margin-bottom: 8px;">
                            📊 ${params[0].name}
                          </div>`;

              // 显示基本对比数据
              params.forEach(param => {
                const color = param.color;
                result += `<div style="margin: 4px 0; display: flex; align-items: center;">
                          <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;"></span>
                          <span style="font-weight: 500;">${param.seriesName}:</span>
                          <span style="margin-left: 8px; color: #67C23A; font-weight: bold;">${self.formatNumber(param.value)}</span>
                        </div>`;
              });

              // 根据对比类型显示详细信息
              if (currentData) {
                result += '<div style="border-top: 1px solid #666; margin: 8px 0; padding-top: 8px;">';

                if (self.queryParams.compareType === 'paramNumber' && currentData.paramDetails) {
                  result += '<div style="color: #E6A23C; font-weight: bold; margin-bottom: 6px;">📋 参数明细信息</div>';
                  if (currentData.material) {
                    result += `<div style="margin: 2px 0;"><span style="color: #909399;">材料:</span> ${currentData.material}</div>`;
                  }
                  if (currentData.processType) {
                    result += `<div style="margin: 2px 0;"><span style="color: #909399;">工艺:</span> ${currentData.processType}</div>`;
                  }
                  if (currentData.paramDetails && currentData.paramDetails.length > 0) {
                    result += '<div style="margin: 4px 0; color: #909399;">参数列表:</div>';
                    currentData.paramDetails.slice(0, 5).forEach(param => {
                      result += `<div style="margin: 1px 0; padding-left: 12px; font-size: 11px;">
                                • ${param.paramName}: <span style="color: #67C23A;">${self.formatNumber(param.paramValue)}</span>
                                ${param.unit ? ' <span style="color: #909399;">' + param.unit + '</span>' : ''}
                              </div>`;
                    });
                    if (currentData.paramDetails.length > 5) {
                      result += `<div style="margin: 2px 0; padding-left: 12px; color: #909399; font-size: 11px;">
                                ... 还有 ${currentData.paramDetails.length - 5} 个参数
                              </div>`;
                    }
                  }
                } else if (self.queryParams.compareType === 'material' && currentData.supplier) {
                  result += '<div style="color: #E6A23C; font-weight: bold; margin-bottom: 6px;">🏭 供应商信息</div>';
                  result += `<div style="margin: 2px 0;"><span style="color: #909399;">供应商:</span> ${currentData.supplier}</div>`;
                  if (currentData.dataCount) {
                    result += `<div style="margin: 2px 0;"><span style="color: #909399;">数据量:</span> ${currentData.dataCount} 条</div>`;
                  }
                } else if (self.queryParams.compareType === 'supplier' && currentData.accuracy) {
                  result += '<div style="color: #E6A23C; font-weight: bold; margin-bottom: 6px;">📈 质量指标</div>';
                  result += `<div style="margin: 2px 0;"><span style="color: #909399;">准确率:</span> <span style="color: ${currentData.accuracy > 90 ? '#67C23A' : currentData.accuracy > 80 ? '#E6A23C' : '#F56C6C'};">${currentData.accuracy}%</span></div>`;
                  if (currentData.dataCount) {
                    result += `<div style="margin: 2px 0;"><span style="color: #909399;">数据量:</span> ${currentData.dataCount} 条</div>`;
                  }
                } else if (self.queryParams.compareType === 'processType' && currentData.stability) {
                  result += '<div style="color: #E6A23C; font-weight: bold; margin-bottom: 6px;">⚙️ 工艺指标</div>';
                  result += `<div style="margin: 2px 0;"><span style="color: #909399;">稳定性:</span> <span style="color: ${currentData.stability > 90 ? '#67C23A' : currentData.stability > 80 ? '#E6A23C' : '#F56C6C'};">${currentData.stability}%</span></div>`;
                  if (currentData.dataCount) {
                    result += `<div style="margin: 2px 0;"><span style="color: #909399;">数据量:</span> ${currentData.dataCount} 条</div>`;
                  }
                }

                result += '</div>';
              }

              return result;
            }
          },
          legend: {
            top: '10%',
            data: ['供应商数据', '测试数据']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.chartData.map(item => item.name)
          },
          yAxis: {
            type: 'value',
            name: '数值'
          },
          series: [
            {
              name: '供应商数据',
              type: 'line',
              data: this.chartData.map(item => item.supplierAvg || 0),
              smooth: true,
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '测试数据',
              type: 'line',
              data: this.chartData.map(item => item.testAvg || 0),
              smooth: true,
              symbol: 'triangle',
              symbolSize: 6
            }
          ]
        };
      }
    },

    /** 获取柱状图配置 */
    getBarChartOption() {
      const self = this;

      return {
        title: {
          text: this.chartTitle,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const dataIndex = params[0].dataIndex;
            const currentData = self.chartData[dataIndex];

            let result = params[0].name + '<br/>';

            // 显示基本对比数据
            params.forEach(param => {
              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
            });

            // 如果是参数编号对比且有参数明细，显示参数明细信息
            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {
              result += '<br/><strong>参数明细信息：</strong><br/>';
              if (currentData.material) {
                result += '材料：' + currentData.material + '<br/>';
              }
              if (currentData.processType) {
                result += '工艺：' + currentData.processType + '<br/>';
              }
              result += '参数列表：<br/>';

              currentData.paramDetails.forEach(param => {
                result += '• ' + param.paramName + ': ' + param.paramValue;
                if (param.unit) {
                  result += ' ' + param.unit;
                }
                result += '<br/>';
              });
            }

            return result;
          }
        },
        legend: {
          top: '10%',
          data: ['供应商数据', '测试数据']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map(item => item.name),
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '数值'
        },
        series: [
          {
            name: '供应商数据',
            type: 'bar',
            data: this.chartData.map(item => item.supplierAvg || 0),
            barWidth: '30%',
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '测试数据',
            type: 'bar',
            data: this.chartData.map(item => item.testAvg || 0),
            barWidth: '30%',
            itemStyle: {
              color: '#91cc75'
            }
          }
        ]
      };
    },

    /** 获取散点图配置 */
    getScatterChartOption() {
      const self = this;

      // 散点图主要用于供应商vs测试值对比
      const scatterData = this.chartData.map((item, index) => [
        parseFloat(item.supplierValue || item.supplierAvg) || 0,
        parseFloat(item.testValue || item.testAvg) || 0,
        item.name, // 用于tooltip显示
        index // 数据索引，用于获取详细信息
      ]);

      return {
        title: {
          text: this.chartTitle,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const [supplierVal, testVal, name, dataIndex] = params.data;
            const currentData = self.chartData[dataIndex];

            let result = `${name}<br/>供应商值: ${supplierVal}<br/>测试值: ${testVal}<br/>差值: ${Math.abs(supplierVal - testVal).toFixed(2)}`;

            // 如果是参数编号对比且有参数明细，显示参数明细信息
            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {
              result += '<br/><br/><strong>参数明细信息：</strong><br/>';
              if (currentData.material) {
                result += '材料：' + currentData.material + '<br/>';
              }
              if (currentData.processType) {
                result += '工艺：' + currentData.processType + '<br/>';
              }
              result += '参数列表：<br/>';

              currentData.paramDetails.forEach(param => {
                result += '• ' + param.paramName + ': ' + param.paramValue;
                if (param.unit) {
                  result += ' ' + param.unit;
                }
                result += '<br/>';
              });
            }

            return result;
          }
        },
        xAxis: {
          type: 'value',
          name: '供应商数据',
          scale: true,
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'value',
          name: '测试数据',
          scale: true,
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: [{
          name: '数据对比',
          type: 'scatter',
          data: scatterData,
          symbolSize: 8,
          itemStyle: {
            color: '#5470c6'
          }
        }, {
          name: '理想线',
          type: 'line',
          data: [[0, 0], [Math.max(...scatterData.map(d => d[0])), Math.max(...scatterData.map(d => d[0]))]],
          lineStyle: {
            color: '#ff6b6b',
            type: 'dashed'
          },
          symbol: 'none'
        }]
      };
    },

    /** 获取雷达图配置 */
    getRadarChartOption() {
      // 雷达图用于多维度对比，基于chartData生成指标
      const indicators = [
        { name: '供应商平均值', max: 100 },
        { name: '测试平均值', max: 100 },
        { name: '数据量', max: 50 },
        { name: '准确率', max: 100 },
        { name: '稳定性', max: 10 }
      ];

      const radarData = this.chartData.map(item => {
        const supplierAvg = parseFloat(item.supplierAvg) || 0;
        const testAvg = parseFloat(item.testAvg) || 0;
        const dataCount = parseInt(item.dataCount) || 0;
        const accuracy = parseFloat(item.accuracy) || 0;
        const stability = parseFloat(item.stability) || 0;

        return {
          name: item.name,
          value: [
            Math.min(supplierAvg, 100),
            Math.min(testAvg, 100),
            Math.min(dataCount, 50),
            Math.min(accuracy, 100),
            Math.min(stability, 10)
          ]
        };
      });

      const self = this;

      return {
        title: {
          text: this.chartTitle,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const dataIndex = params.dataIndex;
            const currentData = self.chartData[dataIndex];

            let result = params.name + '<br/>';

            // 显示雷达图数据
            const indicators = ['供应商平均值', '测试平均值', '数据量', '准确率', '稳定性'];
            params.value.forEach((value, index) => {
              result += indicators[index] + ': ' + value + '<br/>';
            });

            // 如果是参数编号对比且有参数明细，显示参数明细信息
            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {
              result += '<br/><strong>参数明细信息：</strong><br/>';
              if (currentData.material) {
                result += '材料：' + currentData.material + '<br/>';
              }
              if (currentData.processType) {
                result += '工艺：' + currentData.processType + '<br/>';
              }
              result += '参数列表：<br/>';

              currentData.paramDetails.forEach(param => {
                result += '• ' + param.paramName + ': ' + param.paramValue;
                if (param.unit) {
                  result += ' ' + param.unit;
                }
                result += '<br/>';
              });
            }

            return result;
          }
        },
        legend: {
          top: '10%',
          data: radarData.map(item => item.name)
        },
        radar: {
          indicator: indicators,
          radius: '60%'
        },
        series: [{
          type: 'radar',
          data: radarData
        }]
      };
    },

    /** 获取热力图配置 */
    getHeatmapChartOption() {
      // 热力图用于展示数据密度和分布
      const xAxisData = [...new Set(this.chartData.map(item => item.name))];
      const yAxisData = ['供应商数据', '测试数据', '偏差'];

      const heatmapData = [];
      this.chartData.forEach((item, xIndex) => {
        const supplierAvg = parseFloat(item.supplierAvg) || 0;
        const testAvg = parseFloat(item.testAvg) || 0;
        const deviation = Math.abs(supplierAvg - testAvg);

        heatmapData.push([xIndex, 0, supplierAvg]); // 供应商数据
        heatmapData.push([xIndex, 1, testAvg]);     // 测试数据
        heatmapData.push([xIndex, 2, deviation]);   // 偏差
      });

      const maxValue = Math.max(...heatmapData.map(d => d[2]));

      const self = this;

      return {
        title: {
          text: this.chartTitle,
          left: 'center'
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            const [x, y, value] = params.data;
            const xLabel = xAxisData[x];
            const yLabel = yAxisData[y];
            const currentData = self.chartData[x];

            let result = `${xLabel}<br/>${yLabel}: ${value.toFixed(2)}`;

            // 如果是参数编号对比且有参数明细，显示参数明细信息
            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {
              result += '<br/><br/><strong>参数明细信息：</strong><br/>';
              if (currentData.material) {
                result += '材料：' + currentData.material + '<br/>';
              }
              if (currentData.processType) {
                result += '工艺：' + currentData.processType + '<br/>';
              }
              result += '参数列表：<br/>';

              currentData.paramDetails.forEach(param => {
                result += '• ' + param.paramName + ': ' + param.paramValue;
                if (param.unit) {
                  result += ' ' + param.unit;
                }
                result += '<br/>';
              });
            }

            return result;
          }
        },
        grid: {
          height: '50%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'category',
          data: yAxisData
        },
        visualMap: {
          min: 0,
          max: maxValue || 100,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: ['#50a3ba', '#eac736', '#d94e5d']
          }
        },
        series: [{
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            formatter: function(params) {
              return params.data[2].toFixed(1);
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
    },

    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        compareType: 'material',
        paramNumbers: [],
        materialNames: [],
        supplierNames: [],
        processTypes: [],
        dateRange: null,
        compareParam: null
      };
      this.updateChartTitle();
    },

    /** 刷新图表 */
    refreshChart() {
      if (this.chartData.length > 0) {
        this.renderChart();
      }
    },

    /** 导出图表 */
    exportChart() {
      if (!this.chart) {
        this.$message.warning('请先生成图表');
        return;
      }

      const url = this.chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = url;
      link.download = `${this.chartTitle}_${new Date().getTime()}.png`;
      link.click();
    },

    /** 切换数据表显示 */
    toggleDataTable() {
      this.showDataTable = !this.showDataTable;
      if (!this.showDataTable && this.chart) {
        this.$nextTick(() => {
          this.chart.resize();
        });
      }
    },

    /** 切换项目详情显示 */
    toggleProjectDetails() {
      this.showProjectDetails = !this.showProjectDetails;
    },

    /** 切换全屏显示 */
    toggleFullscreen() {
      if (this.isFullscreen) {
        this.chartHeight = 400;
        this.isFullscreen = false;
      } else {
        this.chartHeight = window.innerHeight - 200;
        this.isFullscreen = true;
      }

      this.$nextTick(() => {
        if (this.chart) {
          this.chart.resize();
        }
      });
    },

    /** 显示图表帮助 */
    showChartHelp() {
      this.helpDialogVisible = true;
    },

    /** 格式化数字显示 */
    formatNumber(value) {
      if (value === null || value === undefined || isNaN(value)) {
        return 'N/A';
      }
      const num = parseFloat(value);
      if (num === 0) return '0';
      if (Math.abs(num) >= 1000000) {
        return (num / 1000000).toFixed(2) + 'M';
      } else if (Math.abs(num) >= 1000) {
        return (num / 1000).toFixed(2) + 'K';
      } else if (Math.abs(num) < 1) {
        return num.toFixed(4);
      } else {
        return num.toFixed(2);
      }
    },

    /** 获取参数标签类型 */
    getParamTagType(param) {
      if (!param.paramValue) return '';
      const value = parseFloat(param.paramValue);
      if (isNaN(value)) return '';

      // 根据参数值范围设置不同颜色
      if (value > 100) return 'danger';
      if (value > 50) return 'warning';
      if (value > 10) return 'success';
      return 'info';
    },

    /** 显示参数详情 */
    showParamDetail(param) {
      this.currentParamDetail = param;
      this.paramDetailDialogVisible = true;
    },

    /** 更新选中参数详情 */
    updateSelectedParamDetails() {
      // 根据当前选择的对比类型和选项，更新参数详情信息
      const { compareType } = this.queryParams;
      this.selectedParamDetails = [];

      if (compareType === 'material' && this.queryParams.materialNames.length > 0) {
        this.queryParams.materialNames.forEach(materialId => {
          const material = this.materialOptions.find(m => m.materialId === materialId);
          if (material) {
            this.selectedParamDetails.push({
              name: material.materialName,
              materialName: material.materialName,
              supplierName: material.supplierName,
              processType: material.processType,
              testCount: material.testCount || 0,
              statistics: material.statistics
            });
          }
        });
      } else if (compareType === 'supplier' && this.queryParams.supplierNames.length > 0) {
        this.queryParams.supplierNames.forEach(supplier => {
          this.selectedParamDetails.push({
            name: supplier,
            supplierName: supplier,
            testCount: 0 // 这里可以从API获取实际数据
          });
        });
      } else if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length > 0) {
        this.queryParams.paramNumbers.forEach(paramId => {
          const param = this.paramNumberOptions.find(p => p.groupId === paramId);
          if (param) {
            this.selectedParamDetails.push({
              name: param.paramNumber,
              paramNumber: param.paramNumber,
              materialName: param.materialName,
              processType: param.processType,
              mainParams: param.paramItems || [],
              testCount: param.testCount || 0
            });
          }
        });
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  margin-right: 10px;
  color: #409EFF;
  font-size: 28px;
}

.page-description p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

/* 增强卡片样式 */
.enhanced-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  background: white;
  margin-bottom: 20px;
}

.enhanced-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left i {
  color: #409EFF;
  font-size: 18px;
}

.header-title {
  font-weight: bold;
  font-size: 16px;
  color: #2c3e50;
}

.header-right {
  display: flex;
  gap: 8px;
}

.header-right .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.close-guide-btn {
  color: white;
  font-size: 16px;
}

.close-guide-btn:hover {
  color: #f0f0f0;
}

.guide-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  border: none;
  color: white;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.export-btn {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  border: none;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.param-detail-card {
  height: 100%;
  background-color: #f8f9fa;
}

.detail-item {
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.detail-item span {
  color: #303133;
}

.statistics-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  padding: 20px;
}

.statistics-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.statistics-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.statistics-desc {
  font-size: 12px;
  color: #C0C4CC;
}

.chart-help-content {
  line-height: 1.6;
}

.chart-help-content h4 {
  color: #409EFF;
  margin-top: 20px;
  margin-bottom: 10px;
}

.chart-help-content ul {
  padding-left: 20px;
}

.chart-help-content li {
  margin-bottom: 8px;
}

.chart-help-content strong {
  color: #303133;
}

/* 使用指南样式 */
.usage-guide-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.usage-guide-card .el-card__header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.guide-item h4 {
  color: #fff;
  margin-bottom: 8px;
  font-size: 14px;
}

.guide-item p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}

/* 参数详情卡片样式 */
.param-details-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.param-detail-card {
  height: 100%;
  background: white;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.param-detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.detail-section {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-weight: bold;
  color: #409EFF;
  font-size: 12px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 5px;
}

.detail-item {
  margin-bottom: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.detail-icon {
  color: #909399;
  margin-right: 5px;
  font-size: 12px;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 60px;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 10px;
  color: #909399;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

.params-container {
  max-height: 80px;
  overflow-y: auto;
}

.params-container::-webkit-scrollbar {
  width: 4px;
}

.params-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.params-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.params-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 统一按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.el-button--success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  border: none !important;
}

.el-button--info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  border: none !important;
}

.el-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  border: none !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
}
</style>
