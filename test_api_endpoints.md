# 测试方案配置API接口验证指南

## 问题描述
测试方案配置模块显示"无法找到接口"，需要验证后端API是否正常工作。

## 验证步骤

### 1. 数据库验证
首先执行数据库修复脚本：
```sql
-- 在数据库管理工具中执行
source sql/fix_test_plan_issues.sql;
```

### 2. 后端服务验证
确保后端服务正在运行，然后测试以下API端点：

#### 2.1 测试方案列表API
```bash
# 使用curl测试（需要先获取token）
curl -X GET "http://localhost:8080/material/testPlan/list" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 2.2 测试方案选项API
```bash
# 测试方案编号选项
curl -X GET "http://localhost:8080/material/testPlan/options?type=planCode" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 性能类型选项
curl -X GET "http://localhost:8080/material/testPlan/options?type=performanceType" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 测试设备选项
curl -X GET "http://localhost:8080/material/testPlan/options?type=testEquipment" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 前端验证
在浏览器中：

#### 3.1 打开开发者工具
1. 按F12打开开发者工具
2. 切换到Network标签页
3. 访问测试方案管理页面

#### 3.2 检查API调用
1. 点击方案编号输入框
2. 查看Network标签页是否有API请求
3. 检查请求URL是否为：`/material/testPlan/options?type=planCode`
4. 检查响应状态码是否为200
5. 检查响应数据是否包含方案编号列表

#### 3.3 检查控制台日志
1. 切换到Console标签页
2. 查看是否有以下日志：
   - "方案编号选项响应："
   - "性能类型选项响应："
   - "测试设备选项响应："

### 4. 常见问题排查

#### 4.1 404错误 - 接口不存在
**可能原因：**
- 后端Controller未正确注册
- 请求路径不正确
- 权限配置问题

**解决方法：**
1. 检查TestPlanController是否有@RestController注解
2. 检查@RequestMapping("/material/testPlan")路径
3. 检查权限配置

#### 4.2 500错误 - 服务器内部错误
**可能原因：**
- 数据库连接问题
- SQL语句错误
- 空指针异常

**解决方法：**
1. 检查数据库连接
2. 查看后端日志
3. 验证test_plans表是否存在

#### 4.3 空数据响应
**可能原因：**
- 数据库中没有测试方案数据
- SQL查询条件错误

**解决方法：**
1. 执行fix_test_plan_issues.sql脚本
2. 验证数据是否插入成功

### 5. 验证成功标志

#### 5.1 数据库验证成功
- test_plans表存在且包含测试数据
- 各选项查询返回正确数据

#### 5.2 API验证成功
- 所有API端点返回200状态码
- 选项API返回正确的数组数据

#### 5.3 前端验证成功
- 点击输入框显示候选项下拉列表
- 控制台无错误信息
- 网络请求正常

## 修复完成确认

当以下条件都满足时，表示修复成功：
1. ✅ 数据库包含测试方案数据
2. ✅ 后端API正常响应
3. ✅ 前端能正常显示候选项
4. ✅ 无控制台错误信息
5. ✅ 网络请求状态正常

## 后续建议

1. **定期数据备份**：定期备份test_plans表数据
2. **监控API性能**：监控选项查询API的响应时间
3. **用户权限管理**：确保用户有正确的测试方案管理权限
4. **数据维护**：定期清理无效的测试方案数据
