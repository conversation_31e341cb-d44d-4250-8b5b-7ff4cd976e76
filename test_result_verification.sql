-- 数据录入模块修复验证SQL脚本

-- 1. 检查测试结果表结构
DESCRIBE test_results;

-- 2. 检查测试方案表是否包含新字段
DESCRIBE test_plans;

-- 3. 检查材料表是否包含材料型号字段
DESCRIBE materials;

-- 4. 验证关联查询是否正常工作
SELECT 
    tr.test_result_id,
    tr.test_plan_id,
    tr.group_id,
    tr.supplier_datasheet_val,
    tr.test_value,
    tp.plan_code,
    tp.performance_name,
    tp.performance_type,
    tp.test_equipment,
    tp.test_parameter,
    pg.param_number,
    pg.process_type,
    m.material_name,
    m.supplier_name,
    m.material_model,
    pi.param_name,
    pi.unit as param_unit
FROM test_results tr
LEFT JOIN test_plans tp ON tr.test_plan_id = tp.test_plan_id
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
LEFT JOIN materials m ON pg.material_id = m.material_id
LEFT JOIN process_param_item pi ON pg.group_id = pi.group_id
LIMIT 5;

-- 5. 验证选项查询
-- 参数编号选项
SELECT DISTINCT param_number 
FROM process_param_group 
WHERE param_number IS NOT NULL AND param_number != '' 
ORDER BY param_number;

-- 材料名称选项
SELECT DISTINCT material_name 
FROM materials 
WHERE material_name IS NOT NULL AND material_name != '' 
ORDER BY material_name;

-- 供应商名称选项
SELECT DISTINCT supplier_name 
FROM materials 
WHERE supplier_name IS NOT NULL AND supplier_name != '' 
ORDER BY supplier_name;

-- 材料型号选项
SELECT DISTINCT material_model 
FROM materials 
WHERE material_model IS NOT NULL AND material_model != '' 
ORDER BY material_model;

-- 工艺类型选项
SELECT DISTINCT process_type 
FROM process_param_group 
WHERE process_type IS NOT NULL AND process_type != '' 
ORDER BY process_type;

-- 性能类型选项
SELECT DISTINCT performance_type 
FROM test_plans 
WHERE performance_type IS NOT NULL AND performance_type != '' 
ORDER BY performance_type;

-- 测试设备选项
SELECT DISTINCT test_equipment 
FROM test_plans 
WHERE test_equipment IS NOT NULL AND test_equipment != '' 
ORDER BY test_equipment;

-- 6. 验证参数组查询包含供应商信息
SELECT 
    g.group_id,
    g.material_id,
    g.process_type,
    g.param_number,
    m.material_name,
    m.supplier_name
FROM process_param_group g
LEFT JOIN materials m ON g.material_id = m.material_id
LIMIT 10;

-- 7. 验证参数组详情查询
SELECT 
    g.group_id,
    g.material_id,
    g.process_type,
    g.param_number,
    m.material_name,
    m.supplier_name,
    i.item_id,
    i.param_name,
    i.param_value,
    i.unit
FROM process_param_group g
LEFT JOIN materials m ON g.material_id = m.material_id
LEFT JOIN process_param_item i ON i.group_id = g.group_id
WHERE g.group_id = 1;

-- 8. 检查数据完整性
-- 检查是否有测试结果没有关联的测试方案
SELECT COUNT(*) as orphaned_results
FROM test_results tr
LEFT JOIN test_plans tp ON tr.test_plan_id = tp.test_plan_id
WHERE tp.test_plan_id IS NULL;

-- 检查是否有测试结果没有关联的参数组
SELECT COUNT(*) as orphaned_results
FROM test_results tr
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
WHERE pg.group_id IS NULL;

-- 检查是否有参数组没有关联的材料
SELECT COUNT(*) as orphaned_groups
FROM process_param_group pg
LEFT JOIN materials m ON pg.material_id = m.material_id
WHERE m.material_id IS NULL;
