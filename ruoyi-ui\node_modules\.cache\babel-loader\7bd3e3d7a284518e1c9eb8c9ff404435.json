{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue", "mtime": 1754278951915}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\babel.config.js", "mtime": 1753339103954}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_testResult", "require", "_testPlanGroup", "_testParamItem", "_processParamGroup", "_processParamItem", "_auth", "name", "directives", "drag", "bind", "el", "dialogHeaderEl", "querySelector", "dragDom", "style", "cursor", "sty", "currentStyle", "window", "getComputedStyle", "onmousedown", "e", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "styL", "styT", "left", "includes", "document", "body", "clientWidth", "replace", "clientHeight", "top", "<PERSON><PERSON><PERSON><PERSON>", "l", "t", "concat", "onmouseup", "data", "loading", "ids", "single", "multiple", "total", "testResultList", "title", "open", "testPlanGroupOptions", "paramGroupOptions", "filteredParamGroupOptions", "selectedParamDetail", "selectedTestPlanDetail", "queryParams", "pageNum", "pageSize", "testPlanCode", "groupId", "paramNumber", "materialName", "supplierName", "materialModel", "processType", "performanceType", "testEquipment", "form", "rules", "required", "message", "trigger", "planGroupId", "testValue", "detailDialogVisible", "detailData", "detailParamItems", "detailTestPlanParams", "columnSettingVisible", "columnOptions", "performanceName", "supplierDatasheetVal", "createBy", "createTime", "updateBy", "updateTime", "selectedColumns", "visibleColumns", "paramNumberSuggestions", "materialNameSuggestions", "supplierNameSuggestions", "materialModelSuggestions", "processTypeSuggestions", "performanceTypeSuggestions", "testEquipmentSuggestions", "testPlanGroupSuggestions", "fileList", "attachmentList", "attachmentDialogVisible", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "created", "getList", "getTestPlanGroupOptions", "getParamGroupOptions", "loadSuggestions", "initVisibleColumns", "methods", "_this", "for<PERSON>ach", "col", "_this2", "getTestResultOptions", "type", "then", "response", "map", "item", "value", "catch", "testPlanSuggestions", "queryParamNumberSuggestions", "queryString", "cb", "suggestions", "length", "filter", "toLowerCase", "indexOf", "queryMaterialNameSuggestions", "querySupplierNameSuggestions", "queryMaterialModelSuggestions", "queryProcessTypeSuggestions", "queryPerformanceTypeSuggestions", "queryTestEquipmentSuggestions", "handleParamNumberFocus", "_this3", "handleMaterialNameFocus", "_this4", "handleSupplierNameFocus", "_this5", "handleMaterialModelFocus", "_this6", "handleProcessTypeFocus", "_this7", "handlePerformanceTypeFocus", "_this8", "handleTestEquipmentFocus", "_this9", "queryTestPlanGroupSuggestions", "handleTestPlanGroupFocus", "_this0", "handleTestPlanGroupSelect", "handleFormTestPlanChange", "handleParamGroupChange", "_this1", "listTestResult", "rows", "_this10", "listTestPlanGroup", "handleFormPlanGroupChange", "testParamId", "testParamOptions", "selectedGroup", "find", "group", "planCode", "paramName", "paramValue", "unit", "getTestParamOptionsByPlanGroupId", "handleFormTestParamChange", "<PERSON><PERSON><PERSON><PERSON>", "param", "_this11", "listProcessParamGroup", "handlePlanChange", "handleQuery", "_this12", "getProcessParamGroup", "console", "log", "listProcessParamItem", "paramResponse", "paramItems", "displayText", "undefined", "formatDecimal", "$forceUpdate", "error", "cancel", "reset", "testResultId", "attachments", "remark", "resetForm", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "handleRowClick", "row", "$refs", "multipleTable", "toggleRowSelection", "handleAdd", "handleEdit", "_this13", "getTestResult", "parseAttachments", "$nextTick", "handleDetail", "_this14", "planGroup", "listTestParamItem", "testParams", "submitForm", "_this15", "validate", "valid", "file", "url", "join", "$store", "state", "user", "updateTestResult", "$modal", "msgSuccess", "addTestResult", "handleDelete", "_this16", "testResultIds", "confirm", "delTestResult", "handleExport", "download", "_objectSpread2", "default", "Date", "getTime", "handleColumnSetting", "handleColumnConfirm", "_this17", "handleUploadSuccess", "_this18", "code", "Array", "isArray", "_item$raw", "size", "formatFileSize", "raw", "uid", "status", "msgError", "msg", "handleFileRemove", "_this19", "_item$raw2", "beforeUpload", "isLt10M", "handleViewAttachments", "_typeof2", "trimmed", "trim", "startsWith", "endsWith", "parsed", "JSON", "parse", "index", "jsonError", "warn", "split", "cleanUrl", "fileName", "substring", "lastIndexOf", "downloadAttachment", "link", "createElement", "href", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "now", "urls", "units", "Math", "round", "num", "parseFloat", "isNaN", "toString", "toFixed"], "sources": ["src/views/material/testResult/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-edit-outline\"></i>\r\n        <span>测试结果数据录入</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📊 录入和管理测试结果数据，支持多维度筛选和批量操作</p>\r\n        <el-alert\r\n          title=\"使用提示：先选择筛选条件，然后点击查询按钮查看数据，支持列设置自定义显示\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"result-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-edit-outline\"></i>\r\n          <span class=\"header-title\">测试结果管理</span>\r\n          <el-badge :value=\"total\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAdd\">\r\n            <span>新增结果</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"multiple\" @click=\"handleDelete\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExport\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"info\" icon=\"el-icon-setting\" size=\"small\" @click=\"handleColumnSetting\" class=\"column-setting-btn\">\r\n            <span>列设置</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\" class=\"search-form\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramNumber\"\r\n                  :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n                  placeholder=\"请输入参数编号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleParamNumberFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-tickets\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试方案组\" prop=\"testPlanCode\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testPlanCode\"\r\n                  :fetch-suggestions=\"queryTestPlanGroupSuggestions\"\r\n                  placeholder=\"请输入测试方案组\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestPlanGroupFocus\"\r\n                  @select=\"handleTestPlanGroupSelect\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-document\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试参数\" prop=\"paramName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramName\"\r\n                  :fetch-suggestions=\"queryTestParamSuggestions\"\r\n                  placeholder=\"请输入测试参数\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestParamFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-data-line\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialName\"\r\n                  :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n                  placeholder=\"请输入材料名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.supplierName\"\r\n                  :fetch-suggestions=\"querySupplierNameSuggestions\"\r\n                  placeholder=\"请输入供应商名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleSupplierNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-office-building\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialModel\"\r\n                  :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n                  placeholder=\"请输入材料型号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialModelFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-goods\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.processType\"\r\n                  :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n                  placeholder=\"请输入工艺类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleProcessTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-setting\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.performanceType\"\r\n                  :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n                  placeholder=\"请输入性能类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handlePerformanceTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-lightning\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testEquipment\"\r\n                  :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n                  placeholder=\"请输入测试设备\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestEquipmentFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-cpu\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\" style=\"text-align: right;\">\r\n              <el-form-item>\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\" class=\"search-btn\">\r\n                  <span>搜索</span>\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\" class=\"reset-btn\">\r\n                  <span>重置</span>\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"testResultList\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"multipleTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n\r\n        <!-- 动态列显示 -->\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialName\"\r\n          prop=\"materialName\"\r\n          label=\"材料名称\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierName\"\r\n          prop=\"supplierName\"\r\n          label=\"供应商\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialModel\"\r\n          prop=\"materialModel\"\r\n          label=\"材料型号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.processType\"\r\n          prop=\"processType\"\r\n          label=\"工艺类型\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.paramNumber\"\r\n          prop=\"paramNumber\"\r\n          label=\"参数编号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceType\"\r\n          prop=\"performanceType\"\r\n          label=\"性能类型\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceName\"\r\n          prop=\"performanceName\"\r\n          label=\"性能名称\"\r\n          min-width=\"150\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testEquipment\"\r\n          prop=\"testEquipment\"\r\n          label=\"测试设备\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierDatasheetVal\"\r\n          prop=\"supplierDatasheetVal\"\r\n          label=\"供应商数据\"\r\n          width=\"120\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.supplierDatasheetVal) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testValue\"\r\n          prop=\"testValue\"\r\n          label=\"测试值\"\r\n          width=\"100\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.testValue) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createBy\"\r\n          prop=\"createBy\"\r\n          label=\"创建人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createTime\"\r\n          prop=\"createTime\"\r\n          label=\"创建时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateBy\"\r\n          prop=\"updateBy\"\r\n          label=\"更新人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateTime\"\r\n          prop=\"updateTime\"\r\n          label=\"更新时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body v-drag>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数编号\" prop=\"groupId\">\r\n              <el-select\r\n                v-model=\"form.groupId\"\r\n                placeholder=\"请选择参数编号\"\r\n                style=\"width: 100%;\"\r\n                filterable\r\n                @change=\"handleParamGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in filteredParamGroupOptions\"\r\n                  :key=\"group.groupId\"\r\n                  :label=\"group.paramNumber + ' - ' + (group.materialName || '') + ' (' + (group.supplierName || '') + ')'\"\r\n                  :value=\"group.groupId\"\r\n                >\r\n                  <div style=\"display: flex; justify-content: space-between;\">\r\n                    <span>{{ group.paramNumber || 'N/A' }}</span>\r\n                    <span style=\"color: #8492a6; font-size: 13px;\">{{ (group.materialName || 'N/A') + ' - ' + (group.supplierName || 'N/A') }}</span>\r\n                  </div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试方案组\" prop=\"planGroupId\">\r\n              <el-select\r\n                v-model=\"form.planGroupId\"\r\n                placeholder=\"请选择测试方案组\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                @change=\"handleFormPlanGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in testPlanGroupOptions\"\r\n                  :key=\"group.planGroupId\"\r\n                  :label=\"group.planCode + ' - ' + group.performanceName\"\r\n                  :value=\"group.planGroupId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试参数\" prop=\"testParamId\">\r\n              <el-select\r\n                v-model=\"form.testParamId\"\r\n                placeholder=\"请选择测试参数\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"!form.planGroupId\"\r\n                @change=\"handleFormTestParamChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"param in testParamOptions\"\r\n                  :key=\"param.testParamId\"\r\n                  :label=\"param.paramName + (param.paramValue ? ' (' + param.paramValue + param.unit + ')' : '')\"\r\n                  :value=\"param.testParamId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- 空列，保持布局 -->\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 工艺参数信息展示 -->\r\n        <el-card v-if=\"selectedParamDetail\" class=\"param-detail-card\" style=\"margin-bottom: 15px;\" :key=\"selectedParamDetail.groupId + '_' + (selectedParamDetail.paramItems ? selectedParamDetail.paramItems.length : 0)\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-setting\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">工艺参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"材料名称\">{{ selectedParamDetail.materialName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"供应商\">{{ selectedParamDetail.supplierName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"工艺类型\">{{ selectedParamDetail.processType || '-' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n          <div v-if=\"selectedParamDetail.paramItems && selectedParamDetail.paramItems.length > 0\" style=\"margin-top: 15px;\">\r\n            <div style=\"margin-bottom: 10px; font-weight: bold; color: #606266;\">\r\n              <i class=\"el-icon-data-line\"></i>\r\n              <span style=\"margin-left: 5px;\">参数明细：</span>\r\n            </div>\r\n            <el-table :data=\"selectedParamDetail.paramItems\" size=\"mini\" style=\"width: 100%;\" border>\r\n              <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n              <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n            </el-table>\r\n          </div>\r\n          <div v-else style=\"margin-top: 15px; text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\"></i>\r\n            <span style=\"margin-left: 5px;\">暂无参数明细信息</span>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 测试方案参数信息展示 -->\r\n        <el-card v-if=\"selectedTestPlanDetail\" class=\"test-plan-detail-card\" style=\"margin-bottom: 15px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-document\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">测试方案参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"方案编号\">{{ selectedTestPlanDetail.planCode || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能类型\">{{ selectedTestPlanDetail.performanceType || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能名称\">{{ selectedTestPlanDetail.performanceName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"测试设备\">{{ selectedTestPlanDetail.testEquipment || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数名称\">{{ selectedTestPlanDetail.paramName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数数值\">{{ selectedTestPlanDetail.paramValue || '-' }} {{ selectedTestPlanDetail.unit || '' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"供应商数据\" prop=\"supplierDatasheetVal\">\r\n              <el-input v-model=\"form.supplierDatasheetVal\" placeholder=\"请输入供应商Datasheet值\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"实际测试值\" prop=\"testValue\">\r\n              <el-input-number v-model=\"form.testValue\" :precision=\"6\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"upload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"fileList\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :before-upload=\"beforeUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" clearable />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"测试结果详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body v-drag>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"材料名称\">{{ detailData.materialName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供应商\">{{ detailData.supplierName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"材料型号\">{{ detailData.materialModel || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"工艺类型\">{{ detailData.processType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"参数编号\">{{ detailData.paramNumber || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能类型\">{{ detailData.performanceType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能名称\">{{ detailData.performanceName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试设备\">{{ detailData.testEquipment || '-' }}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"供应商数据\">{{ formatDecimal(detailData.supplierDatasheetVal) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试值\">{{ formatDecimal(detailData.testValue) }} {{ detailData.paramUnit || '' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建人\">{{ detailData.createBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建时间\">{{ parseTime(detailData.createTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新人\">{{ detailData.updateBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新时间\">{{ parseTime(detailData.updateTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ detailData.remark || '-' }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 参数明细信息 -->\r\n      <el-card v-if=\"detailParamItems && detailParamItems.length > 0\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">材料参数明细信息</span>\r\n        </div>\r\n        <el-table :data=\"detailParamItems\" style=\"width: 100%\" size=\"small\">\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n          <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n        </el-table>\r\n      </el-card>\r\n\r\n      <!-- 测试方案参数信息 -->\r\n      <el-card v-if=\"detailTestPlanParams\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">测试方案参数信息</span>\r\n        </div>\r\n        <el-descriptions :column=\"2\" border size=\"small\" style=\"margin-bottom: 20px;\">\r\n          <el-descriptions-item label=\"方案编号\">{{ detailTestPlanParams.planCode || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能类型\">{{ detailTestPlanParams.performanceType || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能名称\">{{ detailTestPlanParams.performanceName || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"测试设备\">{{ detailTestPlanParams.testEquipment || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 测试参数列表 -->\r\n        <div v-if=\"detailTestPlanParams.testParams && detailTestPlanParams.testParams.length > 0\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">📋 测试参数明细</h4>\r\n          <el-table :data=\"detailTestPlanParams.testParams\" size=\"small\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n            <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip />\r\n            <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.paramValue || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.unit || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.remark || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        <div v-else style=\"text-align: center; color: #909399; padding: 20px;\">\r\n          <i class=\"el-icon-info\"></i> 该测试方案组暂无测试参数\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 附件信息 -->\r\n      <el-card v-if=\"detailData.attachments\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">附件信息</span>\r\n        </div>\r\n        <el-button size=\"mini\" type=\"text\" @click=\"handleViewAttachments(detailData.attachments)\">查看附件</el-button>\r\n      </el-card>\r\n    </el-dialog>\r\n\r\n    <!-- 列设置对话框 -->\r\n    <el-dialog title=\"列设置\" :visible.sync=\"columnSettingVisible\" width=\"500px\" append-to-body v-drag>\r\n      <el-checkbox-group v-model=\"selectedColumns\">\r\n        <el-row>\r\n          <el-col :span=\"12\" v-for=\"(label, key) in columnOptions\" :key=\"key\">\r\n            <el-checkbox :label=\"key\">{{ label }}</el-checkbox>\r\n          </el-col>\r\n        </el-row>\r\n      </el-checkbox-group>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleColumnConfirm\">确 定</el-button>\r\n        <el-button @click=\"columnSettingVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" />\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"downloadAttachment(scope.row.url, scope.row.name)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"attachmentDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestResult, getTestResult, delTestResult, addTestResult, updateTestResult,\r\n  getTestResultOptions\r\n} from \"@/api/material/testResult\";\r\nimport { listTestPlanGroup } from \"@/api/material/testPlanGroup\";\r\nimport { listTestParamItem } from \"@/api/material/testParamItem\";\r\nimport { listProcessParamGroup, getProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestResult\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 测试结果表格数据\r\n      testResultList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 测试方案组选项\r\n      testPlanGroupOptions: [],\r\n\r\n      // 参数组选项\r\n      paramGroupOptions: [],\r\n      // 过滤后的参数组选项\r\n      filteredParamGroupOptions: [],\r\n      // 选中的参数详情\r\n      selectedParamDetail: null,\r\n      // 选中的测试方案详情\r\n      selectedTestPlanDetail: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        testPlanCode: null,\r\n\r\n        groupId: null,\r\n        paramNumber: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        processType: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        groupId: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"change\" }\r\n        ],\r\n        planGroupId: [\r\n          { required: true, message: \"测试方案组不能为空\", trigger: \"change\" }\r\n        ],\r\n\r\n        testValue: [\r\n          { required: true, message: \"实际测试值不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 详情对话框\r\n      detailDialogVisible: false,\r\n      detailData: {},\r\n      detailParamItems: [],\r\n      detailTestPlanParams: null,\r\n\r\n      // 列设置相关\r\n      columnSettingVisible: false,\r\n      columnOptions: {\r\n        materialName: '材料名称',\r\n        supplierName: '供应商',\r\n        materialModel: '材料型号',\r\n        processType: '工艺类型',\r\n        paramNumber: '参数编号',\r\n        performanceType: '性能类型',\r\n        performanceName: '性能名称',\r\n\r\n        testEquipment: '测试设备',\r\n        supplierDatasheetVal: '供应商数据',\r\n        testValue: '测试值',\r\n        createBy: '创建人',\r\n        createTime: '创建时间',\r\n        updateBy: '更新人',\r\n        updateTime: '更新时间'\r\n      },\r\n      selectedColumns: ['materialName', 'supplierName', 'materialModel', 'processType', 'paramNumber', 'performanceType', 'performanceName', 'testEquipment', 'supplierDatasheetVal', 'testValue', 'createBy', 'createTime', 'updateBy', 'updateTime'],\r\n      visibleColumns: {},\r\n\r\n      // 搜索建议数据\r\n      paramNumberSuggestions: [],\r\n      materialNameSuggestions: [],\r\n      supplierNameSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      testPlanGroupSuggestions: [],\r\n\r\n      // 附件相关\r\n      fileList: [],\r\n      attachmentList: [],\r\n      attachmentDialogVisible: false,\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getTestPlanGroupOptions();\r\n    this.getParamGroupOptions();\r\n    this.loadSuggestions();\r\n    this.initVisibleColumns();\r\n  },\r\n  methods: {\r\n    /** 初始化可见列 */\r\n    initVisibleColumns() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取参数编号建议\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料名称建议\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商名称建议\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取性能类型建议\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试设备建议\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试方案建议\r\n      getTestResultOptions({ type: 'planCode' }).then(response => {\r\n        this.testPlanSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商名称搜索建议 */\r\n    querySupplierNameSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 性能类型搜索建议 */\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 测试设备搜索建议 */\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商名称焦点事件 */\r\n    handleSupplierNameFocus() {\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 性能类型焦点事件 */\r\n    handlePerformanceTypeFocus() {\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试设备焦点事件 */\r\n    handleTestEquipmentFocus() {\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组搜索建议 */\r\n    queryTestPlanGroupSuggestions(queryString, cb) {\r\n      let suggestions = this.testPlanGroupSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.testPlanGroupSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 测试方案组焦点事件 */\r\n    handleTestPlanGroupFocus() {\r\n      getTestResultOptions({ type: 'testPlanGroup' }).then(response => {\r\n        this.testPlanGroupSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组选择事件 */\r\n    handleTestPlanGroupSelect(item) {\r\n      this.queryParams.testPlanCode = item.value;\r\n    },\r\n\r\n    /** 表单测试方案改变事件 */\r\n    handleFormTestPlanChange(value) {\r\n      // 当测试方案改变时，可以重新加载参数详情\r\n      if (value && this.form.groupId) {\r\n        this.handleParamGroupChange(this.form.groupId);\r\n      }\r\n    },\r\n\r\n    /** 查询测试结果列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTestResult(this.queryParams).then(response => {\r\n        this.testResultList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 获取测试方案组选项 */\r\n    getTestPlanGroupOptions() {\r\n      listTestPlanGroup().then(response => {\r\n        this.testPlanGroupOptions = response.rows || [];\r\n      }).catch(() => {\r\n        this.testPlanGroupOptions = [];\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 处理测试方案组变化 */\r\n    handleFormPlanGroupChange(planGroupId) {\r\n      this.form.testParamId = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.testParamOptions = [];\r\n\r\n      if (!planGroupId) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试方案组详情\r\n      const selectedGroup = this.testPlanGroupOptions.find(group => group.planGroupId === planGroupId);\r\n      if (selectedGroup) {\r\n        // 立即显示测试方案组基本信息\r\n        this.selectedTestPlanDetail = {\r\n          planCode: selectedGroup.planCode,\r\n          performanceType: selectedGroup.performanceType,\r\n          performanceName: selectedGroup.performanceName,\r\n          testEquipment: selectedGroup.testEquipment,\r\n          paramName: '',\r\n          paramValue: '',\r\n          unit: ''\r\n        };\r\n\r\n        // 加载该测试方案组下的测试参数选项\r\n        this.getTestParamOptionsByPlanGroupId(planGroupId);\r\n      }\r\n    },\r\n\r\n    /** 处理测试参数变化 */\r\n    handleFormTestParamChange(testParamId) {\r\n      if (!testParamId || !this.selectedTestPlanDetail) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试参数详情\r\n      const selectedParam = this.testParamOptions.find(param => param.testParamId === testParamId);\r\n      if (selectedParam) {\r\n        this.selectedTestPlanDetail.paramName = selectedParam.paramName;\r\n        this.selectedTestPlanDetail.paramValue = selectedParam.paramValue;\r\n        this.selectedTestPlanDetail.unit = selectedParam.unit;\r\n      }\r\n    },\r\n\r\n\r\n\r\n    /** 获取参数组选项 */\r\n    getParamGroupOptions() {\r\n      listProcessParamGroup().then(response => {\r\n        this.paramGroupOptions = response.rows;\r\n        this.filteredParamGroupOptions = response.rows;\r\n      });\r\n    },\r\n\r\n    /** 方案改变事件 */\r\n    handlePlanChange(value) {\r\n      // 可以根据方案过滤参数组\r\n      this.queryParams.groupId = null;\r\n      this.handleQuery();\r\n    },\r\n\r\n\r\n\r\n    /** 参数组改变事件 */\r\n    handleParamGroupChange(value) {\r\n      if (value) {\r\n        // 先清空之前的数据\r\n        this.selectedParamDetail = null;\r\n\r\n        // 获取参数组详情\r\n        getProcessParamGroup(value).then(response => {\r\n          this.selectedParamDetail = response.data;\r\n          console.log('获取到的参数组详情：', this.selectedParamDetail);\r\n\r\n          // 立即获取参数明细，不需要等待测试方案选择\r\n          listProcessParamItem({ groupId: value }).then(paramResponse => {\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = paramResponse.rows || [];\r\n              // 为每个参数项添加显示文本，包含参数值\r\n              this.selectedParamDetail.paramItems.forEach(item => {\r\n                let displayText = item.paramName || 'N/A';\r\n                if (item.paramValue !== null && item.paramValue !== undefined) {\r\n                  displayText += `: ${this.formatDecimal(item.paramValue)}`;\r\n                }\r\n                if (item.unit) {\r\n                  displayText += ` ${item.unit}`;\r\n                }\r\n                item.displayText = displayText;\r\n              });\r\n              console.log('获取到的参数明细：', this.selectedParamDetail.paramItems);\r\n\r\n              // 强制更新视图\r\n              this.$forceUpdate();\r\n            }\r\n          }).catch(error => {\r\n            console.error('获取参数明细失败：', error);\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = [];\r\n            }\r\n          });\r\n        }).catch(error => {\r\n          console.error('获取参数组详情失败：', error);\r\n          this.selectedParamDetail = null;\r\n        });\r\n      } else {\r\n        this.selectedParamDetail = null;\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        testResultId: null,\r\n        planGroupId: null,\r\n        groupId: null,\r\n        supplierDatasheetVal: null,\r\n        testValue: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.fileList = [];\r\n      this.selectedParamDetail = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.testResultId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    // 行点击选择\r\n    handleRowClick(row) {\r\n      this.$refs.multipleTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加测试结果\";\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    handleEdit(row) {\r\n      this.reset();\r\n      const testResultId = row.testResultId || this.ids[0];\r\n      getTestResult(testResultId).then(response => {\r\n        console.log('编辑获取的数据：', response.data);\r\n        this.form = response.data;\r\n        // 解析附件数据\r\n        this.fileList = this.parseAttachments(response.data.attachments);\r\n        console.log('编辑时解析的文件列表：', this.fileList);\r\n        this.open = true;\r\n        this.title = \"修改测试结果\";\r\n\r\n        // 触发参数组改变事件以加载材料参数详情\r\n        if (this.form.groupId) {\r\n          this.handleParamGroupChange(this.form.groupId);\r\n        }\r\n\r\n        // 触发测试方案组改变事件以加载测试方案参数详情\r\n        if (this.form.planGroupId) {\r\n          this.handleFormPlanGroupChange(this.form.planGroupId);\r\n\r\n          // 如果有测试参数ID，在加载完测试参数选项后设置选中的参数\r\n          if (this.form.testParamId) {\r\n            this.$nextTick(() => {\r\n              this.handleFormTestParamChange(this.form.testParamId);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      this.detailData = row;\r\n      this.detailParamItems = [];\r\n      this.detailTestPlanParams = null;\r\n\r\n      // 如果有参数组ID，获取参数明细\r\n      if (row.groupId) {\r\n        listProcessParamItem({ groupId: row.groupId }).then(response => {\r\n          this.detailParamItems = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('获取参数明细失败：', error);\r\n          this.detailParamItems = [];\r\n        });\r\n      }\r\n\r\n      // 如果有测试方案组ID，获取测试方案参数信息\r\n      if (row.planGroupId) {\r\n        // 从当前的测试方案组选项中查找对应的方案信息\r\n        const planGroup = this.testPlanGroupOptions.find(group => group.planGroupId === row.planGroupId);\r\n        if (planGroup) {\r\n          // 获取测试方案组下的所有测试参数\r\n          listTestParamItem({ planGroupId: row.planGroupId }).then(response => {\r\n            this.detailTestPlanParams = {\r\n              planCode: planGroup.planCode,\r\n              performanceType: planGroup.performanceType,\r\n              performanceName: planGroup.performanceName,\r\n              testEquipment: planGroup.testEquipment,\r\n              testParams: response.rows || []\r\n            };\r\n          }).catch(error => {\r\n            console.error('获取测试方案参数失败：', error);\r\n          });\r\n        }\r\n      }\r\n\r\n      this.detailDialogVisible = true;\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 将附件列表转换为逗号分隔的URL字符串\r\n          if (this.fileList && this.fileList.length > 0) {\r\n            this.form.attachments = this.fileList.map(file => file.url).join(',');\r\n          } else {\r\n            this.form.attachments = '';\r\n          }\r\n\r\n          // testPlanId已经直接选择，无需转换\r\n\r\n          // 设置创建人和更新人\r\n          if (this.form.testResultId != null) {\r\n            // 更新操作，设置更新人\r\n            this.form.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.form.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          console.log('提交的表单数据：', this.form);\r\n\r\n          if (this.form.testResultId != null) {\r\n            updateTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const testResultIds = row.testResultId || this.ids;\r\n      this.$modal.confirm('是否确认删除测试结果编号为\"' + testResultIds + '\"的数据项？').then(function() {\r\n        return delTestResult(testResultIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('material/testResult/export', {\r\n        ...this.queryParams\r\n      }, `test_result_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 列设置 */\r\n    handleColumnSetting() {\r\n      this.columnSettingVisible = true;\r\n    },\r\n\r\n    /** 列设置确认 */\r\n    handleColumnConfirm() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n      this.columnSettingVisible = false;\r\n    },\r\n\r\n    /** 附件上传成功 */\r\n    handleUploadSuccess(response, file, fileList) {\r\n      console.log('上传成功回调：', { response, file, fileList });\r\n      if (response.code === 200) {\r\n        // 确保fileList是数组\r\n        if (Array.isArray(fileList)) {\r\n          this.fileList = fileList.map(item => ({\r\n            name: item.name,\r\n            url: item.response ? item.response.url : item.url,\r\n            size: this.formatFileSize(item.size || item.raw?.size),\r\n            uid: item.uid,\r\n            status: 'success'\r\n          }));\r\n        } else {\r\n          console.error('fileList不是数组：', fileList);\r\n          this.fileList = [];\r\n        }\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 附件移除 */\r\n    handleFileRemove(file, fileList) {\r\n      console.log('附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组\r\n      if (Array.isArray(fileList)) {\r\n        this.fileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.fileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 附件上传前检查 */\r\n    beforeUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      console.log('查看附件被调用，附件数据：', attachments, '类型：', typeof attachments);\r\n\r\n      this.attachmentList = [];\r\n\r\n      if (!attachments) {\r\n        console.log('附件数据为空');\r\n        this.attachmentDialogVisible = true;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            console.log('附件字符串为空');\r\n            this.attachmentDialogVisible = true;\r\n            return;\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                this.attachmentList = parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  size: item.size || '未知大小'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n              // 按逗号分割处理\r\n              this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n                const cleanUrl = url.trim();\r\n                const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n                return {\r\n                  name: fileName,\r\n                  url: cleanUrl,\r\n                  size: '未知大小'\r\n                };\r\n              });\r\n            }\r\n          } else {\r\n            // 按逗号分割处理\r\n            this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const cleanUrl = url.trim();\r\n              const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: cleanUrl,\r\n                size: '未知大小'\r\n              };\r\n            });\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            size: item.size || '未知大小'\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n        this.attachmentList = [];\r\n      }\r\n\r\n      console.log('解析后的附件列表：', this.attachmentList);\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, fileName) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      console.log('解析附件数据：', attachments, '类型：', typeof attachments);\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        // 如果已经是数组，直接返回\r\n        if (Array.isArray(attachments)) {\r\n          return attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            uid: item.uid || Date.now() + index,\r\n            status: 'success'\r\n          }));\r\n        }\r\n\r\n        // 如果是字符串，尝试解析\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            return [];\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                return parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  uid: item.uid || Date.now() + index,\r\n                  status: 'success'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n            }\r\n          }\r\n\r\n          // 按逗号分割处理\r\n          const urls = trimmed.split(',').filter(url => url.trim());\r\n          console.log('分割后的URL列表：', urls);\r\n          return urls.map((url, index) => {\r\n            const cleanUrl = url.trim();\r\n            const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: cleanUrl,\r\n              uid: Date.now() + index,\r\n              status: 'success'\r\n            };\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '未知大小';\r\n      const units = ['B', 'KB', 'MB', 'GB'];\r\n      let index = 0;\r\n      while (size >= 1024 && index < units.length - 1) {\r\n        size /= 1024;\r\n        index++;\r\n      }\r\n      return Math.round(size * 100) / 100 + ' ' + units[index];\r\n    },\r\n\r\n    /** 格式化小数 */\r\n    formatDecimal(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (isNaN(num)) {\r\n        return value;\r\n      }\r\n      // 如果是整数，直接返回，不添加.00\r\n      if (num % 1 === 0) {\r\n        return num.toString();\r\n      }\r\n      // 保留两位小数\r\n      return num.toFixed(2);\r\n    }\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.column-setting-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.column-setting-btn:hover {\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-row .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n  flex: 1;\r\n  min-width: 250px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n.param-detail-card {\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .form-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .form-row .el-form-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .search-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6nBA,IAAAA,WAAA,GAAAC,OAAA;AAIA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACA;IACAC,IAAA;MACAC,IAAA,WAAAA,KAAAC,EAAA;QACA,IAAAC,cAAA,GAAAD,EAAA,CAAAE,aAAA;QACA,IAAAC,OAAA,GAAAH,EAAA,CAAAE,aAAA;QACAD,cAAA,CAAAG,KAAA,CAAAC,MAAA;;QAEA;QACA,IAAAC,GAAA,GAAAH,OAAA,CAAAI,YAAA,IAAAC,MAAA,CAAAC,gBAAA,CAAAN,OAAA;QAEAF,cAAA,CAAAS,WAAA,aAAAC,CAAA;UACA;UACA,IAAAC,IAAA,GAAAD,CAAA,CAAAE,OAAA,GAAAZ,cAAA,CAAAa,UAAA;UACA,IAAAC,IAAA,GAAAJ,CAAA,CAAAK,OAAA,GAAAf,cAAA,CAAAgB,SAAA;;UAEA;UACA,IAAAC,IAAA,EAAAC,IAAA;;UAEA;UACA,IAAAb,GAAA,CAAAc,IAAA,CAAAC,QAAA;YACAH,IAAA,IAAAI,QAAA,CAAAC,IAAA,CAAAC,WAAA,KAAAlB,GAAA,CAAAc,IAAA,CAAAK,OAAA;YACAN,IAAA,IAAAG,QAAA,CAAAC,IAAA,CAAAG,YAAA,KAAApB,GAAA,CAAAqB,GAAA,CAAAF,OAAA;UACA;YACAP,IAAA,IAAAZ,GAAA,CAAAc,IAAA,CAAAK,OAAA;YACAN,IAAA,IAAAb,GAAA,CAAAqB,GAAA,CAAAF,OAAA;UACA;UAEAH,QAAA,CAAAM,WAAA,aAAAjB,CAAA;YACA;YACA,IAAAkB,CAAA,GAAAlB,CAAA,CAAAE,OAAA,GAAAD,IAAA;YACA,IAAAkB,CAAA,GAAAnB,CAAA,CAAAK,OAAA,GAAAD,IAAA;;YAEA;YACAZ,OAAA,CAAAC,KAAA,CAAAgB,IAAA,MAAAW,MAAA,CAAAF,CAAA,GAAAX,IAAA;YACAf,OAAA,CAAAC,KAAA,CAAAuB,GAAA,MAAAI,MAAA,CAAAD,CAAA,GAAAX,IAAA;;YAEA;YACA;UACA;UAEAG,QAAA,CAAAU,SAAA,aAAArB,CAAA;YACAW,QAAA,CAAAM,WAAA;YACAN,QAAA,CAAAU,SAAA;UACA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MAEA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,oBAAA;MAEA;MACAC,iBAAA;MACA;MACAC,yBAAA;MACA;MACAC,mBAAA;MACA;MACAC,sBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QAEAC,OAAA;QACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAT,OAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QAEAE,SAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,mBAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,oBAAA;MAEA;MACAC,oBAAA;MACAC,aAAA;QACAlB,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,WAAA;QACAJ,WAAA;QACAK,eAAA;QACAe,eAAA;QAEAd,aAAA;QACAe,oBAAA;QACAR,SAAA;QACAS,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAC,eAAA;MACAC,cAAA;MAEA;MACAC,sBAAA;MACAC,uBAAA;MACAC,uBAAA;MACAC,wBAAA;MACAC,sBAAA;MACAC,0BAAA;MACAC,wBAAA;MACAC,wBAAA;MAEA;MACAC,QAAA;MACAC,cAAA;MACAC,uBAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,uBAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,eAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA,aACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,cAAA;MACA,KAAAD,eAAA,CAAA4B,OAAA,WAAAC,GAAA;QACAF,KAAA,CAAA1B,cAAA,CAAA4B,GAAA;MACA;IACA;IAEA,eACAL,eAAA,WAAAA,gBAAA;MAAA,IAAAM,MAAA;MACA;MACA,IAAAC,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAA5B,sBAAA,GAAAgC,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAA3B,uBAAA,GAAA+B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAA1B,uBAAA,GAAA8B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAzB,wBAAA,GAAA6B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAxB,sBAAA,GAAA4B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAvB,0BAAA,GAAA2B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAtB,wBAAA,GAAA0B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAS,mBAAA,GAAAL,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAE,2BAAA,WAAAA,4BAAAC,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAzC,sBAAA;MACA,IAAAuC,WAAA,IAAAE,WAAA,IAAAA,WAAA,CAAAC,MAAA;QACAD,WAAA,QAAAzC,sBAAA,CAAA2C,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAK,4BAAA,WAAAA,6BAAAP,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAxC,uBAAA;MACA,IAAAsC,WAAA;QACAE,WAAA,QAAAxC,uBAAA,CAAA0C,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,gBACAM,4BAAA,WAAAA,6BAAAR,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAvC,uBAAA;MACA,IAAAqC,WAAA;QACAE,WAAA,QAAAvC,uBAAA,CAAAyC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAO,6BAAA,WAAAA,8BAAAT,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAtC,wBAAA;MACA,IAAAoC,WAAA;QACAE,WAAA,QAAAtC,wBAAA,CAAAwC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAQ,2BAAA,WAAAA,4BAAAV,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAArC,sBAAA;MACA,IAAAmC,WAAA;QACAE,WAAA,QAAArC,sBAAA,CAAAuC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAS,+BAAA,WAAAA,gCAAAX,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAApC,0BAAA;MACA,IAAAkC,WAAA;QACAE,WAAA,QAAApC,0BAAA,CAAAsC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAU,6BAAA,WAAAA,8BAAAZ,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAnC,wBAAA;MACA,IAAAiC,WAAA;QACAE,WAAA,QAAAnC,wBAAA,CAAAqC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,eACAW,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,IAAAxB,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAArD,sBAAA,GAAAgC,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAkB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,IAAA1B,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAuB,MAAA,CAAAtD,uBAAA,GAAA+B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,gBACAoB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,IAAA5B,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAvD,uBAAA,GAAA8B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAsB,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA,IAAA9B,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA2B,MAAA,CAAAxD,wBAAA,GAAA6B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAwB,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,IAAAhC,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAAzD,sBAAA,GAAA4B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACA0B,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,MAAA;MACA,IAAAlC,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAA1D,0BAAA,GAAA2B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACA4B,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA,IAAApC,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAiC,MAAA,CAAA3D,wBAAA,GAAA0B,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,gBACA8B,6BAAA,WAAAA,8BAAA3B,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAlC,wBAAA;MACA,IAAAgC,WAAA,IAAAE,WAAA,IAAAA,WAAA,CAAAC,MAAA;QACAD,WAAA,QAAAlC,wBAAA,CAAAoC,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAN,WAAA,CAAAK,WAAA;QACA;MACA;MACAJ,EAAA,CAAAC,WAAA;IACA;IAEA,gBACA0B,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA,IAAAvC,gCAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAA7D,wBAAA,GAAAyB,QAAA,CAAA/E,IAAA,CAAAgF,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,gBACAiC,yBAAA,WAAAA,0BAAAnC,IAAA;MACA,KAAAnE,WAAA,CAAAG,YAAA,GAAAgE,IAAA,CAAAC,KAAA;IACA;IAEA,iBACAmC,wBAAA,WAAAA,yBAAAnC,KAAA;MACA;MACA,IAAAA,KAAA,SAAAxD,IAAA,CAAAR,OAAA;QACA,KAAAoG,sBAAA,MAAA5F,IAAA,CAAAR,OAAA;MACA;IACA;IAEA,eACAgD,OAAA,WAAAA,QAAA;MAAA,IAAAqD,MAAA;MACA,KAAAtH,OAAA;MACA,IAAAuH,0BAAA,OAAA1G,WAAA,EAAAgE,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAAjH,cAAA,GAAAyE,QAAA,CAAA0C,IAAA;QACAF,MAAA,CAAAlH,KAAA,GAAA0E,QAAA,CAAA1E,KAAA;QACAkH,MAAA,CAAAtH,OAAA;MACA;IACA;IAEA,gBACAkE,uBAAA,WAAAA,wBAAA;MAAA,IAAAuD,OAAA;MACA,IAAAC,gCAAA,IAAA7C,IAAA,WAAAC,QAAA;QACA2C,OAAA,CAAAjH,oBAAA,GAAAsE,QAAA,CAAA0C,IAAA;MACA,GAAAtC,KAAA;QACAuC,OAAA,CAAAjH,oBAAA;MACA;IACA;IAIA,gBACAmH,yBAAA,WAAAA,0BAAA7F,WAAA;MACA,KAAAL,IAAA,CAAAmG,WAAA;MACA,KAAAhH,sBAAA;MACA,KAAAiH,gBAAA;MAEA,KAAA/F,WAAA;QACA;MACA;;MAEA;MACA,IAAAgG,aAAA,QAAAtH,oBAAA,CAAAuH,IAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAlG,WAAA,KAAAA,WAAA;MAAA;MACA,IAAAgG,aAAA;QACA;QACA,KAAAlH,sBAAA;UACAqH,QAAA,EAAAH,aAAA,CAAAG,QAAA;UACA1G,eAAA,EAAAuG,aAAA,CAAAvG,eAAA;UACAe,eAAA,EAAAwF,aAAA,CAAAxF,eAAA;UACAd,aAAA,EAAAsG,aAAA,CAAAtG,aAAA;UACA0G,SAAA;UACAC,UAAA;UACAC,IAAA;QACA;;QAEA;QACA,KAAAC,gCAAA,CAAAvG,WAAA;MACA;IACA;IAEA,eACAwG,yBAAA,WAAAA,0BAAAV,WAAA;MACA,KAAAA,WAAA,UAAAhH,sBAAA;QACA;MACA;;MAEA;MACA,IAAA2H,aAAA,QAAAV,gBAAA,CAAAE,IAAA,WAAAS,KAAA;QAAA,OAAAA,KAAA,CAAAZ,WAAA,KAAAA,WAAA;MAAA;MACA,IAAAW,aAAA;QACA,KAAA3H,sBAAA,CAAAsH,SAAA,GAAAK,aAAA,CAAAL,SAAA;QACA,KAAAtH,sBAAA,CAAAuH,UAAA,GAAAI,aAAA,CAAAJ,UAAA;QACA,KAAAvH,sBAAA,CAAAwH,IAAA,GAAAG,aAAA,CAAAH,IAAA;MACA;IACA;IAIA,cACAjE,oBAAA,WAAAA,qBAAA;MAAA,IAAAsE,OAAA;MACA,IAAAC,wCAAA,IAAA7D,IAAA,WAAAC,QAAA;QACA2D,OAAA,CAAAhI,iBAAA,GAAAqE,QAAA,CAAA0C,IAAA;QACAiB,OAAA,CAAA/H,yBAAA,GAAAoE,QAAA,CAAA0C,IAAA;MACA;IACA;IAEA,aACAmB,gBAAA,WAAAA,iBAAA1D,KAAA;MACA;MACA,KAAApE,WAAA,CAAAI,OAAA;MACA,KAAA2H,WAAA;IACA;IAIA,cACAvB,sBAAA,WAAAA,uBAAApC,KAAA;MAAA,IAAA4D,OAAA;MACA,IAAA5D,KAAA;QACA;QACA,KAAAtE,mBAAA;;QAEA;QACA,IAAAmI,uCAAA,EAAA7D,KAAA,EAAAJ,IAAA,WAAAC,QAAA;UACA+D,OAAA,CAAAlI,mBAAA,GAAAmE,QAAA,CAAA/E,IAAA;UACAgJ,OAAA,CAAAC,GAAA,eAAAH,OAAA,CAAAlI,mBAAA;;UAEA;UACA,IAAAsI,sCAAA;YAAAhI,OAAA,EAAAgE;UAAA,GAAAJ,IAAA,WAAAqE,aAAA;YACA,IAAAL,OAAA,CAAAlI,mBAAA;cACAkI,OAAA,CAAAlI,mBAAA,CAAAwI,UAAA,GAAAD,aAAA,CAAA1B,IAAA;cACA;cACAqB,OAAA,CAAAlI,mBAAA,CAAAwI,UAAA,CAAA3E,OAAA,WAAAQ,IAAA;gBACA,IAAAoE,WAAA,GAAApE,IAAA,CAAAkD,SAAA;gBACA,IAAAlD,IAAA,CAAAmD,UAAA,aAAAnD,IAAA,CAAAmD,UAAA,KAAAkB,SAAA;kBACAD,WAAA,SAAAvJ,MAAA,CAAAgJ,OAAA,CAAAS,aAAA,CAAAtE,IAAA,CAAAmD,UAAA;gBACA;gBACA,IAAAnD,IAAA,CAAAoD,IAAA;kBACAgB,WAAA,QAAAvJ,MAAA,CAAAmF,IAAA,CAAAoD,IAAA;gBACA;gBACApD,IAAA,CAAAoE,WAAA,GAAAA,WAAA;cACA;cACAL,OAAA,CAAAC,GAAA,cAAAH,OAAA,CAAAlI,mBAAA,CAAAwI,UAAA;;cAEA;cACAN,OAAA,CAAAU,YAAA;YACA;UACA,GAAArE,KAAA,WAAAsE,KAAA;YACAT,OAAA,CAAAS,KAAA,cAAAA,KAAA;YACA,IAAAX,OAAA,CAAAlI,mBAAA;cACAkI,OAAA,CAAAlI,mBAAA,CAAAwI,UAAA;YACA;UACA;QACA,GAAAjE,KAAA,WAAAsE,KAAA;UACAT,OAAA,CAAAS,KAAA,eAAAA,KAAA;UACAX,OAAA,CAAAlI,mBAAA;QACA;MACA;QACA,KAAAA,mBAAA;MACA;IACA;IAEA;IACA8I,MAAA,WAAAA,OAAA;MACA,KAAAlJ,IAAA;MACA,KAAAmJ,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAjI,IAAA;QACAkI,YAAA;QACA7H,WAAA;QACAb,OAAA;QACAsB,oBAAA;QACAR,SAAA;QACA6H,WAAA;QACAC,MAAA;MACA;MACA,KAAAvG,QAAA;MACA,KAAA3C,mBAAA;MACA,KAAAC,sBAAA;MACA,KAAAkJ,SAAA;IACA;IAEA,aACAlB,WAAA,WAAAA,YAAA;MACA,KAAA/H,WAAA,CAAAC,OAAA;MACA,KAAAmD,OAAA;IACA;IAEA,aACA8F,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MACA,KAAAlB,WAAA;IACA;IAEA;IACAoB,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhK,GAAA,GAAAgK,SAAA,CAAAlF,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA2E,YAAA;MAAA;MACA,KAAAzJ,MAAA,GAAA+J,SAAA,CAAAzE,MAAA;MACA,KAAArF,QAAA,IAAA8J,SAAA,CAAAzE,MAAA;IACA;IAEA;IACA0E,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAH,GAAA;IACA;IAEA,aACAI,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAnJ,IAAA;MACA,KAAAD,KAAA;IACA;IAEA,aACAkK,UAAA,WAAAA,WAAAL,GAAA;MAAA,IAAAM,OAAA;MACA,KAAAf,KAAA;MACA,IAAAC,YAAA,GAAAQ,GAAA,CAAAR,YAAA,SAAA1J,GAAA;MACA,IAAAyK,yBAAA,EAAAf,YAAA,EAAA9E,IAAA,WAAAC,QAAA;QACAiE,OAAA,CAAAC,GAAA,aAAAlE,QAAA,CAAA/E,IAAA;QACA0K,OAAA,CAAAhJ,IAAA,GAAAqD,QAAA,CAAA/E,IAAA;QACA;QACA0K,OAAA,CAAAnH,QAAA,GAAAmH,OAAA,CAAAE,gBAAA,CAAA7F,QAAA,CAAA/E,IAAA,CAAA6J,WAAA;QACAb,OAAA,CAAAC,GAAA,gBAAAyB,OAAA,CAAAnH,QAAA;QACAmH,OAAA,CAAAlK,IAAA;QACAkK,OAAA,CAAAnK,KAAA;;QAEA;QACA,IAAAmK,OAAA,CAAAhJ,IAAA,CAAAR,OAAA;UACAwJ,OAAA,CAAApD,sBAAA,CAAAoD,OAAA,CAAAhJ,IAAA,CAAAR,OAAA;QACA;;QAEA;QACA,IAAAwJ,OAAA,CAAAhJ,IAAA,CAAAK,WAAA;UACA2I,OAAA,CAAA9C,yBAAA,CAAA8C,OAAA,CAAAhJ,IAAA,CAAAK,WAAA;;UAEA;UACA,IAAA2I,OAAA,CAAAhJ,IAAA,CAAAmG,WAAA;YACA6C,OAAA,CAAAG,SAAA;cACAH,OAAA,CAAAnC,yBAAA,CAAAmC,OAAA,CAAAhJ,IAAA,CAAAmG,WAAA;YACA;UACA;QACA;MACA;IACA;IAEA,aACAiD,YAAA,WAAAA,aAAAV,GAAA;MAAA,IAAAW,OAAA;MACA,KAAA7I,UAAA,GAAAkI,GAAA;MACA,KAAAjI,gBAAA;MACA,KAAAC,oBAAA;;MAEA;MACA,IAAAgI,GAAA,CAAAlJ,OAAA;QACA,IAAAgI,sCAAA;UAAAhI,OAAA,EAAAkJ,GAAA,CAAAlJ;QAAA,GAAA4D,IAAA,WAAAC,QAAA;UACAgG,OAAA,CAAA5I,gBAAA,GAAA4C,QAAA,CAAA0C,IAAA;QACA,GAAAtC,KAAA,WAAAsE,KAAA;UACAT,OAAA,CAAAS,KAAA,cAAAA,KAAA;UACAsB,OAAA,CAAA5I,gBAAA;QACA;MACA;;MAEA;MACA,IAAAiI,GAAA,CAAArI,WAAA;QACA;QACA,IAAAiJ,SAAA,QAAAvK,oBAAA,CAAAuH,IAAA,WAAAC,KAAA;UAAA,OAAAA,KAAA,CAAAlG,WAAA,KAAAqI,GAAA,CAAArI,WAAA;QAAA;QACA,IAAAiJ,SAAA;UACA;UACA,IAAAC,gCAAA;YAAAlJ,WAAA,EAAAqI,GAAA,CAAArI;UAAA,GAAA+C,IAAA,WAAAC,QAAA;YACAgG,OAAA,CAAA3I,oBAAA;cACA8F,QAAA,EAAA8C,SAAA,CAAA9C,QAAA;cACA1G,eAAA,EAAAwJ,SAAA,CAAAxJ,eAAA;cACAe,eAAA,EAAAyI,SAAA,CAAAzI,eAAA;cACAd,aAAA,EAAAuJ,SAAA,CAAAvJ,aAAA;cACAyJ,UAAA,EAAAnG,QAAA,CAAA0C,IAAA;YACA;UACA,GAAAtC,KAAA,WAAAsE,KAAA;YACAT,OAAA,CAAAS,KAAA,gBAAAA,KAAA;UACA;QACA;MACA;MAEA,KAAAxH,mBAAA;IACA;IAEA,WACAkJ,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAf,KAAA,SAAAgB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,OAAA,CAAA7H,QAAA,IAAA6H,OAAA,CAAA7H,QAAA,CAAAkC,MAAA;YACA2F,OAAA,CAAA1J,IAAA,CAAAmI,WAAA,GAAAuB,OAAA,CAAA7H,QAAA,CAAAyB,GAAA,WAAAuG,IAAA;cAAA,OAAAA,IAAA,CAAAC,GAAA;YAAA,GAAAC,IAAA;UACA;YACAL,OAAA,CAAA1J,IAAA,CAAAmI,WAAA;UACA;;UAEA;;UAEA;UACA,IAAAuB,OAAA,CAAA1J,IAAA,CAAAkI,YAAA;YACA;YACAwB,OAAA,CAAA1J,IAAA,CAAAiB,QAAA,GAAAyI,OAAA,CAAAM,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjO,IAAA;UACA;YACA;YACAyN,OAAA,CAAA1J,IAAA,CAAAe,QAAA,GAAA2I,OAAA,CAAAM,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjO,IAAA;UACA;UAEAqL,OAAA,CAAAC,GAAA,aAAAmC,OAAA,CAAA1J,IAAA;UAEA,IAAA0J,OAAA,CAAA1J,IAAA,CAAAkI,YAAA;YACA,IAAAiC,4BAAA,EAAAT,OAAA,CAAA1J,IAAA,EAAAoD,IAAA,WAAAC,QAAA;cACAqG,OAAA,CAAAU,MAAA,CAAAC,UAAA;cACAX,OAAA,CAAA5K,IAAA;cACA4K,OAAA,CAAAlH,OAAA;YACA;UACA;YACA,IAAA8H,yBAAA,EAAAZ,OAAA,CAAA1J,IAAA,EAAAoD,IAAA,WAAAC,QAAA;cACAqG,OAAA,CAAAU,MAAA,CAAAC,UAAA;cACAX,OAAA,CAAA5K,IAAA;cACA4K,OAAA,CAAAlH,OAAA;YACA;UACA;QACA;MACA;IACA;IAEA,aACA+H,YAAA,WAAAA,aAAA7B,GAAA;MAAA,IAAA8B,OAAA;MACA,IAAAC,aAAA,GAAA/B,GAAA,CAAAR,YAAA,SAAA1J,GAAA;MACA,KAAA4L,MAAA,CAAAM,OAAA,oBAAAD,aAAA,aAAArH,IAAA;QACA,WAAAuH,yBAAA,EAAAF,aAAA;MACA,GAAArH,IAAA;QACAoH,OAAA,CAAAhI,OAAA;QACAgI,OAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAA5G,KAAA;IACA;IAEA,aACAmH,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,mCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA3L,WAAA,kBAAAhB,MAAA,CACA,IAAA4M,IAAA,GAAAC,OAAA;IACA;IAEA,UACAC,mBAAA,WAAAA,oBAAA;MACA,KAAAvK,oBAAA;IACA;IAEA,YACAwK,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhK,cAAA;MACA,KAAAD,eAAA,CAAA4B,OAAA,WAAAC,GAAA;QACAoI,OAAA,CAAAhK,cAAA,CAAA4B,GAAA;MACA;MACA,KAAArC,oBAAA;IACA;IAEA,aACA0K,mBAAA,WAAAA,oBAAAhI,QAAA,EAAAwG,IAAA,EAAAhI,QAAA;MAAA,IAAAyJ,OAAA;MACAhE,OAAA,CAAAC,GAAA;QAAAlE,QAAA,EAAAA,QAAA;QAAAwG,IAAA,EAAAA,IAAA;QAAAhI,QAAA,EAAAA;MAAA;MACA,IAAAwB,QAAA,CAAAkI,IAAA;QACA;QACA,IAAAC,KAAA,CAAAC,OAAA,CAAA5J,QAAA;UACA,KAAAA,QAAA,GAAAA,QAAA,CAAAyB,GAAA,WAAAC,IAAA;YAAA,IAAAmI,SAAA;YAAA;cACAzP,IAAA,EAAAsH,IAAA,CAAAtH,IAAA;cACA6N,GAAA,EAAAvG,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAyG,GAAA,GAAAvG,IAAA,CAAAuG,GAAA;cACA6B,IAAA,EAAAL,OAAA,CAAAM,cAAA,CAAArI,IAAA,CAAAoI,IAAA,MAAAD,SAAA,GAAAnI,IAAA,CAAAsI,GAAA,cAAAH,SAAA,uBAAAA,SAAA,CAAAC,IAAA;cACAG,GAAA,EAAAvI,IAAA,CAAAuI,GAAA;cACAC,MAAA;YACA;UAAA;QACA;UACAzE,OAAA,CAAAS,KAAA,kBAAAlG,QAAA;UACA,KAAAA,QAAA;QACA;QACA,KAAAuI,MAAA,CAAAC,UAAA;MACA;QACA,KAAAD,MAAA,CAAA4B,QAAA,CAAA3I,QAAA,CAAA4I,GAAA;MACA;IACA;IAEA,WACAC,gBAAA,WAAAA,iBAAArC,IAAA,EAAAhI,QAAA;MAAA,IAAAsK,OAAA;MACA7E,OAAA,CAAAC,GAAA;QAAAsC,IAAA,EAAAA,IAAA;QAAAhI,QAAA,EAAAA;MAAA;MACA;MACA,IAAA2J,KAAA,CAAAC,OAAA,CAAA5J,QAAA;QACA,KAAAA,QAAA,GAAAA,QAAA,CAAAyB,GAAA,WAAAC,IAAA;UAAA,IAAA6I,UAAA;UAAA;YACAnQ,IAAA,EAAAsH,IAAA,CAAAtH,IAAA;YACA6N,GAAA,EAAAvG,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAyG,GAAA,GAAAvG,IAAA,CAAAuG,GAAA;YACA6B,IAAA,EAAAQ,OAAA,CAAAP,cAAA,CAAArI,IAAA,CAAAoI,IAAA,MAAAS,UAAA,GAAA7I,IAAA,CAAAsI,GAAA,cAAAO,UAAA,uBAAAA,UAAA,CAAAT,IAAA;YACAG,GAAA,EAAAvI,IAAA,CAAAuI,GAAA;YACAC,MAAA,EAAAxI,IAAA,CAAAwI,MAAA;UACA;QAAA;MACA;QACAzE,OAAA,CAAAS,KAAA,kBAAAlG,QAAA;QACA,KAAAA,QAAA;MACA;MACA,KAAAuI,MAAA,CAAAC,UAAA;IACA;IAEA,cACAgC,YAAA,WAAAA,aAAAxC,IAAA;MACA,IAAAyC,OAAA,GAAAzC,IAAA,CAAA8B,IAAA;MACA,KAAAW,OAAA;QACA,KAAAlC,MAAA,CAAA4B,QAAA;MACA;MACA,OAAAM,OAAA;IACA;IAEA,WACAC,qBAAA,WAAAA,sBAAApE,WAAA;MACAb,OAAA,CAAAC,GAAA,kBAAAY,WAAA,aAAAqE,QAAA,CAAAzB,OAAA,EAAA5C,WAAA;MAEA,KAAArG,cAAA;MAEA,KAAAqG,WAAA;QACAb,OAAA,CAAAC,GAAA;QACA,KAAAxF,uBAAA;QACA;MACA;MAEA;QACA,WAAAoG,WAAA;UACA,IAAAsE,OAAA,GAAAtE,WAAA,CAAAuE,IAAA;UACA,KAAAD,OAAA;YACAnF,OAAA,CAAAC,GAAA;YACA,KAAAxF,uBAAA;YACA;UACA;;UAEA;UACA,IAAA0K,OAAA,CAAAE,UAAA,SAAAF,OAAA,CAAAG,QAAA;YACA;cACA,IAAAC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAN,OAAA;cACA,IAAAjB,KAAA,CAAAC,OAAA,CAAAoB,MAAA;gBACA,KAAA/K,cAAA,GAAA+K,MAAA,CAAAvJ,GAAA,WAAAC,IAAA,EAAAyJ,KAAA;kBAAA;oBACA/Q,IAAA,EAAAsH,IAAA,CAAAtH,IAAA,mBAAAmC,MAAA,CAAA4O,KAAA;oBACAlD,GAAA,EAAAvG,IAAA,CAAAuG,GAAA,IAAAvG,IAAA;oBACAoI,IAAA,EAAApI,IAAA,CAAAoI,IAAA;kBACA;gBAAA;cACA;YACA,SAAAsB,SAAA;cACA3F,OAAA,CAAA4F,IAAA,sBAAAD,SAAA;cACA;cACA,KAAAnL,cAAA,GAAA2K,OAAA,CAAAU,KAAA,MAAAnJ,MAAA,WAAA8F,GAAA;gBAAA,OAAAA,GAAA,CAAA4C,IAAA;cAAA,GAAApJ,GAAA,WAAAwG,GAAA,EAAAkD,KAAA;gBACA,IAAAI,QAAA,GAAAtD,GAAA,CAAA4C,IAAA;gBACA,IAAAW,QAAA,GAAAD,QAAA,CAAAE,SAAA,CAAAF,QAAA,CAAAG,WAAA,6BAAAnP,MAAA,CAAA4O,KAAA;gBACA;kBACA/Q,IAAA,EAAAoR,QAAA;kBACAvD,GAAA,EAAAsD,QAAA;kBACAzB,IAAA;gBACA;cACA;YACA;UACA;YACA;YACA,KAAA7J,cAAA,GAAA2K,OAAA,CAAAU,KAAA,MAAAnJ,MAAA,WAAA8F,GAAA;cAAA,OAAAA,GAAA,CAAA4C,IAAA;YAAA,GAAApJ,GAAA,WAAAwG,GAAA,EAAAkD,KAAA;cACA,IAAAI,QAAA,GAAAtD,GAAA,CAAA4C,IAAA;cACA,IAAAW,QAAA,GAAAD,QAAA,CAAAE,SAAA,CAAAF,QAAA,CAAAG,WAAA,6BAAAnP,MAAA,CAAA4O,KAAA;cACA;gBACA/Q,IAAA,EAAAoR,QAAA;gBACAvD,GAAA,EAAAsD,QAAA;gBACAzB,IAAA;cACA;YACA;UACA;QACA,WAAAH,KAAA,CAAAC,OAAA,CAAAtD,WAAA;UACA,KAAArG,cAAA,GAAAqG,WAAA,CAAA7E,GAAA,WAAAC,IAAA,EAAAyJ,KAAA;YAAA;cACA/Q,IAAA,EAAAsH,IAAA,CAAAtH,IAAA,mBAAAmC,MAAA,CAAA4O,KAAA;cACAlD,GAAA,EAAAvG,IAAA,CAAAuG,GAAA,IAAAvG,IAAA;cACAoI,IAAA,EAAApI,IAAA,CAAAoI,IAAA;YACA;UAAA;QACA;MACA,SAAA5D,KAAA;QACAT,OAAA,CAAAS,KAAA,iBAAAA,KAAA;QACA,KAAAjG,cAAA;MACA;MAEAwF,OAAA,CAAAC,GAAA,mBAAAzF,cAAA;MACA,KAAAC,uBAAA;IACA;IAEA,WACAyL,kBAAA,WAAAA,mBAAA1D,GAAA,EAAAuD,QAAA;MACA,IAAAI,IAAA,GAAA9P,QAAA,CAAA+P,aAAA;MACAD,IAAA,CAAAE,IAAA,GAAA7D,GAAA;MACA2D,IAAA,CAAA5C,QAAA,GAAAwC,QAAA;MACA1P,QAAA,CAAAC,IAAA,CAAAgQ,WAAA,CAAAH,IAAA;MACAA,IAAA,CAAAI,KAAA;MACAlQ,QAAA,CAAAC,IAAA,CAAAkQ,WAAA,CAAAL,IAAA;IACA;IAEA,aACAvE,gBAAA,WAAAA,iBAAAf,WAAA;MACAb,OAAA,CAAAC,GAAA,YAAAY,WAAA,aAAAqE,QAAA,CAAAzB,OAAA,EAAA5C,WAAA;MACA,KAAAA,WAAA;QACA;MACA;MAEA;QACA;QACA,IAAAqD,KAAA,CAAAC,OAAA,CAAAtD,WAAA;UACA,OAAAA,WAAA,CAAA7E,GAAA,WAAAC,IAAA,EAAAyJ,KAAA;YAAA;cACA/Q,IAAA,EAAAsH,IAAA,CAAAtH,IAAA,mBAAAmC,MAAA,CAAA4O,KAAA;cACAlD,GAAA,EAAAvG,IAAA,CAAAuG,GAAA,IAAAvG,IAAA;cACAuI,GAAA,EAAAvI,IAAA,CAAAuI,GAAA,IAAAd,IAAA,CAAA+C,GAAA,KAAAf,KAAA;cACAjB,MAAA;YACA;UAAA;QACA;;QAEA;QACA,WAAA5D,WAAA;UACA,IAAAsE,OAAA,GAAAtE,WAAA,CAAAuE,IAAA;UACA,KAAAD,OAAA;YACA;UACA;;UAEA;UACA,IAAAA,OAAA,CAAAE,UAAA,SAAAF,OAAA,CAAAG,QAAA;YACA;cACA,IAAAC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAN,OAAA;cACA,IAAAjB,KAAA,CAAAC,OAAA,CAAAoB,MAAA;gBACA,OAAAA,MAAA,CAAAvJ,GAAA,WAAAC,IAAA,EAAAyJ,KAAA;kBAAA;oBACA/Q,IAAA,EAAAsH,IAAA,CAAAtH,IAAA,mBAAAmC,MAAA,CAAA4O,KAAA;oBACAlD,GAAA,EAAAvG,IAAA,CAAAuG,GAAA,IAAAvG,IAAA;oBACAuI,GAAA,EAAAvI,IAAA,CAAAuI,GAAA,IAAAd,IAAA,CAAA+C,GAAA,KAAAf,KAAA;oBACAjB,MAAA;kBACA;gBAAA;cACA;YACA,SAAAkB,SAAA;cACA3F,OAAA,CAAA4F,IAAA,sBAAAD,SAAA;YACA;UACA;;UAEA;UACA,IAAAe,IAAA,GAAAvB,OAAA,CAAAU,KAAA,MAAAnJ,MAAA,WAAA8F,GAAA;YAAA,OAAAA,GAAA,CAAA4C,IAAA;UAAA;UACApF,OAAA,CAAAC,GAAA,eAAAyG,IAAA;UACA,OAAAA,IAAA,CAAA1K,GAAA,WAAAwG,GAAA,EAAAkD,KAAA;YACA,IAAAI,QAAA,GAAAtD,GAAA,CAAA4C,IAAA;YACA,IAAAW,QAAA,GAAAD,QAAA,CAAAE,SAAA,CAAAF,QAAA,CAAAG,WAAA,6BAAAnP,MAAA,CAAA4O,KAAA;YACA;cACA/Q,IAAA,EAAAoR,QAAA;cACAvD,GAAA,EAAAsD,QAAA;cACAtB,GAAA,EAAAd,IAAA,CAAA+C,GAAA,KAAAf,KAAA;cACAjB,MAAA;YACA;UACA;QACA;MACA,SAAAhE,KAAA;QACAT,OAAA,CAAAS,KAAA,iBAAAA,KAAA;MACA;MAEA;IACA;IAEA,cACA6D,cAAA,WAAAA,eAAAD,IAAA;MACA,KAAAA,IAAA;MACA,IAAAsC,KAAA;MACA,IAAAjB,KAAA;MACA,OAAArB,IAAA,YAAAqB,KAAA,GAAAiB,KAAA,CAAAlK,MAAA;QACA4H,IAAA;QACAqB,KAAA;MACA;MACA,OAAAkB,IAAA,CAAAC,KAAA,CAAAxC,IAAA,sBAAAsC,KAAA,CAAAjB,KAAA;IACA;IAEA,YACAnF,aAAA,WAAAA,cAAArE,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAoE,SAAA,IAAApE,KAAA;QACA;MACA;MACA,IAAA4K,GAAA,GAAAC,UAAA,CAAA7K,KAAA;MACA,IAAA8K,KAAA,CAAAF,GAAA;QACA,OAAA5K,KAAA;MACA;MACA;MACA,IAAA4K,GAAA;QACA,OAAAA,GAAA,CAAAG,QAAA;MACA;MACA;MACA,OAAAH,GAAA,CAAAI,OAAA;IACA;EACA;EAEA,WACAhB,kBAAA,WAAAA,mBAAA1D,GAAA,EAAA7N,IAAA;IACA,IAAAwR,IAAA,GAAA9P,QAAA,CAAA+P,aAAA;IACAD,IAAA,CAAAE,IAAA,GAAA7D,GAAA;IACA2D,IAAA,CAAA5C,QAAA,GAAA5O,IAAA;IACAwR,IAAA,CAAAI,KAAA;EACA;EAEA,cACAjC,cAAA,WAAAA,eAAAD,IAAA;IACA,IAAAA,IAAA;MACA,OAAAA,IAAA;IACA,WAAAA,IAAA;MACA,QAAAA,IAAA,SAAA6C,OAAA;IACA;MACA,QAAA7C,IAAA,gBAAA6C,OAAA;IACA;EACA;AACA", "ignoreList": []}]}