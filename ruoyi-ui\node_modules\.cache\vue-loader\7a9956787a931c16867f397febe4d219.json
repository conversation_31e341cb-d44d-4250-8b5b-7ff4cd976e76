{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=template&id=24309412&scoped=true", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754281779689}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753339890378}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}