{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=template&id=24309412&scoped=true", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754285582786}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753339890378}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDwhLS0g6aG16Z2i5qCH6aKY5ZKM6K+05piOIC0tPgogIDxkaXYgY2xhc3M9InBhZ2UtaGVhZGVyIj4KICAgIDxkaXYgY2xhc3M9InBhZ2UtdGl0bGUiPgogICAgICA8aSBjbGFzcz0iZWwtaWNvbi1kYXRhLWxpbmUiPjwvaT4KICAgICAgPHNwYW4+6LaL5Yq/5a+55q+U5YiG5p6QPC9zcGFuPgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJwYWdlLWRlc2NyaXB0aW9uIj4KICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyBhbGlnbi1pdGVtczogY2VudGVyOyI+CiAgICAgICAgPHA+8J+TiCDlpJrnu7TluqbmlbDmja7otovlir/lr7nmr5TliIbmnpDvvIzmlK/mjIHmnZDmlpnmgKfog73jgIHkvpvlupTllYbotKjph4/nrYnlpJrnp43lr7nmr5Tnu7TluqY8L3A+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBAY2xpY2s9InNob3dVc2FnZUd1aWRlID0gdHJ1ZSIgc3R5bGU9ImNvbG9yOiAjNDA5RUZGOyI+CiAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1xdWVzdGlvbiI+PC9pPgogICAgICAgICAgPHNwYW4+5L2/55So5oyH5Y2XPC9zcGFuPgogICAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8L2Rpdj4KICAgICAgPGVsLWFsZXJ0CiAgICAgICAgdGl0bGU9IuS9v+eUqOaPkOekuu+8mumAieaLqeWvueavlOe7tOW6piDihpIg6YWN572u562b6YCJ5p2h5Lu2IOKGkiDnlJ/miJDlm77ooajliIbmnpAiCiAgICAgICAgdHlwZT0iaW5mbyIKICAgICAgICA6Y2xvc2FibGU9ImZhbHNlIgogICAgICAgIHNob3ctaWNvbgogICAgICAgIHN0eWxlPSJtYXJnaW4tdG9wOiAxMHB4OyI+CiAgICAgIDwvZWwtYWxlcnQ+CiAgICA8L2Rpdj4KICA8L2Rpdj4KCiAgPCEtLSDkvb/nlKjmjIfljZfljaHniYcgLS0+CiAgPGVsLWNhcmQgY2xhc3M9InVzYWdlLWd1aWRlLWNhcmQgZW5oYW5jZWQtY2FyZCIgc3R5bGU9Im1hcmdpbi1ib3R0b206IDIwcHg7IiB2LWlmPSJzaG93VXNhZ2VHdWlkZSI+CiAgICA8ZGl2IHNsb3Q9ImhlYWRlciIgY2xhc3M9ImNhcmQtaGVhZGVyIj4KICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWxlZnQiPgogICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWluZm8iPjwvaT4KICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyLXRpdGxlIj7wn5OKIOi2i+WKv+WvueavlOWIhuaekOS9v+eUqOaMh+WNlzwvc3Bhbj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1yaWdodCI+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBAY2xpY2s9InNob3dVc2FnZUd1aWRlID0gZmFsc2UiIGNsYXNzPSJjbG9zZS1ndWlkZS1idG4iPgogICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tY2xvc2UiPjwvaT4KICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgPGRpdiBjbGFzcz0iZ3VpZGUtaXRlbSI+CiAgICAgICAgICA8aDQ+8J+OryDlr7nmr5Tnu7Tluqbor7TmmI48L2g0PgogICAgICAgICAgPHVsIHN0eWxlPSJtYXJnaW46IDEwcHggMDsgcGFkZGluZy1sZWZ0OiAyMHB4OyBsaW5lLWhlaWdodDogMS42OyI+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuadkOaWmeWvueavlO+8mjwvc3Ryb25nPuaMieadkOaWmeWQjeensOWIhue7hO+8jOiuoeeul+avj+enjeadkOaWmeeahOS+m+W6lOWVhuaVsOaNruWSjOa1i+ivleaVsOaNruW5s+Wdh+WAvDwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuS+m+W6lOWVhuWvueavlO+8mjwvc3Ryb25nPuaMieS+m+W6lOWVhuWIhue7hO+8jOiuoeeul+WHhuehrueOh++8iOS+m+W6lOWVhuaVsOaNruS4jua1i+ivleaVsOaNrueahOWBj+W3ru+8iTwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuWPguaVsOe8luWPt+WvueavlO+8mjwvc3Ryb25nPuaMieWPguaVsOe7hOWIhue7hO+8jOWxleekuuW3peiJuuWPguaVsOaYjue7huWSjOa1i+ivlee7k+aenOe7n+iuoTwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuW3peiJuuexu+Wei+WvueavlO+8mjwvc3Ryb25nPuaMieW3peiJuuexu+Wei+WIhue7hO+8jOiuoeeul+eos+WumuaAp++8iOa1i+ivleaVsOaNruagh+WHhuW3ru+8iTwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuaXtumXtOi2i+WKv++8mjwvc3Ryb25nPuaMieaXpeacn+WIhue7hO+8jOWxleekuua1i+ivleaVsOaNrumaj+aXtumXtOeahOWPmOWMlui2i+WKvzwvbGk+CiAgICAgICAgICA8L3VsPgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWNvbD4KICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgIDxkaXYgY2xhc3M9Imd1aWRlLWl0ZW0iPgogICAgICAgICAgPGg0PvCfk4og5pWw5o2u5p2l5rqQ6K+m6KejPC9oND4KICAgICAgICAgIDx1bCBzdHlsZT0ibWFyZ2luOiAxMHB4IDA7IHBhZGRpbmctbGVmdDogMjBweDsgbGluZS1oZWlnaHQ6IDEuNjsiPgogICAgICAgICAgICA8bGk+PHN0cm9uZz7kvpvlupTllYblubPlnYflgLzvvJo8L3N0cm9uZz7pgInkuK3pobnnm67kuIvmiYDmnInmtYvor5XorrDlvZXnmoTkvpvlupTllYbmlbDmja7lubPlnYflgLw8L2xpPgogICAgICAgICAgICA8bGk+PHN0cm9uZz7mtYvor5XlubPlnYflgLzvvJo8L3N0cm9uZz7pgInkuK3pobnnm67kuIvmiYDmnInmtYvor5XorrDlvZXnmoTlrp7pmYXmtYvor5XlgLzlubPlnYflgLw8L2xpPgogICAgICAgICAgICA8bGk+PHN0cm9uZz7lh4bnoa7njofvvJo8L3N0cm9uZz4xMDAlIC0gfOS+m+W6lOWVhuW5s+Wdh+WAvCAtIOa1i+ivleW5s+Wdh+WAvHwgLyDkvpvlupTllYblubPlnYflgLwgw5cgMTAwJTwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPueos+WumuaAp++8mjwvc3Ryb25nPuWfuuS6jua1i+ivleaVsOaNruagh+WHhuW3ruiuoeeul++8jOaVsOWAvOi2iuWwj+i2iueos+WumjwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuaVsOaNrumHj++8mjwvc3Ryb25nPuWPguS4juiuoeeul+eahOa1i+ivleiusOW9leaAu+aVsDwvbGk+CiAgICAgICAgICAgIDxsaT48c3Ryb25nPuWPguaVsOaYjue7hu+8mjwvc3Ryb25nPuadpeiHquW3peiJuuWPguaVsOmFjee9ruS4reeahOWFt+S9k+WPguaVsOmhuTwvbGk+CiAgICAgICAgICA8L3VsPgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWNvbD4KICAgIDwvZWwtcm93PgogICAgPGVsLXJvdyBzdHlsZT0ibWFyZ2luLXRvcDogMTVweDsiPgogICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgPGRpdiBjbGFzcz0iZ3VpZGUtaXRlbSI+CiAgICAgICAgICA8aDQ+8J+SoSDkvb/nlKjlu7rorq48L2g0PgogICAgICAgICAgPGRpdiBjbGFzcz0idXNhZ2Utc3VnZ2VzdGlvbiI+CiAgICAgICAgICAgIDxwPjEuIOmAieaLqeWvueavlOe7tOW6piDihpIgMi4g6YWN572u562b6YCJ5p2h5Lu277yI5pSv5oyB5aSa6YCJ77yJIOKGkiAzLiDngrnlh7si55Sf5oiQ5Zu+6KGoIuWIhuaekCDihpIgNC4g5YiH5o2i5Zu+6KGo57G75Z6L5p+l55yL5LiN5ZCM6KeG6KeSIOKGkiA1LiDmn6XnnIvor6bnu4bmlbDmja7ooajojrflj5blhbfkvZPmlbDlgLw8L3A+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9lbC1jb2w+CiAgICA8L2VsLXJvdz4KICA8L2VsLWNhcmQ+CgogIDxlbC1jYXJkIGNsYXNzPSJ0cmVuZC1hbmFseXNpcy1jYXJkIGVuaGFuY2VkLWNhcmQiPgogICAgPGRpdiBzbG90PSJoZWFkZXIiIGNsYXNzPSJjYXJkLWhlYWRlciI+CiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1sZWZ0Ij4KICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1kYXRhLWxpbmUiPjwvaT4KICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyLXRpdGxlIj7mlbDmja7otovlir/lr7nmr5TliIbmnpA8L3NwYW4+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItcmlnaHQiPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0iaW5mbyIgaWNvbj0iZWwtaWNvbi1xdWVzdGlvbiIgc2l6ZT0ic21hbGwiIEBjbGljaz0ic2hvd1VzYWdlR3VpZGUgPSAhc2hvd1VzYWdlR3VpZGUiIGNsYXNzPSJndWlkZS1idG4iPgogICAgICAgICAgPHNwYW4+5L2/55So5oyH5Y2XPC9zcGFuPgogICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJyZWZyZXNoQ2hhcnQiIGNsYXNzPSJyZWZyZXNoLWJ0biI+CiAgICAgICAgICA8c3Bhbj7liLfmlrDmlbDmja48L3NwYW4+CiAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJzdWNjZXNzIiBpY29uPSJlbC1pY29uLWRvd25sb2FkIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJleHBvcnRDaGFydCIgY2xhc3M9ImV4cG9ydC1idG4iPgogICAgICAgICAgPHNwYW4+5a+85Ye65Zu+6KGoPC9zcGFuPgogICAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0g562b6YCJ5p2h5Lu2IC0tPgogICAgPGVsLWZvcm0gOm1vZGVsPSJxdWVyeVBhcmFtcyIgcmVmPSJxdWVyeUZvcm0iIHNpemU9InNtYWxsIiA6aW5saW5lPSJ0cnVlIiBsYWJlbC13aWR0aD0iMTAwcHgiIHN0eWxlPSJtYXJnaW4tYm90dG9tOiAyMHB4OyI+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWvueavlOe7tOW6piIgcHJvcD0iY29tcGFyZVR5cGUiPgogICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMuY29tcGFyZVR5cGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6nlr7nmr5Tnu7TluqYiIHN0eWxlPSJ3aWR0aDogMjUwcHg7IiBjbGVhcmFibGUgQGNoYW5nZT0iaGFuZGxlQ29tcGFyZVR5cGVDaGFuZ2UiPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i8J+TiiDmnZDmlpnmgKfog73lr7nmr5QiIHZhbHVlPSJtYXRlcmlhbCI+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogbGVmdCI+8J+TiiDmnZDmlpnmgKfog73lr7nmr5Q8L3NwYW4+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogcmlnaHQ7IGNvbG9yOiAjODQ5MmE2OyBmb250LXNpemU6IDEycHgiPuavlOi+g+S4jeWQjOadkOaWmeaAp+iDvTwvc3Bhbj4KICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i8J+PrSDkvpvlupTllYbmlbDmja7lr7nmr5QiIHZhbHVlPSJzdXBwbGllciI+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogbGVmdCI+8J+PrSDkvpvlupTllYbmlbDmja7lr7nmr5Q8L3NwYW4+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogcmlnaHQ7IGNvbG9yOiAjODQ5MmE2OyBmb250LXNpemU6IDEycHgiPuavlOi+g+S+m+W6lOWVhui0qOmHjzwvc3Bhbj4KICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i8J+UoiDlj4LmlbDnvJblj7flr7nmr5QiIHZhbHVlPSJwYXJhbU51bWJlciI+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogbGVmdCI+8J+UoiDlj4LmlbDnvJblj7flr7nmr5Q8L3NwYW4+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogcmlnaHQ7IGNvbG9yOiAjODQ5MmE2OyBmb250LXNpemU6IDEycHgiPuavlOi+g+S4jeWQjOWPguaVsOWAvDwvc3Bhbj4KICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i4pqZ77iPIOW3peiJuuexu+Wei+WvueavlCIgdmFsdWU9InByb2Nlc3NUeXBlIj4KICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZsb2F0OiBsZWZ0Ij7impnvuI8g5bel6Im657G75Z6L5a+55q+UPC9zcGFuPgogICAgICAgICAgICA8c3BhbiBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyBjb2xvcjogIzg0OTJhNjsgZm9udC1zaXplOiAxMnB4Ij7mr5TovoPlt6XoibrmlYjmnpw8L3NwYW4+CiAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IvCfk4gg5pe26Ze06LaL5Yq/5YiG5p6QIiB2YWx1ZT0idGltZVRyZW5kIj4KICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZsb2F0OiBsZWZ0Ij7wn5OIIOaXtumXtOi2i+WKv+WIhuaekDwvc3Bhbj4KICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZsb2F0OiByaWdodDsgY29sb3I6ICM4NDkyYTY7IGZvbnQtc2l6ZTogMTJweCI+5p+l55yL5pe26Ze05Y+Y5YyWPC9zcGFuPgogICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLimpbvuI8g5L6b5bqU5ZWGdnPmtYvor5XlgLwiIHZhbHVlPSJzdXBwbGllclZzVGVzdCI+CiAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmbG9hdDogbGVmdCI+4pqW77iPIOS+m+W6lOWVhnZz5rWL6K+V5YC8PC9zcGFuPgogICAgICAgICAgICA8c3BhbiBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyBjb2xvcjogIzg0OTJhNjsgZm9udC1zaXplOiAxMnB4Ij7lr7nmr5TmlbDmja7lt67lvII8L3NwYW4+CiAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8IS0tIOadkOaWmeaAp+iDveWvueavlCAtLT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6YCJ5oup5p2Q5paZIiBwcm9wPSJtYXRlcmlhbE5hbWVzIiB2LWlmPSJxdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ21hdGVyaWFsJyI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMubWF0ZXJpYWxOYW1lcyIKICAgICAgICAgIG11bHRpcGxlCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup6KaB5a+55q+U55qE5p2Q5paZIgogICAgICAgICAgc3R5bGU9IndpZHRoOiAzMDBweDsiCiAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICBjbGVhcmFibGUKICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSJtYXRlcmlhbCBpbiBtYXRlcmlhbE9wdGlvbnMiCiAgICAgICAgICAgIDprZXk9Im1hdGVyaWFsLm1hdGVyaWFsSWQiCiAgICAgICAgICAgIDpsYWJlbD0ibWF0ZXJpYWwubWF0ZXJpYWxOYW1lICsgJyAoJyArIG1hdGVyaWFsLnN1cHBsaWVyTmFtZSArICcpJyIKICAgICAgICAgICAgOnZhbHVlPSJtYXRlcmlhbC5tYXRlcmlhbElkIgogICAgICAgICAgLz4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8IS0tIOS+m+W6lOWVhuaVsOaNruWvueavlCAtLT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6YCJ5oup5L6b5bqU5ZWGIiBwcm9wPSJzdXBwbGllck5hbWVzIiB2LWlmPSJxdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ3N1cHBsaWVyJyI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuc3VwcGxpZXJOYW1lcyIKICAgICAgICAgIG11bHRpcGxlCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup6KaB5a+55q+U55qE5L6b5bqU5ZWGIgogICAgICAgICAgc3R5bGU9IndpZHRoOiAzMDBweDsiCiAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICBjbGVhcmFibGUKICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSJzdXBwbGllciBpbiBzdXBwbGllck9wdGlvbnMiCiAgICAgICAgICAgIDprZXk9InN1cHBsaWVyIgogICAgICAgICAgICA6bGFiZWw9InN1cHBsaWVyIgogICAgICAgICAgICA6dmFsdWU9InN1cHBsaWVyIgogICAgICAgICAgLz4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8IS0tIOWPguaVsOe8luWPt+WvueavlCAtLT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6YCJ5oup5Y+C5pWwIiBwcm9wPSJwYXJhbU51bWJlcnMiIHYtaWY9InF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlID09PSAncGFyYW1OdW1iZXInIj4KICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5wYXJhbU51bWJlcnMiCiAgICAgICAgICBtdWx0aXBsZQogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeimgeWvueavlOeahOWPguaVsOe8luWPtyIKICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMzAwcHg7IgogICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgPgogICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICB2LWZvcj0icGFyYW0gaW4gcGFyYW1OdW1iZXJPcHRpb25zIgogICAgICAgICAgICA6a2V5PSJwYXJhbS5ncm91cElkIgogICAgICAgICAgICA6bGFiZWw9InBhcmFtLnBhcmFtTnVtYmVyICsgJyAtICcgKyBwYXJhbS5tYXRlcmlhbE5hbWUiCiAgICAgICAgICAgIDp2YWx1ZT0icGFyYW0uZ3JvdXBJZCIKICAgICAgICAgIC8+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPCEtLSDlt6Xoibrnsbvlnovlr7nmr5QgLS0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IumAieaLqeW3peiJuiIgcHJvcD0icHJvY2Vzc1R5cGVzIiB2LWlmPSJxdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ3Byb2Nlc3NUeXBlJyI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMucHJvY2Vzc1R5cGVzIgogICAgICAgICAgbXVsdGlwbGUKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nopoHlr7nmr5TnmoTlt6XoibrnsbvlnosiCiAgICAgICAgICBzdHlsZT0id2lkdGg6IDMwMHB4OyIKICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgID4KICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgdi1mb3I9InR5cGUgaW4gcHJvY2Vzc1R5cGVPcHRpb25zIgogICAgICAgICAgICA6a2V5PSJ0eXBlIgogICAgICAgICAgICA6bGFiZWw9InR5cGUiCiAgICAgICAgICAgIDp2YWx1ZT0idHlwZSIKICAgICAgICAgIC8+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPCEtLSDml7bpl7Totovlir/liIbmnpAgLS0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaXtumXtOiMg+WbtCIgcHJvcD0iZGF0ZVJhbmdlIiB2LWlmPSJxdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ3RpbWVUcmVuZCciPgogICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuZGF0ZVJhbmdlIgogICAgICAgICAgdHlwZT0iZGF0ZXJhbmdlIgogICAgICAgICAgcmFuZ2Utc2VwYXJhdG9yPSLoh7MiCiAgICAgICAgICBzdGFydC1wbGFjZWhvbGRlcj0i5byA5aeL5pel5pyfIgogICAgICAgICAgZW5kLXBsYWNlaG9sZGVyPSLnu5PmnZ/ml6XmnJ8iCiAgICAgICAgICBzdHlsZT0id2lkdGg6IDMwMHB4OyIKICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPCEtLSDkvpvlupTllYZ2c+a1i+ivleWAvOWvueavlCAtLT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6YCJ5oup5Y+C5pWwIiBwcm9wPSJjb21wYXJlUGFyYW0iIHYtaWY9InF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlID09PSAnc3VwcGxpZXJWc1Rlc3QnIj4KICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5jb21wYXJlUGFyYW0iCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup6KaB5a+55q+U55qE5Y+C5pWwIgogICAgICAgICAgc3R5bGU9IndpZHRoOiAzMDBweDsiCiAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICBjbGVhcmFibGUKICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSJwYXJhbSBpbiBwYXJhbU51bWJlck9wdGlvbnMiCiAgICAgICAgICAgIDprZXk9InBhcmFtLmdyb3VwSWQiCiAgICAgICAgICAgIDpsYWJlbD0icGFyYW0ucGFyYW1OdW1iZXIgKyAnIC0gJyArIHBhcmFtLm1hdGVyaWFsTmFtZSIKICAgICAgICAgICAgOnZhbHVlPSJwYXJhbS5ncm91cElkIgogICAgICAgICAgLz4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgoKCiAgICAgIDxlbC1mb3JtLWl0ZW0+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXNlYXJjaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVRdWVyeSIgOmxvYWRpbmc9ImxvYWRpbmciPueUn+aIkOWvueavlOWbvuihqDwvZWwtYnV0dG9uPgogICAgICAgIDxlbC1idXR0b24gaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc2V0UXVlcnkiPumHjee9ruadoeS7tjwvZWwtYnV0dG9uPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgIDwvZWwtZm9ybT4KCiAgICA8IS0tIOWbvuihqOexu+Wei+mAieaLqSAtLT4KICAgIDxlbC1yb3cgc3R5bGU9Im1hcmdpbi1ib3R0b206IDIwcHg7Ij4KICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJjaGFydFR5cGUiIEBjaGFuZ2U9ImhhbmRsZUNoYXJ0VHlwZUNoYW5nZSI+CiAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJsaW5lIj7mipjnur/lm748L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9ImJhciI+5p+x54q25Zu+PC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJzY2F0dGVyIj7mlaPngrnlm748L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9InJhZGFyIj7pm7fovr7lm748L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9ImhlYXRtYXAiPueDreWKm+WbvjwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgPGVsLWJ1dHRvbi1ncm91cCBzdHlsZT0ibWFyZ2luLWxlZnQ6IDIwcHg7Ij4KICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIEBjbGljaz0idG9nZ2xlRGF0YVRhYmxlIj57eyBzaG93RGF0YVRhYmxlID8gJ+makOiXjycgOiAn5pi+56S6JyB9feaVsOaNruihqDwvZWwtYnV0dG9uPgogICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJzbWFsbCIgQGNsaWNrPSJ0b2dnbGVQcm9qZWN0RGV0YWlscyIgOmRpc2FibGVkPSJzZWxlY3RlZFBhcmFtRGV0YWlscy5sZW5ndGggPT09IDAiPgogICAgICAgICAgICB7eyBzaG93UHJvamVjdERldGFpbHMgPyAn6ZqQ6JePJyA6ICfmmL7npLonIH196aG555uu6K+m5oOFCiAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIEBjbGljaz0idG9nZ2xlRnVsbHNjcmVlbiI+5YWo5bGP5pi+56S6PC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1idXR0b24tZ3JvdXA+CiAgICAgIDwvZWwtY29sPgogICAgPC9lbC1yb3c+CiAgPC9lbC1jYXJkPgoKICA8IS0tIOWPguaVsOivpuaDheS/oeaBr+WNoeeJhyAtLT4KICA8ZWwtY2FyZCB2LWlmPSJzZWxlY3RlZFBhcmFtRGV0YWlscy5sZW5ndGggPiAwICYmIHNob3dQcm9qZWN0RGV0YWlscyIgY2xhc3M9ImJveC1jYXJkIHBhcmFtLWRldGFpbHMtY2FyZCIgc3R5bGU9Im1hcmdpbi1ib3R0b206IDIwcHg7Ij4KICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTRweDsiPvCfk4sg6YCJ5Lit6aG555uu6K+m5oOF5L+h5oGvPC9zcGFuPgogICAgICA8ZWwtdGFnIHNpemU9Im1pbmkiIHR5cGU9ImluZm8iIHN0eWxlPSJtYXJnaW4tbGVmdDogMTBweDsiPnt7IHNlbGVjdGVkUGFyYW1EZXRhaWxzLmxlbmd0aCB9femhuTwvZWwtdGFnPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIEBjbGljaz0ic2hvd1Byb2plY3REZXRhaWxzID0gZmFsc2UiIHN0eWxlPSJmbG9hdDogcmlnaHQ7IGNvbG9yOiAjOTA5Mzk5OyI+CiAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tY2xvc2UiPjwvaT4KICAgICAgPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICA8ZWwtY29sIDpzcGFuPSI4IiB2LWZvcj0iKGRldGFpbCwgaW5kZXgpIGluIHNlbGVjdGVkUGFyYW1EZXRhaWxzIiA6a2V5PSJpbmRleCI+CiAgICAgICAgPGVsLWNhcmQgY2xhc3M9InBhcmFtLWRldGFpbC1jYXJkIiBzaGFkb3c9ImhvdmVyIj4KICAgICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IGNvbG9yOiAjNDA5RUZGOyI+CiAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tZGF0YS1saW5lIj48L2k+CiAgICAgICAgICAgICAge3sgZGV0YWlsLnBhcmFtTnVtYmVyIHx8IGRldGFpbC5uYW1lIH19CiAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgPGVsLXRhZyBzaXplPSJtaW5pIiB0eXBlPSJzdWNjZXNzIiBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyIgdi1pZj0iZGV0YWlsLnRlc3RDb3VudCI+CiAgICAgICAgICAgICAge3sgZGV0YWlsLnRlc3RDb3VudCB9feasoea1i+ivlQogICAgICAgICAgICA8L2VsLXRhZz4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDwhLS0g5Z+65pys5L+h5oGvIC0tPgogICAgICAgICAgPGRpdiBjbGFzcz0iZGV0YWlsLXNlY3Rpb24iPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj4KICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1pbmZvIj48L2k+CiAgICAgICAgICAgICAg5Z+65pys5L+h5oGvCiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZWwtZGVzY3JpcHRpb25zIDpjb2x1bW49IjIiIGJvcmRlciBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLmnZDmlpnlkI3np7AiIHYtaWY9ImRldGFpbC5tYXRlcmlhbE5hbWUiPgogICAgICAgICAgICAgICAgPGVsLXRhZyB0eXBlPSJwcmltYXJ5IiBzaXplPSJtaW5pIj57eyBkZXRhaWwubWF0ZXJpYWxOYW1lIH19PC9lbC10YWc+CiAgICAgICAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuS+m+W6lOWVhiIgdi1pZj0iZGV0YWlsLnN1cHBsaWVyTmFtZSI+CiAgICAgICAgICAgICAgICA8ZWwtdGFnIHR5cGU9InN1Y2Nlc3MiIHNpemU9Im1pbmkiPnt7IGRldGFpbC5zdXBwbGllck5hbWUgfX08L2VsLXRhZz4KICAgICAgICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5p2Q5paZ5Z6L5Y+3IiB2LWlmPSJkZXRhaWwubWF0ZXJpYWxNb2RlbCI+CiAgICAgICAgICAgICAgICA8c3Bhbj57eyBkZXRhaWwubWF0ZXJpYWxNb2RlbCB9fTwvc3Bhbj4KICAgICAgICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5bel6Im657G75Z6LIiB2LWlmPSJkZXRhaWwucHJvY2Vzc1R5cGUiPgogICAgICAgICAgICAgICAgPGVsLXRhZyB0eXBlPSJ3YXJuaW5nIiBzaXplPSJtaW5pIj57eyBkZXRhaWwucHJvY2Vzc1R5cGUgfX08L2VsLXRhZz4KICAgICAgICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5oCn6IO957G75Z6LIiB2LWlmPSJkZXRhaWwucGVyZm9ybWFuY2VUeXBlIj4KICAgICAgICAgICAgICAgIDxlbC10YWcgdHlwZT0iaW5mbyIgc2l6ZT0ibWluaSI+e3sgZGV0YWlsLnBlcmZvcm1hbmNlVHlwZSB9fTwvZWwtdGFnPgogICAgICAgICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlj4LmlbDnvJblj7ciIHYtaWY9ImRldGFpbC5wYXJhbU51bWJlciI+CiAgICAgICAgICAgICAgICA8c3Bhbj57eyBkZXRhaWwucGFyYW1OdW1iZXIgfX08L3NwYW4+CiAgICAgICAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuaVsOaNrumHjyIgdi1pZj0iZGV0YWlsLmRhdGFDb3VudCAhPT0gdW5kZWZpbmVkIj4KICAgICAgICAgICAgICAgIDxlbC10YWcgdHlwZT0iZGFuZ2VyIiBzaXplPSJtaW5pIj57eyBkZXRhaWwuZGF0YUNvdW50IH195p2hPC9lbC10YWc+CiAgICAgICAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuWIm+W7uuaXtumXtCIgdi1pZj0iZGV0YWlsLmNyZWF0ZVRpbWUiPgogICAgICAgICAgICAgICAgPHNwYW4+e3sgZGV0YWlsLmNyZWF0ZVRpbWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnM+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8IS0tIOe7n+iuoeS/oeaBryAtLT4KICAgICAgICAgIDxkaXYgY2xhc3M9ImRldGFpbC1zZWN0aW9uIiB2LWlmPSJkZXRhaWwuc3RhdGlzdGljcyI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tdGl0bGUiPgogICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRhdGEtYW5hbHlzaXMiPjwvaT4KICAgICAgICAgICAgICDnu5/orqHkv6Hmga8KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTAiPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIiB2LWlmPSJkZXRhaWwuc3RhdGlzdGljcy5hdmdWYWx1ZSAhPT0gdW5kZWZpbmVkIj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtaXRlbSI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtbGFiZWwiPuW5s+Wdh+WAvDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LXZhbHVlIj57eyBmb3JtYXROdW1iZXIoZGV0YWlsLnN0YXRpc3RpY3MuYXZnVmFsdWUpIH19PC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiIgdi1pZj0iZGV0YWlsLnN0YXRpc3RpY3MubWF4VmFsdWUgIT09IHVuZGVmaW5lZCI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWl0ZW0iPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWxhYmVsIj7mnIDlpKflgLw8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC12YWx1ZSI+e3sgZm9ybWF0TnVtYmVyKGRldGFpbC5zdGF0aXN0aWNzLm1heFZhbHVlKSB9fTwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiIHYtaWY9ImRldGFpbC5zdGF0aXN0aWNzLm1pblZhbHVlICE9PSB1bmRlZmluZWQiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1pdGVtIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1sYWJlbCI+5pyA5bCP5YC8PC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtdmFsdWUiPnt7IGZvcm1hdE51bWJlcihkZXRhaWwuc3RhdGlzdGljcy5taW5WYWx1ZSkgfX08L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIiB2LWlmPSJkZXRhaWwuc3RhdGlzdGljcy5zdGREZXYgIT09IHVuZGVmaW5lZCI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWl0ZW0iPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWxhYmVsIj7moIflh4blt648L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC12YWx1ZSI+e3sgZm9ybWF0TnVtYmVyKGRldGFpbC5zdGF0aXN0aWNzLnN0ZERldikgfX08L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICA8L2VsLXJvdz4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDwhLS0g5Y+C5pWw5piO57uGIC0tPgogICAgICAgICAgPGRpdiBjbGFzcz0iZGV0YWlsLXNlY3Rpb24iIHYtaWY9ImRldGFpbC5tYWluUGFyYW1zICYmIGRldGFpbC5tYWluUGFyYW1zLmxlbmd0aCA+IDAiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj4KICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1tZW51Ij48L2k+CiAgICAgICAgICAgICAg5Y+C5pWw5piO57uGCiAgICAgICAgICAgICAgPGVsLXRhZyBzaXplPSJtaW5pIiB0eXBlPSJpbmZvIiBzdHlsZT0ibWFyZ2luLWxlZnQ6IDVweDsiPnt7IGRldGFpbC5tYWluUGFyYW1zLmxlbmd0aCB9feS4qjwvZWwtdGFnPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0icGFyYW1zLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgPGVsLXRvb2x0aXAKICAgICAgICAgICAgICAgIHYtZm9yPSJwYXJhbSBpbiBkZXRhaWwubWFpblBhcmFtcyIKICAgICAgICAgICAgICAgIDprZXk9InBhcmFtLnBhcmFtTmFtZSIKICAgICAgICAgICAgICAgIDpjb250ZW50PSJgJHtwYXJhbS5wYXJhbU5hbWV9OiAke3BhcmFtLnBhcmFtVmFsdWUgfHwgJ04vQSd9ICR7cGFyYW0udW5pdCB8fCAnJ31gIgogICAgICAgICAgICAgICAgcGxhY2VtZW50PSJ0b3AiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLXRhZwogICAgICAgICAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgICAgICAgICA6dHlwZT0iZ2V0UGFyYW1UYWdUeXBlKHBhcmFtKSIKICAgICAgICAgICAgICAgICAgc3R5bGU9Im1hcmdpbi1yaWdodDogNXB4OyBtYXJnaW4tYm90dG9tOiAzcHg7IGN1cnNvcjogcG9pbnRlcjsiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0ic2hvd1BhcmFtRGV0YWlsKHBhcmFtKSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAge3sgcGFyYW0ucGFyYW1OYW1lIH19CiAgICAgICAgICAgICAgICAgIDxzcGFuIHYtaWY9InBhcmFtLnBhcmFtVmFsdWUiIHN0eWxlPSJtYXJnaW4tbGVmdDogM3B4OyBvcGFjaXR5OiAwLjg7Ij4KICAgICAgICAgICAgICAgICAgICAoe3sgZm9ybWF0TnVtYmVyKHBhcmFtLnBhcmFtVmFsdWUpIH19KQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2VsLXRhZz4KICAgICAgICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CgogICAgICAgICAgPCEtLSDmtYvor5XmlrnmoYjkv6Hmga8gLS0+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJkZXRhaWwtc2VjdGlvbiIgdi1pZj0iZGV0YWlsLnRlc3RQbGFuSW5mbyI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tdGl0bGUiPgogICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRvY3VtZW50Ij48L2k+CiAgICAgICAgICAgICAg5rWL6K+V5pa55qGICiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJkZXRhaWwtaXRlbSI+CiAgICAgICAgICAgICAgPGxhYmVsPuaWueahiOe8luWPt++8mjwvbGFiZWw+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImRldGFpbC12YWx1ZSI+e3sgZGV0YWlsLnRlc3RQbGFuSW5mby5wbGFuQ29kZSB9fTwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImRldGFpbC1pdGVtIiB2LWlmPSJkZXRhaWwudGVzdFBsYW5JbmZvLnRlc3RFcXVpcG1lbnQiPgogICAgICAgICAgICAgIDxsYWJlbD7mtYvor5Xorr7lpIfvvJo8L2xhYmVsPgogICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJkZXRhaWwtdmFsdWUiPnt7IGRldGFpbC50ZXN0UGxhbkluZm8udGVzdEVxdWlwbWVudCB9fTwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLWNhcmQ+CiAgICAgIDwvZWwtY29sPgogICAgPC9lbC1yb3c+CiAgPC9lbC1jYXJkPgoKICA8IS0tIOWbvuihqOWMuuWfnyAtLT4KICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgPGRpdiBzbG90PSJoZWFkZXIiIGNsYXNzPSJjbGVhcmZpeCI+CiAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgZm9udC1zaXplOiAxNHB4OyI+e3sgY2hhcnRUaXRsZSB9fTwvc3Bhbj4KICAgICAgPGRpdiBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyI+CiAgICAgICAgPGVsLXRvb2x0aXAgY29udGVudD0i5Zu+6KGo6K+05piOIiBwbGFjZW1lbnQ9InRvcCI+CiAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIGljb249ImVsLWljb24tcXVlc3Rpb24iIEBjbGljaz0ic2hvd0NoYXJ0SGVscCIgLz4KICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CgogICAgPGRpdiB2LWxvYWRpbmc9ImNoYXJ0TG9hZGluZyIgZWxlbWVudC1sb2FkaW5nLXRleHQ9Iuato+WcqOeUn+aIkOWbvuihqC4uLiI+CiAgICAgIDxkaXYKICAgICAgICByZWY9ImNoYXJ0IgogICAgICAgIDpzdHlsZT0ieyBoZWlnaHQ6IGNoYXJ0SGVpZ2h0ICsgJ3B4Jywgd2lkdGg6ICcxMDAlJyB9IgogICAgICAgIHYtc2hvdz0iIXNob3dEYXRhVGFibGUiCiAgICAgID48L2Rpdj4KCiAgICAgIDwhLS0g5pWw5o2u6KGo5qC8IC0tPgogICAgICA8ZWwtdGFibGUKICAgICAgICB2LXNob3c9InNob3dEYXRhVGFibGUiCiAgICAgICAgOmRhdGE9ImNoYXJ0RGF0YSIKICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgOm1heC1oZWlnaHQ9ImNoYXJ0SGVpZ2h0IgogICAgICAgIGNsYXNzPSJ0cmVuZC1kYXRhLXRhYmxlIgogICAgICA+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiB0eXBlPSJpbmRleCIgbGFiZWw9IuW6j+WPtyIgd2lkdGg9IjYwIiBhbGlnbj0iY2VudGVyIiAvPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgIHYtZm9yPSJjb2x1bW4gaW4gdGFibGVDb2x1bW5zIgogICAgICAgICAgOmtleT0iY29sdW1uLnByb3AiCiAgICAgICAgICA6cHJvcD0iY29sdW1uLnByb3AiCiAgICAgICAgICA6bGFiZWw9ImNvbHVtbi5sYWJlbCIKICAgICAgICAgIDp3aWR0aD0iY29sdW1uLndpZHRoIgogICAgICAgICAgc2hvdy1vdmVyZmxvdy10b29sdGlwCiAgICAgICAgPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgPGRpdiB2LWlmPSJjb2x1bW4ucHJvcCA9PT0gJ3BhcmFtRGV0YWlscyciPgogICAgICAgICAgICAgIDxkaXYgdi1pZj0ic2NvcGUucm93LnBhcmFtRGV0YWlscyAmJiBzY29wZS5yb3cucGFyYW1EZXRhaWxzLmxlbmd0aCA+IDAiIGNsYXNzPSJwYXJhbS1kZXRhaWxzLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICAgIHYtZm9yPSIocGFyYW0sIGluZGV4KSBpbiBzY29wZS5yb3cucGFyYW1EZXRhaWxzIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgY2xhc3M9InBhcmFtLWRldGFpbC1pdGVtIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0icGFyYW0tbmFtZSI+e3sgcGFyYW0ucGFyYW1OYW1lIH19Ojwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InBhcmFtLXZhbHVlIj57eyBmb3JtYXRQYXJhbVZhbHVlKHBhcmFtLnBhcmFtVmFsdWUpIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0icGFyYW0tdW5pdCIgdi1pZj0icGFyYW0udW5pdCI+e3sgcGFyYW0udW5pdCB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxzcGFuIHYtZWxzZSBjbGFzcz0iZW1wdHktZGF0YSI+5pqC5peg5Y+C5pWwPC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiB2LWVsc2UtaWY9ImNvbHVtbi5wcm9wID09PSAncGFyYW1OdW1iZXInIj4KICAgICAgICAgICAgICA8ZWwtdGFnIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiB2LWlmPSJzY29wZS5yb3cucGFyYW1OdW1iZXIiPgogICAgICAgICAgICAgICAge3sgc2NvcGUucm93LnBhcmFtTnVtYmVyIH19CiAgICAgICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgICAgICAgPHNwYW4gdi1lbHNlIGNsYXNzPSJlbXB0eS1kYXRhIj4tPC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiB2LWVsc2UtaWY9ImNvbHVtbi5wcm9wID09PSAnbWF0ZXJpYWxOYW1lJyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibWF0ZXJpYWwtaW5mbyI+CiAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1ib3giPjwvaT4KICAgICAgICAgICAgICAgIDxzcGFuPnt7IHNjb3BlLnJvdy5tYXRlcmlhbE5hbWUgfHwgJy0nIH19PC9zcGFuPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiB2LWVsc2U+CiAgICAgICAgICAgICAge3sgc2NvcGUucm93W2NvbHVtbi5wcm9wXSB8fCAnLScgfX0KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICA8L2VsLXRhYmxlPgogICAgPC9kaXY+CiAgPC9lbC1jYXJkPgoKICA8IS0tIOe7n+iuoeS/oeaBr+WNoeeJhyAtLT4KICA8ZWwtcm93IDpndXR0ZXI9IjIwIiBzdHlsZT0ibWFyZ2luLXRvcDogMjBweDsiIHYtaWY9InN0YXRpc3RpY3NEYXRhLmxlbmd0aCA+IDAiPgogICAgPGVsLWNvbCA6c3Bhbj0iNiIgdi1mb3I9IihzdGF0LCBpbmRleCkgaW4gc3RhdGlzdGljc0RhdGEiIDprZXk9ImluZGV4Ij4KICAgICAgPGVsLWNhcmQgY2xhc3M9InN0YXRpc3RpY3MtY2FyZCI+CiAgICAgICAgPGRpdiBjbGFzcz0ic3RhdGlzdGljcy1jb250ZW50Ij4KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXRpc3RpY3MtdGl0bGUiPnt7IHN0YXQudGl0bGUgfX08L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXRpc3RpY3MtdmFsdWUiPnt7IHN0YXQudmFsdWUgfX08L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXRpc3RpY3MtZGVzYyI+e3sgc3RhdC5kZXNjcmlwdGlvbiB9fTwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWNhcmQ+CiAgICA8L2VsLWNvbD4KICA8L2VsLXJvdz4KCiAgPCEtLSDlm77ooajor7TmmI7lr7nor53moYYgLS0+CiAgPGVsLWRpYWxvZyB0aXRsZT0i8J+TiiDlm77ooajor7TmmI4iIDp2aXNpYmxlLnN5bmM9ImhlbHBEaWFsb2dWaXNpYmxlIiB3aWR0aD0iNzAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGRpdiBjbGFzcz0iY2hhcnQtaGVscC1jb250ZW50Ij4KICAgICAgPGg0PvCfjq8g5Zu+6KGo57G75Z6L6K+05piO77yaPC9oND4KICAgICAgPHVsPgogICAgICAgIDxsaT48c3Ryb25nPvCfk4gg5oqY57q/5Zu+77yaPC9zdHJvbmc+6YCC55So5LqO5bGV56S65pWw5o2u6ZqP5pe26Ze05oiW5YW25LuW6L+e57ut5Y+Y6YeP55qE5Y+Y5YyW6LaL5Yq/77yM5riF5pmw5pi+56S65pWw5o2u6LWw5ZCRPC9saT4KICAgICAgICA8bGk+PHN0cm9uZz7wn5OKIOafseeKtuWbvu+8mjwvc3Ryb25nPumAgueUqOS6juavlOi+g+S4jeWQjOexu+WIq+S5i+mXtOeahOaVsOWAvOWkp+Wwj++8jOebtOinguWvueavlOW3ruW8gjwvbGk+CiAgICAgICAgPGxpPjxzdHJvbmc+8J+UtSDmlaPngrnlm77vvJo8L3N0cm9uZz7pgILnlKjkuo7lsZXnpLrkuKTkuKrlj5jph4/kuYvpl7TnmoTnm7jlhbPlhbPns7vvvIzlj5HnjrDmlbDmja7op4Tlvos8L2xpPgogICAgICAgIDxsaT48c3Ryb25nPvCflbjvuI8g6Zu36L6+5Zu+77yaPC9zdHJvbmc+6YCC55So5LqO5aSa57u05bqm5pWw5o2u55qE57u85ZCI5a+55q+U77yM5YWo6Z2i6K+E5Lyw5oCn6IO9PC9saT4KICAgICAgICA8bGk+PHN0cm9uZz7wn4yh77iPIOeDreWKm+Wbvu+8mjwvc3Ryb25nPumAgueUqOS6juWxleekuuaVsOaNrueahOWIhuW4g+WvhuW6puWSjOebuOWFs+aAp++8jOivhuWIq+eDreeCueWMuuWfnzwvbGk+CiAgICAgIDwvdWw+CgogICAgICA8aDQ+8J+UjSDlr7nmr5Tnu7Tluqbor7TmmI7vvJo8L2g0PgogICAgICA8dWw+CiAgICAgICAgPGxpPjxzdHJvbmc+8J+TiiDmnZDmlpnmgKfog73lr7nmr5TvvJo8L3N0cm9uZz7mr5TovoPkuI3lkIzmnZDmlpnnmoTmgKfog73ooajnjrDvvIzor4bliKvmnIDkvJjmnZDmlpk8L2xpPgogICAgICAgIDxsaT48c3Ryb25nPvCfj60g5L6b5bqU5ZWG5pWw5o2u5a+55q+U77yaPC9zdHJvbmc+5q+U6L6D5LiN5ZCM5L6b5bqU5ZWG5p2Q5paZ55qE6LSo6YeP5beu5byC77yM6K+E5Lyw5L6b5bqU5ZWG5Y+v6Z2g5oCnPC9saT4KICAgICAgICA8bGk+PHN0cm9uZz7wn5SiIOWPguaVsOe8luWPt+WvueavlO+8mjwvc3Ryb25nPuavlOi+g+S4jeWQjOWPguaVsOe8luWPt+S4i+eahOa1i+ivleWAvOi2i+WKv++8jOWIhuaekOWPguaVsOW9seWTjTwvbGk+CiAgICAgICAgPGxpPjxzdHJvbmc+4pqZ77iPIOW3peiJuuexu+Wei+WvueavlO+8mjwvc3Ryb25nPuavlOi+g+S4jeWQjOW3peiJuuexu+Wei+eahOaViOaenO+8jOS8mOWMluW3peiJuua1geeoizwvbGk+CiAgICAgICAgPGxpPjxzdHJvbmc+8J+TiCDml7bpl7Totovlir/liIbmnpDvvJo8L3N0cm9uZz7lsZXnpLrmtYvor5XmlbDmja7pmo/ml7bpl7TnmoTlj5jljJbop4TlvovvvIzpooTmtYvlj5HlsZXotovlir88L2xpPgogICAgICAgIDxsaT48c3Ryb25nPuKalu+4jyDkvpvlupTllYZ2c+a1i+ivleWAvO+8mjwvc3Ryb25nPuWvueavlOS+m+W6lOWVhuaPkOS+m+aVsOaNruS4juWunumZhea1i+ivlee7k+aenOeahOW3ruW8gjwvbGk+CiAgICAgIDwvdWw+CgogICAgICA8aDQ+8J+SoSDkvb/nlKjmioDlt6fvvJo8L2g0PgogICAgICA8dWw+CiAgICAgICAgPGxpPuWwhum8oOagh+aCrOWBnOWcqOWbvuihqOaVsOaNrueCueS4iuWPr+afpeeci+ivpue7huS/oeaBr+WSjOWPguaVsOaYjue7hjwvbGk+CiAgICAgICAgPGxpPueCueWHu+WPguaVsOagh+etvuWPr+afpeeci+ivpeWPguaVsOeahOivpue7huS/oeaBrzwvbGk+CiAgICAgICAgPGxpPuS9v+eUqCLmmL7npLrmlbDmja7ooagi5Yqf6IO95Y+v5p+l55yL5Y6f5aeL5pWw5o2uPC9saT4KICAgICAgICA8bGk+6YCJ5oup5ZCI6YCC55qE5Zu+6KGo57G75Z6L6IO95pu05aW95Zyw5bGV56S65pWw5o2u54m55b6BPC9saT4KICAgICAgICA8bGk+5aSa6YCJ5a+55q+U6aG555uu5Y+v6L+b6KGM5qiq5ZCR5q+U6L6D5YiG5p6QPC9saT4KICAgICAgPC91bD4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOWPguaVsOivpuaDheWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i8J+TiyDlj4LmlbDor6bnu4bkv6Hmga8iCiAgICA6dmlzaWJsZS5zeW5jPSJwYXJhbURldGFpbERpYWxvZ1Zpc2libGUiCiAgICB3aWR0aD0iNTAwcHgiCiAgICBhcHBlbmQtdG8tYm9keQogICAgdi1pZj0iY3VycmVudFBhcmFtRGV0YWlsIgogID4KICAgIDxkaXYgY2xhc3M9InBhcmFtLWRldGFpbC1jb250ZW50Ij4KICAgICAgPGVsLWRlc2NyaXB0aW9ucyA6Y29sdW1uPSIyIiBib3JkZXIgc2l6ZT0ic21hbGwiPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5Y+C5pWw5ZCN56ewIj4KICAgICAgICAgIDxlbC10YWcgdHlwZT0icHJpbWFyeSI+e3sgY3VycmVudFBhcmFtRGV0YWlsLnBhcmFtTmFtZSB9fTwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlj4LmlbDmlbDlgLwiPgogICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBjb2xvcjogIzY3QzIzQTsiPgogICAgICAgICAgICB7eyBmb3JtYXROdW1iZXIoY3VycmVudFBhcmFtRGV0YWlsLnBhcmFtVmFsdWUpIH19CiAgICAgICAgICA8L3NwYW4+CiAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuWPguaVsOWNleS9jSIgdi1pZj0iY3VycmVudFBhcmFtRGV0YWlsLnVuaXQiPgogICAgICAgICAge3sgY3VycmVudFBhcmFtRGV0YWlsLnVuaXQgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5pWw5o2u57G75Z6LIj4KICAgICAgICAgIHt7IHR5cGVvZiBjdXJyZW50UGFyYW1EZXRhaWwucGFyYW1WYWx1ZSA9PT0gJ251bWJlcicgPyAn5pWw5YC85Z6LJyA6ICfmlofmnKzlnosnIH19CiAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuWIm+W7uuaXtumXtCIgdi1pZj0iY3VycmVudFBhcmFtRGV0YWlsLmNyZWF0ZVRpbWUiPgogICAgICAgICAge3sgY3VycmVudFBhcmFtRGV0YWlsLmNyZWF0ZVRpbWUgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5pu05paw5pe26Ze0IiB2LWlmPSJjdXJyZW50UGFyYW1EZXRhaWwudXBkYXRlVGltZSI+CiAgICAgICAgICB7eyBjdXJyZW50UGFyYW1EZXRhaWwudXBkYXRlVGltZSB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDwvZWwtZGVzY3JpcHRpb25zPgoKICAgICAgPGRpdiB2LWlmPSJjdXJyZW50UGFyYW1EZXRhaWwucmVtYXJrIiBzdHlsZT0ibWFyZ2luLXRvcDogMTVweDsiPgogICAgICAgIDxoNCBzdHlsZT0iY29sb3I6ICM0MDlFRkY7IG1hcmdpbi1ib3R0b206IDhweDsiPvCfk50g5aSH5rOo5L+h5oGv77yaPC9oND4KICAgICAgICA8cCBzdHlsZT0iYmFja2dyb3VuZDogI2Y1ZjdmYTsgcGFkZGluZzogMTBweDsgYm9yZGVyLXJhZGl1czogNHB4OyBtYXJnaW46IDA7Ij4KICAgICAgICAgIHt7IGN1cnJlbnRQYXJhbURldGFpbC5yZW1hcmsgfX0KICAgICAgICA8L3A+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}