{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue", "mtime": 1754285271290}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0VGVzdFBsYW5Hcm91cCwgZ2V0VGVzdFBsYW5Hcm91cCwgZGVsVGVzdFBsYW5Hcm91cCwgYWRkVGVzdFBsYW5Hcm91cCwgdXBkYXRlVGVzdFBsYW5Hcm91cCwNCiAgZXhwb3J0VGVzdFBsYW5Hcm91cCwgZ2V0VGVzdFBsYW5Hcm91cE9wdGlvbnMNCn0gZnJvbSAiQC9hcGkvbWF0ZXJpYWwvdGVzdFBsYW5Hcm91cCI7DQppbXBvcnQgew0KICBsaXN0VGVzdFBhcmFtSXRlbSwgZ2V0VGVzdFBhcmFtSXRlbSwgZGVsVGVzdFBhcmFtSXRlbSwgYWRkVGVzdFBhcmFtSXRlbSwgdXBkYXRlVGVzdFBhcmFtSXRlbSwNCiAgZXhwb3J0VGVzdFBhcmFtSXRlbSwgZ2V0VGVzdFBhcmFtSXRlbU9wdGlvbnMsIGxpc3RCeVBsYW5Hcm91cElkDQp9IGZyb20gIkAvYXBpL21hdGVyaWFsL3Rlc3RQYXJhbUl0ZW0iOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJUZXN0UGxhbiIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOa1i+ivleaWueahiOe7hOebuOWFsw0KICAgICAgcGxhbkdyb3VwTG9hZGluZzogZmFsc2UsDQogICAgICBwbGFuR3JvdXBMaXN0OiBbXSwNCiAgICAgIHBsYW5Hcm91cFRvdGFsOiAwLA0KICAgICAgcGxhbkdyb3VwT3BlbjogZmFsc2UsDQogICAgICBwbGFuR3JvdXBUaXRsZTogIiIsDQogICAgICBwbGFuR3JvdXBGb3JtOiB7fSwNCiAgICAgIHBsYW5Hcm91cEZpbGVMaXN0OiBbXSwNCiAgICAgIHBsYW5Hcm91cElkczogW10sDQogICAgICBwbGFuR3JvdXBTaW5nbGU6IHRydWUsDQogICAgICBwbGFuR3JvdXBNdWx0aXBsZTogdHJ1ZSwNCiAgICAgIHBsYW5Hcm91cFF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcGxhbkNvZGU6IG51bGwsDQogICAgICAgIHBlcmZvcm1hbmNlVHlwZTogbnVsbCwNCiAgICAgICAgdGVzdEVxdWlwbWVudDogbnVsbA0KICAgICAgfSwNCiAgICAgIHBsYW5Hcm91cFJ1bGVzOiB7DQogICAgICAgIHBsYW5Db2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaWueahiOe8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHBlcmZvcm1hbmNlVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmgKfog73nsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyDmtYvor5Xlj4LmlbDmmI7nu4bnm7jlhbMNCiAgICAgIHRlc3RQYXJhbUxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGVzdFBhcmFtTGlzdDogW10sDQogICAgICB0ZXN0UGFyYW1Ub3RhbDogMCwNCiAgICAgIHRlc3RQYXJhbU9wZW46IGZhbHNlLA0KICAgICAgdGVzdFBhcmFtVGl0bGU6ICIiLA0KICAgICAgdGVzdFBhcmFtRm9ybToge30sDQogICAgICB0ZXN0UGFyYW1GaWxlTGlzdDogW10sDQogICAgICB0ZXN0UGFyYW1JZHM6IFtdLA0KICAgICAgdGVzdFBhcmFtU2luZ2xlOiB0cnVlLA0KICAgICAgdGVzdFBhcmFtTXVsdGlwbGU6IHRydWUsDQogICAgICB0ZXN0UGFyYW1RdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHBsYW5Hcm91cElkOiBudWxsLA0KICAgICAgICBwYXJhbU5hbWU6IG51bGwsDQogICAgICAgIHVuaXQ6IG51bGwNCiAgICAgIH0sDQogICAgICB0ZXN0UGFyYW1SdWxlczogew0KICAgICAgICBwYXJhbU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y+C5pWw5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCg0KICAgICAgLy8g5b2T5YmN6YCJ5Lit55qE5rWL6K+V5pa55qGI57uEDQogICAgICBjdXJyZW50UGxhbkdyb3VwOiBudWxsLA0KDQogICAgICAvLyDpmYTku7bmn6XnnIsNCiAgICAgIGF0dGFjaG1lbnREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGF0dGFjaG1lbnRMaXN0OiBbXSwNCg0KICAgICAgLy8g5pCc57Si5bu66K6u5pWw5o2uDQogICAgICBwbGFuQ29kZVN1Z2dlc3Rpb25zOiBbXSwNCiAgICAgIHBlcmZvcm1hbmNlVHlwZVN1Z2dlc3Rpb25zOiBbXSwNCiAgICAgIHRlc3RFcXVpcG1lbnRTdWdnZXN0aW9uczogW10sDQogICAgICBwYXJhbU5hbWVTdWdnZXN0aW9uczogW10sDQogICAgICB1bml0U3VnZ2VzdGlvbnM6IFtdLA0KDQogICAgICAvLyDkuIrkvKDnm7jlhbMNCiAgICAgIHVwbG9hZEZpbGVVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2NvbW1vbi91cGxvYWQiLA0KICAgICAgdXBsb2FkSGVhZGVyczogew0KICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFBsYW5Hcm91cExpc3QoKTsNCiAgICB0aGlzLmxvYWRTdWdnZXN0aW9ucygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOiOt+WPluaAp+iDveexu+Wei+agh+etvuminOiJsiAqLw0KICAgIGdldFBlcmZvcm1hbmNlVHlwZVRhZyh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAn5Yqb5a2m5oCn6IO9JzogJ3N1Y2Nlc3MnLA0KICAgICAgICAn55S15a2m5oCn6IO9JzogJ3ByaW1hcnknLA0KICAgICAgICAn54Ot5a2m5oCn6IO9JzogJ3dhcm5pbmcnLA0KICAgICAgICAn5YWJ5a2m5oCn6IO9JzogJ2luZm8nLA0KICAgICAgICAn5YyW5a2m5oCn6IO9JzogJ2RhbmdlcicsDQogICAgICAgICfniannkIbmgKfog70nOiAnJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICcnOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5rWL6K+V5pa55qGI57uE5YiX6KGoICovDQogICAgZ2V0UGxhbkdyb3VwTGlzdCgpIHsNCiAgICAgIHRoaXMucGxhbkdyb3VwTG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0VGVzdFBsYW5Hcm91cCh0aGlzLnBsYW5Hcm91cFF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5wbGFuR3JvdXBMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy5wbGFuR3JvdXBUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5rWL6K+V5Y+C5pWw5piO57uG5YiX6KGoICovDQogICAgZ2V0VGVzdFBhcmFtTGlzdCgpIHsNCiAgICAgIGlmICghdGhpcy5jdXJyZW50UGxhbkdyb3VwKSByZXR1cm47DQoNCiAgICAgIHRoaXMudGVzdFBhcmFtTG9hZGluZyA9IHRydWU7DQogICAgICB0aGlzLnRlc3RQYXJhbVF1ZXJ5UGFyYW1zLnBsYW5Hcm91cElkID0gdGhpcy5jdXJyZW50UGxhbkdyb3VwLnBsYW5Hcm91cElkOw0KICAgICAgbGlzdFRlc3RQYXJhbUl0ZW0odGhpcy50ZXN0UGFyYW1RdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudGVzdFBhcmFtTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudGVzdFBhcmFtVG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy50ZXN0UGFyYW1Mb2FkaW5nID0gZmFsc2U7DQoNCiAgICAgICAgLy8g5pu05paw5Y+C5pWw562b6YCJ6YCJ6aG5DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnVwZGF0ZVBhcmFtRmlsdGVyT3B0aW9ucygpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L295pCc57Si5bu66K6u5pWw5o2uICovDQogICAgbG9hZFN1Z2dlc3Rpb25zKCkgew0KICAgICAgLy8g6I635Y+W5pa55qGI57yW5Y+35bu66K6uDQogICAgICBnZXRUZXN0UGxhbkdyb3VwT3B0aW9ucyh7IHR5cGU6ICdwbGFuQ29kZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLnBsYW5Db2RlU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOiOt+WPluaAp+iDveexu+Wei+W7uuiurg0KICAgICAgZ2V0VGVzdFBsYW5Hcm91cE9wdGlvbnMoeyB0eXBlOiAncGVyZm9ybWFuY2VUeXBlJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIHRoaXMucGVyZm9ybWFuY2VUeXBlU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOiOt+WPlua1i+ivleiuvuWkh+W7uuiurg0KICAgICAgZ2V0VGVzdFBsYW5Hcm91cE9wdGlvbnMoeyB0eXBlOiAndGVzdEVxdWlwbWVudCcgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLnRlc3RFcXVpcG1lbnRTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgLy8g6I635Y+W5Y+C5pWw5ZCN56ew5bu66K6uDQogICAgICBnZXRUZXN0UGFyYW1JdGVtT3B0aW9ucyh7IHR5cGU6ICdwYXJhbU5hbWUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5wYXJhbU5hbWVTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgLy8g6I635Y+W5Y+C5pWw5Y2V5L2N5bu66K6uDQogICAgICBnZXRUZXN0UGFyYW1JdGVtT3B0aW9ucyh7IHR5cGU6ICd1bml0JyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIHRoaXMudW5pdFN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5pCc57Si5bu66K6u5pa55rOVICovDQogICAgcXVlcnlQbGFuQ29kZVN1Z2dlc3Rpb25zKHF1ZXJ5U3RyaW5nLCBjYikgew0KICAgICAgbGV0IHN1Z2dlc3Rpb25zID0gdGhpcy5wbGFuQ29kZVN1Z2dlc3Rpb25zOw0KICAgICAgaWYgKHF1ZXJ5U3RyaW5nKSB7DQogICAgICAgIHN1Z2dlc3Rpb25zID0gdGhpcy5wbGFuQ29kZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgcXVlcnlQZXJmb3JtYW5jZVR5cGVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGVyZm9ybWFuY2VUeXBlU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnBlcmZvcm1hbmNlVHlwZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgcXVlcnlUZXN0RXF1aXBtZW50U3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLnRlc3RFcXVpcG1lbnRTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMudGVzdEVxdWlwbWVudFN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgcXVlcnlQYXJhbU5hbWVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGFyYW1OYW1lU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnBhcmFtTmFtZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgcXVlcnlVbml0U3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLnVuaXRTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMudW5pdFN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgLyoqIOeEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZVBsYW5Db2RlRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UGxhbkdyb3VwT3B0aW9ucyh7IHR5cGU6ICdwbGFuQ29kZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLnBsYW5Db2RlU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZVBlcmZvcm1hbmNlVHlwZUZvY3VzKCkgew0KICAgICAgZ2V0VGVzdFBsYW5Hcm91cE9wdGlvbnMoeyB0eXBlOiAncGVyZm9ybWFuY2VUeXBlJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIHRoaXMucGVyZm9ybWFuY2VUeXBlU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZVRlc3RFcXVpcG1lbnRGb2N1cygpIHsNCiAgICAgIGdldFRlc3RQbGFuR3JvdXBPcHRpb25zKHsgdHlwZTogJ3Rlc3RFcXVpcG1lbnQnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy50ZXN0RXF1aXBtZW50U3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZVBhcmFtTmFtZUZvY3VzKCkgew0KICAgICAgLy8g5Y+q6I635Y+W5b2T5YmN6YCJ5Lit5rWL6K+V5pa55qGI57uE5LiL55qE5Y+C5pWw5ZCN56ew6YCJ6aG5DQogICAgICBpZiAodGhpcy5jdXJyZW50UGxhbkdyb3VwICYmIHRoaXMuY3VycmVudFBsYW5Hcm91cC5wbGFuR3JvdXBJZCkgew0KICAgICAgICAvLyDku47lvZPliY3mmL7npLrnmoTmtYvor5Xlj4LmlbDliJfooajkuK3mj5Dlj5blj4LmlbDlkI3np7ANCiAgICAgICAgY29uc3QgcGFyYW1OYW1lcyA9IFsuLi5uZXcgU2V0KHRoaXMudGVzdFBhcmFtTGlzdC5tYXAoaXRlbSA9PiBpdGVtLnBhcmFtTmFtZSkuZmlsdGVyKG5hbWUgPT4gbmFtZSkpXTsNCiAgICAgICAgdGhpcy5wYXJhbU5hbWVTdWdnZXN0aW9ucyA9IHBhcmFtTmFtZXMubWFwKG5hbWUgPT4gKHsgdmFsdWU6IG5hbWUgfSkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6YCJ5Lit5pa55qGI57uE77yM6I635Y+W5omA5pyJ5Y+C5pWw5ZCN56ewDQogICAgICAgIGdldFRlc3RQYXJhbUl0ZW1PcHRpb25zKHsgdHlwZTogJ3BhcmFtTmFtZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgICAgdGhpcy5wYXJhbU5hbWVTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVVuaXRGb2N1cygpIHsNCiAgICAgIC8vIOWPquiOt+WPluW9k+WJjemAieS4rea1i+ivleaWueahiOe7hOS4i+eahOWPguaVsOWNleS9jemAiemhuQ0KICAgICAgaWYgKHRoaXMuY3VycmVudFBsYW5Hcm91cCAmJiB0aGlzLmN1cnJlbnRQbGFuR3JvdXAucGxhbkdyb3VwSWQpIHsNCiAgICAgICAgLy8g5LuO5b2T5YmN5pi+56S655qE5rWL6K+V5Y+C5pWw5YiX6KGo5Lit5o+Q5Y+W5Y+C5pWw5Y2V5L2NDQogICAgICAgIGNvbnN0IHVuaXRzID0gWy4uLm5ldyBTZXQodGhpcy50ZXN0UGFyYW1MaXN0Lm1hcChpdGVtID0+IGl0ZW0udW5pdCkuZmlsdGVyKHVuaXQgPT4gdW5pdCkpXTsNCiAgICAgICAgdGhpcy51bml0U3VnZ2VzdGlvbnMgPSB1bml0cy5tYXAodW5pdCA9PiAoeyB2YWx1ZTogdW5pdCB9KSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInpgInkuK3mlrnmoYjnu4TvvIzojrflj5bmiYDmnInlj4LmlbDljZXkvY0NCiAgICAgICAgZ2V0VGVzdFBhcmFtSXRlbU9wdGlvbnMoeyB0eXBlOiAndW5pdCcgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgICAgdGhpcy51bml0U3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5rWL6K+V5pa55qGI57uE55u45YWz5pa55rOVICovDQogICAgaGFuZGxlUGxhbkdyb3VwU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5wbGFuR3JvdXBJZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5wbGFuR3JvdXBJZCk7DQogICAgICB0aGlzLnBsYW5Hcm91cFNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLnBsYW5Hcm91cE11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCg0KICAgIGhhbmRsZVBsYW5Hcm91cFJvd0NsaWNrKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50UGxhbkdyb3VwID0gcm93Ow0KICAgICAgdGhpcy4kcmVmcy5wbGFuR3JvdXBUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93KTsNCg0KICAgICAgLy8g5riF56m65rWL6K+V5Y+C5pWw562b6YCJ5p2h5Lu2DQogICAgICB0aGlzLnRlc3RQYXJhbVF1ZXJ5UGFyYW1zLnBhcmFtTmFtZSA9IG51bGw7DQogICAgICB0aGlzLnRlc3RQYXJhbVF1ZXJ5UGFyYW1zLnVuaXQgPSBudWxsOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInRlc3RQYXJhbVF1ZXJ5Rm9ybSIpOw0KDQogICAgICAvLyDliqDovb3mtYvor5Xlj4LmlbDliJfooagNCiAgICAgIHRoaXMuZ2V0VGVzdFBhcmFtTGlzdCgpOw0KDQogICAgICAvLyDlu7bov5/mm7TmlrDnrZvpgInpgInpobnvvIznoa7kv510ZXN0UGFyYW1MaXN05bey5Yqg6L29DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMudXBkYXRlUGFyYW1GaWx0ZXJPcHRpb25zKCk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0UGxhbkdyb3VwUm93Q2xhc3NOYW1lKHtyb3csIHJvd0luZGV4fSkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFBsYW5Hcm91cCAmJiByb3cucGxhbkdyb3VwSWQgPT09IHRoaXMuY3VycmVudFBsYW5Hcm91cC5wbGFuR3JvdXBJZCkgew0KICAgICAgICByZXR1cm4gJ2N1cnJlbnQtcm93JzsNCiAgICAgIH0NCiAgICAgIHJldHVybiAnJzsNCiAgICB9LA0KDQogICAgaGFuZGxlUGxhbkdyb3VwUXVlcnkoKSB7DQogICAgICB0aGlzLnBsYW5Hcm91cFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRQbGFuR3JvdXBMaXN0KCk7DQogICAgfSwNCg0KICAgIHJlc2V0UGxhbkdyb3VwUXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicGxhbkdyb3VwUXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVBsYW5Hcm91cFF1ZXJ5KCk7DQogICAgfSwNCg0KICAgIGhhbmRsZUFkZFBsYW5Hcm91cCgpIHsNCiAgICAgIHRoaXMucmVzZXRQbGFuR3JvdXBGb3JtKCk7DQogICAgICB0aGlzLnBsYW5Hcm91cE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5wbGFuR3JvdXBUaXRsZSA9ICLmt7vliqDmtYvor5XmlrnmoYjnu4QiOw0KICAgIH0sDQoNCiAgICBoYW5kbGVFZGl0UGxhbkdyb3VwKHJvdykgew0KICAgICAgdGhpcy5yZXNldFBsYW5Hcm91cEZvcm0oKTsNCiAgICAgIGNvbnN0IHBsYW5Hcm91cElkID0gcm93LnBsYW5Hcm91cElkIHx8IHRoaXMucGxhbkdyb3VwSWRzOw0KICAgICAgZ2V0VGVzdFBsYW5Hcm91cChwbGFuR3JvdXBJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGxhbkdyb3VwRm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMucGFyc2VQbGFuR3JvdXBBdHRhY2htZW50cygpOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cE9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cFRpdGxlID0gIuS/ruaUuea1i+ivleaWueahiOe7hCI7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlQ29weVBsYW5Hcm91cChyb3cpIHsNCiAgICAgIHRoaXMucmVzZXRQbGFuR3JvdXBGb3JtKCk7DQogICAgICBjb25zdCBwbGFuR3JvdXBJZCA9IHJvdy5wbGFuR3JvdXBJZDsNCiAgICAgIGdldFRlc3RQbGFuR3JvdXAocGxhbkdyb3VwSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBsYW5Hcm91cEZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cEZvcm0ucGxhbkdyb3VwSWQgPSBudWxsOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cEZvcm0ucGxhbkNvZGUgPSB0aGlzLnBsYW5Hcm91cEZvcm0ucGxhbkNvZGUgKyAiX2NvcHkiOw0KICAgICAgICB0aGlzLnBhcnNlUGxhbkdyb3VwQXR0YWNobWVudHMoKTsNCiAgICAgICAgdGhpcy5wbGFuR3JvdXBPcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5wbGFuR3JvdXBUaXRsZSA9ICLlpI3liLbmtYvor5XmlrnmoYjnu4QiOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZURlbGV0ZVBsYW5Hcm91cChyb3cpIHsNCiAgICAgIGNvbnN0IHBsYW5Hcm91cElkcyA9IHJvdy5wbGFuR3JvdXBJZCB8fCB0aGlzLnBsYW5Hcm91cElkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOa1i+ivleaWueahiOe7hOe8luWPt+S4uiInICsgcm93LnBsYW5Db2RlICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVGVzdFBsYW5Hcm91cChwbGFuR3JvdXBJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0UGxhbkdyb3VwTGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFBsYW5Hcm91cCAmJiB0aGlzLmN1cnJlbnRQbGFuR3JvdXAucGxhbkdyb3VwSWQgPT09IHJvdy5wbGFuR3JvdXBJZCkgew0KICAgICAgICAgIHRoaXMuY3VycmVudFBsYW5Hcm91cCA9IG51bGw7DQogICAgICAgICAgdGhpcy50ZXN0UGFyYW1MaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlQmF0Y2hEZWxldGVQbGFuR3JvdXAoKSB7DQogICAgICBjb25zdCBwbGFuR3JvdXBJZHMgPSB0aGlzLnBsYW5Hcm91cElkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOmAieS4reeahCcgKyBwbGFuR3JvdXBJZHMubGVuZ3RoICsgJ+adoeaVsOaNru+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxUZXN0UGxhbkdyb3VwKHBsYW5Hcm91cElkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRQbGFuR3JvdXBMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB0aGlzLmN1cnJlbnRQbGFuR3JvdXAgPSBudWxsOw0KICAgICAgICB0aGlzLnRlc3RQYXJhbUxpc3QgPSBbXTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlRXhwb3J0UGxhbkdyb3VwKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWF0ZXJpYWwvdGVzdFBsYW5Hcm91cC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucGxhbkdyb3VwUXVlcnlQYXJhbXMNCiAgICAgIH0sIGB0ZXN0X3BsYW5fZ3JvdXBfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDlj4LmlbDnrZvpgInpgInpobkgKi8NCiAgICB1cGRhdGVQYXJhbUZpbHRlck9wdGlvbnMoKSB7DQogICAgICBpZiAodGhpcy5jdXJyZW50UGxhbkdyb3VwICYmIHRoaXMudGVzdFBhcmFtTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIC8vIOS7juW9k+WJjea1i+ivleWPguaVsOWIl+ihqOS4reaPkOWPluWUr+S4gOeahOWPguaVsOWQjeensOWSjOWNleS9jQ0KICAgICAgICBjb25zdCBwYXJhbU5hbWVzID0gWy4uLm5ldyBTZXQodGhpcy50ZXN0UGFyYW1MaXN0Lm1hcChpdGVtID0+IGl0ZW0ucGFyYW1OYW1lKS5maWx0ZXIobmFtZSA9PiBuYW1lKSldOw0KICAgICAgICBjb25zdCB1bml0cyA9IFsuLi5uZXcgU2V0KHRoaXMudGVzdFBhcmFtTGlzdC5tYXAoaXRlbSA9PiBpdGVtLnVuaXQpLmZpbHRlcih1bml0ID0+IHVuaXQpKV07DQoNCiAgICAgICAgdGhpcy5wYXJhbU5hbWVTdWdnZXN0aW9ucyA9IHBhcmFtTmFtZXMubWFwKG5hbWUgPT4gKHsgdmFsdWU6IG5hbWUgfSkpOw0KICAgICAgICB0aGlzLnVuaXRTdWdnZXN0aW9ucyA9IHVuaXRzLm1hcCh1bml0ID0+ICh7IHZhbHVlOiB1bml0IH0pKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleWPguaVsOaYjue7huebuOWFs+aWueazlSAqLw0KICAgIGhhbmRsZVRlc3RQYXJhbVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMudGVzdFBhcmFtSWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udGVzdFBhcmFtSWQpOw0KICAgICAgdGhpcy50ZXN0UGFyYW1TaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy50ZXN0UGFyYW1NdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQoNCiAgICBoYW5kbGVUZXN0UGFyYW1Sb3dDbGljayhyb3cpIHsNCiAgICAgIHRoaXMuJHJlZnMudGVzdFBhcmFtVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdyk7DQogICAgfSwNCg0KICAgIGhhbmRsZVRlc3RQYXJhbVF1ZXJ5KCkgew0KICAgICAgdGhpcy50ZXN0UGFyYW1RdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0VGVzdFBhcmFtTGlzdCgpOw0KICAgIH0sDQoNCiAgICByZXNldFRlc3RQYXJhbVF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInRlc3RQYXJhbVF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVUZXN0UGFyYW1RdWVyeSgpOw0KICAgIH0sDQoNCiAgICBoYW5kbGVBZGRUZXN0UGFyYW0oKSB7DQogICAgICB0aGlzLnJlc2V0VGVzdFBhcmFtRm9ybSgpOw0KICAgICAgdGhpcy50ZXN0UGFyYW1PcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGVzdFBhcmFtVGl0bGUgPSAi5re75Yqg5rWL6K+V5Y+C5pWw5piO57uGIjsNCiAgICB9LA0KDQogICAgaGFuZGxlRWRpdFRlc3RQYXJhbShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXRUZXN0UGFyYW1Gb3JtKCk7DQogICAgICBjb25zdCB0ZXN0UGFyYW1JZCA9IHJvdy50ZXN0UGFyYW1JZCB8fCB0aGlzLnRlc3RQYXJhbUlkczsNCiAgICAgIGdldFRlc3RQYXJhbUl0ZW0odGVzdFBhcmFtSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnRlc3RQYXJhbUZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnBhcnNlVGVzdFBhcmFtQXR0YWNobWVudHMoKTsNCiAgICAgICAgdGhpcy50ZXN0UGFyYW1PcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50ZXN0UGFyYW1UaXRsZSA9ICLkv67mlLnmtYvor5Xlj4LmlbDmmI7nu4YiOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZURlbGV0ZVRlc3RQYXJhbShyb3cpIHsNCiAgICAgIGNvbnN0IHRlc3RQYXJhbUlkcyA9IHJvdy50ZXN0UGFyYW1JZCB8fCB0aGlzLnRlc3RQYXJhbUlkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOWPguaVsOWQjeensOS4uiInICsgcm93LnBhcmFtTmFtZSArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFRlc3RQYXJhbUl0ZW0odGVzdFBhcmFtSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldFRlc3RQYXJhbUxpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCg0KICAgIGhhbmRsZUJhdGNoRGVsZXRlVGVzdFBhcmFtKCkgew0KICAgICAgY29uc3QgdGVzdFBhcmFtSWRzID0gdGhpcy50ZXN0UGFyYW1JZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoQnICsgdGVzdFBhcmFtSWRzLmxlbmd0aCArICfmnaHmlbDmja7vvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVGVzdFBhcmFtSXRlbSh0ZXN0UGFyYW1JZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0VGVzdFBhcmFtTGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlRXhwb3J0VGVzdFBhcmFtKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWF0ZXJpYWwvdGVzdFBhcmFtSXRlbS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMudGVzdFBhcmFtUXVlcnlQYXJhbXMNCiAgICAgIH0sIGB0ZXN0X3BhcmFtX2l0ZW1fJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCg0KICAgIC8qKiDooajljZXnm7jlhbPmlrnms5UgKi8NCiAgICByZXNldFBsYW5Hcm91cEZvcm0oKSB7DQogICAgICB0aGlzLnBsYW5Hcm91cEZvcm0gPSB7DQogICAgICAgIHBsYW5Hcm91cElkOiBudWxsLA0KICAgICAgICBwbGFuQ29kZTogbnVsbCwNCiAgICAgICAgcGVyZm9ybWFuY2VUeXBlOiBudWxsLA0KICAgICAgICBwZXJmb3JtYW5jZU5hbWU6IG51bGwsDQogICAgICAgIHRlc3RFcXVpcG1lbnQ6IG51bGwsDQogICAgICAgIGF0dGFjaG1lbnRzOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnBsYW5Hcm91cEZpbGVMaXN0ID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicGxhbkdyb3VwRm9ybSIpOw0KICAgIH0sDQoNCiAgICByZXNldFRlc3RQYXJhbUZvcm0oKSB7DQogICAgICB0aGlzLnRlc3RQYXJhbUZvcm0gPSB7DQogICAgICAgIHRlc3RQYXJhbUlkOiBudWxsLA0KICAgICAgICBwbGFuR3JvdXBJZDogdGhpcy5jdXJyZW50UGxhbkdyb3VwID8gdGhpcy5jdXJyZW50UGxhbkdyb3VwLnBsYW5Hcm91cElkIDogbnVsbCwNCiAgICAgICAgcGFyYW1OYW1lOiBudWxsLA0KICAgICAgICBwYXJhbVZhbHVlOiBudWxsLA0KICAgICAgICB1bml0OiBudWxsLA0KICAgICAgICBhdHRhY2htZW50czogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy50ZXN0UGFyYW1GaWxlTGlzdCA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInRlc3RQYXJhbUZvcm0iKTsNCiAgICB9LA0KDQogICAgc3VibWl0UGxhbkdyb3VwRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbInBsYW5Hcm91cEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMucGxhbkdyb3VwRm9ybS5hdHRhY2htZW50cyA9IHRoaXMucGxhbkdyb3VwRmlsZUxpc3QubWFwKGZpbGUgPT4gZmlsZS51cmwpLmpvaW4oJywnKTsNCiAgICAgICAgICBpZiAodGhpcy5wbGFuR3JvdXBGb3JtLnBsYW5Hcm91cElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVRlc3RQbGFuR3JvdXAodGhpcy5wbGFuR3JvdXBGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMucGxhbkdyb3VwT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldFBsYW5Hcm91cExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRUZXN0UGxhbkdyb3VwKHRoaXMucGxhbkdyb3VwRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLnBsYW5Hcm91cE9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRQbGFuR3JvdXBMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBzdWJtaXRUZXN0UGFyYW1Gb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1sidGVzdFBhcmFtRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy50ZXN0UGFyYW1Gb3JtLmF0dGFjaG1lbnRzID0gdGhpcy50ZXN0UGFyYW1GaWxlTGlzdC5tYXAoZmlsZSA9PiBmaWxlLnVybCkuam9pbignLCcpOw0KICAgICAgICAgIGlmICh0aGlzLnRlc3RQYXJhbUZvcm0udGVzdFBhcmFtSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlVGVzdFBhcmFtSXRlbSh0aGlzLnRlc3RQYXJhbUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy50ZXN0UGFyYW1PcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0VGVzdFBhcmFtTGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFRlc3RQYXJhbUl0ZW0odGhpcy50ZXN0UGFyYW1Gb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMudGVzdFBhcmFtT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldFRlc3RQYXJhbUxpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGNhbmNlbFBsYW5Hcm91cCgpIHsNCiAgICAgIHRoaXMucGxhbkdyb3VwT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldFBsYW5Hcm91cEZvcm0oKTsNCiAgICB9LA0KDQogICAgY2FuY2VsVGVzdFBhcmFtKCkgew0KICAgICAgdGhpcy50ZXN0UGFyYW1PcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0VGVzdFBhcmFtRm9ybSgpOw0KICAgIH0sDQoNCiAgICAvKiog6ZmE5Lu255u45YWz5pa55rOVICovDQogICAgcGFyc2VQbGFuR3JvdXBBdHRhY2htZW50cygpIHsNCiAgICAgIGlmICh0aGlzLnBsYW5Hcm91cEZvcm0uYXR0YWNobWVudHMpIHsNCiAgICAgICAgY29uc3QgdXJscyA9IHRoaXMucGxhbkdyb3VwRm9ybS5hdHRhY2htZW50cy5zcGxpdCgnLCcpOw0KICAgICAgICB0aGlzLnBsYW5Hcm91cEZpbGVMaXN0ID0gdXJscy5tYXAoKHVybCwgaW5kZXgpID0+ICh7DQogICAgICAgICAgbmFtZTogdXJsLnN1YnN0cmluZyh1cmwubGFzdEluZGV4T2YoJy8nKSArIDEpLA0KICAgICAgICAgIHVybDogdXJsLA0KICAgICAgICAgIHVpZDogaW5kZXgNCiAgICAgICAgfSkpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBwYXJzZVRlc3RQYXJhbUF0dGFjaG1lbnRzKCkgew0KICAgICAgaWYgKHRoaXMudGVzdFBhcmFtRm9ybS5hdHRhY2htZW50cykgew0KICAgICAgICBjb25zdCB1cmxzID0gdGhpcy50ZXN0UGFyYW1Gb3JtLmF0dGFjaG1lbnRzLnNwbGl0KCcsJyk7DQogICAgICAgIHRoaXMudGVzdFBhcmFtRmlsZUxpc3QgPSB1cmxzLm1hcCgodXJsLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICBuYW1lOiB1cmwuc3Vic3RyaW5nKHVybC5sYXN0SW5kZXhPZignLycpICsgMSksDQogICAgICAgICAgdXJsOiB1cmwsDQogICAgICAgICAgdWlkOiBpbmRleA0KICAgICAgICB9KSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVBsYW5Hcm91cFVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsNCiAgICAgIHRoaXMucGxhbkdyb3VwRmlsZUxpc3QucHVzaCh7DQogICAgICAgIG5hbWU6IGZpbGUubmFtZSwNCiAgICAgICAgdXJsOiByZXNwb25zZS51cmwsDQogICAgICAgIHVpZDogZmlsZS51aWQNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBoYW5kbGVUZXN0UGFyYW1VcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlKSB7DQogICAgICB0aGlzLnRlc3RQYXJhbUZpbGVMaXN0LnB1c2goew0KICAgICAgICBuYW1lOiBmaWxlLm5hbWUsDQogICAgICAgIHVybDogcmVzcG9uc2UudXJsLA0KICAgICAgICB1aWQ6IGZpbGUudWlkDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlUGxhbkdyb3VwVXBsb2FkUmVtb3ZlKGZpbGUpIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5wbGFuR3JvdXBGaWxlTGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLnVpZCA9PT0gZmlsZS51aWQpOw0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgdGhpcy5wbGFuR3JvdXBGaWxlTGlzdC5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVUZXN0UGFyYW1VcGxvYWRSZW1vdmUoZmlsZSkgew0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLnRlc3RQYXJhbUZpbGVMaXN0LmZpbmRJbmRleChpdGVtID0+IGl0ZW0udWlkID09PSBmaWxlLnVpZCk7DQogICAgICBpZiAoaW5kZXggPiAtMSkgew0KICAgICAgICB0aGlzLnRlc3RQYXJhbUZpbGVMaXN0LnNwbGljZShpbmRleCwgMSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIGJlZm9yZVBsYW5Hcm91cFVwbG9hZChmaWxlKSB7DQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMDsNCiAgICAgIGlmICghaXNMdDEwTSkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhJyk7DQogICAgICB9DQogICAgICByZXR1cm4gaXNMdDEwTTsNCiAgICB9LA0KDQogICAgYmVmb3JlVGVzdFBhcmFtVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOw0KICAgICAgaWYgKCFpc0x0MTBNKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBpc0x0MTBNOw0KICAgIH0sDQoNCiAgICBoYW5kbGVWaWV3UGxhbkdyb3VwQXR0YWNobWVudHMoYXR0YWNobWVudHMpIHsNCiAgICAgIHRoaXMudmlld0F0dGFjaG1lbnRzKGF0dGFjaG1lbnRzKTsNCiAgICB9LA0KDQogICAgaGFuZGxlVmlld1Rlc3RQYXJhbUF0dGFjaG1lbnRzKGF0dGFjaG1lbnRzKSB7DQogICAgICB0aGlzLnZpZXdBdHRhY2htZW50cyhhdHRhY2htZW50cyk7DQogICAgfSwNCg0KICAgIHZpZXdBdHRhY2htZW50cyhhdHRhY2htZW50cykgew0KICAgICAgaWYgKCFhdHRhY2htZW50cykgcmV0dXJuOw0KICAgICAgY29uc3QgdXJscyA9IGF0dGFjaG1lbnRzLnNwbGl0KCcsJyk7DQogICAgICB0aGlzLmF0dGFjaG1lbnRMaXN0ID0gdXJscy5tYXAodXJsID0+ICh7DQogICAgICAgIG5hbWU6IHVybC5zdWJzdHJpbmcodXJsLmxhc3RJbmRleE9mKCcvJykgKyAxKSwNCiAgICAgICAgdXJsOiB1cmwNCiAgICAgIH0pKTsNCiAgICAgIHRoaXMuYXR0YWNobWVudERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBkb3dubG9hZEF0dGFjaG1lbnQodXJsLCBuYW1lKSB7DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IG5hbWU7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgfSwNCg0KICAgIGhhbmRsZVBsYW5Db2RlU2VsZWN0KGl0ZW0pIHsNCiAgICAgIHRoaXMucGxhbkdyb3VwUXVlcnlQYXJhbXMucGxhbkNvZGUgPSBpdGVtLnZhbHVlOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAogBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/testPlan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span>测试方案配置管理</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理测试方案组和测试参数的二层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击测试方案组行查看对应的测试参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 测试方案组表格 -->\r\n    <el-card class=\"plan-group-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"header-title\">测试方案组管理</span>\r\n          <el-badge :value=\"planGroupTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddPlanGroup\">\r\n            <span>新增方案组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"planGroupMultiple\" @click=\"handleBatchDeletePlanGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试方案组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"planGroupQueryParams\" ref=\"planGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.planCode\"\r\n              :fetch-suggestions=\"queryPlanCodeSuggestions\"\r\n              placeholder=\"请输入方案编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handlePlanCodeSelect\"\r\n              @focus=\"handlePlanCodeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-document\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.performanceType\"\r\n              :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n              placeholder=\"请输入性能类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handlePerformanceTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-lightning\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.testEquipment\"\r\n              :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n              placeholder=\"请输入测试设备\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleTestEquipmentFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-cpu\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handlePlanGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetPlanGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试方案组表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"planGroupLoading\"\r\n          :data=\"planGroupList\"\r\n          style=\"width: 100%\"\r\n          @selection-change=\"handlePlanGroupSelectionChange\"\r\n          @row-click=\"handlePlanGroupRowClick\"\r\n          highlight-current-row\r\n          :row-class-name=\"getPlanGroupRowClassName\"\r\n          ref=\"planGroupTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试方案数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (planGroupQueryParams.pageNum - 1) * planGroupQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"planCode\" label=\"方案编号\" min-width=\"160\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"plan-code-cell\">\r\n                <i class=\"el-icon-document plan-icon\"></i>\r\n                <span class=\"plan-code\">{{ scope.row.planCode }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceType\" label=\"性能类型\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" :type=\"getPerformanceTypeTag(scope.row.performanceType)\">\r\n                {{ scope.row.performanceType }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceName\" label=\"性能名称\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"performance-name-cell\">\r\n                <i class=\"el-icon-lightning performance-icon\"></i>\r\n                <span>{{ scope.row.performanceName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"testEquipment\" label=\"测试设备\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"equipment-cell\">\r\n                <i class=\"el-icon-cpu equipment-icon\"></i>\r\n                <span>{{ scope.row.testEquipment || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewPlanGroupAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleEditPlanGroup(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleCopyPlanGroup(scope.row)\" class=\"copy-btn\">\r\n                  <i class=\"el-icon-copy-document\"></i>\r\n                  复制\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDeletePlanGroup(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"planGroupTotal > 0\"\r\n        :total=\"planGroupTotal\"\r\n        :page.sync=\"planGroupQueryParams.pageNum\"\r\n        :limit.sync=\"planGroupQueryParams.pageSize\"\r\n        @pagination=\"getPlanGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试参数明细表格 -->\r\n    <el-card class=\"test-param-card enhanced-card\" v-show=\"currentPlanGroup\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">测试参数明细</span>\r\n          <div class=\"plan-group-indicator\" v-if=\"currentPlanGroup\">\r\n            <el-tag type=\"warning\" size=\"small\">\r\n              <i class=\"el-icon-document\"></i>\r\n              {{ currentPlanGroup.planCode }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"testParamTotal\" class=\"item-count-badge\" type=\"warning\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>新增参数</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"testParamMultiple || !currentPlanGroup\" @click=\"handleBatchDeleteTestParam\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试参数明细查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"testParamQueryParams\" ref=\"testParamQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.paramName\"\r\n              :fetch-suggestions=\"queryParamNameSuggestions\"\r\n              placeholder=\"请输入参数名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-data-line\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数单位\" prop=\"unit\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.unit\"\r\n              :fetch-suggestions=\"queryUnitSuggestions\"\r\n              placeholder=\"请输入参数单位\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleUnitFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-price-tag\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleTestParamQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetTestParamQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试参数明细表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"testParamLoading\"\r\n          :data=\"testParamList\"\r\n          @selection-change=\"handleTestParamSelectionChange\"\r\n          @row-click=\"handleTestParamRowClick\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getTestParamRowClassName\"\r\n          ref=\"testParamTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试参数数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (testParamQueryParams.pageNum - 1) * testParamQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"test-param-name-cell\">\r\n                <i class=\"el-icon-data-line test-param-icon\"></i>\r\n                <span class=\"test-param-name\">{{ scope.row.paramName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"test-param-value\" v-if=\"scope.row.paramValue !== null\">{{ scope.row.paramValue }}</span>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"success\" v-if=\"scope.row.unit\">{{ scope.row.unit }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewTestParamAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditTestParam(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteTestParam(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"testParamTotal > 0\"\r\n        :total=\"testParamTotal\"\r\n        :page.sync=\"testParamQueryParams.pageNum\"\r\n        :limit.sync=\"testParamQueryParams.pageSize\"\r\n        @pagination=\"getTestParamList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试方案组对话框 -->\r\n    <el-dialog :title=\"planGroupTitle\" :visible.sync=\"planGroupOpen\" width=\"800px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"planGroupForm\" :model=\"planGroupForm\" :rules=\"planGroupRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n              <el-input v-model=\"planGroupForm.planCode\" placeholder=\"请输入方案编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n              <el-input v-model=\"planGroupForm.performanceType\" placeholder=\"请输入性能类型\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能名称\" prop=\"performanceName\">\r\n              <el-input v-model=\"planGroupForm.performanceName\" placeholder=\"请输入性能名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n              <el-input v-model=\"planGroupForm.testEquipment\" placeholder=\"请输入测试设备\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"planGroupUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"planGroupFileList\"\r\n            :on-success=\"handlePlanGroupUploadSuccess\"\r\n            :on-remove=\"handlePlanGroupUploadRemove\"\r\n            :before-upload=\"beforePlanGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"planGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPlanGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPlanGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 测试参数明细对话框 -->\r\n    <el-dialog :title=\"testParamTitle\" :visible.sync=\"testParamOpen\" width=\"600px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"testParamForm\" :model=\"testParamForm\" :rules=\"testParamRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"testParamForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"testParamForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"testParamForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"testParamUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"testParamFileList\"\r\n            :on-success=\"handleTestParamUploadSuccess\"\r\n            :on-remove=\"handleTestParamUploadRemove\"\r\n            :before-upload=\"beforeTestParamUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"testParamForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitTestParamForm\">确 定</el-button>\r\n        <el-button @click=\"cancelTestParam\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestPlanGroup, getTestPlanGroup, delTestPlanGroup, addTestPlanGroup, updateTestPlanGroup,\r\n  exportTestPlanGroup, getTestPlanGroupOptions\r\n} from \"@/api/material/testPlanGroup\";\r\nimport {\r\n  listTestParamItem, getTestParamItem, delTestParamItem, addTestParamItem, updateTestParamItem,\r\n  exportTestParamItem, getTestParamItemOptions, listByPlanGroupId\r\n} from \"@/api/material/testParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestPlan\",\r\n  data() {\r\n    return {\r\n      // 测试方案组相关\r\n      planGroupLoading: false,\r\n      planGroupList: [],\r\n      planGroupTotal: 0,\r\n      planGroupOpen: false,\r\n      planGroupTitle: \"\",\r\n      planGroupForm: {},\r\n      planGroupFileList: [],\r\n      planGroupIds: [],\r\n      planGroupSingle: true,\r\n      planGroupMultiple: true,\r\n      planGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planCode: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      planGroupRules: {\r\n        planCode: [\r\n          { required: true, message: \"方案编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        performanceType: [\r\n          { required: true, message: \"性能类型不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 测试参数明细相关\r\n      testParamLoading: false,\r\n      testParamList: [],\r\n      testParamTotal: 0,\r\n      testParamOpen: false,\r\n      testParamTitle: \"\",\r\n      testParamForm: {},\r\n      testParamFileList: [],\r\n      testParamIds: [],\r\n      testParamSingle: true,\r\n      testParamMultiple: true,\r\n      testParamQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planGroupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      testParamRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 当前选中的测试方案组\r\n      currentPlanGroup: null,\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      planCodeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      unitSuggestions: [],\r\n\r\n      // 上传相关\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getPlanGroupList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 获取性能类型标签颜色 */\r\n    getPerformanceTypeTag(type) {\r\n      const typeMap = {\r\n        '力学性能': 'success',\r\n        '电学性能': 'primary',\r\n        '热学性能': 'warning',\r\n        '光学性能': 'info',\r\n        '化学性能': 'danger',\r\n        '物理性能': ''\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 查询测试方案组列表 */\r\n    getPlanGroupList() {\r\n      this.planGroupLoading = true;\r\n      listTestPlanGroup(this.planGroupQueryParams).then(response => {\r\n        this.planGroupList = response.rows;\r\n        this.planGroupTotal = response.total;\r\n        this.planGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询测试参数明细列表 */\r\n    getTestParamList() {\r\n      if (!this.currentPlanGroup) return;\r\n\r\n      this.testParamLoading = true;\r\n      this.testParamQueryParams.planGroupId = this.currentPlanGroup.planGroupId;\r\n      listTestParamItem(this.testParamQueryParams).then(response => {\r\n        this.testParamList = response.rows;\r\n        this.testParamTotal = response.total;\r\n        this.testParamLoading = false;\r\n\r\n        // 更新参数筛选选项\r\n        this.$nextTick(() => {\r\n          this.updateParamFilterOptions();\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取方案编号建议\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取性能类型建议\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取测试设备建议\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数名称建议\r\n      getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数单位建议\r\n      getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 搜索建议方法 */\r\n    queryPlanCodeSuggestions(queryString, cb) {\r\n      let suggestions = this.planCodeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.planCodeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 焦点事件 */\r\n    handlePlanCodeFocus() {\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handlePerformanceTypeFocus() {\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleTestEquipmentFocus() {\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleParamNameFocus() {\r\n      // 只获取当前选中测试方案组下的参数名称选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数名称\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数名称\r\n        getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleUnitFocus() {\r\n      // 只获取当前选中测试方案组下的参数单位选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数单位\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数单位\r\n        getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 测试方案组相关方法 */\r\n    handlePlanGroupSelectionChange(selection) {\r\n      this.planGroupIds = selection.map(item => item.planGroupId);\r\n      this.planGroupSingle = selection.length !== 1;\r\n      this.planGroupMultiple = !selection.length;\r\n    },\r\n\r\n    handlePlanGroupRowClick(row) {\r\n      this.currentPlanGroup = row;\r\n      this.$refs.planGroupTable.toggleRowSelection(row);\r\n\r\n      // 清空测试参数筛选条件\r\n      this.testParamQueryParams.paramName = null;\r\n      this.testParamQueryParams.unit = null;\r\n      this.resetForm(\"testParamQueryForm\");\r\n\r\n      // 加载测试参数列表\r\n      this.getTestParamList();\r\n\r\n      // 延迟更新筛选选项，确保testParamList已加载\r\n      this.$nextTick(() => {\r\n        this.updateParamFilterOptions();\r\n      });\r\n    },\r\n\r\n    getPlanGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentPlanGroup && row.planGroupId === this.currentPlanGroup.planGroupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    handlePlanGroupQuery() {\r\n      this.planGroupQueryParams.pageNum = 1;\r\n      this.getPlanGroupList();\r\n    },\r\n\r\n    resetPlanGroupQuery() {\r\n      this.resetForm(\"planGroupQueryForm\");\r\n      this.handlePlanGroupQuery();\r\n    },\r\n\r\n    handleAddPlanGroup() {\r\n      this.resetPlanGroupForm();\r\n      this.planGroupOpen = true;\r\n      this.planGroupTitle = \"添加测试方案组\";\r\n    },\r\n\r\n    handleEditPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId || this.planGroupIds;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"修改测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleCopyPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.planGroupForm.planGroupId = null;\r\n        this.planGroupForm.planCode = this.planGroupForm.planCode + \"_copy\";\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"复制测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleDeletePlanGroup(row) {\r\n      const planGroupIds = row.planGroupId || this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除测试方案组编号为\"' + row.planCode + '\"的数据项？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        if (this.currentPlanGroup && this.currentPlanGroup.planGroupId === row.planGroupId) {\r\n          this.currentPlanGroup = null;\r\n          this.testParamList = [];\r\n        }\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeletePlanGroup() {\r\n      const planGroupIds = this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + planGroupIds.length + '条数据？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        this.currentPlanGroup = null;\r\n        this.testParamList = [];\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportPlanGroup() {\r\n      this.download('material/testPlanGroup/export', {\r\n        ...this.planGroupQueryParams\r\n      }, `test_plan_group_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 更新参数筛选选项 */\r\n    updateParamFilterOptions() {\r\n      if (this.currentPlanGroup && this.testParamList.length > 0) {\r\n        // 从当前测试参数列表中提取唯一的参数名称和单位\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      }\r\n    },\r\n\r\n    /** 测试参数明细相关方法 */\r\n    handleTestParamSelectionChange(selection) {\r\n      this.testParamIds = selection.map(item => item.testParamId);\r\n      this.testParamSingle = selection.length !== 1;\r\n      this.testParamMultiple = !selection.length;\r\n    },\r\n\r\n    handleTestParamRowClick(row) {\r\n      this.$refs.testParamTable.toggleRowSelection(row);\r\n    },\r\n\r\n    handleTestParamQuery() {\r\n      this.testParamQueryParams.pageNum = 1;\r\n      this.getTestParamList();\r\n    },\r\n\r\n    resetTestParamQuery() {\r\n      this.resetForm(\"testParamQueryForm\");\r\n      this.handleTestParamQuery();\r\n    },\r\n\r\n    handleAddTestParam() {\r\n      this.resetTestParamForm();\r\n      this.testParamOpen = true;\r\n      this.testParamTitle = \"添加测试参数明细\";\r\n    },\r\n\r\n    handleEditTestParam(row) {\r\n      this.resetTestParamForm();\r\n      const testParamId = row.testParamId || this.testParamIds;\r\n      getTestParamItem(testParamId).then(response => {\r\n        this.testParamForm = response.data;\r\n        this.parseTestParamAttachments();\r\n        this.testParamOpen = true;\r\n        this.testParamTitle = \"修改测试参数明细\";\r\n      });\r\n    },\r\n\r\n    handleDeleteTestParam(row) {\r\n      const testParamIds = row.testParamId || this.testParamIds;\r\n      this.$modal.confirm('是否确认删除参数名称为\"' + row.paramName + '\"的数据项？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeleteTestParam() {\r\n      const testParamIds = this.testParamIds;\r\n      this.$modal.confirm('是否确认删除选中的' + testParamIds.length + '条数据？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportTestParam() {\r\n      this.download('material/testParamItem/export', {\r\n        ...this.testParamQueryParams\r\n      }, `test_param_item_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 表单相关方法 */\r\n    resetPlanGroupForm() {\r\n      this.planGroupForm = {\r\n        planGroupId: null,\r\n        planCode: null,\r\n        performanceType: null,\r\n        performanceName: null,\r\n        testEquipment: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.planGroupFileList = [];\r\n      this.resetForm(\"planGroupForm\");\r\n    },\r\n\r\n    resetTestParamForm() {\r\n      this.testParamForm = {\r\n        testParamId: null,\r\n        planGroupId: this.currentPlanGroup ? this.currentPlanGroup.planGroupId : null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.testParamFileList = [];\r\n      this.resetForm(\"testParamForm\");\r\n    },\r\n\r\n    submitPlanGroupForm() {\r\n      this.$refs[\"planGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.planGroupForm.attachments = this.planGroupFileList.map(file => file.url).join(',');\r\n          if (this.planGroupForm.planGroupId != null) {\r\n            updateTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          } else {\r\n            addTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    submitTestParamForm() {\r\n      this.$refs[\"testParamForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.testParamForm.attachments = this.testParamFileList.map(file => file.url).join(',');\r\n          if (this.testParamForm.testParamId != null) {\r\n            updateTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          } else {\r\n            addTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    cancelPlanGroup() {\r\n      this.planGroupOpen = false;\r\n      this.resetPlanGroupForm();\r\n    },\r\n\r\n    cancelTestParam() {\r\n      this.testParamOpen = false;\r\n      this.resetTestParamForm();\r\n    },\r\n\r\n    /** 附件相关方法 */\r\n    parsePlanGroupAttachments() {\r\n      if (this.planGroupForm.attachments) {\r\n        const urls = this.planGroupForm.attachments.split(',');\r\n        this.planGroupFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    parseTestParamAttachments() {\r\n      if (this.testParamForm.attachments) {\r\n        const urls = this.testParamForm.attachments.split(',');\r\n        this.testParamFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    handlePlanGroupUploadSuccess(response, file) {\r\n      this.planGroupFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handleTestParamUploadSuccess(response, file) {\r\n      this.testParamFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handlePlanGroupUploadRemove(file) {\r\n      const index = this.planGroupFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.planGroupFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    handleTestParamUploadRemove(file) {\r\n      const index = this.testParamFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.testParamFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    beforePlanGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    beforeTestParamUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    handleViewPlanGroupAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    handleViewTestParamAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    viewAttachments(attachments) {\r\n      if (!attachments) return;\r\n      const urls = attachments.split(',');\r\n      this.attachmentList = urls.map(url => ({\r\n        name: url.substring(url.lastIndexOf('/') + 1),\r\n        url: url\r\n      }));\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    handlePlanCodeSelect(item) {\r\n      this.planGroupQueryParams.planCode = item.value;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.plan-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.performance-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.performance-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.equipment-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.equipment-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 测试参数明细样式 */\r\n.test-param-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.test-param-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.test-param-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.test-param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n}\r\n\r\n/* 指示器样式 */\r\n.plan-group-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.plan-group-indicator .el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.copy-btn {\r\n  color: #67C23A;\r\n  font-weight: 500;\r\n}\r\n\r\n.copy-btn:hover {\r\n  color: #85ce61;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}