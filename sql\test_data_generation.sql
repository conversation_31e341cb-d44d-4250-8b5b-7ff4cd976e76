-- 测试数据生成脚本
-- 根据新的数据录入结构生成丰富的测试数据

-- 1. 清理现有测试数据（可选）
-- DELETE FROM test_results WHERE test_result_id > 0;
-- DELETE FROM test_param_item WHERE test_param_id > 0;
-- DELETE FROM test_plan_group WHERE plan_group_id > 0;
-- DELETE FROM process_param_item WHERE item_id > 0;
-- DELETE FROM process_param_group WHERE group_id > 0;
-- DELETE FROM materials WHERE material_id > 0;

-- 2. 插入材料数据
INSERT INTO materials (material_name, supplier_name, material_model, material_type, create_by, create_time) VALUES
('碳纤维复合材料', '东丽工业', 'T700-12K', '复合材料', 'admin', NOW()),
('玻璃纤维复合材料', '巨石集团', 'E-Glass-2400', '复合材料', 'admin', NOW()),
('芳纶纤维复合材料', '杜邦公司', 'Kevlar-49', '复合材料', 'admin', NOW()),
('碳纤维复合材料', '中复神鹰', 'SYT49-12K', '复合材料', 'admin', NOW()),
('玻璃纤维复合材料', '重庆国际', 'C-Glass-1200', '复合材料', 'admin', NOW()),
('碳纤维复合材料', '威海光威', 'GW-T300', '复合材料', 'admin', NOW()),
('环氧树脂', '亨斯迈', 'Araldite-LY556', '树脂基体', 'admin', NOW()),
('聚酰亚胺树脂', '长春应化', 'PI-2080', '树脂基体', 'admin', NOW()),
('酚醛树脂', '圣泉集团', 'PF-2123', '树脂基体', 'admin', NOW()),
('不饱和聚酯树脂', '天津合成', 'UPR-196', '树脂基体', 'admin', NOW());

-- 3. 插入工艺参数组数据
INSERT INTO process_param_group (material_id, param_number, process_type, create_by, create_time) VALUES
(1, 'CF-T700-001', '预浸料制备', 'admin', NOW()),
(1, 'CF-T700-002', '热压成型', 'admin', NOW()),
(1, 'CF-T700-003', '后固化处理', 'admin', NOW()),
(2, 'GF-E2400-001', '拉挤成型', 'admin', NOW()),
(2, 'GF-E2400-002', '缠绕成型', 'admin', NOW()),
(3, 'AF-K49-001', '编织预成型', 'admin', NOW()),
(3, 'AF-K49-002', 'RTM成型', 'admin', NOW()),
(4, 'CF-SYT49-001', '预浸料制备', 'admin', NOW()),
(4, 'CF-SYT49-002', '模压成型', 'admin', NOW()),
(5, 'GF-C1200-001', '手糊成型', 'admin', NOW()),
(6, 'CF-GW300-001', '预浸料制备', 'admin', NOW()),
(6, 'CF-GW300-002', '自动铺放', 'admin', NOW()),
(7, 'EP-LY556-001', '真空浸渍', 'admin', NOW()),
(8, 'PI-2080-001', '溶液浸渍', 'admin', NOW()),
(9, 'PF-2123-001', '压制成型', 'admin', NOW());

-- 4. 插入工艺参数明细数据
INSERT INTO process_param_item (group_id, param_name, param_value, unit, create_by, create_time) VALUES
-- CF-T700-001 预浸料制备参数
(1, '树脂含量', '35.5', '%', 'admin', NOW()),
(1, '固化温度', '125', '℃', 'admin', NOW()),
(1, '固化时间', '90', 'min', 'admin', NOW()),
(1, '压力', '0.6', 'MPa', 'admin', NOW()),
-- CF-T700-002 热压成型参数
(2, '成型温度', '135', '℃', 'admin', NOW()),
(2, '成型压力', '1.2', 'MPa', 'admin', NOW()),
(2, '保压时间', '120', 'min', 'admin', NOW()),
(2, '升温速率', '2.5', '℃/min', 'admin', NOW()),
-- CF-T700-003 后固化处理参数
(3, '后固化温度', '180', '℃', 'admin', NOW()),
(3, '后固化时间', '240', 'min', 'admin', NOW()),
(3, '升温速率', '1.5', '℃/min', 'admin', NOW()),
-- GF-E2400-001 拉挤成型参数
(4, '拉挤速度', '0.8', 'm/min', 'admin', NOW()),
(4, '模具温度', '145', '℃', 'admin', NOW()),
(4, '纤维含量', '65', '%', 'admin', NOW()),
(4, '牵引力', '1500', 'N', 'admin', NOW()),
-- GF-E2400-002 缠绕成型参数
(5, '缠绕角度', '55', '°', 'admin', NOW()),
(5, '缠绕张力', '80', 'N', 'admin', NOW()),
(5, '固化温度', '120', '℃', 'admin', NOW()),
(5, '转速', '15', 'rpm', 'admin', NOW()),
-- AF-K49-001 编织预成型参数
(6, '编织密度', '12', '根/cm', 'admin', NOW()),
(6, '编织角度', '45', '°', 'admin', NOW()),
(6, '张力控制', '25', 'N', 'admin', NOW()),
-- AF-K49-002 RTM成型参数
(7, '注射压力', '0.4', 'MPa', 'admin', NOW()),
(7, '注射温度', '80', '℃', 'admin', NOW()),
(7, '固化温度', '160', '℃', 'admin', NOW()),
(7, '固化时间', '180', 'min', 'admin', NOW()),
-- CF-SYT49-001 预浸料制备参数
(8, '树脂含量', '38', '%', 'admin', NOW()),
(8, '固化温度', '130', '℃', 'admin', NOW()),
(8, '固化时间', '85', 'min', 'admin', NOW()),
-- CF-SYT49-002 模压成型参数
(9, '模压温度', '140', '℃', 'admin', NOW()),
(9, '模压压力', '2.0', 'MPa', 'admin', NOW()),
(9, '保压时间', '150', 'min', 'admin', NOW()),
-- GF-C1200-001 手糊成型参数
(10, '层间时间', '30', 'min', 'admin', NOW()),
(10, '固化温度', '25', '℃', 'admin', NOW()),
(10, '固化时间', '1440', 'min', 'admin', NOW()),
-- CF-GW300-001 预浸料制备参数
(11, '树脂含量', '33', '%', 'admin', NOW()),
(11, '固化温度', '120', '℃', 'admin', NOW()),
(11, '固化时间', '95', 'min', 'admin', NOW()),
-- CF-GW300-002 自动铺放参数
(12, '铺放速度', '25', 'm/min', 'admin', NOW()),
(12, '压实压力', '200', 'N', 'admin', NOW()),
(12, '铺放角度', '0', '°', 'admin', NOW()),
-- EP-LY556-001 真空浸渍参数
(13, '真空度', '0.09', 'MPa', 'admin', NOW()),
(13, '浸渍温度', '60', '℃', 'admin', NOW()),
(13, '浸渍时间', '120', 'min', 'admin', NOW()),
-- PI-2080-001 溶液浸渍参数
(14, '溶液浓度', '15', '%', 'admin', NOW()),
(14, '浸渍温度', '80', '℃', 'admin', NOW()),
(14, '干燥温度', '150', '℃', 'admin', NOW()),
-- PF-2123-001 压制成型参数
(15, '压制温度', '160', '℃', 'admin', NOW()),
(15, '压制压力', '15', 'MPa', 'admin', NOW()),
(15, '保压时间', '300', 'min', 'admin', NOW());

-- 5. 插入测试方案组数据
INSERT INTO test_plan_group (plan_code, performance_name, performance_type, test_equipment, create_by, create_time) VALUES
('TP-MECH-001', '拉伸性能测试', '力学性能', 'Instron-5985万能试验机', 'admin', NOW()),
('TP-MECH-002', '弯曲性能测试', '力学性能', 'Instron-5985万能试验机', 'admin', NOW()),
('TP-MECH-003', '冲击性能测试', '力学性能', 'Charpy冲击试验机', 'admin', NOW()),
('TP-MECH-004', '疲劳性能测试', '力学性能', 'MTS-810疲劳试验机', 'admin', NOW()),
('TP-THER-001', '热变形温度测试', '热学性能', 'HDT/Vicat测试仪', 'admin', NOW()),
('TP-THER-002', '玻璃化转变温度', '热学性能', 'DSC差示扫描量热仪', 'admin', NOW()),
('TP-THER-003', '热膨胀系数测试', '热学性能', 'TMA热机械分析仪', 'admin', NOW()),
('TP-ELEC-001', '介电性能测试', '电学性能', 'Agilent-E4991A阻抗分析仪', 'admin', NOW()),
('TP-ELEC-002', '绝缘电阻测试', '电学性能', 'Keithley-6517B电阻计', 'admin', NOW()),
('TP-PHYS-001', '密度测试', '物理性能', 'Mettler-Toledo密度计', 'admin', NOW()),
('TP-PHYS-002', '吸水率测试', '物理性能', '恒温水浴箱', 'admin', NOW()),
('TP-SURF-001', '表面粗糙度测试', '表面性能', 'Mitutoyo粗糙度仪', 'admin', NOW());

-- 6. 插入测试参数明细数据
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, create_by, create_time) VALUES
-- TP-MECH-001 拉伸性能测试参数
(1, '拉伸强度', '1500', 'MPa', 'admin', NOW()),
(1, '拉伸模量', '150', 'GPa', 'admin', NOW()),
(1, '断裂伸长率', '1.2', '%', 'admin', NOW()),
(1, '泊松比', '0.3', '', 'admin', NOW()),
-- TP-MECH-002 弯曲性能测试参数
(2, '弯曲强度', '1200', 'MPa', 'admin', NOW()),
(2, '弯曲模量', '120', 'GPa', 'admin', NOW()),
(2, '最大弯曲应变', '1.5', '%', 'admin', NOW()),
-- TP-MECH-003 冲击性能测试参数
(3, '冲击强度', '85', 'kJ/m²', 'admin', NOW()),
(3, '冲击韧性', '45', 'J', 'admin', NOW()),
-- TP-MECH-004 疲劳性能测试参数
(4, '疲劳强度', '800', 'MPa', 'admin', NOW()),
(4, '疲劳寿命', '1000000', '次', 'admin', NOW()),
-- TP-THER-001 热变形温度测试参数
(5, '热变形温度', '180', '℃', 'admin', NOW()),
(5, '载荷', '1.8', 'MPa', 'admin', NOW()),
-- TP-THER-002 玻璃化转变温度参数
(6, '玻璃化转变温度', '165', '℃', 'admin', NOW()),
(6, '升温速率', '10', '℃/min', 'admin', NOW()),
-- TP-THER-003 热膨胀系数测试参数
(7, '线性热膨胀系数', '5.2', '10⁻⁶/℃', 'admin', NOW()),
(7, '测试温度范围', '25-200', '℃', 'admin', NOW()),
-- TP-ELEC-001 介电性能测试参数
(8, '介电常数', '3.8', '', 'admin', NOW()),
(8, '介电损耗', '0.02', '', 'admin', NOW()),
(8, '测试频率', '1000', 'Hz', 'admin', NOW()),
-- TP-ELEC-002 绝缘电阻测试参数
(9, '体积电阻率', '1.5E14', 'Ω·cm', 'admin', NOW()),
(9, '表面电阻率', '2.3E13', 'Ω', 'admin', NOW()),
-- TP-PHYS-001 密度测试参数
(10, '密度', '1.58', 'g/cm³', 'admin', NOW()),
(10, '测试温度', '23', '℃', 'admin', NOW()),
-- TP-PHYS-002 吸水率测试参数
(11, '24小时吸水率', '0.15', '%', 'admin', NOW()),
(11, '平衡吸水率', '0.35', '%', 'admin', NOW()),
-- TP-SURF-001 表面粗糙度测试参数
(12, '算术平均粗糙度Ra', '0.8', 'μm', 'admin', NOW()),
(12, '最大高度粗糙度Rz', '4.2', 'μm', 'admin', NOW());

SELECT '测试数据生成完成！' as '状态信息';
SELECT '请继续执行 test_results_data.sql 生成测试结果数据' as '下一步操作';
