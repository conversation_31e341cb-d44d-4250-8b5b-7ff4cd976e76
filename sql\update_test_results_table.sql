-- 更新test_results表结构以适配新的测试方案结构
-- 将test_plan_id字段替换为plan_group_id和test_param_id

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS test_results_backup AS 
SELECT * FROM test_results;

-- 2. 添加新字段
ALTER TABLE test_results 
ADD COLUMN plan_group_id BIGINT(20) DEFAULT NULL COMMENT '测试方案组ID' AFTER test_result_id,
ADD COLUMN test_param_id BIGINT(20) DEFAULT NULL COMMENT '测试参数明细ID' AFTER plan_group_id;

-- 3. 如果存在旧的test_plan_id字段，可以尝试迁移数据
-- 注意：这需要根据实际的数据映射关系来调整
-- UPDATE test_results tr 
-- SET plan_group_id = (
--     SELECT tpg.plan_group_id 
--     FROM test_plan_group tpg 
--     WHERE tpg.plan_code = (
--         SELECT tp.plan_code 
--         FROM test_plans tp 
--         WHERE tp.test_plan_id = tr.test_plan_id
--     )
--     LIMIT 1
-- )
-- WHERE tr.test_plan_id IS NOT NULL;

-- 4. 删除旧的test_plan_id字段（如果存在）
-- ALTER TABLE test_results DROP COLUMN test_plan_id;

-- 5. 添加外键约束（可选）
-- ALTER TABLE test_results 
-- ADD CONSTRAINT fk_test_results_plan_group 
-- FOREIGN KEY (plan_group_id) REFERENCES test_plan_group(plan_group_id) ON DELETE SET NULL;

-- ALTER TABLE test_results 
-- ADD CONSTRAINT fk_test_results_test_param 
-- FOREIGN KEY (test_param_id) REFERENCES test_param_item(test_param_id) ON DELETE SET NULL;

-- 6. 验证表结构
DESCRIBE test_results;

-- 7. 检查数据完整性
SELECT 
    COUNT(*) as '总记录数',
    COUNT(plan_group_id) as '有方案组ID的记录数',
    COUNT(test_param_id) as '有参数ID的记录数',
    COUNT(CASE WHEN plan_group_id IS NULL AND test_param_id IS NULL THEN 1 END) as '无关联的记录数'
FROM test_results;

-- 8. 显示示例数据
SELECT 
    test_result_id,
    plan_group_id,
    test_param_id,
    group_id,
    supplier_datasheet_val,
    test_value,
    create_time
FROM test_results 
LIMIT 10;

SELECT 'test_results表结构更新完成！' as '更新状态';
