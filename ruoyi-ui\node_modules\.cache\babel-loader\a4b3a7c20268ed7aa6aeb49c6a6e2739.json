{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754281779689}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\babel.config.js", "mtime": 1753339103954}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_testResult", "_material", "_processParamGroup", "_processParamItem", "name", "data", "loading", "chart", "chartType", "chartHeight", "chartTitle", "chartLoading", "showDataTable", "isFullscreen", "queryParams", "compareType", "paramNumbers", "materialNames", "supplierNames", "processTypes", "date<PERSON><PERSON><PERSON>", "compareParam", "paramNumberOptions", "materialOptions", "supplierOptions", "processTypeOptions", "chartData", "tableColumns", "selectedParamDetails", "statisticsData", "helpDialogVisible", "showUsageGuide", "showProjectDetails", "paramDetailDialogVisible", "currentParamDetail", "mounted", "initChart", "loadOptions", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "_this", "init", "$refs", "window", "addEventListener", "resize", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "paramResponse", "materialResponse", "supplierResponse", "processResponse", "_t", "w", "_context", "p", "n", "listProcessParamGroup", "v", "rows", "listMaterial", "getTestResultOptions", "type", "console", "error", "$modal", "msgError", "a", "handleCompareTypeChange", "value", "updateChartTitle", "typeMap", "handleChartTypeChange", "length", "<PERSON><PERSON><PERSON>", "handleQuery", "_this3", "_callee2", "paramDetails", "_t2", "_t3", "_context2", "validate<PERSON><PERSON>y", "getMaterialCompareData", "getSupplierCompareData", "getParamNumberCompareData", "getProcessTypeCompareData", "getTimeTrendData", "getSupplierVsTestData", "updateTableColumns", "updateSelectedParamDetails", "f", "$message", "warning", "_this4", "_callee3", "materialIds", "compareData", "_iterator", "_step", "_loop", "_t6", "_context4", "_createForOfIteratorHelper2", "materialId", "paramGroupResponse", "paramGroups", "material", "allSupplierValues", "allTestValues", "_iterator2", "_step2", "group", "testResponse", "testResults", "supplierValues", "testValues", "_t4", "_t5", "_context3", "pageNum", "pageSize", "find", "s", "done", "listTestResult", "groupId", "map", "r", "parseFloat", "supplierDatasheetVal", "filter", "isNaN", "testValue", "concat", "e", "push", "materialName", "supplier", "supplierName", "supplierAvg", "reduce", "b", "toFixed", "testAvg", "supplierMax", "Math", "max", "apply", "_toConsumableArray2", "testMax", "supplierMin", "min", "testMin", "dataCount", "d", "_regeneratorValues2", "_this5", "_callee4", "suppliers", "_iterator3", "_step3", "response", "_t7", "_t8", "_context5", "accuracy", "abs", "_this6", "_callee5", "paramGroupIds", "_iterator4", "_step4", "_loop2", "_t0", "_context7", "paramItemResponse", "paramGroup", "paramItems", "_t9", "_context6", "listProcessParamItem", "item", "paramName", "paramValue", "undefined", "String", "unit", "paramNumber", "processType", "deviation", "_this7", "_callee6", "_iterator5", "_step5", "_t1", "_t10", "_context8", "stability", "calculateStandardDeviation", "_this8", "_callee7", "_this8$queryParams$da", "startDate", "endDate", "trendData", "dateGroups", "_t11", "_context9", "_slicedToArray2", "for<PERSON>ach", "result", "date", "createTime", "split", "Object", "keys", "sort", "dayResults", "avgValue", "count", "_this9", "_callee8", "_t12", "_context0", "supplierValue", "difference", "values", "avg", "squareDiffs", "pow", "avgSquareDiff", "sqrt", "formatParamDetails", "row", "column", "cellValue", "Array", "isArray", "param", "text", "join", "prop", "label", "width", "option", "getLineChartOption", "getBarChartOption", "getScatterChartOption", "getRadarChartOption", "getHeatmapChartOption", "setOption", "title", "left", "tooltip", "trigger", "formatter", "params", "marker", "seriesName", "grid", "right", "bottom", "containLabel", "xAxis", "yAxis", "series", "smooth", "symbol", "symbolSize", "self", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "fontSize", "extraCssText", "dataIndex", "currentData", "formatNumber", "slice", "legend", "top", "axisPointer", "axisLabel", "rotate", "interval", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "scatterData", "index", "_params$data", "supplierVal", "testVal", "scale", "lineStyle", "indicators", "radarData", "parseInt", "radar", "indicator", "radius", "xAxisData", "Set", "yAxisData", "heatmapData", "xIndex", "maxValue", "position", "_params$data2", "x", "y", "xLabel", "yLabel", "height", "visualMap", "calculable", "orient", "inRange", "show", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "reset<PERSON><PERSON>y", "resetForm", "refresh<PERSON><PERSON>", "exportChart", "url", "getDataURL", "pixelRatio", "link", "document", "createElement", "href", "download", "Date", "getTime", "click", "toggleDataTable", "_this0", "$nextTick", "toggleProjectDetails", "toggleFullscreen", "_this1", "innerHeight", "showChartHelp", "num", "getParamTagType", "showParamDetail", "_this10", "testCount", "statistics", "paramId", "mainParams"], "sources": ["src/views/material/trend/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-data-line\"></i>\r\n        <span>趋势对比分析</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <p>📈 多维度数据趋势对比分析，支持材料性能、供应商质量等多种对比维度</p>\r\n          <el-button type=\"text\" @click=\"showUsageGuide = true\" style=\"color: #409EFF;\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <span>使用指南</span>\r\n          </el-button>\r\n        </div>\r\n        <el-alert\r\n          title=\"使用提示：选择对比维度 → 配置筛选条件 → 生成图表分析\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用指南卡片 -->\r\n    <el-card class=\"usage-guide-card enhanced-card\" style=\"margin-bottom: 20px;\" v-if=\"showUsageGuide\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"header-title\">📊 趋势对比分析使用指南</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"text\" @click=\"showUsageGuide = false\" class=\"close-guide-btn\">\r\n            <i class=\"el-icon-close\"></i>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>🎯 对比维度说明</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>材料对比：</strong>按材料名称分组，计算每种材料的供应商数据和测试数据平均值</li>\r\n              <li><strong>供应商对比：</strong>按供应商分组，计算准确率（供应商数据与测试数据的偏差）</li>\r\n              <li><strong>参数编号对比：</strong>按参数组分组，展示工艺参数明细和测试结果统计</li>\r\n              <li><strong>工艺类型对比：</strong>按工艺类型分组，计算稳定性（测试数据标准差）</li>\r\n              <li><strong>时间趋势：</strong>按日期分组，展示测试数据随时间的变化趋势</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>📊 数据来源详解</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>供应商平均值：</strong>选中项目下所有测试记录的供应商数据平均值</li>\r\n              <li><strong>测试平均值：</strong>选中项目下所有测试记录的实际测试值平均值</li>\r\n              <li><strong>准确率：</strong>100% - |供应商平均值 - 测试平均值| / 供应商平均值 × 100%</li>\r\n              <li><strong>稳定性：</strong>基于测试数据标准差计算，数值越小越稳定</li>\r\n              <li><strong>数据量：</strong>参与计算的测试记录总数</li>\r\n              <li><strong>参数明细：</strong>来自工艺参数配置中的具体参数项</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row style=\"margin-top: 15px;\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"guide-item\">\r\n            <h4>💡 使用建议</h4>\r\n            <p style=\"margin: 10px 0; line-height: 1.6; color: #606266;\">\r\n              1. 选择对比维度 → 2. 配置筛选条件（支持多选） → 3. 点击\"生成图表\"分析 → 4. 切换图表类型查看不同视角 → 5. 查看详细数据表获取具体数值\r\n            </p>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <el-card class=\"trend-analysis-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">数据趋势对比分析</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"info\" icon=\"el-icon-question\" size=\"small\" @click=\"showUsageGuide = !showUsageGuide\" class=\"guide-btn\">\r\n            <span>使用指南</span>\r\n          </el-button>\r\n          <el-button type=\"primary\" icon=\"el-icon-refresh\" size=\"small\" @click=\"refreshChart\" class=\"refresh-btn\">\r\n            <span>刷新数据</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"exportChart\" class=\"export-btn\">\r\n            <span>导出图表</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 筛选条件 -->\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"对比维度\" prop=\"compareType\">\r\n          <el-select v-model=\"queryParams.compareType\" placeholder=\"请选择对比维度\" style=\"width: 250px;\" clearable @change=\"handleCompareTypeChange\">\r\n            <el-option label=\"📊 材料性能对比\" value=\"material\">\r\n              <span style=\"float: left\">📊 材料性能对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同材料性能</span>\r\n            </el-option>\r\n            <el-option label=\"🏭 供应商数据对比\" value=\"supplier\">\r\n              <span style=\"float: left\">🏭 供应商数据对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较供应商质量</span>\r\n            </el-option>\r\n            <el-option label=\"🔢 参数编号对比\" value=\"paramNumber\">\r\n              <span style=\"float: left\">🔢 参数编号对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同参数值</span>\r\n            </el-option>\r\n            <el-option label=\"⚙️ 工艺类型对比\" value=\"processType\">\r\n              <span style=\"float: left\">⚙️ 工艺类型对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较工艺效果</span>\r\n            </el-option>\r\n            <el-option label=\"📈 时间趋势分析\" value=\"timeTrend\">\r\n              <span style=\"float: left\">📈 时间趋势分析</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">查看时间变化</span>\r\n            </el-option>\r\n            <el-option label=\"⚖️ 供应商vs测试值\" value=\"supplierVsTest\">\r\n              <span style=\"float: left\">⚖️ 供应商vs测试值</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">对比数据差异</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 材料性能对比 -->\r\n        <el-form-item label=\"选择材料\" prop=\"materialNames\" v-if=\"queryParams.compareType === 'material'\">\r\n          <el-select\r\n            v-model=\"queryParams.materialNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的材料\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"material in materialOptions\"\r\n              :key=\"material.materialId\"\r\n              :label=\"material.materialName + ' (' + material.supplierName + ')'\"\r\n              :value=\"material.materialId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 供应商数据对比 -->\r\n        <el-form-item label=\"选择供应商\" prop=\"supplierNames\" v-if=\"queryParams.compareType === 'supplier'\">\r\n          <el-select\r\n            v-model=\"queryParams.supplierNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的供应商\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"supplier in supplierOptions\"\r\n              :key=\"supplier\"\r\n              :label=\"supplier\"\r\n              :value=\"supplier\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 参数编号对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"paramNumbers\" v-if=\"queryParams.compareType === 'paramNumber'\">\r\n          <el-select\r\n            v-model=\"queryParams.paramNumbers\"\r\n            multiple\r\n            placeholder=\"请选择要对比的参数编号\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 工艺类型对比 -->\r\n        <el-form-item label=\"选择工艺\" prop=\"processTypes\" v-if=\"queryParams.compareType === 'processType'\">\r\n          <el-select\r\n            v-model=\"queryParams.processTypes\"\r\n            multiple\r\n            placeholder=\"请选择要对比的工艺类型\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"type in processTypeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 时间趋势分析 -->\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\" v-if=\"queryParams.compareType === 'timeTrend'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            style=\"width: 300px;\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 供应商vs测试值对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"compareParam\" v-if=\"queryParams.compareType === 'supplierVsTest'\">\r\n          <el-select\r\n            v-model=\"queryParams.compareParam\"\r\n            placeholder=\"请选择要对比的参数\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\" :loading=\"loading\">生成对比图表</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 图表类型选择 -->\r\n      <el-row style=\"margin-bottom: 20px;\">\r\n        <el-col :span=\"24\">\r\n          <el-radio-group v-model=\"chartType\" @change=\"handleChartTypeChange\">\r\n            <el-radio-button label=\"line\">折线图</el-radio-button>\r\n            <el-radio-button label=\"bar\">柱状图</el-radio-button>\r\n            <el-radio-button label=\"scatter\">散点图</el-radio-button>\r\n            <el-radio-button label=\"radar\">雷达图</el-radio-button>\r\n            <el-radio-button label=\"heatmap\">热力图</el-radio-button>\r\n          </el-radio-group>\r\n          <el-button-group style=\"margin-left: 20px;\">\r\n            <el-button size=\"small\" @click=\"toggleDataTable\">{{ showDataTable ? '隐藏' : '显示' }}数据表</el-button>\r\n            <el-button size=\"small\" @click=\"toggleProjectDetails\" :disabled=\"selectedParamDetails.length === 0\">\r\n              {{ showProjectDetails ? '隐藏' : '显示' }}项目详情\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"toggleFullscreen\">全屏显示</el-button>\r\n          </el-button-group>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 参数详情信息卡片 -->\r\n    <el-card v-if=\"selectedParamDetails.length > 0 && showProjectDetails\" class=\"box-card param-details-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">📋 选中项目详情信息</span>\r\n        <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 10px;\">{{ selectedParamDetails.length }}项</el-tag>\r\n        <el-button type=\"text\" @click=\"showProjectDetails = false\" style=\"float: right; color: #909399;\">\r\n          <i class=\"el-icon-close\"></i>\r\n        </el-button>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\" v-for=\"(detail, index) in selectedParamDetails\" :key=\"index\">\r\n          <el-card class=\"param-detail-card\" shadow=\"hover\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span style=\"font-weight: bold; color: #409EFF;\">\r\n                <i class=\"el-icon-data-line\"></i>\r\n                {{ detail.paramNumber || detail.name }}\r\n              </span>\r\n              <el-tag size=\"mini\" type=\"success\" style=\"float: right;\" v-if=\"detail.testCount\">\r\n                {{ detail.testCount }}次测试\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 基本信息 -->\r\n            <div class=\"detail-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-info\"></i>\r\n                基本信息\r\n              </div>\r\n              <el-descriptions :column=\"2\" border size=\"small\">\r\n                <el-descriptions-item label=\"材料名称\" v-if=\"detail.materialName\">\r\n                  <el-tag type=\"primary\" size=\"mini\">{{ detail.materialName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"供应商\" v-if=\"detail.supplierName\">\r\n                  <el-tag type=\"success\" size=\"mini\">{{ detail.supplierName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"材料型号\" v-if=\"detail.materialModel\">\r\n                  <span>{{ detail.materialModel }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"工艺类型\" v-if=\"detail.processType\">\r\n                  <el-tag type=\"warning\" size=\"mini\">{{ detail.processType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"性能类型\" v-if=\"detail.performanceType\">\r\n                  <el-tag type=\"info\" size=\"mini\">{{ detail.performanceType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"参数编号\" v-if=\"detail.paramNumber\">\r\n                  <span>{{ detail.paramNumber }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"数据量\" v-if=\"detail.dataCount !== undefined\">\r\n                  <el-tag type=\"danger\" size=\"mini\">{{ detail.dataCount }}条</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"创建时间\" v-if=\"detail.createTime\">\r\n                  <span>{{ detail.createTime }}</span>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n\r\n            <!-- 统计信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.statistics\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                统计信息\r\n              </div>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.avgValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">平均值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.avgValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.maxValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最大值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.maxValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.minValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最小值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.minValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.stdDev !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">标准差</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.stdDev) }}</div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 参数明细 -->\r\n            <div class=\"detail-section\" v-if=\"detail.mainParams && detail.mainParams.length > 0\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-menu\"></i>\r\n                参数明细\r\n                <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 5px;\">{{ detail.mainParams.length }}个</el-tag>\r\n              </div>\r\n              <div class=\"params-container\">\r\n                <el-tooltip\r\n                  v-for=\"param in detail.mainParams\"\r\n                  :key=\"param.paramName\"\r\n                  :content=\"`${param.paramName}: ${param.paramValue || 'N/A'} ${param.unit || ''}`\"\r\n                  placement=\"top\"\r\n                >\r\n                  <el-tag\r\n                    size=\"mini\"\r\n                    :type=\"getParamTagType(param)\"\r\n                    style=\"margin-right: 5px; margin-bottom: 3px; cursor: pointer;\"\r\n                    @click=\"showParamDetail(param)\"\r\n                  >\r\n                    {{ param.paramName }}\r\n                    <span v-if=\"param.paramValue\" style=\"margin-left: 3px; opacity: 0.8;\">\r\n                      ({{ formatNumber(param.paramValue) }})\r\n                    </span>\r\n                  </el-tag>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 测试方案信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.testPlanInfo\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-document\"></i>\r\n                测试方案\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <label>方案编号：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.planCode }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" v-if=\"detail.testPlanInfo.testEquipment\">\r\n                <label>测试设备：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.testEquipment }}</span>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 图表区域 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">{{ chartTitle }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-tooltip content=\"图表说明\" placement=\"top\">\r\n            <el-button type=\"text\" icon=\"el-icon-question\" @click=\"showChartHelp\" />\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"chartLoading\" element-loading-text=\"正在生成图表...\">\r\n        <div\r\n          ref=\"chart\"\r\n          :style=\"{ height: chartHeight + 'px', width: '100%' }\"\r\n          v-show=\"!showDataTable\"\r\n        ></div>\r\n\r\n        <!-- 数据表格 -->\r\n        <el-table\r\n          v-show=\"showDataTable\"\r\n          :data=\"chartData\"\r\n          style=\"width: 100%\"\r\n          :max-height=\"chartHeight\"\r\n        >\r\n          <el-table-column\r\n            v-for=\"column in tableColumns\"\r\n            :key=\"column.prop\"\r\n            :prop=\"column.prop\"\r\n            :label=\"column.label\"\r\n            :width=\"column.width\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"column.prop === 'paramDetails'\">\r\n              <span v-if=\"scope.row.paramDetails && scope.row.paramDetails.length > 0\">\r\n                <el-tag\r\n                  v-for=\"(param, index) in scope.row.paramDetails\"\r\n                  :key=\"index\"\r\n                  size=\"mini\"\r\n                  type=\"info\"\r\n                  style=\"margin-right: 5px; margin-bottom: 2px;\"\r\n                >\r\n                  {{ param.paramName }}: {{ param.paramValue }}{{ param.unit }}\r\n                </el-tag>\r\n              </span>\r\n              <span v-else style=\"color: #909399;\">暂无参数</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\" v-if=\"statisticsData.length > 0\">\r\n      <el-col :span=\"6\" v-for=\"(stat, index) in statisticsData\" :key=\"index\">\r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-content\">\r\n            <div class=\"statistics-title\">{{ stat.title }}</div>\r\n            <div class=\"statistics-value\">{{ stat.value }}</div>\r\n            <div class=\"statistics-desc\">{{ stat.description }}</div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表说明对话框 -->\r\n    <el-dialog title=\"📊 图表说明\" :visible.sync=\"helpDialogVisible\" width=\"700px\" append-to-body>\r\n      <div class=\"chart-help-content\">\r\n        <h4>🎯 图表类型说明：</h4>\r\n        <ul>\r\n          <li><strong>📈 折线图：</strong>适用于展示数据随时间或其他连续变量的变化趋势，清晰显示数据走向</li>\r\n          <li><strong>📊 柱状图：</strong>适用于比较不同类别之间的数值大小，直观对比差异</li>\r\n          <li><strong>🔵 散点图：</strong>适用于展示两个变量之间的相关关系，发现数据规律</li>\r\n          <li><strong>🕸️ 雷达图：</strong>适用于多维度数据的综合对比，全面评估性能</li>\r\n          <li><strong>🌡️ 热力图：</strong>适用于展示数据的分布密度和相关性，识别热点区域</li>\r\n        </ul>\r\n\r\n        <h4>🔍 对比维度说明：</h4>\r\n        <ul>\r\n          <li><strong>📊 材料性能对比：</strong>比较不同材料的性能表现，识别最优材料</li>\r\n          <li><strong>🏭 供应商数据对比：</strong>比较不同供应商材料的质量差异，评估供应商可靠性</li>\r\n          <li><strong>🔢 参数编号对比：</strong>比较不同参数编号下的测试值趋势，分析参数影响</li>\r\n          <li><strong>⚙️ 工艺类型对比：</strong>比较不同工艺类型的效果，优化工艺流程</li>\r\n          <li><strong>📈 时间趋势分析：</strong>展示测试数据随时间的变化规律，预测发展趋势</li>\r\n          <li><strong>⚖️ 供应商vs测试值：</strong>对比供应商提供数据与实际测试结果的差异</li>\r\n        </ul>\r\n\r\n        <h4>💡 使用技巧：</h4>\r\n        <ul>\r\n          <li>将鼠标悬停在图表数据点上可查看详细信息和参数明细</li>\r\n          <li>点击参数标签可查看该参数的详细信息</li>\r\n          <li>使用\"显示数据表\"功能可查看原始数据</li>\r\n          <li>选择合适的图表类型能更好地展示数据特征</li>\r\n          <li>多选对比项目可进行横向比较分析</li>\r\n        </ul>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数详情对话框 -->\r\n    <el-dialog\r\n      title=\"📋 参数详细信息\"\r\n      :visible.sync=\"paramDetailDialogVisible\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      v-if=\"currentParamDetail\"\r\n    >\r\n      <div class=\"param-detail-content\">\r\n        <el-descriptions :column=\"2\" border size=\"small\">\r\n          <el-descriptions-item label=\"参数名称\">\r\n            <el-tag type=\"primary\">{{ currentParamDetail.paramName }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数数值\">\r\n            <span style=\"font-weight: bold; color: #67C23A;\">\r\n              {{ formatNumber(currentParamDetail.paramValue) }}\r\n            </span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数单位\" v-if=\"currentParamDetail.unit\">\r\n            {{ currentParamDetail.unit }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"数据类型\">\r\n            {{ typeof currentParamDetail.paramValue === 'number' ? '数值型' : '文本型' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\" v-if=\"currentParamDetail.createTime\">\r\n            {{ currentParamDetail.createTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\" v-if=\"currentParamDetail.updateTime\">\r\n            {{ currentParamDetail.updateTime }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <div v-if=\"currentParamDetail.remark\" style=\"margin-top: 15px;\">\r\n          <h4 style=\"color: #409EFF; margin-bottom: 8px;\">📝 备注信息：</h4>\r\n          <p style=\"background: #f5f7fa; padding: 10px; border-radius: 4px; margin: 0;\">\r\n            {{ currentParamDetail.remark }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { listTestResult, getTestResultOptions } from \"@/api/material/testResult\";\r\nimport { listMaterial } from \"@/api/material/material\";\r\nimport { listProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\n\r\nexport default {\r\n  name: \"MaterialTrend\",\r\n  data() {\r\n    return {\r\n      // 加载状态\r\n      loading: false,\r\n      // 图表实例\r\n      chart: null,\r\n      // 图表类型\r\n      chartType: 'line',\r\n      // 图表高度\r\n      chartHeight: 400,\r\n      // 图表标题\r\n      chartTitle: '数据趋势对比分析',\r\n      // 图表加载状态\r\n      chartLoading: false,\r\n      // 是否显示数据表\r\n      showDataTable: false,\r\n      // 是否全屏\r\n      isFullscreen: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      },\r\n\r\n      // 选项数据\r\n      paramNumberOptions: [],\r\n      materialOptions: [],\r\n      supplierOptions: [],\r\n      processTypeOptions: [],\r\n\r\n      // 图表数据\r\n      chartData: [],\r\n      tableColumns: [],\r\n\r\n      // 参数详情\r\n      selectedParamDetails: [],\r\n\r\n      // 统计数据\r\n      statisticsData: [],\r\n\r\n      // 帮助对话框\r\n      helpDialogVisible: false,\r\n\r\n      // 使用指南显示状态\r\n      showUsageGuide: false,\r\n\r\n      // 项目详情显示状态\r\n      showProjectDetails: false,\r\n\r\n      // 参数详情对话框\r\n      paramDetailDialogVisible: false,\r\n      currentParamDetail: null\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n    this.loadOptions();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化图表 */\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart);\r\n\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', () => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 加载选项数据 */\r\n    async loadOptions() {\r\n      try {\r\n        // 加载参数编号选项\r\n        const paramResponse = await listProcessParamGroup({});\r\n        this.paramNumberOptions = paramResponse.rows || [];\r\n\r\n        // 加载材料选项\r\n        const materialResponse = await listMaterial({});\r\n        this.materialOptions = materialResponse.rows || [];\r\n\r\n        // 加载供应商选项\r\n        const supplierResponse = await getTestResultOptions({ type: 'supplierName' });\r\n        this.supplierOptions = supplierResponse.data || [];\r\n\r\n        // 加载工艺类型选项\r\n        const processResponse = await getTestResultOptions({ type: 'processType' });\r\n        this.processTypeOptions = processResponse.data || [];\r\n\r\n      } catch (error) {\r\n        console.error('加载选项数据失败：', error);\r\n        this.$modal.msgError('加载选项数据失败');\r\n      }\r\n    },\r\n\r\n    /** 对比类型改变 */\r\n    handleCompareTypeChange(value) {\r\n      // 重置相关参数\r\n      this.queryParams.paramNumbers = [];\r\n      this.queryParams.materialNames = [];\r\n      this.queryParams.supplierNames = [];\r\n      this.queryParams.processTypes = [];\r\n      this.queryParams.dateRange = null;\r\n      this.queryParams.compareParam = null;\r\n\r\n      // 清空选中的参数详情和图表数据\r\n      this.selectedParamDetails = [];\r\n      this.chartData = [];\r\n      this.statisticsData = [];\r\n\r\n      // 更新图表标题\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 更新图表标题 */\r\n    updateChartTitle() {\r\n      const typeMap = {\r\n        'paramNumber': '参数编号对比分析',\r\n        'material': '材料性能对比分析',\r\n        'supplier': '供应商质量对比分析',\r\n        'processType': '工艺类型效果对比分析',\r\n        'timeTrend': '时间趋势对比分析'\r\n      };\r\n      this.chartTitle = typeMap[this.queryParams.compareType] || '对比分析图';\r\n    },\r\n\r\n    /** 图表类型改变 */\r\n    handleChartTypeChange(type) {\r\n      this.chartType = type;\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 查询数据 */\r\n    async handleQuery() {\r\n      if (!this.validateQuery()) {\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      this.chartLoading = true;\r\n\r\n      try {\r\n        // 根据对比类型获取不同的数据\r\n        let chartData = [];\r\n        let paramDetails = [];\r\n\r\n        switch (this.queryParams.compareType) {\r\n          case 'material':\r\n            chartData = await this.getMaterialCompareData();\r\n            break;\r\n          case 'supplier':\r\n            chartData = await this.getSupplierCompareData();\r\n            break;\r\n          case 'paramNumber':\r\n            chartData = await this.getParamNumberCompareData();\r\n            break;\r\n          case 'processType':\r\n            chartData = await this.getProcessTypeCompareData();\r\n            break;\r\n          case 'timeTrend':\r\n            chartData = await this.getTimeTrendData();\r\n            break;\r\n          case 'supplierVsTest':\r\n            chartData = await this.getSupplierVsTestData();\r\n            break;\r\n        }\r\n\r\n        this.chartData = chartData;\r\n        this.updateTableColumns();\r\n        this.renderChart();\r\n\r\n        // 更新选中参数详情\r\n        this.updateSelectedParamDetails();\r\n\r\n      } catch (error) {\r\n        console.error('获取对比数据失败：', error);\r\n        this.$modal.msgError('获取对比数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n        this.chartLoading = false;\r\n      }\r\n    },\r\n\r\n    /** 验证查询条件 */\r\n    validateQuery() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length === 0) {\r\n        this.$message.warning('请选择至少一个参数编号');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length === 0) {\r\n        this.$message.warning('请选择至少一个材料');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplier' && this.queryParams.supplierNames.length === 0) {\r\n        this.$message.warning('请选择至少一个供应商');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'processType' && this.queryParams.processTypes.length === 0) {\r\n        this.$message.warning('请选择至少一个工艺类型');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'timeTrend' && !this.queryParams.dateRange) {\r\n        this.$message.warning('请选择时间范围');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplierVsTest' && !this.queryParams.compareParam) {\r\n        this.$message.warning('请选择要对比的参数');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 获取材料对比数据 */\r\n    async getMaterialCompareData() {\r\n      const materialIds = this.queryParams.materialNames || [];\r\n      const compareData = [];\r\n\r\n      if (materialIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const materialId of materialIds) {\r\n        try {\r\n          // 通过材料ID查找对应的参数组，然后查找测试结果\r\n          const paramGroupResponse = await listProcessParamGroup({\r\n            materialId: materialId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroups = paramGroupResponse.rows || [];\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n\r\n          let allSupplierValues = [];\r\n          let allTestValues = [];\r\n\r\n          // 遍历该材料的所有参数组，获取测试结果\r\n          for (const group of paramGroups) {\r\n            const testResponse = await listTestResult({\r\n              groupId: group.groupId,\r\n              pageNum: 1,\r\n              pageSize: 1000\r\n            });\r\n\r\n            const testResults = testResponse.rows || [];\r\n            const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n            const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n            allSupplierValues = allSupplierValues.concat(supplierValues);\r\n            allTestValues = allTestValues.concat(testValues);\r\n          }\r\n\r\n          compareData.push({\r\n            name: material ? material.materialName : `材料${materialId}`,\r\n            supplier: material ? material.supplierName : '',\r\n            supplierAvg: allSupplierValues.length > 0 ? (allSupplierValues.reduce((a, b) => a + b, 0) / allSupplierValues.length).toFixed(2) : 0,\r\n            testAvg: allTestValues.length > 0 ? (allTestValues.reduce((a, b) => a + b, 0) / allTestValues.length).toFixed(2) : 0,\r\n            supplierMax: allSupplierValues.length > 0 ? Math.max(...allSupplierValues).toFixed(2) : 0,\r\n            testMax: allTestValues.length > 0 ? Math.max(...allTestValues).toFixed(2) : 0,\r\n            supplierMin: allSupplierValues.length > 0 ? Math.min(...allSupplierValues).toFixed(2) : 0,\r\n            testMin: allTestValues.length > 0 ? Math.min(...allTestValues).toFixed(2) : 0,\r\n            dataCount: allTestValues.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取材料${materialId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取供应商对比数据 */\r\n    async getSupplierCompareData() {\r\n      const suppliers = this.queryParams.supplierNames || [];\r\n      const compareData = [];\r\n\r\n      if (suppliers.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const supplier of suppliers) {\r\n        try {\r\n          const response = await listTestResult({\r\n            supplierName: supplier,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: supplier,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            accuracy: supplierValues.length > 0 && testValues.length > 0 ?\r\n              (100 - Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)) /\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length) * 100).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取供应商${supplier}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取参数编号对比数据 */\r\n    async getParamNumberCompareData() {\r\n      const paramGroupIds = this.queryParams.paramNumbers || [];\r\n      const compareData = [];\r\n\r\n      if (paramGroupIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const groupId of paramGroupIds) {\r\n        try {\r\n          // 获取测试结果数据\r\n          const response = await listTestResult({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          // 获取参数明细数据\r\n          const paramItemResponse = await listProcessParamItem({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroup = this.paramNumberOptions.find(p => p.groupId === groupId);\r\n          const testResults = response.rows || [];\r\n          const paramItems = paramItemResponse.rows || [];\r\n\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          // 格式化参数明细信息\r\n          const paramDetails = paramItems.map(item => ({\r\n            paramName: item.paramName || 'N/A',\r\n            paramValue: item.paramValue !== null && item.paramValue !== undefined ?\r\n              String(item.paramValue) : 'N/A',\r\n            unit: item.unit || ''\r\n          }));\r\n\r\n          compareData.push({\r\n            name: paramGroup ? paramGroup.paramNumber : `参数${groupId}`,\r\n            material: paramGroup ? paramGroup.materialName : '',\r\n            processType: paramGroup ? paramGroup.processType : '',\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            deviation: supplierValues.length > 0 && testValues.length > 0 ?\r\n              Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)).toFixed(2) : 0,\r\n            dataCount: testResults.length,\r\n            paramDetails: paramDetails, // 添加参数明细信息\r\n            groupId: groupId // 保存groupId用于后续使用\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取参数组${groupId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取工艺类型对比数据 */\r\n    async getProcessTypeCompareData() {\r\n      const processTypes = this.queryParams.processTypes || [];\r\n      const compareData = [];\r\n\r\n      if (processTypes.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const processType of processTypes) {\r\n        try {\r\n          const response = await listTestResult({\r\n            processType: processType,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: processType,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            stability: testValues.length > 1 ? this.calculateStandardDeviation(testValues).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取工艺类型${processType}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取时间趋势数据 */\r\n    async getTimeTrendData() {\r\n      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const [startDate, endDate] = this.queryParams.dateRange;\r\n        const response = await listTestResult({\r\n          startDate: startDate,\r\n          endDate: endDate,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const trendData = [];\r\n\r\n        // 按日期分组\r\n        const dateGroups = {};\r\n        testResults.forEach(result => {\r\n          const date = result.createTime ? result.createTime.split(' ')[0] : '';\r\n          if (date && !dateGroups[date]) {\r\n            dateGroups[date] = [];\r\n          }\r\n          if (date) {\r\n            dateGroups[date].push(result);\r\n          }\r\n        });\r\n\r\n        // 计算每日平均值\r\n        Object.keys(dateGroups).sort().forEach(date => {\r\n          const dayResults = dateGroups[date];\r\n          const testValues = dayResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          trendData.push({\r\n            date: date,\r\n            avgValue: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            count: dayResults.length\r\n          });\r\n        });\r\n\r\n        return trendData;\r\n      } catch (error) {\r\n        console.error('获取时间趋势数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 获取供应商vs测试值对比数据 */\r\n    async getSupplierVsTestData() {\r\n      const groupId = this.queryParams.compareParam;\r\n\r\n      if (!groupId) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const response = await listTestResult({\r\n          groupId: groupId,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const compareData = testResults.map(result => ({\r\n          name: result.materialName || '未知材料',\r\n          supplier: result.supplierName || '未知供应商',\r\n          supplierValue: parseFloat(result.supplierDatasheetVal) || 0,\r\n          testValue: parseFloat(result.testValue) || 0,\r\n          difference: Math.abs((parseFloat(result.supplierDatasheetVal) || 0) - (parseFloat(result.testValue) || 0)).toFixed(2),\r\n          createTime: result.createTime\r\n        }));\r\n\r\n        return compareData;\r\n      } catch (error) {\r\n        console.error('获取供应商vs测试值数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 计算标准差 */\r\n    calculateStandardDeviation(values) {\r\n      const avg = values.reduce((a, b) => a + b, 0) / values.length;\r\n      const squareDiffs = values.map(value => Math.pow(value - avg, 2));\r\n      const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;\r\n      return Math.sqrt(avgSquareDiff);\r\n    },\r\n\r\n    /** 格式化参数明细显示 */\r\n    formatParamDetails(row, column, cellValue) {\r\n      if (!cellValue || !Array.isArray(cellValue)) {\r\n        return '暂无参数';\r\n      }\r\n\r\n      return cellValue.map(param => {\r\n        let text = param.paramName + ': ' + param.paramValue;\r\n        if (param.unit) {\r\n          text += ' ' + param.unit;\r\n        }\r\n        return text;\r\n      }).join('; ');\r\n    },\r\n\r\n    /** 更新表格列 */\r\n    updateTableColumns() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      switch (compareType) {\r\n        case 'material':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplier':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '供应商', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'accuracy', label: '准确率(%)', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'paramNumber':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '参数编号', width: 120 },\r\n            { prop: 'material', label: '材料名称', width: 120 },\r\n            { prop: 'processType', label: '工艺类型', width: 100 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'deviation', label: '偏差', width: 80 },\r\n            { prop: 'paramDetails', label: '参数明细', width: 200 }\r\n          ];\r\n          break;\r\n        case 'processType':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '工艺类型', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'stability', label: '稳定性', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'timeTrend':\r\n          this.tableColumns = [\r\n            { prop: 'date', label: '日期', width: 120 },\r\n            { prop: 'avgValue', label: '平均值', width: 100 },\r\n            { prop: 'count', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplierVsTest':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierValue', label: '供应商值', width: 100 },\r\n            { prop: 'testValue', label: '测试值', width: 100 },\r\n            { prop: 'difference', label: '差值', width: 80 }\r\n          ];\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 渲染图表 */\r\n    renderChart() {\r\n      if (!this.chart || this.chartData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      let option = {};\r\n\r\n      switch (this.chartType) {\r\n        case 'line':\r\n          option = this.getLineChartOption();\r\n          break;\r\n        case 'bar':\r\n          option = this.getBarChartOption();\r\n          break;\r\n        case 'scatter':\r\n          option = this.getScatterChartOption();\r\n          break;\r\n        case 'radar':\r\n          option = this.getRadarChartOption();\r\n          break;\r\n        case 'heatmap':\r\n          option = this.getHeatmapChartOption();\r\n          break;\r\n      }\r\n\r\n      this.chart.setOption(option, true);\r\n    },\r\n\r\n    /** 获取折线图配置 */\r\n    getLineChartOption() {\r\n      // 根据对比类型生成不同的图表配置\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'timeTrend') {\r\n        // 时间趋势图\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(param => {\r\n                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.date)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '平均值'\r\n          },\r\n          series: [{\r\n            name: '平均值',\r\n            type: 'line',\r\n            data: this.chartData.map(item => item.avgValue),\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }]\r\n        };\r\n      } else {\r\n        // 其他对比类型的折线图\r\n        const self = this;\r\n\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            backgroundColor: 'rgba(50, 50, 50, 0.95)',\r\n            borderColor: '#409EFF',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); border-radius: 8px; padding: 12px;',\r\n            formatter: function(params) {\r\n              const dataIndex = params[0].dataIndex;\r\n              const currentData = self.chartData[dataIndex];\r\n\r\n              let result = `<div style=\"font-size: 14px; font-weight: bold; color: #409EFF; margin-bottom: 8px;\">\r\n                            📊 ${params[0].name}\r\n                          </div>`;\r\n\r\n              // 显示基本对比数据\r\n              params.forEach(param => {\r\n                const color = param.color;\r\n                result += `<div style=\"margin: 4px 0; display: flex; align-items: center;\">\r\n                          <span style=\"display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;\"></span>\r\n                          <span style=\"font-weight: 500;\">${param.seriesName}:</span>\r\n                          <span style=\"margin-left: 8px; color: #67C23A; font-weight: bold;\">${self.formatNumber(param.value)}</span>\r\n                        </div>`;\r\n              });\r\n\r\n              // 根据对比类型显示详细信息\r\n              if (currentData) {\r\n                result += '<div style=\"border-top: 1px solid #666; margin: 8px 0; padding-top: 8px;\">';\r\n\r\n                if (self.queryParams.compareType === 'paramNumber' && currentData.paramDetails) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📋 参数明细信息</div>';\r\n                  if (currentData.material) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">材料:</span> ${currentData.material}</div>`;\r\n                  }\r\n                  if (currentData.processType) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">工艺:</span> ${currentData.processType}</div>`;\r\n                  }\r\n                  if (currentData.paramDetails && currentData.paramDetails.length > 0) {\r\n                    result += '<div style=\"margin: 4px 0; color: #909399;\">参数列表:</div>';\r\n                    currentData.paramDetails.slice(0, 5).forEach(param => {\r\n                      result += `<div style=\"margin: 1px 0; padding-left: 12px; font-size: 11px;\">\r\n                                • ${param.paramName}: <span style=\"color: #67C23A;\">${self.formatNumber(param.paramValue)}</span>\r\n                                ${param.unit ? ' <span style=\"color: #909399;\">' + param.unit + '</span>' : ''}\r\n                              </div>`;\r\n                    });\r\n                    if (currentData.paramDetails.length > 5) {\r\n                      result += `<div style=\"margin: 2px 0; padding-left: 12px; color: #909399; font-size: 11px;\">\r\n                                ... 还有 ${currentData.paramDetails.length - 5} 个参数\r\n                              </div>`;\r\n                    }\r\n                  }\r\n                } else if (self.queryParams.compareType === 'material' && currentData.supplier) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">🏭 供应商信息</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">供应商:</span> ${currentData.supplier}</div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'supplier' && currentData.accuracy) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📈 质量指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">准确率:</span> <span style=\"color: ${currentData.accuracy > 90 ? '#67C23A' : currentData.accuracy > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.accuracy}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'processType' && currentData.stability) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">⚙️ 工艺指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">稳定性:</span> <span style=\"color: ${currentData.stability > 90 ? '#67C23A' : currentData.stability > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.stability}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                }\r\n\r\n                result += '</div>';\r\n              }\r\n\r\n              return result;\r\n            }\r\n          },\r\n          legend: {\r\n            top: '10%',\r\n            data: ['供应商数据', '测试数据']\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.name)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '数值'\r\n          },\r\n          series: [\r\n            {\r\n              name: '供应商数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.supplierAvg || 0),\r\n              smooth: true,\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '测试数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.testAvg || 0),\r\n              smooth: true,\r\n              symbol: 'triangle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    /** 获取柱状图配置 */\r\n    getBarChartOption() {\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params[0].name + '<br/>';\r\n\r\n            // 显示基本对比数据\r\n            params.forEach(param => {\r\n              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: ['供应商数据', '测试数据']\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.chartData.map(item => item.name),\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数值'\r\n        },\r\n        series: [\r\n          {\r\n            name: '供应商数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.supplierAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          {\r\n            name: '测试数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.testAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#91cc75'\r\n            }\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    /** 获取散点图配置 */\r\n    getScatterChartOption() {\r\n      const self = this;\r\n\r\n      // 散点图主要用于供应商vs测试值对比\r\n      const scatterData = this.chartData.map((item, index) => [\r\n        parseFloat(item.supplierValue || item.supplierAvg) || 0,\r\n        parseFloat(item.testValue || item.testAvg) || 0,\r\n        item.name, // 用于tooltip显示\r\n        index // 数据索引，用于获取详细信息\r\n      ]);\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const [supplierVal, testVal, name, dataIndex] = params.data;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = `${name}<br/>供应商值: ${supplierVal}<br/>测试值: ${testVal}<br/>差值: ${Math.abs(supplierVal - testVal).toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '供应商数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '测试数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        series: [{\r\n          name: '数据对比',\r\n          type: 'scatter',\r\n          data: scatterData,\r\n          symbolSize: 8,\r\n          itemStyle: {\r\n            color: '#5470c6'\r\n          }\r\n        }, {\r\n          name: '理想线',\r\n          type: 'line',\r\n          data: [[0, 0], [Math.max(...scatterData.map(d => d[0])), Math.max(...scatterData.map(d => d[0]))]],\r\n          lineStyle: {\r\n            color: '#ff6b6b',\r\n            type: 'dashed'\r\n          },\r\n          symbol: 'none'\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取雷达图配置 */\r\n    getRadarChartOption() {\r\n      // 雷达图用于多维度对比，基于chartData生成指标\r\n      const indicators = [\r\n        { name: '供应商平均值', max: 100 },\r\n        { name: '测试平均值', max: 100 },\r\n        { name: '数据量', max: 50 },\r\n        { name: '准确率', max: 100 },\r\n        { name: '稳定性', max: 10 }\r\n      ];\r\n\r\n      const radarData = this.chartData.map(item => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const dataCount = parseInt(item.dataCount) || 0;\r\n        const accuracy = parseFloat(item.accuracy) || 0;\r\n        const stability = parseFloat(item.stability) || 0;\r\n\r\n        return {\r\n          name: item.name,\r\n          value: [\r\n            Math.min(supplierAvg, 100),\r\n            Math.min(testAvg, 100),\r\n            Math.min(dataCount, 50),\r\n            Math.min(accuracy, 100),\r\n            Math.min(stability, 10)\r\n          ]\r\n        };\r\n      });\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const dataIndex = params.dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params.name + '<br/>';\r\n\r\n            // 显示雷达图数据\r\n            const indicators = ['供应商平均值', '测试平均值', '数据量', '准确率', '稳定性'];\r\n            params.value.forEach((value, index) => {\r\n              result += indicators[index] + ': ' + value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: radarData.map(item => item.name)\r\n        },\r\n        radar: {\r\n          indicator: indicators,\r\n          radius: '60%'\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          data: radarData\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取热力图配置 */\r\n    getHeatmapChartOption() {\r\n      // 热力图用于展示数据密度和分布\r\n      const xAxisData = [...new Set(this.chartData.map(item => item.name))];\r\n      const yAxisData = ['供应商数据', '测试数据', '偏差'];\r\n\r\n      const heatmapData = [];\r\n      this.chartData.forEach((item, xIndex) => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const deviation = Math.abs(supplierAvg - testAvg);\r\n\r\n        heatmapData.push([xIndex, 0, supplierAvg]); // 供应商数据\r\n        heatmapData.push([xIndex, 1, testAvg]);     // 测试数据\r\n        heatmapData.push([xIndex, 2, deviation]);   // 偏差\r\n      });\r\n\r\n      const maxValue = Math.max(...heatmapData.map(d => d[2]));\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          position: 'top',\r\n          formatter: function(params) {\r\n            const [x, y, value] = params.data;\r\n            const xLabel = xAxisData[x];\r\n            const yLabel = yAxisData[y];\r\n            const currentData = self.chartData[x];\r\n\r\n            let result = `${xLabel}<br/>${yLabel}: ${value.toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        grid: {\r\n          height: '50%',\r\n          top: '15%'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: yAxisData\r\n        },\r\n        visualMap: {\r\n          min: 0,\r\n          max: maxValue || 100,\r\n          calculable: true,\r\n          orient: 'horizontal',\r\n          left: 'center',\r\n          bottom: '5%',\r\n          inRange: {\r\n            color: ['#50a3ba', '#eac736', '#d94e5d']\r\n          }\r\n        },\r\n        series: [{\r\n          type: 'heatmap',\r\n          data: heatmapData,\r\n          label: {\r\n            show: true,\r\n            formatter: function(params) {\r\n              return params.data[2].toFixed(1);\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          }\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 重置查询 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams = {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      };\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 刷新图表 */\r\n    refreshChart() {\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 导出图表 */\r\n    exportChart() {\r\n      if (!this.chart) {\r\n        this.$message.warning('请先生成图表');\r\n        return;\r\n      }\r\n\r\n      const url = this.chart.getDataURL({\r\n        type: 'png',\r\n        pixelRatio: 2,\r\n        backgroundColor: '#fff'\r\n      });\r\n\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${this.chartTitle}_${new Date().getTime()}.png`;\r\n      link.click();\r\n    },\r\n\r\n    /** 切换数据表显示 */\r\n    toggleDataTable() {\r\n      this.showDataTable = !this.showDataTable;\r\n      if (!this.showDataTable && this.chart) {\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 切换项目详情显示 */\r\n    toggleProjectDetails() {\r\n      this.showProjectDetails = !this.showProjectDetails;\r\n    },\r\n\r\n    /** 切换全屏显示 */\r\n    toggleFullscreen() {\r\n      if (this.isFullscreen) {\r\n        this.chartHeight = 400;\r\n        this.isFullscreen = false;\r\n      } else {\r\n        this.chartHeight = window.innerHeight - 200;\r\n        this.isFullscreen = true;\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 显示图表帮助 */\r\n    showChartHelp() {\r\n      this.helpDialogVisible = true;\r\n    },\r\n\r\n    /** 格式化数字显示 */\r\n    formatNumber(value) {\r\n      if (value === null || value === undefined || isNaN(value)) {\r\n        return 'N/A';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (num === 0) return '0';\r\n      if (Math.abs(num) >= 1000000) {\r\n        return (num / 1000000).toFixed(2) + 'M';\r\n      } else if (Math.abs(num) >= 1000) {\r\n        return (num / 1000).toFixed(2) + 'K';\r\n      } else if (Math.abs(num) < 1) {\r\n        return num.toFixed(4);\r\n      } else {\r\n        return num.toFixed(2);\r\n      }\r\n    },\r\n\r\n    /** 获取参数标签类型 */\r\n    getParamTagType(param) {\r\n      if (!param.paramValue) return '';\r\n      const value = parseFloat(param.paramValue);\r\n      if (isNaN(value)) return '';\r\n\r\n      // 根据参数值范围设置不同颜色\r\n      if (value > 100) return 'danger';\r\n      if (value > 50) return 'warning';\r\n      if (value > 10) return 'success';\r\n      return 'info';\r\n    },\r\n\r\n    /** 显示参数详情 */\r\n    showParamDetail(param) {\r\n      this.currentParamDetail = param;\r\n      this.paramDetailDialogVisible = true;\r\n    },\r\n\r\n    /** 更新选中参数详情 */\r\n    updateSelectedParamDetails() {\r\n      // 根据当前选择的对比类型和选项，更新参数详情信息\r\n      const { compareType } = this.queryParams;\r\n      this.selectedParamDetails = [];\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length > 0) {\r\n        this.queryParams.materialNames.forEach(materialId => {\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n          if (material) {\r\n            this.selectedParamDetails.push({\r\n              name: material.materialName,\r\n              materialName: material.materialName,\r\n              supplierName: material.supplierName,\r\n              processType: material.processType,\r\n              testCount: material.testCount || 0,\r\n              statistics: material.statistics\r\n            });\r\n          }\r\n        });\r\n      } else if (compareType === 'supplier' && this.queryParams.supplierNames.length > 0) {\r\n        this.queryParams.supplierNames.forEach(supplier => {\r\n          this.selectedParamDetails.push({\r\n            name: supplier,\r\n            supplierName: supplier,\r\n            testCount: 0 // 这里可以从API获取实际数据\r\n          });\r\n        });\r\n      } else if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length > 0) {\r\n        this.queryParams.paramNumbers.forEach(paramId => {\r\n          const param = this.paramNumberOptions.find(p => p.groupId === paramId);\r\n          if (param) {\r\n            this.selectedParamDetails.push({\r\n              name: param.paramNumber,\r\n              paramNumber: param.paramNumber,\r\n              materialName: param.materialName,\r\n              processType: param.processType,\r\n              mainParams: param.paramItems || [],\r\n              testCount: param.testCount || 0\r\n            });\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-guide-btn {\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-guide-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.guide-btn {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n}\r\n\r\n.export-btn {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\r\n  border: none;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n}\r\n\r\n.statistics-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.statistics-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.statistics-content {\r\n  padding: 20px;\r\n}\r\n\r\n.statistics-title {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.statistics-desc {\r\n  font-size: 12px;\r\n  color: #C0C4CC;\r\n}\r\n\r\n.chart-help-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.chart-help-content h4 {\r\n  color: #409EFF;\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.chart-help-content ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.chart-help-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.chart-help-content strong {\r\n  color: #303133;\r\n}\r\n\r\n/* 使用指南样式 */\r\n.usage-guide-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.usage-guide-card .el-card__header {\r\n  background: transparent;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.guide-item h4 {\r\n  color: #fff;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.guide-item p {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n}\r\n\r\n/* 参数详情卡片样式 */\r\n.param-details-card {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background: white;\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.param-detail-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.detail-section:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.section-title {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-icon {\r\n  color: #909399;\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 60px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 8px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 10px;\r\n  color: #909399;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.params-container {\r\n  max-height: 80px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.params-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiiBA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,aAAA;MACA;MACAC,YAAA;MAEA;MACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,YAAA;MACA;MAEA;MACAC,kBAAA;MACAC,eAAA;MACAC,eAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,YAAA;MAEA;MACAC,oBAAA;MAEA;MACAC,cAAA;MAEA;MACAC,iBAAA;MAEA;MACAC,cAAA;MAEA;MACAC,kBAAA;MAEA;MACAC,wBAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAA/B,KAAA;MACA,KAAAA,KAAA,CAAAgC,OAAA;IACA;EACA;EACAC,OAAA;IACA,YACAJ,SAAA,WAAAA,UAAA;MAAA,IAAAK,KAAA;MACA,KAAAlC,KAAA,GAAAV,OAAA,CAAA6C,IAAA,MAAAC,KAAA,CAAApC,KAAA;;MAEA;MACAqC,MAAA,CAAAC,gBAAA;QACA,IAAAJ,KAAA,CAAAlC,KAAA;UACAkC,KAAA,CAAAlC,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,aACAT,WAAA,WAAAA,YAAA;MAAA,IAAAU,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,EAAA;QAAA,WAAAP,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAC,wCAAA;YAAA;cAAAT,aAAA,GAAAM,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAzB,kBAAA,GAAA+B,aAAA,CAAAW,IAAA;;cAEA;cAAAL,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAI,sBAAA;YAAA;cAAAX,gBAAA,GAAAK,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAxB,eAAA,GAAA+B,gBAAA,CAAAU,IAAA;;cAEA;cAAAL,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAK,gCAAA;gBAAAC,IAAA;cAAA;YAAA;cAAAZ,gBAAA,GAAAI,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAvB,eAAA,GAAA+B,gBAAA,CAAAlD,IAAA;;cAEA;cAAAsD,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAK,gCAAA;gBAAAC,IAAA;cAAA;YAAA;cAAAX,eAAA,GAAAG,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAtB,kBAAA,GAAA+B,eAAA,CAAAnD,IAAA;cAAAsD,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAGAK,OAAA,CAAAC,KAAA,cAAAZ,EAAA;cACAV,MAAA,CAAAuB,MAAA,CAAAC,QAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAa,CAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IAEA,aACAqB,uBAAA,WAAAA,wBAAAC,KAAA;MACA;MACA,KAAA5D,WAAA,CAAAE,YAAA;MACA,KAAAF,WAAA,CAAAG,aAAA;MACA,KAAAH,WAAA,CAAAI,aAAA;MACA,KAAAJ,WAAA,CAAAK,YAAA;MACA,KAAAL,WAAA,CAAAM,SAAA;MACA,KAAAN,WAAA,CAAAO,YAAA;;MAEA;MACA,KAAAO,oBAAA;MACA,KAAAF,SAAA;MACA,KAAAG,cAAA;;MAEA;MACA,KAAA8C,gBAAA;IACA;IAEA,aACAA,gBAAA,WAAAA,iBAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAlE,UAAA,GAAAkE,OAAA,MAAA9D,WAAA,CAAAC,WAAA;IACA;IAEA,aACA8D,qBAAA,WAAAA,sBAAAV,IAAA;MACA,KAAA3D,SAAA,GAAA2D,IAAA;MACA,SAAAzC,SAAA,CAAAoD,MAAA;QACA,KAAAC,WAAA;MACA;IACA;IAEA,WACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+B,SAAA;QAAA,IAAAxD,SAAA,EAAAyD,YAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA4B,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,CAAA,GAAA0B,SAAA,CAAAzB,CAAA;YAAA;cAAA,IACAoB,MAAA,CAAAM,aAAA;gBAAAD,SAAA,CAAAzB,CAAA;gBAAA;cAAA;cAAA,OAAAyB,SAAA,CAAAd,CAAA;YAAA;cAIAS,MAAA,CAAA3E,OAAA;cACA2E,MAAA,CAAAtE,YAAA;cAAA2E,SAAA,CAAA1B,CAAA;cAGA;cACAlC,SAAA;cACAyD,YAAA;cAAAC,GAAA,GAEAH,MAAA,CAAAnE,WAAA,CAAAC,WAAA;cAAAuE,SAAA,CAAAzB,CAAA,GAAAuB,GAAA,KACA,iBAAAA,GAAA,KAGA,iBAAAA,GAAA,KAGA,oBAAAA,GAAA,KAGA,oBAAAA,GAAA,KAGA,mBAAAA,GAAA,KAGA;cAAA;YAAA;cAAAE,SAAA,CAAAzB,CAAA;cAAA,OAdAoB,MAAA,CAAAO,sBAAA;YAAA;cAAA9D,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAQ,sBAAA;YAAA;cAAA/D,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAS,yBAAA;YAAA;cAAAhE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAU,yBAAA;YAAA;cAAAjE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAW,gBAAA;YAAA;cAAAlE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAY,qBAAA;YAAA;cAAAnE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAIAS,MAAA,CAAAvD,SAAA,GAAAA,SAAA;cACAuD,MAAA,CAAAa,kBAAA;cACAb,MAAA,CAAAF,WAAA;;cAEA;cACAE,MAAA,CAAAc,0BAAA;cAAAT,SAAA,CAAAzB,CAAA;cAAA;YAAA;cAAAyB,SAAA,CAAA1B,CAAA;cAAAyB,GAAA,GAAAC,SAAA,CAAAvB,CAAA;cAGAK,OAAA,CAAAC,KAAA,cAAAgB,GAAA;cACAJ,MAAA,CAAAX,MAAA,CAAAC,QAAA;YAAA;cAAAe,SAAA,CAAA1B,CAAA;cAEAqB,MAAA,CAAA3E,OAAA;cACA2E,MAAA,CAAAtE,YAAA;cAAA,OAAA2E,SAAA,CAAAU,CAAA;YAAA;cAAA,OAAAV,SAAA,CAAAd,CAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IAEA;IAEA,aACAK,aAAA,WAAAA,cAAA;MACA,IAAAxE,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,IAAAA,WAAA,2BAAAD,WAAA,CAAAE,YAAA,CAAA8D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,wBAAAD,WAAA,CAAAG,aAAA,CAAA6D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,wBAAAD,WAAA,CAAAI,aAAA,CAAA4D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,2BAAAD,WAAA,CAAAK,YAAA,CAAA2D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,0BAAAD,WAAA,CAAAM,SAAA;QACA,KAAA6E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,+BAAAD,WAAA,CAAAO,YAAA;QACA,KAAA4E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA;IACA;IAEA,eACAV,sBAAA,WAAAA,uBAAA;MAAA,IAAAW,MAAA;MAAA,WAAAnD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiD,SAAA;QAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAAxD,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAiD,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,CAAA,GAAA+C,SAAA,CAAA9C,CAAA;YAAA;cACAwC,WAAA,GAAAF,MAAA,CAAArF,WAAA,CAAAG,aAAA;cACAqF,WAAA;cAAA,MAEAD,WAAA,CAAAvB,MAAA;gBAAA6B,SAAA,CAAA9C,CAAA;gBAAA;cAAA;cAAA,OAAA8C,SAAA,CAAAnC,CAAA,IACA8B,WAAA;YAAA;cAAAC,SAAA,OAAAK,2BAAA,CAAA3D,OAAA,EAGAoD,WAAA;cAAAM,SAAA,CAAA/C,CAAA;cAAA6C,KAAA,oBAAAvD,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAsD,MAAA;gBAAA,IAAAI,UAAA,EAAAC,kBAAA,EAAAC,WAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,GAAA;gBAAA,WAAAzE,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAkE,SAAA;kBAAA,kBAAAA,SAAA,CAAAhE,CAAA,GAAAgE,SAAA,CAAA/D,CAAA;oBAAA;sBAAAgD,UAAA,GAAAL,KAAA,CAAA9B,KAAA;sBAAAkD,SAAA,CAAAhE,CAAA;sBAAAgE,SAAA,CAAA/D,CAAA;sBAAA,OAGA,IAAAC,wCAAA;wBACA+C,UAAA,EAAAA,UAAA;wBACAgB,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAhB,kBAAA,GAAAc,SAAA,CAAA7D,CAAA;sBAMAgD,WAAA,GAAAD,kBAAA,CAAA9C,IAAA;sBACAgD,QAAA,GAAAb,MAAA,CAAA5E,eAAA,CAAAwG,IAAA,WAAA5E,CAAA;wBAAA,OAAAA,CAAA,CAAA0D,UAAA,KAAAA,UAAA;sBAAA;sBAEAI,iBAAA;sBACAC,aAAA,OAEA;sBAAAC,UAAA,OAAAP,2BAAA,CAAA3D,OAAA,EACA8D,WAAA;sBAAAa,SAAA,CAAAhE,CAAA;sBAAAuD,UAAA,CAAAa,CAAA;oBAAA;sBAAA,KAAAZ,MAAA,GAAAD,UAAA,CAAAtD,CAAA,IAAAoE,IAAA;wBAAAL,SAAA,CAAA/D,CAAA;wBAAA;sBAAA;sBAAAwD,KAAA,GAAAD,MAAA,CAAA1C,KAAA;sBAAAkD,SAAA,CAAA/D,CAAA;sBAAA,OACA,IAAAqE,0BAAA;wBACAC,OAAA,EAAAd,KAAA,CAAAc,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAR,YAAA,GAAAM,SAAA,CAAA7D,CAAA;sBAMAwD,WAAA,GAAAD,YAAA,CAAAtD,IAAA;sBACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;sBAAA,GAAAC,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;sBAAA,GAAAF,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBAEAkD,iBAAA,GAAAA,iBAAA,CAAA0B,MAAA,CAAAnB,cAAA;sBACAN,aAAA,GAAAA,aAAA,CAAAyB,MAAA,CAAAlB,UAAA;oBAAA;sBAAAG,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAAhE,CAAA;sBAAA8D,GAAA,GAAAE,SAAA,CAAA7D,CAAA;sBAAAoD,UAAA,CAAAyB,CAAA,CAAAlB,GAAA;oBAAA;sBAAAE,SAAA,CAAAhE,CAAA;sBAAAuD,UAAA,CAAAnB,CAAA;sBAAA,OAAA4B,SAAA,CAAA5B,CAAA;oBAAA;sBAGAM,WAAA,CAAAuC,IAAA;wBACAzI,IAAA,EAAA4G,QAAA,GAAAA,QAAA,CAAA8B,YAAA,kBAAAH,MAAA,CAAA9B,UAAA;wBACAkC,QAAA,EAAA/B,QAAA,GAAAA,QAAA,CAAAgC,YAAA;wBACAC,WAAA,EAAAhC,iBAAA,CAAAnC,MAAA,QAAAmC,iBAAA,CAAAiC,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAAlC,iBAAA,CAAAnC,MAAA,EAAAsE,OAAA;wBACAC,OAAA,EAAAnC,aAAA,CAAApC,MAAA,QAAAoC,aAAA,CAAAgC,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAAjC,aAAA,CAAApC,MAAA,EAAAsE,OAAA;wBACAE,WAAA,EAAArC,iBAAA,CAAAnC,MAAA,OAAAyE,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAgE,iBAAA,GAAAmC,OAAA;wBACAO,OAAA,EAAAzC,aAAA,CAAApC,MAAA,OAAAyE,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAiE,aAAA,GAAAkC,OAAA;wBACAQ,WAAA,EAAA3C,iBAAA,CAAAnC,MAAA,OAAAyE,IAAA,CAAAM,GAAA,CAAAJ,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAgE,iBAAA,GAAAmC,OAAA;wBACAU,OAAA,EAAA5C,aAAA,CAAApC,MAAA,OAAAyE,IAAA,CAAAM,GAAA,CAAAJ,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAiE,aAAA,GAAAkC,OAAA;wBACAW,SAAA,EAAA7C,aAAA,CAAApC;sBACA;sBAAA8C,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAAhE,CAAA;sBAAA+D,GAAA,GAAAC,SAAA,CAAA7D,CAAA;sBAEAK,OAAA,CAAAC,KAAA,4BAAAsE,MAAA,CAAA9B,UAAA,qCAAAc,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAApD,CAAA;kBAAA;gBAAA,GAAAiC,KAAA;cAAA;cAAAF,SAAA,CAAAyB,CAAA;YAAA;cAAA,KAAAxB,KAAA,GAAAD,SAAA,CAAA1C,CAAA,IAAAoE,IAAA;gBAAAtB,SAAA,CAAA9C,CAAA;gBAAA;cAAA;cAAA,OAAA8C,SAAA,CAAAqD,CAAA,KAAAC,mBAAA,CAAAhH,OAAA,EAAAwD,KAAA;YAAA;cAAAE,SAAA,CAAA9C,CAAA;cAAA;YAAA;cAAA8C,SAAA,CAAA9C,CAAA;cAAA;YAAA;cAAA8C,SAAA,CAAA/C,CAAA;cAAA8C,GAAA,GAAAC,SAAA,CAAA5C,CAAA;cAAAwC,SAAA,CAAAqC,CAAA,CAAAlC,GAAA;YAAA;cAAAC,SAAA,CAAA/C,CAAA;cAAA2C,SAAA,CAAAP,CAAA;cAAA,OAAAW,SAAA,CAAAX,CAAA;YAAA;cAAA,OAAAW,SAAA,CAAAnC,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAAF,QAAA;MAAA;IACA;IAEA,gBACAX,sBAAA,WAAAA,uBAAA;MAAA,IAAAyE,MAAA;MAAA,WAAAlH,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgH,SAAA;QAAA,IAAAC,SAAA,EAAA9D,WAAA,EAAA+D,UAAA,EAAAC,MAAA,EAAAvB,QAAA,EAAAwB,QAAA,EAAAhD,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA+C,GAAA,EAAAC,GAAA;QAAA,WAAAvH,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAgH,SAAA;UAAA,kBAAAA,SAAA,CAAA9G,CAAA,GAAA8G,SAAA,CAAA7G,CAAA;YAAA;cACAuG,SAAA,GAAAF,MAAA,CAAApJ,WAAA,CAAAI,aAAA;cACAoF,WAAA;cAAA,MAEA8D,SAAA,CAAAtF,MAAA;gBAAA4F,SAAA,CAAA7G,CAAA;gBAAA;cAAA;cAAA,OAAA6G,SAAA,CAAAlG,CAAA,IACA8B,WAAA;YAAA;cAAA+D,UAAA,OAAAzD,2BAAA,CAAA3D,OAAA,EAGAmH,SAAA;cAAAM,SAAA,CAAA9G,CAAA;cAAAyG,UAAA,CAAArC,CAAA;YAAA;cAAA,KAAAsC,MAAA,GAAAD,UAAA,CAAAxG,CAAA,IAAAoE,IAAA;gBAAAyC,SAAA,CAAA7G,CAAA;gBAAA;cAAA;cAAAkF,QAAA,GAAAuB,MAAA,CAAA5F,KAAA;cAAAgG,SAAA,CAAA9G,CAAA;cAAA8G,SAAA,CAAA7G,CAAA;cAAA,OAEA,IAAAqE,0BAAA;gBACAc,YAAA,EAAAD,QAAA;gBACAlB,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAAG,SAAA,CAAA3G,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;cAAA,GAAAC,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;cAAA,GAAAF,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cAEAuC,WAAA,CAAAuC,IAAA;gBACAzI,IAAA,EAAA2I,QAAA;gBACAE,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;gBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;gBACAuB,QAAA,EAAAnD,cAAA,CAAA1C,MAAA,QAAA2C,UAAA,CAAA3C,MAAA,OACA,OAAAyE,IAAA,CAAAqB,GAAA,CAAApD,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,GACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,KACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,SAAAsE,OAAA;gBACAW,SAAA,EAAAxC,WAAA,CAAAzC;cACA;cAAA4F,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,CAAA;cAAA4G,GAAA,GAAAE,SAAA,CAAA3G,CAAA;cAEAK,OAAA,CAAAC,KAAA,kCAAAsE,MAAA,CAAAI,QAAA,qCAAAyB,GAAA;YAAA;cAAAE,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,CAAA;cAAA6G,GAAA,GAAAC,SAAA,CAAA3G,CAAA;cAAAsG,UAAA,CAAAzB,CAAA,CAAA6B,GAAA;YAAA;cAAAC,SAAA,CAAA9G,CAAA;cAAAyG,UAAA,CAAArE,CAAA;cAAA,OAAA0E,SAAA,CAAA1E,CAAA;YAAA;cAAA,OAAA0E,SAAA,CAAAlG,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAA6D,QAAA;MAAA;IACA;IAEA,iBACAzE,yBAAA,WAAAA,0BAAA;MAAA,IAAAmF,MAAA;MAAA,WAAA7H,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA2H,SAAA;QAAA,IAAAC,aAAA,EAAAzE,WAAA,EAAA0E,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,GAAA;QAAA,WAAAjI,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA0H,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,CAAA,GAAAwH,SAAA,CAAAvH,CAAA;YAAA;cACAkH,aAAA,GAAAF,MAAA,CAAA/J,WAAA,CAAAE,YAAA;cACAsF,WAAA;cAAA,MAEAyE,aAAA,CAAAjG,MAAA;gBAAAsG,SAAA,CAAAvH,CAAA;gBAAA;cAAA;cAAA,OAAAuH,SAAA,CAAA5G,CAAA,IACA8B,WAAA;YAAA;cAAA0E,UAAA,OAAApE,2BAAA,CAAA3D,OAAA,EAGA8H,aAAA;cAAAK,SAAA,CAAAxH,CAAA;cAAAsH,MAAA,oBAAAhI,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+H,OAAA;gBAAA,IAAA/C,OAAA,EAAAoC,QAAA,EAAAc,iBAAA,EAAAC,UAAA,EAAA/D,WAAA,EAAAgE,UAAA,EAAA/D,cAAA,EAAAC,UAAA,EAAAtC,YAAA,EAAAqG,GAAA;gBAAA,WAAAtI,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA+H,SAAA;kBAAA,kBAAAA,SAAA,CAAA7H,CAAA,GAAA6H,SAAA,CAAA5H,CAAA;oBAAA;sBAAAsE,OAAA,GAAA8C,MAAA,CAAAvG,KAAA;sBAAA+G,SAAA,CAAA7H,CAAA;sBAAA6H,SAAA,CAAA5H,CAAA;sBAAA,OAGA,IAAAqE,0BAAA;wBACAC,OAAA,EAAAA,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAyC,QAAA,GAAAkB,SAAA,CAAA1H,CAAA;sBAAA0H,SAAA,CAAA5H,CAAA;sBAAA,OAOA,IAAA6H,sCAAA;wBACAvD,OAAA,EAAAA,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAuD,iBAAA,GAAAI,SAAA,CAAA1H,CAAA;sBAMAuH,UAAA,GAAAT,MAAA,CAAAvJ,kBAAA,CAAAyG,IAAA,WAAAnE,CAAA;wBAAA,OAAAA,CAAA,CAAAuE,OAAA,KAAAA,OAAA;sBAAA;sBACAZ,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;sBACAuH,UAAA,GAAAF,iBAAA,CAAArH,IAAA;sBAEAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;sBAAA,GAAAC,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;sBAAA,GAAAF,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA,IAEA;sBACAoB,YAAA,GAAAoG,UAAA,CAAAnD,GAAA,WAAAuD,IAAA;wBAAA;0BACAC,SAAA,EAAAD,IAAA,CAAAC,SAAA;0BACAC,UAAA,EAAAF,IAAA,CAAAE,UAAA,aAAAF,IAAA,CAAAE,UAAA,KAAAC,SAAA,GACAC,MAAA,CAAAJ,IAAA,CAAAE,UAAA;0BACAG,IAAA,EAAAL,IAAA,CAAAK,IAAA;wBACA;sBAAA;sBAEA1F,WAAA,CAAAuC,IAAA;wBACAzI,IAAA,EAAAkL,UAAA,GAAAA,UAAA,CAAAW,WAAA,kBAAAtD,MAAA,CAAAR,OAAA;wBACAnB,QAAA,EAAAsE,UAAA,GAAAA,UAAA,CAAAxC,YAAA;wBACAoD,WAAA,EAAAZ,UAAA,GAAAA,UAAA,CAAAY,WAAA;wBACAjD,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;wBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;wBACA+C,SAAA,EAAA3E,cAAA,CAAA1C,MAAA,QAAA2C,UAAA,CAAA3C,MAAA,OACAyE,IAAA,CAAAqB,GAAA,CAAApD,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA3B,cAAA,CAAA1C,MAAA,GACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;wBACAW,SAAA,EAAAxC,WAAA,CAAAzC,MAAA;wBACAK,YAAA,EAAAA,YAAA;wBAAA;wBACAgD,OAAA,EAAAA,OAAA;sBACA;sBAAAsD,SAAA,CAAA5H,CAAA;sBAAA;oBAAA;sBAAA4H,SAAA,CAAA7H,CAAA;sBAAA4H,GAAA,GAAAC,SAAA,CAAA1H,CAAA;sBAEAK,OAAA,CAAAC,KAAA,kCAAAsE,MAAA,CAAAR,OAAA,qCAAAqD,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAAjH,CAAA;kBAAA;gBAAA,GAAA0G,MAAA;cAAA;cAAAF,UAAA,CAAAhD,CAAA;YAAA;cAAA,KAAAiD,MAAA,GAAAD,UAAA,CAAAnH,CAAA,IAAAoE,IAAA;gBAAAmD,SAAA,CAAAvH,CAAA;gBAAA;cAAA;cAAA,OAAAuH,SAAA,CAAApB,CAAA,KAAAC,mBAAA,CAAAhH,OAAA,EAAAiI,MAAA;YAAA;cAAAE,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAxH,CAAA;cAAAuH,GAAA,GAAAC,SAAA,CAAArH,CAAA;cAAAiH,UAAA,CAAApC,CAAA,CAAAuC,GAAA;YAAA;cAAAC,SAAA,CAAAxH,CAAA;cAAAoH,UAAA,CAAAhF,CAAA;cAAA,OAAAoF,SAAA,CAAApF,CAAA;YAAA;cAAA,OAAAoF,SAAA,CAAA5G,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA;IACA;IAEA,iBACAnF,yBAAA,WAAAA,0BAAA;MAAA,IAAAyG,MAAA;MAAA,WAAApJ,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAkJ,SAAA;QAAA,IAAAlL,YAAA,EAAAmF,WAAA,EAAAgG,UAAA,EAAAC,MAAA,EAAAL,WAAA,EAAA3B,QAAA,EAAAhD,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA+E,GAAA,EAAAC,IAAA;QAAA,WAAAvJ,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAgJ,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,CAAA,GAAA8I,SAAA,CAAA7I,CAAA;YAAA;cACA1C,YAAA,GAAAiL,MAAA,CAAAtL,WAAA,CAAAK,YAAA;cACAmF,WAAA;cAAA,MAEAnF,YAAA,CAAA2D,MAAA;gBAAA4H,SAAA,CAAA7I,CAAA;gBAAA;cAAA;cAAA,OAAA6I,SAAA,CAAAlI,CAAA,IACA8B,WAAA;YAAA;cAAAgG,UAAA,OAAA1F,2BAAA,CAAA3D,OAAA,EAGA9B,YAAA;cAAAuL,SAAA,CAAA9I,CAAA;cAAA0I,UAAA,CAAAtE,CAAA;YAAA;cAAA,KAAAuE,MAAA,GAAAD,UAAA,CAAAzI,CAAA,IAAAoE,IAAA;gBAAAyE,SAAA,CAAA7I,CAAA;gBAAA;cAAA;cAAAqI,WAAA,GAAAK,MAAA,CAAA7H,KAAA;cAAAgI,SAAA,CAAA9I,CAAA;cAAA8I,SAAA,CAAA7I,CAAA;cAAA,OAEA,IAAAqE,0BAAA;gBACAgE,WAAA,EAAAA,WAAA;gBACArE,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAAmC,SAAA,CAAA3I,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;cAAA,GAAAC,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;cAAA,GAAAF,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cAEAuC,WAAA,CAAAuC,IAAA;gBACAzI,IAAA,EAAA8L,WAAA;gBACAjD,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;gBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;gBACAuD,SAAA,EAAAlF,UAAA,CAAA3C,MAAA,OAAAsH,MAAA,CAAAQ,0BAAA,CAAAnF,UAAA,EAAA2B,OAAA;gBACAW,SAAA,EAAAxC,WAAA,CAAAzC;cACA;cAAA4H,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA9I,CAAA;cAAA4I,GAAA,GAAAE,SAAA,CAAA3I,CAAA;cAEAK,OAAA,CAAAC,KAAA,wCAAAsE,MAAA,CAAAuD,WAAA,qCAAAM,GAAA;YAAA;cAAAE,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA9I,CAAA;cAAA6I,IAAA,GAAAC,SAAA,CAAA3I,CAAA;cAAAuI,UAAA,CAAA1D,CAAA,CAAA6D,IAAA;YAAA;cAAAC,SAAA,CAAA9I,CAAA;cAAA0I,UAAA,CAAAtG,CAAA;cAAA,OAAA0G,SAAA,CAAA1G,CAAA;YAAA;cAAA,OAAA0G,SAAA,CAAAlI,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA;IACA;IAEA,eACAzG,gBAAA,WAAAA,iBAAA;MAAA,IAAAiH,MAAA;MAAA,WAAA7J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA2J,SAAA;QAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAAC,OAAA,EAAA1C,QAAA,EAAAhD,WAAA,EAAA2F,SAAA,EAAAC,UAAA,EAAAC,IAAA;QAAA,WAAAlK,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA2J,SAAA;UAAA,kBAAAA,SAAA,CAAAzJ,CAAA,GAAAyJ,SAAA,CAAAxJ,CAAA;YAAA;cAAA,MACA,CAAAgJ,MAAA,CAAA/L,WAAA,CAAAM,SAAA,IAAAyL,MAAA,CAAA/L,WAAA,CAAAM,SAAA,CAAA0D,MAAA;gBAAAuI,SAAA,CAAAxJ,CAAA;gBAAA;cAAA;cAAA,OAAAwJ,SAAA,CAAA7I,CAAA,IACA;YAAA;cAAA6I,SAAA,CAAAzJ,CAAA;cAAAmJ,qBAAA,OAAAO,eAAA,CAAArK,OAAA,EAIA4J,MAAA,CAAA/L,WAAA,CAAAM,SAAA,MAAA4L,SAAA,GAAAD,qBAAA,KAAAE,OAAA,GAAAF,qBAAA;cAAAM,SAAA,CAAAxJ,CAAA;cAAA,OACA,IAAAqE,0BAAA;gBACA8E,SAAA,EAAAA,SAAA;gBACAC,OAAA,EAAAA,OAAA;gBACApF,OAAA;gBACAC,QAAA;cACA;YAAA;cALAyC,QAAA,GAAA8C,SAAA,CAAAtJ,CAAA;cAOAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAkJ,SAAA,OAEA;cACAC,UAAA;cACA5F,WAAA,CAAAgG,OAAA,WAAAC,MAAA;gBACA,IAAAC,IAAA,GAAAD,MAAA,CAAAE,UAAA,GAAAF,MAAA,CAAAE,UAAA,CAAAC,KAAA;gBACA,IAAAF,IAAA,KAAAN,UAAA,CAAAM,IAAA;kBACAN,UAAA,CAAAM,IAAA;gBACA;gBACA,IAAAA,IAAA;kBACAN,UAAA,CAAAM,IAAA,EAAA5E,IAAA,CAAA2E,MAAA;gBACA;cACA;;cAEA;cACAI,MAAA,CAAAC,IAAA,CAAAV,UAAA,EAAAW,IAAA,GAAAP,OAAA,WAAAE,IAAA;gBACA,IAAAM,UAAA,GAAAZ,UAAA,CAAAM,IAAA;gBACA,IAAAhG,UAAA,GAAAsG,UAAA,CAAA3F,GAAA,WAAAC,CAAA;kBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;gBAAA,GAAAF,MAAA,WAAAzE,CAAA;kBAAA,QAAA0E,KAAA,CAAA1E,CAAA;gBAAA;gBAEAmJ,SAAA,CAAArE,IAAA;kBACA4E,IAAA,EAAAA,IAAA;kBACAO,QAAA,EAAAvG,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;oBAAA,OAAA3E,CAAA,GAAA2E,CAAA;kBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;kBACA6E,KAAA,EAAAF,UAAA,CAAAjJ;gBACA;cACA;cAAA,OAAAuI,SAAA,CAAA7I,CAAA,IAEA0I,SAAA;YAAA;cAAAG,SAAA,CAAAzJ,CAAA;cAAAwJ,IAAA,GAAAC,SAAA,CAAAtJ,CAAA;cAEAK,OAAA,CAAAC,KAAA,gBAAA+I,IAAA;cAAA,OAAAC,SAAA,CAAA7I,CAAA,IACA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IAEA;IAEA,qBACAjH,qBAAA,WAAAA,sBAAA;MAAA,IAAAqI,MAAA;MAAA,WAAAlL,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgL,SAAA;QAAA,IAAAhG,OAAA,EAAAoC,QAAA,EAAAhD,WAAA,EAAAjB,WAAA,EAAA8H,IAAA;QAAA,WAAAlL,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA2K,SAAA;UAAA,kBAAAA,SAAA,CAAAzK,CAAA,GAAAyK,SAAA,CAAAxK,CAAA;YAAA;cACAsE,OAAA,GAAA+F,MAAA,CAAApN,WAAA,CAAAO,YAAA;cAAA,IAEA8G,OAAA;gBAAAkG,SAAA,CAAAxK,CAAA;gBAAA;cAAA;cAAA,OAAAwK,SAAA,CAAA7J,CAAA,IACA;YAAA;cAAA6J,SAAA,CAAAzK,CAAA;cAAAyK,SAAA,CAAAxK,CAAA;cAAA,OAIA,IAAAqE,0BAAA;gBACAC,OAAA,EAAAA,OAAA;gBACAN,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAA8D,SAAA,CAAAtK,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAsC,WAAA,GAAAiB,WAAA,CAAAa,GAAA,WAAAoF,MAAA;gBAAA;kBACApN,IAAA,EAAAoN,MAAA,CAAA1E,YAAA;kBACAC,QAAA,EAAAyE,MAAA,CAAAxE,YAAA;kBACAsF,aAAA,EAAAhG,UAAA,CAAAkF,MAAA,CAAAjF,oBAAA;kBACAG,SAAA,EAAAJ,UAAA,CAAAkF,MAAA,CAAA9E,SAAA;kBACA6F,UAAA,EAAAhF,IAAA,CAAAqB,GAAA,EAAAtC,UAAA,CAAAkF,MAAA,CAAAjF,oBAAA,WAAAD,UAAA,CAAAkF,MAAA,CAAA9E,SAAA,SAAAU,OAAA;kBACAsE,UAAA,EAAAF,MAAA,CAAAE;gBACA;cAAA;cAAA,OAAAW,SAAA,CAAA7J,CAAA,IAEA8B,WAAA;YAAA;cAAA+H,SAAA,CAAAzK,CAAA;cAAAwK,IAAA,GAAAC,SAAA,CAAAtK,CAAA;cAEAK,OAAA,CAAAC,KAAA,oBAAA+J,IAAA;cAAA,OAAAC,SAAA,CAAA7J,CAAA,IACA;UAAA;QAAA,GAAA2J,QAAA;MAAA;IAEA;IAEA,YACAvB,0BAAA,WAAAA,2BAAA4B,MAAA;MACA,IAAAC,GAAA,GAAAD,MAAA,CAAAtF,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;QAAA,OAAA3E,CAAA,GAAA2E,CAAA;MAAA,QAAAqF,MAAA,CAAA1J,MAAA;MACA,IAAA4J,WAAA,GAAAF,MAAA,CAAApG,GAAA,WAAA1D,KAAA;QAAA,OAAA6E,IAAA,CAAAoF,GAAA,CAAAjK,KAAA,GAAA+J,GAAA;MAAA;MACA,IAAAG,aAAA,GAAAF,WAAA,CAAAxF,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;QAAA,OAAA3E,CAAA,GAAA2E,CAAA;MAAA,QAAAuF,WAAA,CAAA5J,MAAA;MACA,OAAAyE,IAAA,CAAAsF,IAAA,CAAAD,aAAA;IACA;IAEA,gBACAE,kBAAA,WAAAA,mBAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA,KAAAC,KAAA,CAAAC,OAAA,CAAAF,SAAA;QACA;MACA;MAEA,OAAAA,SAAA,CAAA7G,GAAA,WAAAgH,KAAA;QACA,IAAAC,IAAA,GAAAD,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;QACA,IAAAuD,KAAA,CAAApD,IAAA;UACAqD,IAAA,UAAAD,KAAA,CAAApD,IAAA;QACA;QACA,OAAAqD,IAAA;MACA,GAAAC,IAAA;IACA;IAEA,YACAxJ,kBAAA,WAAAA,mBAAA;MACA,IAAA/E,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,QAAAA,WAAA;QACA;UACA,KAAAY,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;MACA;IACA;IAEA,WACA1K,WAAA,WAAAA,YAAA;MACA,UAAAxE,KAAA,SAAAmB,SAAA,CAAAoD,MAAA;QACA;MACA;MAEA,IAAA4K,MAAA;MAEA,aAAAlP,SAAA;QACA;UACAkP,MAAA,QAAAC,kBAAA;UACA;QACA;UACAD,MAAA,QAAAE,iBAAA;UACA;QACA;UACAF,MAAA,QAAAG,qBAAA;UACA;QACA;UACAH,MAAA,QAAAI,mBAAA;UACA;QACA;UACAJ,MAAA,QAAAK,qBAAA;UACA;MACA;MAEA,KAAAxP,KAAA,CAAAyP,SAAA,CAAAN,MAAA;IACA;IAEA,cACAC,kBAAA,WAAAA,mBAAA;MACA;MACA,IAAA5O,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,IAAAA,WAAA;QACA;QACA;UACAkP,KAAA;YACAZ,IAAA,OAAA3O,UAAA;YACAwP,IAAA;UACA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA,WAAAA,UAAAC,MAAA;cACA,IAAA9C,MAAA,GAAA8C,MAAA,IAAAlQ,IAAA;cACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,IAAA4B,KAAA,CAAAmB,MAAA,GAAAnB,KAAA,CAAAoB,UAAA,UAAApB,KAAA,CAAA1K,KAAA;cACA;cACA,OAAA8I,MAAA;YACA;UACA;UACAiD,IAAA;YACAP,IAAA;YACAQ,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACA1M,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAA8B,IAAA;YAAA;UACA;UACAqD,KAAA;YACA3M,IAAA;YACA/D,IAAA;UACA;UACA2Q,MAAA;YACA3Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAqC,QAAA;YAAA;YACAgD,MAAA;YACAC,MAAA;YACAC,UAAA;UACA;QACA;MACA;QACA;QACA,IAAAC,IAAA;QAEA;UACAlB,KAAA;YACAZ,IAAA,OAAA3O,UAAA;YACAwP,IAAA;UACA;UACAC,OAAA;YACAC,OAAA;YACAgB,eAAA;YACAC,WAAA;YACAC,WAAA;YACAC,SAAA;cACAC,KAAA;cACAC,QAAA;YACA;YACAC,YAAA;YACArB,SAAA,WAAAA,UAAAC,MAAA;cACA,IAAAqB,SAAA,GAAArB,MAAA,IAAAqB,SAAA;cACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;cAEA,IAAAnE,MAAA,wIAAA7E,MAAA,CACA2H,MAAA,IAAAlQ,IAAA,uCACA;;cAEA;cACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;gBACA,IAAAoC,KAAA,GAAApC,KAAA,CAAAoC,KAAA;gBACAhE,MAAA,iLAAA7E,MAAA,CACA6I,KAAA,sHAAA7I,MAAA,CACAyG,KAAA,CAAAoB,UAAA,+GAAA7H,MAAA,CACAwI,IAAA,CAAAU,YAAA,CAAAzC,KAAA,CAAA1K,KAAA,6CACA;cACA;;cAEA;cACA,IAAAkN,WAAA;gBACApE,MAAA;gBAEA,IAAA2D,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,CAAAzM,YAAA;kBACAqI,MAAA;kBACA,IAAAoE,WAAA,CAAA5K,QAAA;oBACAwG,MAAA,0FAAA7E,MAAA,CAAAiJ,WAAA,CAAA5K,QAAA;kBACA;kBACA,IAAA4K,WAAA,CAAA1F,WAAA;oBACAsB,MAAA,0FAAA7E,MAAA,CAAAiJ,WAAA,CAAA1F,WAAA;kBACA;kBACA,IAAA0F,WAAA,CAAAzM,YAAA,IAAAyM,WAAA,CAAAzM,YAAA,CAAAL,MAAA;oBACA0I,MAAA;oBACAoE,WAAA,CAAAzM,YAAA,CAAA2M,KAAA,OAAAvE,OAAA,WAAA6B,KAAA;sBACA5B,MAAA,mHAAA7E,MAAA,CACAyG,KAAA,CAAAxD,SAAA,wCAAAjD,MAAA,CAAAwI,IAAA,CAAAU,YAAA,CAAAzC,KAAA,CAAAvD,UAAA,gDAAAlD,MAAA,CACAyG,KAAA,CAAApD,IAAA,uCAAAoD,KAAA,CAAApD,IAAA,4DACA;oBACA;oBACA,IAAA4F,WAAA,CAAAzM,YAAA,CAAAL,MAAA;sBACA0I,MAAA,6IAAA7E,MAAA,CACAiJ,WAAA,CAAAzM,YAAA,CAAAL,MAAA,kEACA;oBACA;kBACA;gBACA,WAAAqM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,mBAAA6Q,WAAA,CAAA7I,QAAA;kBACAyE,MAAA;kBACAA,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7I,QAAA;kBACA,IAAA6I,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA,WAAAoH,IAAA,CAAArQ,WAAA,CAAAC,WAAA,mBAAA6Q,WAAA,CAAAjH,QAAA;kBACA6C,MAAA;kBACAA,MAAA,qHAAA7E,MAAA,CAAAiJ,WAAA,CAAAjH,QAAA,oBAAAiH,WAAA,CAAAjH,QAAA,uCAAAhC,MAAA,CAAAiJ,WAAA,CAAAjH,QAAA;kBACA,IAAAiH,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA,WAAAoH,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,CAAAjF,SAAA;kBACAa,MAAA;kBACAA,MAAA,qHAAA7E,MAAA,CAAAiJ,WAAA,CAAAjF,SAAA,oBAAAiF,WAAA,CAAAjF,SAAA,uCAAAhE,MAAA,CAAAiJ,WAAA,CAAAjF,SAAA;kBACA,IAAAiF,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA;gBAEAyD,MAAA;cACA;cAEA,OAAAA,MAAA;YACA;UACA;UACAuE,MAAA;YACAC,GAAA;YACA3R,IAAA;UACA;UACAoQ,IAAA;YACAP,IAAA;YACAQ,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACA1M,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAvL,IAAA;YAAA;UACA;UACA0Q,KAAA;YACA3M,IAAA;YACA/D,IAAA;UACA;UACA2Q,MAAA,GACA;YACA3Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAA1C,WAAA;YAAA;YACA+H,MAAA;YACAC,MAAA;YACAC,UAAA;UACA,GACA;YACA9Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAtC,OAAA;YAAA;YACA2H,MAAA;YACAC,MAAA;YACAC,UAAA;UACA;QAEA;MACA;IACA;IAEA,cACAtB,iBAAA,WAAAA,kBAAA;MACA,IAAAuB,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACA6B,WAAA;YACA9N,IAAA;UACA;UACAkM,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqB,SAAA,GAAArB,MAAA,IAAAqB,SAAA;YACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,GAAA8C,MAAA,IAAAlQ,IAAA;;YAEA;YACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;cACA5B,MAAA,IAAA4B,KAAA,CAAAmB,MAAA,GAAAnB,KAAA,CAAAoB,UAAA,UAAApB,KAAA,CAAA1K,KAAA;YACA;;YAEA;YACA,IAAAyM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAuE,MAAA;UACAC,GAAA;UACA3R,IAAA;QACA;QACAoQ,IAAA;UACAP,IAAA;UACAQ,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACA1M,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAvL,IAAA;UAAA;UACA8R,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA;QACAtB,KAAA;UACA3M,IAAA;UACA/D,IAAA;QACA;QACA2Q,MAAA,GACA;UACA3Q,IAAA;UACA+D,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAA1C,WAAA;UAAA;UACAoJ,QAAA;UACAC,SAAA;YACAd,KAAA;UACA;QACA,GACA;UACApR,IAAA;UACA+D,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAtC,OAAA;UAAA;UACAgJ,QAAA;UACAC,SAAA;YACAd,KAAA;UACA;QACA;MAEA;IACA;IAEA,cACA3B,qBAAA,WAAAA,sBAAA;MACA,IAAAsB,IAAA;;MAEA;MACA,IAAAoB,WAAA,QAAA7Q,SAAA,CAAA0G,GAAA,WAAAuD,IAAA,EAAA6G,KAAA;QAAA,QACAlK,UAAA,CAAAqD,IAAA,CAAA2C,aAAA,IAAA3C,IAAA,CAAA1C,WAAA,QACAX,UAAA,CAAAqD,IAAA,CAAAjD,SAAA,IAAAiD,IAAA,CAAAtC,OAAA,QACAsC,IAAA,CAAAvL,IAAA;QAAA;QACAoS,KAAA;QAAA,CACA;MAAA;MAEA;QACAvC,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAmC,YAAA,OAAAnF,eAAA,CAAArK,OAAA,EAAAqN,MAAA,CAAAjQ,IAAA;cAAAqS,WAAA,GAAAD,YAAA;cAAAE,OAAA,GAAAF,YAAA;cAAArS,IAAA,GAAAqS,YAAA;cAAAd,SAAA,GAAAc,YAAA;YACA,IAAAb,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,MAAA7E,MAAA,CAAAvI,IAAA,qCAAAuI,MAAA,CAAA+J,WAAA,+BAAA/J,MAAA,CAAAgK,OAAA,yBAAAhK,MAAA,CAAAY,IAAA,CAAAqB,GAAA,CAAA8H,WAAA,GAAAC,OAAA,EAAAvJ,OAAA;;YAEA;YACA,IAAA+H,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAqD,KAAA;UACA1M,IAAA;UACA/D,IAAA;UACAwS,KAAA;UACAV,SAAA;YACA7B,SAAA;UACA;QACA;QACAS,KAAA;UACA3M,IAAA;UACA/D,IAAA;UACAwS,KAAA;UACAV,SAAA;YACA7B,SAAA;UACA;QACA;QACAU,MAAA;UACA3Q,IAAA;UACA+D,IAAA;UACA9D,IAAA,EAAAkS,WAAA;UACArB,UAAA;UACAoB,SAAA;YACAd,KAAA;UACA;QACA;UACApR,IAAA;UACA+D,IAAA;UACA9D,IAAA,YAAAkJ,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsP,WAAA,CAAAnK,GAAA,WAAA4B,CAAA;YAAA,OAAAA,CAAA;UAAA,MAAAT,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsP,WAAA,CAAAnK,GAAA,WAAA4B,CAAA;YAAA,OAAAA,CAAA;UAAA;UACA6I,SAAA;YACArB,KAAA;YACArN,IAAA;UACA;UACA8M,MAAA;QACA;MACA;IACA;IAEA,cACAnB,mBAAA,WAAAA,oBAAA;MACA;MACA,IAAAgD,UAAA,IACA;QAAA1S,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,EACA;MAEA,IAAAuJ,SAAA,QAAArR,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;QACA,IAAA1C,WAAA,GAAAX,UAAA,CAAAqD,IAAA,CAAA1C,WAAA;QACA,IAAAI,OAAA,GAAAf,UAAA,CAAAqD,IAAA,CAAAtC,OAAA;QACA,IAAAU,SAAA,GAAAiJ,QAAA,CAAArH,IAAA,CAAA5B,SAAA;QACA,IAAAY,QAAA,GAAArC,UAAA,CAAAqD,IAAA,CAAAhB,QAAA;QACA,IAAAgC,SAAA,GAAArE,UAAA,CAAAqD,IAAA,CAAAgB,SAAA;QAEA;UACAvM,IAAA,EAAAuL,IAAA,CAAAvL,IAAA;UACAsE,KAAA,GACA6E,IAAA,CAAAM,GAAA,CAAAZ,WAAA,QACAM,IAAA,CAAAM,GAAA,CAAAR,OAAA,QACAE,IAAA,CAAAM,GAAA,CAAAE,SAAA,OACAR,IAAA,CAAAM,GAAA,CAAAc,QAAA,QACApB,IAAA,CAAAM,GAAA,CAAA8C,SAAA;QAEA;MACA;MAEA,IAAAwE,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqB,SAAA,GAAArB,MAAA,CAAAqB,SAAA;YACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,GAAA8C,MAAA,CAAAlQ,IAAA;;YAEA;YACA,IAAA0S,UAAA;YACAxC,MAAA,CAAA5L,KAAA,CAAA6I,OAAA,WAAA7I,KAAA,EAAA8N,KAAA;cACAhF,MAAA,IAAAsF,UAAA,CAAAN,KAAA,WAAA9N,KAAA;YACA;;YAEA;YACA,IAAAyM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAuE,MAAA;UACAC,GAAA;UACA3R,IAAA,EAAA0S,SAAA,CAAA3K,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAvL,IAAA;UAAA;QACA;QACA6S,KAAA;UACAC,SAAA,EAAAJ,UAAA;UACAK,MAAA;QACA;QACApC,MAAA;UACA5M,IAAA;UACA9D,IAAA,EAAA0S;QACA;MACA;IACA;IAEA,cACAhD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAqD,SAAA,OAAA1J,mBAAA,CAAAzG,OAAA,MAAAoQ,GAAA,MAAA3R,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;QAAA,OAAAA,IAAA,CAAAvL,IAAA;MAAA;MACA,IAAAkT,SAAA;MAEA,IAAAC,WAAA;MACA,KAAA7R,SAAA,CAAA6L,OAAA,WAAA5B,IAAA,EAAA6H,MAAA;QACA,IAAAvK,WAAA,GAAAX,UAAA,CAAAqD,IAAA,CAAA1C,WAAA;QACA,IAAAI,OAAA,GAAAf,UAAA,CAAAqD,IAAA,CAAAtC,OAAA;QACA,IAAA8C,SAAA,GAAA5C,IAAA,CAAAqB,GAAA,CAAA3B,WAAA,GAAAI,OAAA;QAEAkK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAAvK,WAAA;QACAsK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAAnK,OAAA;QACAkK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAArH,SAAA;MACA;MAEA,IAAAsH,QAAA,GAAAlK,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsQ,WAAA,CAAAnL,GAAA,WAAA4B,CAAA;QAAA,OAAAA,CAAA;MAAA;MAEA,IAAAmH,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAuD,QAAA;UACArD,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqD,aAAA,OAAArG,eAAA,CAAArK,OAAA,EAAAqN,MAAA,CAAAjQ,IAAA;cAAAuT,CAAA,GAAAD,aAAA;cAAAE,CAAA,GAAAF,aAAA;cAAAjP,KAAA,GAAAiP,aAAA;YACA,IAAAG,MAAA,GAAAV,SAAA,CAAAQ,CAAA;YACA,IAAAG,MAAA,GAAAT,SAAA,CAAAO,CAAA;YACA,IAAAjC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAkS,CAAA;YAEA,IAAApG,MAAA,MAAA7E,MAAA,CAAAmL,MAAA,WAAAnL,MAAA,CAAAoL,MAAA,QAAApL,MAAA,CAAAjE,KAAA,CAAA0E,OAAA;;YAEA;YACA,IAAA+H,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAiD,IAAA;UACAuD,MAAA;UACAhC,GAAA;QACA;QACAnB,KAAA;UACA1M,IAAA;UACA9D,IAAA,EAAA+S,SAAA;UACAlB,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA;QACAtB,KAAA;UACA3M,IAAA;UACA9D,IAAA,EAAAiT;QACA;QACAW,SAAA;UACApK,GAAA;UACAL,GAAA,EAAAiK,QAAA;UACAS,UAAA;UACAC,MAAA;UACAjE,IAAA;UACAS,MAAA;UACAyD,OAAA;YACA5C,KAAA;UACA;QACA;QACAT,MAAA;UACA5M,IAAA;UACA9D,IAAA,EAAAkT,WAAA;UACA/D,KAAA;YACA6E,IAAA;YACAhE,SAAA,WAAAA,UAAAC,MAAA;cACA,OAAAA,MAAA,CAAAjQ,IAAA,IAAA+I,OAAA;YACA;UACA;UACAkL,QAAA;YACAhC,SAAA;cACAiC,UAAA;cACAC,WAAA;YACA;UACA;QACA;MACA;IACA;IAEA,WACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAA5T,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,YAAA;MACA;MACA,KAAAsD,gBAAA;IACA;IAEA,WACAgQ,YAAA,WAAAA,aAAA;MACA,SAAAjT,SAAA,CAAAoD,MAAA;QACA,KAAAC,WAAA;MACA;IACA;IAEA,WACA6P,WAAA,WAAAA,YAAA;MACA,UAAArU,KAAA;QACA,KAAA0F,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAA2O,GAAA,QAAAtU,KAAA,CAAAuU,UAAA;QACA3Q,IAAA;QACA4Q,UAAA;QACA3D,eAAA;MACA;MAEA,IAAA4D,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,IAAA,CAAAI,QAAA,MAAAzM,MAAA,MAAAjI,UAAA,OAAAiI,MAAA,KAAA0M,IAAA,GAAAC,OAAA;MACAN,IAAA,CAAAO,KAAA;IACA;IAEA,cACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA7U,aAAA,SAAAA,aAAA;MACA,UAAAA,aAAA,SAAAL,KAAA;QACA,KAAAmV,SAAA;UACAD,MAAA,CAAAlV,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,eACA6S,oBAAA,WAAAA,qBAAA;MACA,KAAA3T,kBAAA,SAAAA,kBAAA;IACA;IAEA,aACA4T,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,SAAAhV,YAAA;QACA,KAAAJ,WAAA;QACA,KAAAI,YAAA;MACA;QACA,KAAAJ,WAAA,GAAAmC,MAAA,CAAAkT,WAAA;QACA,KAAAjV,YAAA;MACA;MAEA,KAAA6U,SAAA;QACA,IAAAG,MAAA,CAAAtV,KAAA;UACAsV,MAAA,CAAAtV,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,aACAiT,aAAA,WAAAA,cAAA;MACA,KAAAjU,iBAAA;IACA;IAEA,cACA+P,YAAA,WAAAA,aAAAnN,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAoH,SAAA,IAAArD,KAAA,CAAA/D,KAAA;QACA;MACA;MACA,IAAAsR,GAAA,GAAA1N,UAAA,CAAA5D,KAAA;MACA,IAAAsR,GAAA;MACA,IAAAzM,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,QAAAA,GAAA,YAAA5M,OAAA;MACA,WAAAG,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,QAAAA,GAAA,SAAA5M,OAAA;MACA,WAAAG,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,OAAAA,GAAA,CAAA5M,OAAA;MACA;QACA,OAAA4M,GAAA,CAAA5M,OAAA;MACA;IACA;IAEA,eACA6M,eAAA,WAAAA,gBAAA7G,KAAA;MACA,KAAAA,KAAA,CAAAvD,UAAA;MACA,IAAAnH,KAAA,GAAA4D,UAAA,CAAA8G,KAAA,CAAAvD,UAAA;MACA,IAAApD,KAAA,CAAA/D,KAAA;;MAEA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA,aACAwR,eAAA,WAAAA,gBAAA9G,KAAA;MACA,KAAAlN,kBAAA,GAAAkN,KAAA;MACA,KAAAnN,wBAAA;IACA;IAEA,eACA8D,0BAAA,WAAAA,2BAAA;MAAA,IAAAoQ,OAAA;MACA;MACA,IAAApV,WAAA,QAAAD,WAAA,CAAAC,WAAA;MACA,KAAAa,oBAAA;MAEA,IAAAb,WAAA,wBAAAD,WAAA,CAAAG,aAAA,CAAA6D,MAAA;QACA,KAAAhE,WAAA,CAAAG,aAAA,CAAAsM,OAAA,WAAA1G,UAAA;UACA,IAAAG,QAAA,GAAAmP,OAAA,CAAA5U,eAAA,CAAAwG,IAAA,WAAA5E,CAAA;YAAA,OAAAA,CAAA,CAAA0D,UAAA,KAAAA,UAAA;UAAA;UACA,IAAAG,QAAA;YACAmP,OAAA,CAAAvU,oBAAA,CAAAiH,IAAA;cACAzI,IAAA,EAAA4G,QAAA,CAAA8B,YAAA;cACAA,YAAA,EAAA9B,QAAA,CAAA8B,YAAA;cACAE,YAAA,EAAAhC,QAAA,CAAAgC,YAAA;cACAkD,WAAA,EAAAlF,QAAA,CAAAkF,WAAA;cACAkK,SAAA,EAAApP,QAAA,CAAAoP,SAAA;cACAC,UAAA,EAAArP,QAAA,CAAAqP;YACA;UACA;QACA;MACA,WAAAtV,WAAA,wBAAAD,WAAA,CAAAI,aAAA,CAAA4D,MAAA;QACA,KAAAhE,WAAA,CAAAI,aAAA,CAAAqM,OAAA,WAAAxE,QAAA;UACAoN,OAAA,CAAAvU,oBAAA,CAAAiH,IAAA;YACAzI,IAAA,EAAA2I,QAAA;YACAC,YAAA,EAAAD,QAAA;YACAqN,SAAA;UACA;QACA;MACA,WAAArV,WAAA,2BAAAD,WAAA,CAAAE,YAAA,CAAA8D,MAAA;QACA,KAAAhE,WAAA,CAAAE,YAAA,CAAAuM,OAAA,WAAA+I,OAAA;UACA,IAAAlH,KAAA,GAAA+G,OAAA,CAAA7U,kBAAA,CAAAyG,IAAA,WAAAnE,CAAA;YAAA,OAAAA,CAAA,CAAAuE,OAAA,KAAAmO,OAAA;UAAA;UACA,IAAAlH,KAAA;YACA+G,OAAA,CAAAvU,oBAAA,CAAAiH,IAAA;cACAzI,IAAA,EAAAgP,KAAA,CAAAnD,WAAA;cACAA,WAAA,EAAAmD,KAAA,CAAAnD,WAAA;cACAnD,YAAA,EAAAsG,KAAA,CAAAtG,YAAA;cACAoD,WAAA,EAAAkD,KAAA,CAAAlD,WAAA;cACAqK,UAAA,EAAAnH,KAAA,CAAA7D,UAAA;cACA6K,SAAA,EAAAhH,KAAA,CAAAgH,SAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}