{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754285582786}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\babel.config.js", "mtime": 1753339103954}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_testResult", "_material", "_processParamGroup", "_processParamItem", "name", "data", "loading", "chart", "chartType", "chartHeight", "chartTitle", "chartLoading", "showDataTable", "isFullscreen", "queryParams", "compareType", "paramNumbers", "materialNames", "supplierNames", "processTypes", "date<PERSON><PERSON><PERSON>", "compareParam", "paramNumberOptions", "materialOptions", "supplierOptions", "processTypeOptions", "chartData", "tableColumns", "selectedParamDetails", "statisticsData", "helpDialogVisible", "showUsageGuide", "showProjectDetails", "paramDetailDialogVisible", "currentParamDetail", "mounted", "initChart", "loadOptions", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "_this", "init", "$refs", "window", "addEventListener", "resize", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "paramResponse", "materialResponse", "supplierResponse", "processResponse", "_t", "w", "_context", "p", "n", "listProcessParamGroup", "v", "rows", "listMaterial", "getTestResultOptions", "type", "console", "error", "$modal", "msgError", "a", "handleCompareTypeChange", "value", "updateChartTitle", "typeMap", "handleChartTypeChange", "length", "<PERSON><PERSON><PERSON>", "handleQuery", "_this3", "_callee2", "paramDetails", "_t2", "_t3", "_context2", "validate<PERSON><PERSON>y", "getMaterialCompareData", "getSupplierCompareData", "getParamNumberCompareData", "getProcessTypeCompareData", "getTimeTrendData", "getSupplierVsTestData", "updateTableColumns", "updateSelectedParamDetails", "f", "$message", "warning", "_this4", "_callee3", "materialIds", "compareData", "_iterator", "_step", "_loop", "_t6", "_context4", "_createForOfIteratorHelper2", "materialId", "paramGroupResponse", "paramGroups", "material", "allSupplierValues", "allTestValues", "_iterator2", "_step2", "group", "testResponse", "testResults", "supplierValues", "testValues", "_t4", "_t5", "_context3", "pageNum", "pageSize", "find", "s", "done", "listTestResult", "groupId", "map", "r", "parseFloat", "supplierDatasheetVal", "filter", "isNaN", "testValue", "concat", "e", "push", "materialName", "supplier", "supplierName", "supplierAvg", "reduce", "b", "toFixed", "testAvg", "supplierMax", "Math", "max", "apply", "_toConsumableArray2", "testMax", "supplierMin", "min", "testMin", "dataCount", "d", "_regeneratorValues2", "_this5", "_callee4", "suppliers", "_iterator3", "_step3", "response", "_t7", "_t8", "_context5", "accuracy", "abs", "_this6", "_callee5", "paramGroupIds", "_iterator4", "_step4", "_loop2", "_t0", "_context7", "paramItemResponse", "paramGroup", "paramItems", "_t9", "_context6", "listProcessParamItem", "item", "paramName", "paramValue", "undefined", "String", "unit", "paramNumber", "processType", "deviation", "_this7", "_callee6", "_iterator5", "_step5", "_t1", "_t10", "_context8", "stability", "calculateStandardDeviation", "_this8", "_callee7", "_this8$queryParams$da", "startDate", "endDate", "trendData", "dateGroups", "_t11", "_context9", "_slicedToArray2", "for<PERSON>ach", "result", "date", "createTime", "split", "Object", "keys", "sort", "dayResults", "avgValue", "count", "_this9", "_callee8", "_t12", "_context0", "supplierValue", "difference", "values", "avg", "squareDiffs", "pow", "avgSquareDiff", "sqrt", "formatParamDetails", "row", "column", "cellValue", "Array", "isArray", "param", "text", "join", "prop", "label", "width", "option", "getLineChartOption", "getBarChartOption", "getScatterChartOption", "getRadarChartOption", "getHeatmapChartOption", "setOption", "title", "left", "tooltip", "trigger", "formatter", "params", "marker", "seriesName", "grid", "right", "bottom", "containLabel", "xAxis", "yAxis", "series", "smooth", "symbol", "symbolSize", "self", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "fontSize", "extraCssText", "dataIndex", "currentData", "formatNumber", "slice", "legend", "top", "axisPointer", "axisLabel", "rotate", "interval", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "scatterData", "index", "_params$data", "supplierVal", "testVal", "scale", "lineStyle", "indicators", "radarData", "parseInt", "radar", "indicator", "radius", "xAxisData", "Set", "yAxisData", "heatmapData", "xIndex", "maxValue", "position", "_params$data2", "x", "y", "xLabel", "yLabel", "height", "visualMap", "calculable", "orient", "inRange", "show", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "reset<PERSON><PERSON>y", "resetForm", "refresh<PERSON><PERSON>", "exportChart", "url", "getDataURL", "pixelRatio", "link", "document", "createElement", "href", "download", "Date", "getTime", "click", "toggleDataTable", "_this0", "$nextTick", "toggleProjectDetails", "toggleFullscreen", "_this1", "innerHeight", "showChartHelp", "num", "formatParamValue", "replace", "getParamTagType", "showParamDetail", "_this10", "testCount", "statistics", "paramId", "mainParams"], "sources": ["src/views/material/trend/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-data-line\"></i>\r\n        <span>趋势对比分析</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <p>📈 多维度数据趋势对比分析，支持材料性能、供应商质量等多种对比维度</p>\r\n          <el-button type=\"text\" @click=\"showUsageGuide = true\" style=\"color: #409EFF;\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <span>使用指南</span>\r\n          </el-button>\r\n        </div>\r\n        <el-alert\r\n          title=\"使用提示：选择对比维度 → 配置筛选条件 → 生成图表分析\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用指南卡片 -->\r\n    <el-card class=\"usage-guide-card enhanced-card\" style=\"margin-bottom: 20px;\" v-if=\"showUsageGuide\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"header-title\">📊 趋势对比分析使用指南</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"text\" @click=\"showUsageGuide = false\" class=\"close-guide-btn\">\r\n            <i class=\"el-icon-close\"></i>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>🎯 对比维度说明</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>材料对比：</strong>按材料名称分组，计算每种材料的供应商数据和测试数据平均值</li>\r\n              <li><strong>供应商对比：</strong>按供应商分组，计算准确率（供应商数据与测试数据的偏差）</li>\r\n              <li><strong>参数编号对比：</strong>按参数组分组，展示工艺参数明细和测试结果统计</li>\r\n              <li><strong>工艺类型对比：</strong>按工艺类型分组，计算稳定性（测试数据标准差）</li>\r\n              <li><strong>时间趋势：</strong>按日期分组，展示测试数据随时间的变化趋势</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>📊 数据来源详解</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>供应商平均值：</strong>选中项目下所有测试记录的供应商数据平均值</li>\r\n              <li><strong>测试平均值：</strong>选中项目下所有测试记录的实际测试值平均值</li>\r\n              <li><strong>准确率：</strong>100% - |供应商平均值 - 测试平均值| / 供应商平均值 × 100%</li>\r\n              <li><strong>稳定性：</strong>基于测试数据标准差计算，数值越小越稳定</li>\r\n              <li><strong>数据量：</strong>参与计算的测试记录总数</li>\r\n              <li><strong>参数明细：</strong>来自工艺参数配置中的具体参数项</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row style=\"margin-top: 15px;\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"guide-item\">\r\n            <h4>💡 使用建议</h4>\r\n            <div class=\"usage-suggestion\">\r\n              <p>1. 选择对比维度 → 2. 配置筛选条件（支持多选） → 3. 点击\"生成图表\"分析 → 4. 切换图表类型查看不同视角 → 5. 查看详细数据表获取具体数值</p>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <el-card class=\"trend-analysis-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">数据趋势对比分析</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"info\" icon=\"el-icon-question\" size=\"small\" @click=\"showUsageGuide = !showUsageGuide\" class=\"guide-btn\">\r\n            <span>使用指南</span>\r\n          </el-button>\r\n          <el-button type=\"primary\" icon=\"el-icon-refresh\" size=\"small\" @click=\"refreshChart\" class=\"refresh-btn\">\r\n            <span>刷新数据</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"exportChart\" class=\"export-btn\">\r\n            <span>导出图表</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 筛选条件 -->\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"对比维度\" prop=\"compareType\">\r\n          <el-select v-model=\"queryParams.compareType\" placeholder=\"请选择对比维度\" style=\"width: 250px;\" clearable @change=\"handleCompareTypeChange\">\r\n            <el-option label=\"📊 材料性能对比\" value=\"material\">\r\n              <span style=\"float: left\">📊 材料性能对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同材料性能</span>\r\n            </el-option>\r\n            <el-option label=\"🏭 供应商数据对比\" value=\"supplier\">\r\n              <span style=\"float: left\">🏭 供应商数据对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较供应商质量</span>\r\n            </el-option>\r\n            <el-option label=\"🔢 参数编号对比\" value=\"paramNumber\">\r\n              <span style=\"float: left\">🔢 参数编号对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同参数值</span>\r\n            </el-option>\r\n            <el-option label=\"⚙️ 工艺类型对比\" value=\"processType\">\r\n              <span style=\"float: left\">⚙️ 工艺类型对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较工艺效果</span>\r\n            </el-option>\r\n            <el-option label=\"📈 时间趋势分析\" value=\"timeTrend\">\r\n              <span style=\"float: left\">📈 时间趋势分析</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">查看时间变化</span>\r\n            </el-option>\r\n            <el-option label=\"⚖️ 供应商vs测试值\" value=\"supplierVsTest\">\r\n              <span style=\"float: left\">⚖️ 供应商vs测试值</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">对比数据差异</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 材料性能对比 -->\r\n        <el-form-item label=\"选择材料\" prop=\"materialNames\" v-if=\"queryParams.compareType === 'material'\">\r\n          <el-select\r\n            v-model=\"queryParams.materialNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的材料\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"material in materialOptions\"\r\n              :key=\"material.materialId\"\r\n              :label=\"material.materialName + ' (' + material.supplierName + ')'\"\r\n              :value=\"material.materialId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 供应商数据对比 -->\r\n        <el-form-item label=\"选择供应商\" prop=\"supplierNames\" v-if=\"queryParams.compareType === 'supplier'\">\r\n          <el-select\r\n            v-model=\"queryParams.supplierNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的供应商\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"supplier in supplierOptions\"\r\n              :key=\"supplier\"\r\n              :label=\"supplier\"\r\n              :value=\"supplier\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 参数编号对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"paramNumbers\" v-if=\"queryParams.compareType === 'paramNumber'\">\r\n          <el-select\r\n            v-model=\"queryParams.paramNumbers\"\r\n            multiple\r\n            placeholder=\"请选择要对比的参数编号\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 工艺类型对比 -->\r\n        <el-form-item label=\"选择工艺\" prop=\"processTypes\" v-if=\"queryParams.compareType === 'processType'\">\r\n          <el-select\r\n            v-model=\"queryParams.processTypes\"\r\n            multiple\r\n            placeholder=\"请选择要对比的工艺类型\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"type in processTypeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 时间趋势分析 -->\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\" v-if=\"queryParams.compareType === 'timeTrend'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            style=\"width: 300px;\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 供应商vs测试值对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"compareParam\" v-if=\"queryParams.compareType === 'supplierVsTest'\">\r\n          <el-select\r\n            v-model=\"queryParams.compareParam\"\r\n            placeholder=\"请选择要对比的参数\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\" :loading=\"loading\">生成对比图表</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 图表类型选择 -->\r\n      <el-row style=\"margin-bottom: 20px;\">\r\n        <el-col :span=\"24\">\r\n          <el-radio-group v-model=\"chartType\" @change=\"handleChartTypeChange\">\r\n            <el-radio-button label=\"line\">折线图</el-radio-button>\r\n            <el-radio-button label=\"bar\">柱状图</el-radio-button>\r\n            <el-radio-button label=\"scatter\">散点图</el-radio-button>\r\n            <el-radio-button label=\"radar\">雷达图</el-radio-button>\r\n            <el-radio-button label=\"heatmap\">热力图</el-radio-button>\r\n          </el-radio-group>\r\n          <el-button-group style=\"margin-left: 20px;\">\r\n            <el-button size=\"small\" @click=\"toggleDataTable\">{{ showDataTable ? '隐藏' : '显示' }}数据表</el-button>\r\n            <el-button size=\"small\" @click=\"toggleProjectDetails\" :disabled=\"selectedParamDetails.length === 0\">\r\n              {{ showProjectDetails ? '隐藏' : '显示' }}项目详情\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"toggleFullscreen\">全屏显示</el-button>\r\n          </el-button-group>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 参数详情信息卡片 -->\r\n    <el-card v-if=\"selectedParamDetails.length > 0 && showProjectDetails\" class=\"box-card param-details-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">📋 选中项目详情信息</span>\r\n        <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 10px;\">{{ selectedParamDetails.length }}项</el-tag>\r\n        <el-button type=\"text\" @click=\"showProjectDetails = false\" style=\"float: right; color: #909399;\">\r\n          <i class=\"el-icon-close\"></i>\r\n        </el-button>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\" v-for=\"(detail, index) in selectedParamDetails\" :key=\"index\">\r\n          <el-card class=\"param-detail-card\" shadow=\"hover\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span style=\"font-weight: bold; color: #409EFF;\">\r\n                <i class=\"el-icon-data-line\"></i>\r\n                {{ detail.paramNumber || detail.name }}\r\n              </span>\r\n              <el-tag size=\"mini\" type=\"success\" style=\"float: right;\" v-if=\"detail.testCount\">\r\n                {{ detail.testCount }}次测试\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 基本信息 -->\r\n            <div class=\"detail-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-info\"></i>\r\n                基本信息\r\n              </div>\r\n              <el-descriptions :column=\"2\" border size=\"small\">\r\n                <el-descriptions-item label=\"材料名称\" v-if=\"detail.materialName\">\r\n                  <el-tag type=\"primary\" size=\"mini\">{{ detail.materialName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"供应商\" v-if=\"detail.supplierName\">\r\n                  <el-tag type=\"success\" size=\"mini\">{{ detail.supplierName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"材料型号\" v-if=\"detail.materialModel\">\r\n                  <span>{{ detail.materialModel }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"工艺类型\" v-if=\"detail.processType\">\r\n                  <el-tag type=\"warning\" size=\"mini\">{{ detail.processType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"性能类型\" v-if=\"detail.performanceType\">\r\n                  <el-tag type=\"info\" size=\"mini\">{{ detail.performanceType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"参数编号\" v-if=\"detail.paramNumber\">\r\n                  <span>{{ detail.paramNumber }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"数据量\" v-if=\"detail.dataCount !== undefined\">\r\n                  <el-tag type=\"danger\" size=\"mini\">{{ detail.dataCount }}条</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"创建时间\" v-if=\"detail.createTime\">\r\n                  <span>{{ detail.createTime }}</span>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n\r\n            <!-- 统计信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.statistics\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                统计信息\r\n              </div>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.avgValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">平均值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.avgValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.maxValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最大值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.maxValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.minValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最小值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.minValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.stdDev !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">标准差</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.stdDev) }}</div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 参数明细 -->\r\n            <div class=\"detail-section\" v-if=\"detail.mainParams && detail.mainParams.length > 0\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-menu\"></i>\r\n                参数明细\r\n                <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 5px;\">{{ detail.mainParams.length }}个</el-tag>\r\n              </div>\r\n              <div class=\"params-container\">\r\n                <el-tooltip\r\n                  v-for=\"param in detail.mainParams\"\r\n                  :key=\"param.paramName\"\r\n                  :content=\"`${param.paramName}: ${param.paramValue || 'N/A'} ${param.unit || ''}`\"\r\n                  placement=\"top\"\r\n                >\r\n                  <el-tag\r\n                    size=\"mini\"\r\n                    :type=\"getParamTagType(param)\"\r\n                    style=\"margin-right: 5px; margin-bottom: 3px; cursor: pointer;\"\r\n                    @click=\"showParamDetail(param)\"\r\n                  >\r\n                    {{ param.paramName }}\r\n                    <span v-if=\"param.paramValue\" style=\"margin-left: 3px; opacity: 0.8;\">\r\n                      ({{ formatNumber(param.paramValue) }})\r\n                    </span>\r\n                  </el-tag>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 测试方案信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.testPlanInfo\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-document\"></i>\r\n                测试方案\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <label>方案编号：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.planCode }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" v-if=\"detail.testPlanInfo.testEquipment\">\r\n                <label>测试设备：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.testEquipment }}</span>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 图表区域 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">{{ chartTitle }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-tooltip content=\"图表说明\" placement=\"top\">\r\n            <el-button type=\"text\" icon=\"el-icon-question\" @click=\"showChartHelp\" />\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"chartLoading\" element-loading-text=\"正在生成图表...\">\r\n        <div\r\n          ref=\"chart\"\r\n          :style=\"{ height: chartHeight + 'px', width: '100%' }\"\r\n          v-show=\"!showDataTable\"\r\n        ></div>\r\n\r\n        <!-- 数据表格 -->\r\n        <el-table\r\n          v-show=\"showDataTable\"\r\n          :data=\"chartData\"\r\n          style=\"width: 100%\"\r\n          :max-height=\"chartHeight\"\r\n          class=\"trend-data-table\"\r\n        >\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column\r\n            v-for=\"column in tableColumns\"\r\n            :key=\"column.prop\"\r\n            :prop=\"column.prop\"\r\n            :label=\"column.label\"\r\n            :width=\"column.width\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"column.prop === 'paramDetails'\">\r\n                <div v-if=\"scope.row.paramDetails && scope.row.paramDetails.length > 0\" class=\"param-details-container\">\r\n                  <div\r\n                    v-for=\"(param, index) in scope.row.paramDetails\"\r\n                    :key=\"index\"\r\n                    class=\"param-detail-item\"\r\n                  >\r\n                    <span class=\"param-name\">{{ param.paramName }}:</span>\r\n                    <span class=\"param-value\">{{ formatParamValue(param.paramValue) }}</span>\r\n                    <span class=\"param-unit\" v-if=\"param.unit\">{{ param.unit }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else class=\"empty-data\">暂无参数</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'paramNumber'\">\r\n                <el-tag type=\"primary\" size=\"small\" v-if=\"scope.row.paramNumber\">\r\n                  {{ scope.row.paramNumber }}\r\n                </el-tag>\r\n                <span v-else class=\"empty-data\">-</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'materialName'\">\r\n                <div class=\"material-info\">\r\n                  <i class=\"el-icon-box\"></i>\r\n                  <span>{{ scope.row.materialName || '-' }}</span>\r\n                </div>\r\n              </div>\r\n              <div v-else>\r\n                {{ scope.row[column.prop] || '-' }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\" v-if=\"statisticsData.length > 0\">\r\n      <el-col :span=\"6\" v-for=\"(stat, index) in statisticsData\" :key=\"index\">\r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-content\">\r\n            <div class=\"statistics-title\">{{ stat.title }}</div>\r\n            <div class=\"statistics-value\">{{ stat.value }}</div>\r\n            <div class=\"statistics-desc\">{{ stat.description }}</div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表说明对话框 -->\r\n    <el-dialog title=\"📊 图表说明\" :visible.sync=\"helpDialogVisible\" width=\"700px\" append-to-body>\r\n      <div class=\"chart-help-content\">\r\n        <h4>🎯 图表类型说明：</h4>\r\n        <ul>\r\n          <li><strong>📈 折线图：</strong>适用于展示数据随时间或其他连续变量的变化趋势，清晰显示数据走向</li>\r\n          <li><strong>📊 柱状图：</strong>适用于比较不同类别之间的数值大小，直观对比差异</li>\r\n          <li><strong>🔵 散点图：</strong>适用于展示两个变量之间的相关关系，发现数据规律</li>\r\n          <li><strong>🕸️ 雷达图：</strong>适用于多维度数据的综合对比，全面评估性能</li>\r\n          <li><strong>🌡️ 热力图：</strong>适用于展示数据的分布密度和相关性，识别热点区域</li>\r\n        </ul>\r\n\r\n        <h4>🔍 对比维度说明：</h4>\r\n        <ul>\r\n          <li><strong>📊 材料性能对比：</strong>比较不同材料的性能表现，识别最优材料</li>\r\n          <li><strong>🏭 供应商数据对比：</strong>比较不同供应商材料的质量差异，评估供应商可靠性</li>\r\n          <li><strong>🔢 参数编号对比：</strong>比较不同参数编号下的测试值趋势，分析参数影响</li>\r\n          <li><strong>⚙️ 工艺类型对比：</strong>比较不同工艺类型的效果，优化工艺流程</li>\r\n          <li><strong>📈 时间趋势分析：</strong>展示测试数据随时间的变化规律，预测发展趋势</li>\r\n          <li><strong>⚖️ 供应商vs测试值：</strong>对比供应商提供数据与实际测试结果的差异</li>\r\n        </ul>\r\n\r\n        <h4>💡 使用技巧：</h4>\r\n        <ul>\r\n          <li>将鼠标悬停在图表数据点上可查看详细信息和参数明细</li>\r\n          <li>点击参数标签可查看该参数的详细信息</li>\r\n          <li>使用\"显示数据表\"功能可查看原始数据</li>\r\n          <li>选择合适的图表类型能更好地展示数据特征</li>\r\n          <li>多选对比项目可进行横向比较分析</li>\r\n        </ul>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数详情对话框 -->\r\n    <el-dialog\r\n      title=\"📋 参数详细信息\"\r\n      :visible.sync=\"paramDetailDialogVisible\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      v-if=\"currentParamDetail\"\r\n    >\r\n      <div class=\"param-detail-content\">\r\n        <el-descriptions :column=\"2\" border size=\"small\">\r\n          <el-descriptions-item label=\"参数名称\">\r\n            <el-tag type=\"primary\">{{ currentParamDetail.paramName }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数数值\">\r\n            <span style=\"font-weight: bold; color: #67C23A;\">\r\n              {{ formatNumber(currentParamDetail.paramValue) }}\r\n            </span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数单位\" v-if=\"currentParamDetail.unit\">\r\n            {{ currentParamDetail.unit }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"数据类型\">\r\n            {{ typeof currentParamDetail.paramValue === 'number' ? '数值型' : '文本型' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\" v-if=\"currentParamDetail.createTime\">\r\n            {{ currentParamDetail.createTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\" v-if=\"currentParamDetail.updateTime\">\r\n            {{ currentParamDetail.updateTime }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <div v-if=\"currentParamDetail.remark\" style=\"margin-top: 15px;\">\r\n          <h4 style=\"color: #409EFF; margin-bottom: 8px;\">📝 备注信息：</h4>\r\n          <p style=\"background: #f5f7fa; padding: 10px; border-radius: 4px; margin: 0;\">\r\n            {{ currentParamDetail.remark }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { listTestResult, getTestResultOptions } from \"@/api/material/testResult\";\r\nimport { listMaterial } from \"@/api/material/material\";\r\nimport { listProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\n\r\nexport default {\r\n  name: \"MaterialTrend\",\r\n  data() {\r\n    return {\r\n      // 加载状态\r\n      loading: false,\r\n      // 图表实例\r\n      chart: null,\r\n      // 图表类型\r\n      chartType: 'line',\r\n      // 图表高度\r\n      chartHeight: 400,\r\n      // 图表标题\r\n      chartTitle: '数据趋势对比分析',\r\n      // 图表加载状态\r\n      chartLoading: false,\r\n      // 是否显示数据表\r\n      showDataTable: false,\r\n      // 是否全屏\r\n      isFullscreen: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      },\r\n\r\n      // 选项数据\r\n      paramNumberOptions: [],\r\n      materialOptions: [],\r\n      supplierOptions: [],\r\n      processTypeOptions: [],\r\n\r\n      // 图表数据\r\n      chartData: [],\r\n      tableColumns: [],\r\n\r\n      // 参数详情\r\n      selectedParamDetails: [],\r\n\r\n      // 统计数据\r\n      statisticsData: [],\r\n\r\n      // 帮助对话框\r\n      helpDialogVisible: false,\r\n\r\n      // 使用指南显示状态\r\n      showUsageGuide: false,\r\n\r\n      // 项目详情显示状态\r\n      showProjectDetails: false,\r\n\r\n      // 参数详情对话框\r\n      paramDetailDialogVisible: false,\r\n      currentParamDetail: null\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n    this.loadOptions();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化图表 */\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart);\r\n\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', () => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 加载选项数据 */\r\n    async loadOptions() {\r\n      try {\r\n        // 加载参数编号选项\r\n        const paramResponse = await listProcessParamGroup({});\r\n        this.paramNumberOptions = paramResponse.rows || [];\r\n\r\n        // 加载材料选项\r\n        const materialResponse = await listMaterial({});\r\n        this.materialOptions = materialResponse.rows || [];\r\n\r\n        // 加载供应商选项\r\n        const supplierResponse = await getTestResultOptions({ type: 'supplierName' });\r\n        this.supplierOptions = supplierResponse.data || [];\r\n\r\n        // 加载工艺类型选项\r\n        const processResponse = await getTestResultOptions({ type: 'processType' });\r\n        this.processTypeOptions = processResponse.data || [];\r\n\r\n      } catch (error) {\r\n        console.error('加载选项数据失败：', error);\r\n        this.$modal.msgError('加载选项数据失败');\r\n      }\r\n    },\r\n\r\n    /** 对比类型改变 */\r\n    handleCompareTypeChange(value) {\r\n      // 重置相关参数\r\n      this.queryParams.paramNumbers = [];\r\n      this.queryParams.materialNames = [];\r\n      this.queryParams.supplierNames = [];\r\n      this.queryParams.processTypes = [];\r\n      this.queryParams.dateRange = null;\r\n      this.queryParams.compareParam = null;\r\n\r\n      // 清空选中的参数详情和图表数据\r\n      this.selectedParamDetails = [];\r\n      this.chartData = [];\r\n      this.statisticsData = [];\r\n\r\n      // 更新图表标题\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 更新图表标题 */\r\n    updateChartTitle() {\r\n      const typeMap = {\r\n        'paramNumber': '参数编号对比分析',\r\n        'material': '材料性能对比分析',\r\n        'supplier': '供应商质量对比分析',\r\n        'processType': '工艺类型效果对比分析',\r\n        'timeTrend': '时间趋势对比分析'\r\n      };\r\n      this.chartTitle = typeMap[this.queryParams.compareType] || '对比分析图';\r\n    },\r\n\r\n    /** 图表类型改变 */\r\n    handleChartTypeChange(type) {\r\n      this.chartType = type;\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 查询数据 */\r\n    async handleQuery() {\r\n      if (!this.validateQuery()) {\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      this.chartLoading = true;\r\n\r\n      try {\r\n        // 根据对比类型获取不同的数据\r\n        let chartData = [];\r\n        let paramDetails = [];\r\n\r\n        switch (this.queryParams.compareType) {\r\n          case 'material':\r\n            chartData = await this.getMaterialCompareData();\r\n            break;\r\n          case 'supplier':\r\n            chartData = await this.getSupplierCompareData();\r\n            break;\r\n          case 'paramNumber':\r\n            chartData = await this.getParamNumberCompareData();\r\n            break;\r\n          case 'processType':\r\n            chartData = await this.getProcessTypeCompareData();\r\n            break;\r\n          case 'timeTrend':\r\n            chartData = await this.getTimeTrendData();\r\n            break;\r\n          case 'supplierVsTest':\r\n            chartData = await this.getSupplierVsTestData();\r\n            break;\r\n        }\r\n\r\n        this.chartData = chartData;\r\n        this.updateTableColumns();\r\n        this.renderChart();\r\n\r\n        // 更新选中参数详情\r\n        this.updateSelectedParamDetails();\r\n\r\n      } catch (error) {\r\n        console.error('获取对比数据失败：', error);\r\n        this.$modal.msgError('获取对比数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n        this.chartLoading = false;\r\n      }\r\n    },\r\n\r\n    /** 验证查询条件 */\r\n    validateQuery() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length === 0) {\r\n        this.$message.warning('请选择至少一个参数编号');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length === 0) {\r\n        this.$message.warning('请选择至少一个材料');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplier' && this.queryParams.supplierNames.length === 0) {\r\n        this.$message.warning('请选择至少一个供应商');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'processType' && this.queryParams.processTypes.length === 0) {\r\n        this.$message.warning('请选择至少一个工艺类型');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'timeTrend' && !this.queryParams.dateRange) {\r\n        this.$message.warning('请选择时间范围');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplierVsTest' && !this.queryParams.compareParam) {\r\n        this.$message.warning('请选择要对比的参数');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 获取材料对比数据 */\r\n    async getMaterialCompareData() {\r\n      const materialIds = this.queryParams.materialNames || [];\r\n      const compareData = [];\r\n\r\n      if (materialIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const materialId of materialIds) {\r\n        try {\r\n          // 通过材料ID查找对应的参数组，然后查找测试结果\r\n          const paramGroupResponse = await listProcessParamGroup({\r\n            materialId: materialId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroups = paramGroupResponse.rows || [];\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n\r\n          let allSupplierValues = [];\r\n          let allTestValues = [];\r\n\r\n          // 遍历该材料的所有参数组，获取测试结果\r\n          for (const group of paramGroups) {\r\n            const testResponse = await listTestResult({\r\n              groupId: group.groupId,\r\n              pageNum: 1,\r\n              pageSize: 1000\r\n            });\r\n\r\n            const testResults = testResponse.rows || [];\r\n            const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n            const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n            allSupplierValues = allSupplierValues.concat(supplierValues);\r\n            allTestValues = allTestValues.concat(testValues);\r\n          }\r\n\r\n          compareData.push({\r\n            name: material ? material.materialName : `材料${materialId}`,\r\n            supplier: material ? material.supplierName : '',\r\n            supplierAvg: allSupplierValues.length > 0 ? (allSupplierValues.reduce((a, b) => a + b, 0) / allSupplierValues.length).toFixed(2) : 0,\r\n            testAvg: allTestValues.length > 0 ? (allTestValues.reduce((a, b) => a + b, 0) / allTestValues.length).toFixed(2) : 0,\r\n            supplierMax: allSupplierValues.length > 0 ? Math.max(...allSupplierValues).toFixed(2) : 0,\r\n            testMax: allTestValues.length > 0 ? Math.max(...allTestValues).toFixed(2) : 0,\r\n            supplierMin: allSupplierValues.length > 0 ? Math.min(...allSupplierValues).toFixed(2) : 0,\r\n            testMin: allTestValues.length > 0 ? Math.min(...allTestValues).toFixed(2) : 0,\r\n            dataCount: allTestValues.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取材料${materialId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取供应商对比数据 */\r\n    async getSupplierCompareData() {\r\n      const suppliers = this.queryParams.supplierNames || [];\r\n      const compareData = [];\r\n\r\n      if (suppliers.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const supplier of suppliers) {\r\n        try {\r\n          const response = await listTestResult({\r\n            supplierName: supplier,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: supplier,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            accuracy: supplierValues.length > 0 && testValues.length > 0 ?\r\n              (100 - Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)) /\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length) * 100).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取供应商${supplier}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取参数编号对比数据 */\r\n    async getParamNumberCompareData() {\r\n      const paramGroupIds = this.queryParams.paramNumbers || [];\r\n      const compareData = [];\r\n\r\n      if (paramGroupIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const groupId of paramGroupIds) {\r\n        try {\r\n          // 获取测试结果数据\r\n          const response = await listTestResult({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          // 获取参数明细数据\r\n          const paramItemResponse = await listProcessParamItem({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroup = this.paramNumberOptions.find(p => p.groupId === groupId);\r\n          const testResults = response.rows || [];\r\n          const paramItems = paramItemResponse.rows || [];\r\n\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          // 格式化参数明细信息\r\n          const paramDetails = paramItems.map(item => ({\r\n            paramName: item.paramName || 'N/A',\r\n            paramValue: item.paramValue !== null && item.paramValue !== undefined ?\r\n              String(item.paramValue) : 'N/A',\r\n            unit: item.unit || ''\r\n          }));\r\n\r\n          compareData.push({\r\n            name: paramGroup ? paramGroup.paramNumber : `参数${groupId}`,\r\n            material: paramGroup ? paramGroup.materialName : '',\r\n            processType: paramGroup ? paramGroup.processType : '',\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            deviation: supplierValues.length > 0 && testValues.length > 0 ?\r\n              Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)).toFixed(2) : 0,\r\n            dataCount: testResults.length,\r\n            paramDetails: paramDetails, // 添加参数明细信息\r\n            groupId: groupId // 保存groupId用于后续使用\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取参数组${groupId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取工艺类型对比数据 */\r\n    async getProcessTypeCompareData() {\r\n      const processTypes = this.queryParams.processTypes || [];\r\n      const compareData = [];\r\n\r\n      if (processTypes.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const processType of processTypes) {\r\n        try {\r\n          const response = await listTestResult({\r\n            processType: processType,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: processType,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            stability: testValues.length > 1 ? this.calculateStandardDeviation(testValues).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取工艺类型${processType}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取时间趋势数据 */\r\n    async getTimeTrendData() {\r\n      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const [startDate, endDate] = this.queryParams.dateRange;\r\n        const response = await listTestResult({\r\n          startDate: startDate,\r\n          endDate: endDate,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const trendData = [];\r\n\r\n        // 按日期分组\r\n        const dateGroups = {};\r\n        testResults.forEach(result => {\r\n          const date = result.createTime ? result.createTime.split(' ')[0] : '';\r\n          if (date && !dateGroups[date]) {\r\n            dateGroups[date] = [];\r\n          }\r\n          if (date) {\r\n            dateGroups[date].push(result);\r\n          }\r\n        });\r\n\r\n        // 计算每日平均值\r\n        Object.keys(dateGroups).sort().forEach(date => {\r\n          const dayResults = dateGroups[date];\r\n          const testValues = dayResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          trendData.push({\r\n            date: date,\r\n            avgValue: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            count: dayResults.length\r\n          });\r\n        });\r\n\r\n        return trendData;\r\n      } catch (error) {\r\n        console.error('获取时间趋势数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 获取供应商vs测试值对比数据 */\r\n    async getSupplierVsTestData() {\r\n      const groupId = this.queryParams.compareParam;\r\n\r\n      if (!groupId) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const response = await listTestResult({\r\n          groupId: groupId,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const compareData = testResults.map(result => ({\r\n          name: result.materialName || '未知材料',\r\n          supplier: result.supplierName || '未知供应商',\r\n          supplierValue: parseFloat(result.supplierDatasheetVal) || 0,\r\n          testValue: parseFloat(result.testValue) || 0,\r\n          difference: Math.abs((parseFloat(result.supplierDatasheetVal) || 0) - (parseFloat(result.testValue) || 0)).toFixed(2),\r\n          createTime: result.createTime\r\n        }));\r\n\r\n        return compareData;\r\n      } catch (error) {\r\n        console.error('获取供应商vs测试值数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 计算标准差 */\r\n    calculateStandardDeviation(values) {\r\n      const avg = values.reduce((a, b) => a + b, 0) / values.length;\r\n      const squareDiffs = values.map(value => Math.pow(value - avg, 2));\r\n      const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;\r\n      return Math.sqrt(avgSquareDiff);\r\n    },\r\n\r\n    /** 格式化参数明细显示 */\r\n    formatParamDetails(row, column, cellValue) {\r\n      if (!cellValue || !Array.isArray(cellValue)) {\r\n        return '暂无参数';\r\n      }\r\n\r\n      return cellValue.map(param => {\r\n        let text = param.paramName + ': ' + param.paramValue;\r\n        if (param.unit) {\r\n          text += ' ' + param.unit;\r\n        }\r\n        return text;\r\n      }).join('; ');\r\n    },\r\n\r\n    /** 更新表格列 */\r\n    updateTableColumns() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      switch (compareType) {\r\n        case 'material':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplier':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '供应商', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'accuracy', label: '准确率(%)', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'paramNumber':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '参数编号', width: 120 },\r\n            { prop: 'material', label: '材料名称', width: 120 },\r\n            { prop: 'processType', label: '工艺类型', width: 100 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'deviation', label: '偏差', width: 80 },\r\n            { prop: 'paramDetails', label: '参数明细', width: 200 }\r\n          ];\r\n          break;\r\n        case 'processType':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '工艺类型', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'stability', label: '稳定性', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'timeTrend':\r\n          this.tableColumns = [\r\n            { prop: 'date', label: '日期', width: 120 },\r\n            { prop: 'avgValue', label: '平均值', width: 100 },\r\n            { prop: 'count', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplierVsTest':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierValue', label: '供应商值', width: 100 },\r\n            { prop: 'testValue', label: '测试值', width: 100 },\r\n            { prop: 'difference', label: '差值', width: 80 }\r\n          ];\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 渲染图表 */\r\n    renderChart() {\r\n      if (!this.chart || this.chartData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      let option = {};\r\n\r\n      switch (this.chartType) {\r\n        case 'line':\r\n          option = this.getLineChartOption();\r\n          break;\r\n        case 'bar':\r\n          option = this.getBarChartOption();\r\n          break;\r\n        case 'scatter':\r\n          option = this.getScatterChartOption();\r\n          break;\r\n        case 'radar':\r\n          option = this.getRadarChartOption();\r\n          break;\r\n        case 'heatmap':\r\n          option = this.getHeatmapChartOption();\r\n          break;\r\n      }\r\n\r\n      this.chart.setOption(option, true);\r\n    },\r\n\r\n    /** 获取折线图配置 */\r\n    getLineChartOption() {\r\n      // 根据对比类型生成不同的图表配置\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'timeTrend') {\r\n        // 时间趋势图\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(param => {\r\n                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.date)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '平均值'\r\n          },\r\n          series: [{\r\n            name: '平均值',\r\n            type: 'line',\r\n            data: this.chartData.map(item => item.avgValue),\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }]\r\n        };\r\n      } else {\r\n        // 其他对比类型的折线图\r\n        const self = this;\r\n\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            backgroundColor: 'rgba(50, 50, 50, 0.95)',\r\n            borderColor: '#409EFF',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); border-radius: 8px; padding: 12px;',\r\n            formatter: function(params) {\r\n              const dataIndex = params[0].dataIndex;\r\n              const currentData = self.chartData[dataIndex];\r\n\r\n              let result = `<div style=\"font-size: 14px; font-weight: bold; color: #409EFF; margin-bottom: 8px;\">\r\n                            📊 ${params[0].name}\r\n                          </div>`;\r\n\r\n              // 显示基本对比数据\r\n              params.forEach(param => {\r\n                const color = param.color;\r\n                result += `<div style=\"margin: 4px 0; display: flex; align-items: center;\">\r\n                          <span style=\"display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;\"></span>\r\n                          <span style=\"font-weight: 500;\">${param.seriesName}:</span>\r\n                          <span style=\"margin-left: 8px; color: #67C23A; font-weight: bold;\">${self.formatNumber(param.value)}</span>\r\n                        </div>`;\r\n              });\r\n\r\n              // 根据对比类型显示详细信息\r\n              if (currentData) {\r\n                result += '<div style=\"border-top: 1px solid #666; margin: 8px 0; padding-top: 8px;\">';\r\n\r\n                if (self.queryParams.compareType === 'paramNumber' && currentData.paramDetails) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📋 参数明细信息</div>';\r\n                  if (currentData.material) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">材料:</span> ${currentData.material}</div>`;\r\n                  }\r\n                  if (currentData.processType) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">工艺:</span> ${currentData.processType}</div>`;\r\n                  }\r\n                  if (currentData.paramDetails && currentData.paramDetails.length > 0) {\r\n                    result += '<div style=\"margin: 4px 0; color: #909399;\">参数列表:</div>';\r\n                    currentData.paramDetails.slice(0, 5).forEach(param => {\r\n                      result += `<div style=\"margin: 1px 0; padding-left: 12px; font-size: 11px;\">\r\n                                • ${param.paramName}: <span style=\"color: #67C23A;\">${self.formatNumber(param.paramValue)}</span>\r\n                                ${param.unit ? ' <span style=\"color: #909399;\">' + param.unit + '</span>' : ''}\r\n                              </div>`;\r\n                    });\r\n                    if (currentData.paramDetails.length > 5) {\r\n                      result += `<div style=\"margin: 2px 0; padding-left: 12px; color: #909399; font-size: 11px;\">\r\n                                ... 还有 ${currentData.paramDetails.length - 5} 个参数\r\n                              </div>`;\r\n                    }\r\n                  }\r\n                } else if (self.queryParams.compareType === 'material' && currentData.supplier) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">🏭 供应商信息</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">供应商:</span> ${currentData.supplier}</div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'supplier' && currentData.accuracy) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📈 质量指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">准确率:</span> <span style=\"color: ${currentData.accuracy > 90 ? '#67C23A' : currentData.accuracy > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.accuracy}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'processType' && currentData.stability) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">⚙️ 工艺指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">稳定性:</span> <span style=\"color: ${currentData.stability > 90 ? '#67C23A' : currentData.stability > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.stability}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                }\r\n\r\n                result += '</div>';\r\n              }\r\n\r\n              return result;\r\n            }\r\n          },\r\n          legend: {\r\n            top: '10%',\r\n            data: ['供应商数据', '测试数据']\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.name)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '数值'\r\n          },\r\n          series: [\r\n            {\r\n              name: '供应商数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.supplierAvg || 0),\r\n              smooth: true,\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '测试数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.testAvg || 0),\r\n              smooth: true,\r\n              symbol: 'triangle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    /** 获取柱状图配置 */\r\n    getBarChartOption() {\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params[0].name + '<br/>';\r\n\r\n            // 显示基本对比数据\r\n            params.forEach(param => {\r\n              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: ['供应商数据', '测试数据']\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.chartData.map(item => item.name),\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数值'\r\n        },\r\n        series: [\r\n          {\r\n            name: '供应商数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.supplierAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          {\r\n            name: '测试数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.testAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#91cc75'\r\n            }\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    /** 获取散点图配置 */\r\n    getScatterChartOption() {\r\n      const self = this;\r\n\r\n      // 散点图主要用于供应商vs测试值对比\r\n      const scatterData = this.chartData.map((item, index) => [\r\n        parseFloat(item.supplierValue || item.supplierAvg) || 0,\r\n        parseFloat(item.testValue || item.testAvg) || 0,\r\n        item.name, // 用于tooltip显示\r\n        index // 数据索引，用于获取详细信息\r\n      ]);\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const [supplierVal, testVal, name, dataIndex] = params.data;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = `${name}<br/>供应商值: ${supplierVal}<br/>测试值: ${testVal}<br/>差值: ${Math.abs(supplierVal - testVal).toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '供应商数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '测试数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        series: [{\r\n          name: '数据对比',\r\n          type: 'scatter',\r\n          data: scatterData,\r\n          symbolSize: 8,\r\n          itemStyle: {\r\n            color: '#5470c6'\r\n          }\r\n        }, {\r\n          name: '理想线',\r\n          type: 'line',\r\n          data: [[0, 0], [Math.max(...scatterData.map(d => d[0])), Math.max(...scatterData.map(d => d[0]))]],\r\n          lineStyle: {\r\n            color: '#ff6b6b',\r\n            type: 'dashed'\r\n          },\r\n          symbol: 'none'\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取雷达图配置 */\r\n    getRadarChartOption() {\r\n      // 雷达图用于多维度对比，基于chartData生成指标\r\n      const indicators = [\r\n        { name: '供应商平均值', max: 100 },\r\n        { name: '测试平均值', max: 100 },\r\n        { name: '数据量', max: 50 },\r\n        { name: '准确率', max: 100 },\r\n        { name: '稳定性', max: 10 }\r\n      ];\r\n\r\n      const radarData = this.chartData.map(item => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const dataCount = parseInt(item.dataCount) || 0;\r\n        const accuracy = parseFloat(item.accuracy) || 0;\r\n        const stability = parseFloat(item.stability) || 0;\r\n\r\n        return {\r\n          name: item.name,\r\n          value: [\r\n            Math.min(supplierAvg, 100),\r\n            Math.min(testAvg, 100),\r\n            Math.min(dataCount, 50),\r\n            Math.min(accuracy, 100),\r\n            Math.min(stability, 10)\r\n          ]\r\n        };\r\n      });\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const dataIndex = params.dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params.name + '<br/>';\r\n\r\n            // 显示雷达图数据\r\n            const indicators = ['供应商平均值', '测试平均值', '数据量', '准确率', '稳定性'];\r\n            params.value.forEach((value, index) => {\r\n              result += indicators[index] + ': ' + value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: radarData.map(item => item.name)\r\n        },\r\n        radar: {\r\n          indicator: indicators,\r\n          radius: '60%'\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          data: radarData\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取热力图配置 */\r\n    getHeatmapChartOption() {\r\n      // 热力图用于展示数据密度和分布\r\n      const xAxisData = [...new Set(this.chartData.map(item => item.name))];\r\n      const yAxisData = ['供应商数据', '测试数据', '偏差'];\r\n\r\n      const heatmapData = [];\r\n      this.chartData.forEach((item, xIndex) => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const deviation = Math.abs(supplierAvg - testAvg);\r\n\r\n        heatmapData.push([xIndex, 0, supplierAvg]); // 供应商数据\r\n        heatmapData.push([xIndex, 1, testAvg]);     // 测试数据\r\n        heatmapData.push([xIndex, 2, deviation]);   // 偏差\r\n      });\r\n\r\n      const maxValue = Math.max(...heatmapData.map(d => d[2]));\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          position: 'top',\r\n          formatter: function(params) {\r\n            const [x, y, value] = params.data;\r\n            const xLabel = xAxisData[x];\r\n            const yLabel = yAxisData[y];\r\n            const currentData = self.chartData[x];\r\n\r\n            let result = `${xLabel}<br/>${yLabel}: ${value.toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        grid: {\r\n          height: '50%',\r\n          top: '15%'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: yAxisData\r\n        },\r\n        visualMap: {\r\n          min: 0,\r\n          max: maxValue || 100,\r\n          calculable: true,\r\n          orient: 'horizontal',\r\n          left: 'center',\r\n          bottom: '5%',\r\n          inRange: {\r\n            color: ['#50a3ba', '#eac736', '#d94e5d']\r\n          }\r\n        },\r\n        series: [{\r\n          type: 'heatmap',\r\n          data: heatmapData,\r\n          label: {\r\n            show: true,\r\n            formatter: function(params) {\r\n              return params.data[2].toFixed(1);\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          }\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 重置查询 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams = {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      };\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 刷新图表 */\r\n    refreshChart() {\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 导出图表 */\r\n    exportChart() {\r\n      if (!this.chart) {\r\n        this.$message.warning('请先生成图表');\r\n        return;\r\n      }\r\n\r\n      const url = this.chart.getDataURL({\r\n        type: 'png',\r\n        pixelRatio: 2,\r\n        backgroundColor: '#fff'\r\n      });\r\n\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${this.chartTitle}_${new Date().getTime()}.png`;\r\n      link.click();\r\n    },\r\n\r\n    /** 切换数据表显示 */\r\n    toggleDataTable() {\r\n      this.showDataTable = !this.showDataTable;\r\n      if (!this.showDataTable && this.chart) {\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 切换项目详情显示 */\r\n    toggleProjectDetails() {\r\n      this.showProjectDetails = !this.showProjectDetails;\r\n    },\r\n\r\n    /** 切换全屏显示 */\r\n    toggleFullscreen() {\r\n      if (this.isFullscreen) {\r\n        this.chartHeight = 400;\r\n        this.isFullscreen = false;\r\n      } else {\r\n        this.chartHeight = window.innerHeight - 200;\r\n        this.isFullscreen = true;\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 显示图表帮助 */\r\n    showChartHelp() {\r\n      this.helpDialogVisible = true;\r\n    },\r\n\r\n    /** 格式化数字显示 */\r\n    formatNumber(value) {\r\n      if (value === null || value === undefined || isNaN(value)) {\r\n        return 'N/A';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (num === 0) return '0';\r\n      if (Math.abs(num) >= 1000000) {\r\n        return (num / 1000000).toFixed(2) + 'M';\r\n      } else if (Math.abs(num) >= 1000) {\r\n        return (num / 1000).toFixed(2) + 'K';\r\n      } else if (Math.abs(num) < 1) {\r\n        return num.toFixed(4);\r\n      } else {\r\n        return num.toFixed(2);\r\n      }\r\n    },\r\n\r\n    /** 格式化参数值显示（支持字符串类型，显示完整数值） */\r\n    formatParamValue(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 如果是字符串类型，直接返回完整字符串\r\n      if (typeof value === 'string') {\r\n        // 尝试解析为数字\r\n        const num = parseFloat(value);\r\n        if (!isNaN(num)) {\r\n          // 如果是数字字符串，保留6位小数并去除尾随零\r\n          return num.toFixed(6).replace(/\\.?0+$/, '');\r\n        }\r\n        // 如果不是数字字符串，直接返回\r\n        return value;\r\n      }\r\n\r\n      // 如果是数字类型，保留6位小数并去除尾随零\r\n      if (typeof value === 'number') {\r\n        return value.toFixed(6).replace(/\\.?0+$/, '');\r\n      }\r\n\r\n      return String(value);\r\n    },\r\n\r\n    /** 获取参数标签类型 */\r\n    getParamTagType(param) {\r\n      if (!param.paramValue) return '';\r\n      const value = parseFloat(param.paramValue);\r\n      if (isNaN(value)) return '';\r\n\r\n      // 根据参数值范围设置不同颜色\r\n      if (value > 100) return 'danger';\r\n      if (value > 50) return 'warning';\r\n      if (value > 10) return 'success';\r\n      return 'info';\r\n    },\r\n\r\n    /** 显示参数详情 */\r\n    showParamDetail(param) {\r\n      this.currentParamDetail = param;\r\n      this.paramDetailDialogVisible = true;\r\n    },\r\n\r\n    /** 更新选中参数详情 */\r\n    updateSelectedParamDetails() {\r\n      // 根据当前选择的对比类型和选项，更新参数详情信息\r\n      const { compareType } = this.queryParams;\r\n      this.selectedParamDetails = [];\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length > 0) {\r\n        this.queryParams.materialNames.forEach(materialId => {\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n          if (material) {\r\n            this.selectedParamDetails.push({\r\n              name: material.materialName,\r\n              materialName: material.materialName,\r\n              supplierName: material.supplierName,\r\n              processType: material.processType,\r\n              testCount: material.testCount || 0,\r\n              statistics: material.statistics\r\n            });\r\n          }\r\n        });\r\n      } else if (compareType === 'supplier' && this.queryParams.supplierNames.length > 0) {\r\n        this.queryParams.supplierNames.forEach(supplier => {\r\n          this.selectedParamDetails.push({\r\n            name: supplier,\r\n            supplierName: supplier,\r\n            testCount: 0 // 这里可以从API获取实际数据\r\n          });\r\n        });\r\n      } else if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length > 0) {\r\n        this.queryParams.paramNumbers.forEach(paramId => {\r\n          const param = this.paramNumberOptions.find(p => p.groupId === paramId);\r\n          if (param) {\r\n            this.selectedParamDetails.push({\r\n              name: param.paramNumber,\r\n              paramNumber: param.paramNumber,\r\n              materialName: param.materialName,\r\n              processType: param.processType,\r\n              mainParams: param.paramItems || [],\r\n              testCount: param.testCount || 0\r\n            });\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-guide-btn {\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-guide-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.guide-btn {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n}\r\n\r\n.export-btn {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\r\n  border: none;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n}\r\n\r\n.statistics-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.statistics-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.statistics-content {\r\n  padding: 20px;\r\n}\r\n\r\n.statistics-title {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.statistics-desc {\r\n  font-size: 12px;\r\n  color: #C0C4CC;\r\n}\r\n\r\n.chart-help-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.chart-help-content h4 {\r\n  color: #409EFF;\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.chart-help-content ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.chart-help-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.chart-help-content strong {\r\n  color: #303133;\r\n}\r\n\r\n/* 使用指南样式 */\r\n.usage-guide-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.usage-guide-card .el-card__header {\r\n  background: transparent;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.guide-item h4 {\r\n  color: #fff;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.guide-item p {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n}\r\n\r\n/* 参数详情卡片样式 */\r\n.param-details-card {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background: white;\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.param-detail-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.detail-section:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.section-title {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-icon {\r\n  color: #909399;\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 60px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 8px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 10px;\r\n  color: #909399;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.params-container {\r\n  max-height: 80px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.params-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* 使用建议样式优化 */\r\n.usage-suggestion {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.usage-suggestion p {\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据表格样式优化 */\r\n.trend-data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.trend-data-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.trend-data-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.param-details-container {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 5px;\r\n}\r\n\r\n.param-detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n  padding: 2px 6px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.param-name {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 5px;\r\n  min-width: 80px;\r\n}\r\n\r\n.param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n  font-family: 'Courier New', monospace;\r\n  margin-right: 5px;\r\n}\r\n\r\n.param-unit {\r\n  color: #909399;\r\n  font-size: 11px;\r\n}\r\n\r\n.material-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.material-info i {\r\n  color: #409EFF;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAojBA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,aAAA;MACA;MACAC,YAAA;MAEA;MACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,YAAA;MACA;MAEA;MACAC,kBAAA;MACAC,eAAA;MACAC,eAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,YAAA;MAEA;MACAC,oBAAA;MAEA;MACAC,cAAA;MAEA;MACAC,iBAAA;MAEA;MACAC,cAAA;MAEA;MACAC,kBAAA;MAEA;MACAC,wBAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,WAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAA/B,KAAA;MACA,KAAAA,KAAA,CAAAgC,OAAA;IACA;EACA;EACAC,OAAA;IACA,YACAJ,SAAA,WAAAA,UAAA;MAAA,IAAAK,KAAA;MACA,KAAAlC,KAAA,GAAAV,OAAA,CAAA6C,IAAA,MAAAC,KAAA,CAAApC,KAAA;;MAEA;MACAqC,MAAA,CAAAC,gBAAA;QACA,IAAAJ,KAAA,CAAAlC,KAAA;UACAkC,KAAA,CAAAlC,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,aACAT,WAAA,WAAAA,YAAA;MAAA,IAAAU,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,EAAA;QAAA,WAAAP,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAC,wCAAA;YAAA;cAAAT,aAAA,GAAAM,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAzB,kBAAA,GAAA+B,aAAA,CAAAW,IAAA;;cAEA;cAAAL,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAI,sBAAA;YAAA;cAAAX,gBAAA,GAAAK,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAxB,eAAA,GAAA+B,gBAAA,CAAAU,IAAA;;cAEA;cAAAL,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAK,gCAAA;gBAAAC,IAAA;cAAA;YAAA;cAAAZ,gBAAA,GAAAI,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAvB,eAAA,GAAA+B,gBAAA,CAAAlD,IAAA;;cAEA;cAAAsD,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAK,gCAAA;gBAAAC,IAAA;cAAA;YAAA;cAAAX,eAAA,GAAAG,QAAA,CAAAI,CAAA;cACAhB,MAAA,CAAAtB,kBAAA,GAAA+B,eAAA,CAAAnD,IAAA;cAAAsD,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAGAK,OAAA,CAAAC,KAAA,cAAAZ,EAAA;cACAV,MAAA,CAAAuB,MAAA,CAAAC,QAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAa,CAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IAEA,aACAqB,uBAAA,WAAAA,wBAAAC,KAAA;MACA;MACA,KAAA5D,WAAA,CAAAE,YAAA;MACA,KAAAF,WAAA,CAAAG,aAAA;MACA,KAAAH,WAAA,CAAAI,aAAA;MACA,KAAAJ,WAAA,CAAAK,YAAA;MACA,KAAAL,WAAA,CAAAM,SAAA;MACA,KAAAN,WAAA,CAAAO,YAAA;;MAEA;MACA,KAAAO,oBAAA;MACA,KAAAF,SAAA;MACA,KAAAG,cAAA;;MAEA;MACA,KAAA8C,gBAAA;IACA;IAEA,aACAA,gBAAA,WAAAA,iBAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAlE,UAAA,GAAAkE,OAAA,MAAA9D,WAAA,CAAAC,WAAA;IACA;IAEA,aACA8D,qBAAA,WAAAA,sBAAAV,IAAA;MACA,KAAA3D,SAAA,GAAA2D,IAAA;MACA,SAAAzC,SAAA,CAAAoD,MAAA;QACA,KAAAC,WAAA;MACA;IACA;IAEA,WACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+B,SAAA;QAAA,IAAAxD,SAAA,EAAAyD,YAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA4B,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,CAAA,GAAA0B,SAAA,CAAAzB,CAAA;YAAA;cAAA,IACAoB,MAAA,CAAAM,aAAA;gBAAAD,SAAA,CAAAzB,CAAA;gBAAA;cAAA;cAAA,OAAAyB,SAAA,CAAAd,CAAA;YAAA;cAIAS,MAAA,CAAA3E,OAAA;cACA2E,MAAA,CAAAtE,YAAA;cAAA2E,SAAA,CAAA1B,CAAA;cAGA;cACAlC,SAAA;cACAyD,YAAA;cAAAC,GAAA,GAEAH,MAAA,CAAAnE,WAAA,CAAAC,WAAA;cAAAuE,SAAA,CAAAzB,CAAA,GAAAuB,GAAA,KACA,iBAAAA,GAAA,KAGA,iBAAAA,GAAA,KAGA,oBAAAA,GAAA,KAGA,oBAAAA,GAAA,KAGA,mBAAAA,GAAA,KAGA;cAAA;YAAA;cAAAE,SAAA,CAAAzB,CAAA;cAAA,OAdAoB,MAAA,CAAAO,sBAAA;YAAA;cAAA9D,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAQ,sBAAA;YAAA;cAAA/D,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAS,yBAAA;YAAA;cAAAhE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAU,yBAAA;YAAA;cAAAjE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAW,gBAAA;YAAA;cAAAlE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAAAc,SAAA,CAAAzB,CAAA;cAAA,OAGAoB,MAAA,CAAAY,qBAAA;YAAA;cAAAnE,SAAA,GAAA4D,SAAA,CAAAvB,CAAA;cAAA,OAAAuB,SAAA,CAAAd,CAAA;YAAA;cAIAS,MAAA,CAAAvD,SAAA,GAAAA,SAAA;cACAuD,MAAA,CAAAa,kBAAA;cACAb,MAAA,CAAAF,WAAA;;cAEA;cACAE,MAAA,CAAAc,0BAAA;cAAAT,SAAA,CAAAzB,CAAA;cAAA;YAAA;cAAAyB,SAAA,CAAA1B,CAAA;cAAAyB,GAAA,GAAAC,SAAA,CAAAvB,CAAA;cAGAK,OAAA,CAAAC,KAAA,cAAAgB,GAAA;cACAJ,MAAA,CAAAX,MAAA,CAAAC,QAAA;YAAA;cAAAe,SAAA,CAAA1B,CAAA;cAEAqB,MAAA,CAAA3E,OAAA;cACA2E,MAAA,CAAAtE,YAAA;cAAA,OAAA2E,SAAA,CAAAU,CAAA;YAAA;cAAA,OAAAV,SAAA,CAAAd,CAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IAEA;IAEA,aACAK,aAAA,WAAAA,cAAA;MACA,IAAAxE,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,IAAAA,WAAA,2BAAAD,WAAA,CAAAE,YAAA,CAAA8D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,wBAAAD,WAAA,CAAAG,aAAA,CAAA6D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,wBAAAD,WAAA,CAAAI,aAAA,CAAA4D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,2BAAAD,WAAA,CAAAK,YAAA,CAAA2D,MAAA;QACA,KAAAmB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,0BAAAD,WAAA,CAAAM,SAAA;QACA,KAAA6E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAAnF,WAAA,+BAAAD,WAAA,CAAAO,YAAA;QACA,KAAA4E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA;IACA;IAEA,eACAV,sBAAA,WAAAA,uBAAA;MAAA,IAAAW,MAAA;MAAA,WAAAnD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiD,SAAA;QAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAAxD,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAiD,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,CAAA,GAAA+C,SAAA,CAAA9C,CAAA;YAAA;cACAwC,WAAA,GAAAF,MAAA,CAAArF,WAAA,CAAAG,aAAA;cACAqF,WAAA;cAAA,MAEAD,WAAA,CAAAvB,MAAA;gBAAA6B,SAAA,CAAA9C,CAAA;gBAAA;cAAA;cAAA,OAAA8C,SAAA,CAAAnC,CAAA,IACA8B,WAAA;YAAA;cAAAC,SAAA,OAAAK,2BAAA,CAAA3D,OAAA,EAGAoD,WAAA;cAAAM,SAAA,CAAA/C,CAAA;cAAA6C,KAAA,oBAAAvD,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAsD,MAAA;gBAAA,IAAAI,UAAA,EAAAC,kBAAA,EAAAC,WAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,GAAA;gBAAA,WAAAzE,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAkE,SAAA;kBAAA,kBAAAA,SAAA,CAAAhE,CAAA,GAAAgE,SAAA,CAAA/D,CAAA;oBAAA;sBAAAgD,UAAA,GAAAL,KAAA,CAAA9B,KAAA;sBAAAkD,SAAA,CAAAhE,CAAA;sBAAAgE,SAAA,CAAA/D,CAAA;sBAAA,OAGA,IAAAC,wCAAA;wBACA+C,UAAA,EAAAA,UAAA;wBACAgB,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAhB,kBAAA,GAAAc,SAAA,CAAA7D,CAAA;sBAMAgD,WAAA,GAAAD,kBAAA,CAAA9C,IAAA;sBACAgD,QAAA,GAAAb,MAAA,CAAA5E,eAAA,CAAAwG,IAAA,WAAA5E,CAAA;wBAAA,OAAAA,CAAA,CAAA0D,UAAA,KAAAA,UAAA;sBAAA;sBAEAI,iBAAA;sBACAC,aAAA,OAEA;sBAAAC,UAAA,OAAAP,2BAAA,CAAA3D,OAAA,EACA8D,WAAA;sBAAAa,SAAA,CAAAhE,CAAA;sBAAAuD,UAAA,CAAAa,CAAA;oBAAA;sBAAA,KAAAZ,MAAA,GAAAD,UAAA,CAAAtD,CAAA,IAAAoE,IAAA;wBAAAL,SAAA,CAAA/D,CAAA;wBAAA;sBAAA;sBAAAwD,KAAA,GAAAD,MAAA,CAAA1C,KAAA;sBAAAkD,SAAA,CAAA/D,CAAA;sBAAA,OACA,IAAAqE,0BAAA;wBACAC,OAAA,EAAAd,KAAA,CAAAc,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAR,YAAA,GAAAM,SAAA,CAAA7D,CAAA;sBAMAwD,WAAA,GAAAD,YAAA,CAAAtD,IAAA;sBACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;sBAAA,GAAAC,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;sBAAA,GAAAF,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBAEAkD,iBAAA,GAAAA,iBAAA,CAAA0B,MAAA,CAAAnB,cAAA;sBACAN,aAAA,GAAAA,aAAA,CAAAyB,MAAA,CAAAlB,UAAA;oBAAA;sBAAAG,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAAhE,CAAA;sBAAA8D,GAAA,GAAAE,SAAA,CAAA7D,CAAA;sBAAAoD,UAAA,CAAAyB,CAAA,CAAAlB,GAAA;oBAAA;sBAAAE,SAAA,CAAAhE,CAAA;sBAAAuD,UAAA,CAAAnB,CAAA;sBAAA,OAAA4B,SAAA,CAAA5B,CAAA;oBAAA;sBAGAM,WAAA,CAAAuC,IAAA;wBACAzI,IAAA,EAAA4G,QAAA,GAAAA,QAAA,CAAA8B,YAAA,kBAAAH,MAAA,CAAA9B,UAAA;wBACAkC,QAAA,EAAA/B,QAAA,GAAAA,QAAA,CAAAgC,YAAA;wBACAC,WAAA,EAAAhC,iBAAA,CAAAnC,MAAA,QAAAmC,iBAAA,CAAAiC,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAAlC,iBAAA,CAAAnC,MAAA,EAAAsE,OAAA;wBACAC,OAAA,EAAAnC,aAAA,CAAApC,MAAA,QAAAoC,aAAA,CAAAgC,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAAjC,aAAA,CAAApC,MAAA,EAAAsE,OAAA;wBACAE,WAAA,EAAArC,iBAAA,CAAAnC,MAAA,OAAAyE,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAgE,iBAAA,GAAAmC,OAAA;wBACAO,OAAA,EAAAzC,aAAA,CAAApC,MAAA,OAAAyE,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAiE,aAAA,GAAAkC,OAAA;wBACAQ,WAAA,EAAA3C,iBAAA,CAAAnC,MAAA,OAAAyE,IAAA,CAAAM,GAAA,CAAAJ,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAgE,iBAAA,GAAAmC,OAAA;wBACAU,OAAA,EAAA5C,aAAA,CAAApC,MAAA,OAAAyE,IAAA,CAAAM,GAAA,CAAAJ,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAiE,aAAA,GAAAkC,OAAA;wBACAW,SAAA,EAAA7C,aAAA,CAAApC;sBACA;sBAAA8C,SAAA,CAAA/D,CAAA;sBAAA;oBAAA;sBAAA+D,SAAA,CAAAhE,CAAA;sBAAA+D,GAAA,GAAAC,SAAA,CAAA7D,CAAA;sBAEAK,OAAA,CAAAC,KAAA,4BAAAsE,MAAA,CAAA9B,UAAA,qCAAAc,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAApD,CAAA;kBAAA;gBAAA,GAAAiC,KAAA;cAAA;cAAAF,SAAA,CAAAyB,CAAA;YAAA;cAAA,KAAAxB,KAAA,GAAAD,SAAA,CAAA1C,CAAA,IAAAoE,IAAA;gBAAAtB,SAAA,CAAA9C,CAAA;gBAAA;cAAA;cAAA,OAAA8C,SAAA,CAAAqD,CAAA,KAAAC,mBAAA,CAAAhH,OAAA,EAAAwD,KAAA;YAAA;cAAAE,SAAA,CAAA9C,CAAA;cAAA;YAAA;cAAA8C,SAAA,CAAA9C,CAAA;cAAA;YAAA;cAAA8C,SAAA,CAAA/C,CAAA;cAAA8C,GAAA,GAAAC,SAAA,CAAA5C,CAAA;cAAAwC,SAAA,CAAAqC,CAAA,CAAAlC,GAAA;YAAA;cAAAC,SAAA,CAAA/C,CAAA;cAAA2C,SAAA,CAAAP,CAAA;cAAA,OAAAW,SAAA,CAAAX,CAAA;YAAA;cAAA,OAAAW,SAAA,CAAAnC,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAAF,QAAA;MAAA;IACA;IAEA,gBACAX,sBAAA,WAAAA,uBAAA;MAAA,IAAAyE,MAAA;MAAA,WAAAlH,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgH,SAAA;QAAA,IAAAC,SAAA,EAAA9D,WAAA,EAAA+D,UAAA,EAAAC,MAAA,EAAAvB,QAAA,EAAAwB,QAAA,EAAAhD,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA+C,GAAA,EAAAC,GAAA;QAAA,WAAAvH,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAgH,SAAA;UAAA,kBAAAA,SAAA,CAAA9G,CAAA,GAAA8G,SAAA,CAAA7G,CAAA;YAAA;cACAuG,SAAA,GAAAF,MAAA,CAAApJ,WAAA,CAAAI,aAAA;cACAoF,WAAA;cAAA,MAEA8D,SAAA,CAAAtF,MAAA;gBAAA4F,SAAA,CAAA7G,CAAA;gBAAA;cAAA;cAAA,OAAA6G,SAAA,CAAAlG,CAAA,IACA8B,WAAA;YAAA;cAAA+D,UAAA,OAAAzD,2BAAA,CAAA3D,OAAA,EAGAmH,SAAA;cAAAM,SAAA,CAAA9G,CAAA;cAAAyG,UAAA,CAAArC,CAAA;YAAA;cAAA,KAAAsC,MAAA,GAAAD,UAAA,CAAAxG,CAAA,IAAAoE,IAAA;gBAAAyC,SAAA,CAAA7G,CAAA;gBAAA;cAAA;cAAAkF,QAAA,GAAAuB,MAAA,CAAA5F,KAAA;cAAAgG,SAAA,CAAA9G,CAAA;cAAA8G,SAAA,CAAA7G,CAAA;cAAA,OAEA,IAAAqE,0BAAA;gBACAc,YAAA,EAAAD,QAAA;gBACAlB,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAAG,SAAA,CAAA3G,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;cAAA,GAAAC,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;cAAA,GAAAF,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cAEAuC,WAAA,CAAAuC,IAAA;gBACAzI,IAAA,EAAA2I,QAAA;gBACAE,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;gBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;gBACAuB,QAAA,EAAAnD,cAAA,CAAA1C,MAAA,QAAA2C,UAAA,CAAA3C,MAAA,OACA,OAAAyE,IAAA,CAAAqB,GAAA,CAAApD,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,GACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,KACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,SAAAsE,OAAA;gBACAW,SAAA,EAAAxC,WAAA,CAAAzC;cACA;cAAA4F,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,CAAA;cAAA4G,GAAA,GAAAE,SAAA,CAAA3G,CAAA;cAEAK,OAAA,CAAAC,KAAA,kCAAAsE,MAAA,CAAAI,QAAA,qCAAAyB,GAAA;YAAA;cAAAE,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA7G,CAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,CAAA;cAAA6G,GAAA,GAAAC,SAAA,CAAA3G,CAAA;cAAAsG,UAAA,CAAAzB,CAAA,CAAA6B,GAAA;YAAA;cAAAC,SAAA,CAAA9G,CAAA;cAAAyG,UAAA,CAAArE,CAAA;cAAA,OAAA0E,SAAA,CAAA1E,CAAA;YAAA;cAAA,OAAA0E,SAAA,CAAAlG,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAA6D,QAAA;MAAA;IACA;IAEA,iBACAzE,yBAAA,WAAAA,0BAAA;MAAA,IAAAmF,MAAA;MAAA,WAAA7H,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA2H,SAAA;QAAA,IAAAC,aAAA,EAAAzE,WAAA,EAAA0E,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,GAAA;QAAA,WAAAjI,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA0H,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,CAAA,GAAAwH,SAAA,CAAAvH,CAAA;YAAA;cACAkH,aAAA,GAAAF,MAAA,CAAA/J,WAAA,CAAAE,YAAA;cACAsF,WAAA;cAAA,MAEAyE,aAAA,CAAAjG,MAAA;gBAAAsG,SAAA,CAAAvH,CAAA;gBAAA;cAAA;cAAA,OAAAuH,SAAA,CAAA5G,CAAA,IACA8B,WAAA;YAAA;cAAA0E,UAAA,OAAApE,2BAAA,CAAA3D,OAAA,EAGA8H,aAAA;cAAAK,SAAA,CAAAxH,CAAA;cAAAsH,MAAA,oBAAAhI,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+H,OAAA;gBAAA,IAAA/C,OAAA,EAAAoC,QAAA,EAAAc,iBAAA,EAAAC,UAAA,EAAA/D,WAAA,EAAAgE,UAAA,EAAA/D,cAAA,EAAAC,UAAA,EAAAtC,YAAA,EAAAqG,GAAA;gBAAA,WAAAtI,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA+H,SAAA;kBAAA,kBAAAA,SAAA,CAAA7H,CAAA,GAAA6H,SAAA,CAAA5H,CAAA;oBAAA;sBAAAsE,OAAA,GAAA8C,MAAA,CAAAvG,KAAA;sBAAA+G,SAAA,CAAA7H,CAAA;sBAAA6H,SAAA,CAAA5H,CAAA;sBAAA,OAGA,IAAAqE,0BAAA;wBACAC,OAAA,EAAAA,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAyC,QAAA,GAAAkB,SAAA,CAAA1H,CAAA;sBAAA0H,SAAA,CAAA5H,CAAA;sBAAA,OAOA,IAAA6H,sCAAA;wBACAvD,OAAA,EAAAA,OAAA;wBACAN,OAAA;wBACAC,QAAA;sBACA;oBAAA;sBAJAuD,iBAAA,GAAAI,SAAA,CAAA1H,CAAA;sBAMAuH,UAAA,GAAAT,MAAA,CAAAvJ,kBAAA,CAAAyG,IAAA,WAAAnE,CAAA;wBAAA,OAAAA,CAAA,CAAAuE,OAAA,KAAAA,OAAA;sBAAA;sBACAZ,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;sBACAuH,UAAA,GAAAF,iBAAA,CAAArH,IAAA;sBAEAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;sBAAA,GAAAC,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA;sBACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;wBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;sBAAA,GAAAF,MAAA,WAAAzE,CAAA;wBAAA,QAAA0E,KAAA,CAAA1E,CAAA;sBAAA,IAEA;sBACAoB,YAAA,GAAAoG,UAAA,CAAAnD,GAAA,WAAAuD,IAAA;wBAAA;0BACAC,SAAA,EAAAD,IAAA,CAAAC,SAAA;0BACAC,UAAA,EAAAF,IAAA,CAAAE,UAAA,aAAAF,IAAA,CAAAE,UAAA,KAAAC,SAAA,GACAC,MAAA,CAAAJ,IAAA,CAAAE,UAAA;0BACAG,IAAA,EAAAL,IAAA,CAAAK,IAAA;wBACA;sBAAA;sBAEA1F,WAAA,CAAAuC,IAAA;wBACAzI,IAAA,EAAAkL,UAAA,GAAAA,UAAA,CAAAW,WAAA,kBAAAtD,MAAA,CAAAR,OAAA;wBACAnB,QAAA,EAAAsE,UAAA,GAAAA,UAAA,CAAAxC,YAAA;wBACAoD,WAAA,EAAAZ,UAAA,GAAAA,UAAA,CAAAY,WAAA;wBACAjD,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;wBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;wBACA+C,SAAA,EAAA3E,cAAA,CAAA1C,MAAA,QAAA2C,UAAA,CAAA3C,MAAA,OACAyE,IAAA,CAAAqB,GAAA,CAAApD,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA3B,cAAA,CAAA1C,MAAA,GACA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;0BAAA,OAAA3E,CAAA,GAAA2E,CAAA;wBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;wBACAW,SAAA,EAAAxC,WAAA,CAAAzC,MAAA;wBACAK,YAAA,EAAAA,YAAA;wBAAA;wBACAgD,OAAA,EAAAA,OAAA;sBACA;sBAAAsD,SAAA,CAAA5H,CAAA;sBAAA;oBAAA;sBAAA4H,SAAA,CAAA7H,CAAA;sBAAA4H,GAAA,GAAAC,SAAA,CAAA1H,CAAA;sBAEAK,OAAA,CAAAC,KAAA,kCAAAsE,MAAA,CAAAR,OAAA,qCAAAqD,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAAjH,CAAA;kBAAA;gBAAA,GAAA0G,MAAA;cAAA;cAAAF,UAAA,CAAAhD,CAAA;YAAA;cAAA,KAAAiD,MAAA,GAAAD,UAAA,CAAAnH,CAAA,IAAAoE,IAAA;gBAAAmD,SAAA,CAAAvH,CAAA;gBAAA;cAAA;cAAA,OAAAuH,SAAA,CAAApB,CAAA,KAAAC,mBAAA,CAAAhH,OAAA,EAAAiI,MAAA;YAAA;cAAAE,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAxH,CAAA;cAAAuH,GAAA,GAAAC,SAAA,CAAArH,CAAA;cAAAiH,UAAA,CAAApC,CAAA,CAAAuC,GAAA;YAAA;cAAAC,SAAA,CAAAxH,CAAA;cAAAoH,UAAA,CAAAhF,CAAA;cAAA,OAAAoF,SAAA,CAAApF,CAAA;YAAA;cAAA,OAAAoF,SAAA,CAAA5G,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA;IACA;IAEA,iBACAnF,yBAAA,WAAAA,0BAAA;MAAA,IAAAyG,MAAA;MAAA,WAAApJ,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAkJ,SAAA;QAAA,IAAAlL,YAAA,EAAAmF,WAAA,EAAAgG,UAAA,EAAAC,MAAA,EAAAL,WAAA,EAAA3B,QAAA,EAAAhD,WAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA+E,GAAA,EAAAC,IAAA;QAAA,WAAAvJ,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAAgJ,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,CAAA,GAAA8I,SAAA,CAAA7I,CAAA;YAAA;cACA1C,YAAA,GAAAiL,MAAA,CAAAtL,WAAA,CAAAK,YAAA;cACAmF,WAAA;cAAA,MAEAnF,YAAA,CAAA2D,MAAA;gBAAA4H,SAAA,CAAA7I,CAAA;gBAAA;cAAA;cAAA,OAAA6I,SAAA,CAAAlI,CAAA,IACA8B,WAAA;YAAA;cAAAgG,UAAA,OAAA1F,2BAAA,CAAA3D,OAAA,EAGA9B,YAAA;cAAAuL,SAAA,CAAA9I,CAAA;cAAA0I,UAAA,CAAAtE,CAAA;YAAA;cAAA,KAAAuE,MAAA,GAAAD,UAAA,CAAAzI,CAAA,IAAAoE,IAAA;gBAAAyE,SAAA,CAAA7I,CAAA;gBAAA;cAAA;cAAAqI,WAAA,GAAAK,MAAA,CAAA7H,KAAA;cAAAgI,SAAA,CAAA9I,CAAA;cAAA8I,SAAA,CAAA7I,CAAA;cAAA,OAEA,IAAAqE,0BAAA;gBACAgE,WAAA,EAAAA,WAAA;gBACArE,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAAmC,SAAA,CAAA3I,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAwD,cAAA,GAAAD,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAE,oBAAA;cAAA,GAAAC,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cACA0D,UAAA,GAAAF,WAAA,CAAAa,GAAA,WAAAC,CAAA;gBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;cAAA,GAAAF,MAAA,WAAAzE,CAAA;gBAAA,QAAA0E,KAAA,CAAA1E,CAAA;cAAA;cAEAuC,WAAA,CAAAuC,IAAA;gBACAzI,IAAA,EAAA8L,WAAA;gBACAjD,WAAA,EAAAzB,cAAA,CAAA1C,MAAA,QAAA0C,cAAA,CAAA0B,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA3B,cAAA,CAAA1C,MAAA,EAAAsE,OAAA;gBACAC,OAAA,EAAA5B,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;kBAAA,OAAA3E,CAAA,GAAA2E,CAAA;gBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;gBACAuD,SAAA,EAAAlF,UAAA,CAAA3C,MAAA,OAAAsH,MAAA,CAAAQ,0BAAA,CAAAnF,UAAA,EAAA2B,OAAA;gBACAW,SAAA,EAAAxC,WAAA,CAAAzC;cACA;cAAA4H,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA9I,CAAA;cAAA4I,GAAA,GAAAE,SAAA,CAAA3I,CAAA;cAEAK,OAAA,CAAAC,KAAA,wCAAAsE,MAAA,CAAAuD,WAAA,qCAAAM,GAAA;YAAA;cAAAE,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA7I,CAAA;cAAA;YAAA;cAAA6I,SAAA,CAAA9I,CAAA;cAAA6I,IAAA,GAAAC,SAAA,CAAA3I,CAAA;cAAAuI,UAAA,CAAA1D,CAAA,CAAA6D,IAAA;YAAA;cAAAC,SAAA,CAAA9I,CAAA;cAAA0I,UAAA,CAAAtG,CAAA;cAAA,OAAA0G,SAAA,CAAA1G,CAAA;YAAA;cAAA,OAAA0G,SAAA,CAAAlI,CAAA,IAIA8B,WAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA;IACA;IAEA,eACAzG,gBAAA,WAAAA,iBAAA;MAAA,IAAAiH,MAAA;MAAA,WAAA7J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA2J,SAAA;QAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAAC,OAAA,EAAA1C,QAAA,EAAAhD,WAAA,EAAA2F,SAAA,EAAAC,UAAA,EAAAC,IAAA;QAAA,WAAAlK,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA2J,SAAA;UAAA,kBAAAA,SAAA,CAAAzJ,CAAA,GAAAyJ,SAAA,CAAAxJ,CAAA;YAAA;cAAA,MACA,CAAAgJ,MAAA,CAAA/L,WAAA,CAAAM,SAAA,IAAAyL,MAAA,CAAA/L,WAAA,CAAAM,SAAA,CAAA0D,MAAA;gBAAAuI,SAAA,CAAAxJ,CAAA;gBAAA;cAAA;cAAA,OAAAwJ,SAAA,CAAA7I,CAAA,IACA;YAAA;cAAA6I,SAAA,CAAAzJ,CAAA;cAAAmJ,qBAAA,OAAAO,eAAA,CAAArK,OAAA,EAIA4J,MAAA,CAAA/L,WAAA,CAAAM,SAAA,MAAA4L,SAAA,GAAAD,qBAAA,KAAAE,OAAA,GAAAF,qBAAA;cAAAM,SAAA,CAAAxJ,CAAA;cAAA,OACA,IAAAqE,0BAAA;gBACA8E,SAAA,EAAAA,SAAA;gBACAC,OAAA,EAAAA,OAAA;gBACApF,OAAA;gBACAC,QAAA;cACA;YAAA;cALAyC,QAAA,GAAA8C,SAAA,CAAAtJ,CAAA;cAOAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAkJ,SAAA,OAEA;cACAC,UAAA;cACA5F,WAAA,CAAAgG,OAAA,WAAAC,MAAA;gBACA,IAAAC,IAAA,GAAAD,MAAA,CAAAE,UAAA,GAAAF,MAAA,CAAAE,UAAA,CAAAC,KAAA;gBACA,IAAAF,IAAA,KAAAN,UAAA,CAAAM,IAAA;kBACAN,UAAA,CAAAM,IAAA;gBACA;gBACA,IAAAA,IAAA;kBACAN,UAAA,CAAAM,IAAA,EAAA5E,IAAA,CAAA2E,MAAA;gBACA;cACA;;cAEA;cACAI,MAAA,CAAAC,IAAA,CAAAV,UAAA,EAAAW,IAAA,GAAAP,OAAA,WAAAE,IAAA;gBACA,IAAAM,UAAA,GAAAZ,UAAA,CAAAM,IAAA;gBACA,IAAAhG,UAAA,GAAAsG,UAAA,CAAA3F,GAAA,WAAAC,CAAA;kBAAA,OAAAC,UAAA,CAAAD,CAAA,CAAAK,SAAA;gBAAA,GAAAF,MAAA,WAAAzE,CAAA;kBAAA,QAAA0E,KAAA,CAAA1E,CAAA;gBAAA;gBAEAmJ,SAAA,CAAArE,IAAA;kBACA4E,IAAA,EAAAA,IAAA;kBACAO,QAAA,EAAAvG,UAAA,CAAA3C,MAAA,QAAA2C,UAAA,CAAAyB,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;oBAAA,OAAA3E,CAAA,GAAA2E,CAAA;kBAAA,QAAA1B,UAAA,CAAA3C,MAAA,EAAAsE,OAAA;kBACA6E,KAAA,EAAAF,UAAA,CAAAjJ;gBACA;cACA;cAAA,OAAAuI,SAAA,CAAA7I,CAAA,IAEA0I,SAAA;YAAA;cAAAG,SAAA,CAAAzJ,CAAA;cAAAwJ,IAAA,GAAAC,SAAA,CAAAtJ,CAAA;cAEAK,OAAA,CAAAC,KAAA,gBAAA+I,IAAA;cAAA,OAAAC,SAAA,CAAA7I,CAAA,IACA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IAEA;IAEA,qBACAjH,qBAAA,WAAAA,sBAAA;MAAA,IAAAqI,MAAA;MAAA,WAAAlL,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgL,SAAA;QAAA,IAAAhG,OAAA,EAAAoC,QAAA,EAAAhD,WAAA,EAAAjB,WAAA,EAAA8H,IAAA;QAAA,WAAAlL,aAAA,CAAAD,OAAA,IAAAS,CAAA,WAAA2K,SAAA;UAAA,kBAAAA,SAAA,CAAAzK,CAAA,GAAAyK,SAAA,CAAAxK,CAAA;YAAA;cACAsE,OAAA,GAAA+F,MAAA,CAAApN,WAAA,CAAAO,YAAA;cAAA,IAEA8G,OAAA;gBAAAkG,SAAA,CAAAxK,CAAA;gBAAA;cAAA;cAAA,OAAAwK,SAAA,CAAA7J,CAAA,IACA;YAAA;cAAA6J,SAAA,CAAAzK,CAAA;cAAAyK,SAAA,CAAAxK,CAAA;cAAA,OAIA,IAAAqE,0BAAA;gBACAC,OAAA,EAAAA,OAAA;gBACAN,OAAA;gBACAC,QAAA;cACA;YAAA;cAJAyC,QAAA,GAAA8D,SAAA,CAAAtK,CAAA;cAMAwD,WAAA,GAAAgD,QAAA,CAAAvG,IAAA;cACAsC,WAAA,GAAAiB,WAAA,CAAAa,GAAA,WAAAoF,MAAA;gBAAA;kBACApN,IAAA,EAAAoN,MAAA,CAAA1E,YAAA;kBACAC,QAAA,EAAAyE,MAAA,CAAAxE,YAAA;kBACAsF,aAAA,EAAAhG,UAAA,CAAAkF,MAAA,CAAAjF,oBAAA;kBACAG,SAAA,EAAAJ,UAAA,CAAAkF,MAAA,CAAA9E,SAAA;kBACA6F,UAAA,EAAAhF,IAAA,CAAAqB,GAAA,EAAAtC,UAAA,CAAAkF,MAAA,CAAAjF,oBAAA,WAAAD,UAAA,CAAAkF,MAAA,CAAA9E,SAAA,SAAAU,OAAA;kBACAsE,UAAA,EAAAF,MAAA,CAAAE;gBACA;cAAA;cAAA,OAAAW,SAAA,CAAA7J,CAAA,IAEA8B,WAAA;YAAA;cAAA+H,SAAA,CAAAzK,CAAA;cAAAwK,IAAA,GAAAC,SAAA,CAAAtK,CAAA;cAEAK,OAAA,CAAAC,KAAA,oBAAA+J,IAAA;cAAA,OAAAC,SAAA,CAAA7J,CAAA,IACA;UAAA;QAAA,GAAA2J,QAAA;MAAA;IAEA;IAEA,YACAvB,0BAAA,WAAAA,2BAAA4B,MAAA;MACA,IAAAC,GAAA,GAAAD,MAAA,CAAAtF,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;QAAA,OAAA3E,CAAA,GAAA2E,CAAA;MAAA,QAAAqF,MAAA,CAAA1J,MAAA;MACA,IAAA4J,WAAA,GAAAF,MAAA,CAAApG,GAAA,WAAA1D,KAAA;QAAA,OAAA6E,IAAA,CAAAoF,GAAA,CAAAjK,KAAA,GAAA+J,GAAA;MAAA;MACA,IAAAG,aAAA,GAAAF,WAAA,CAAAxF,MAAA,WAAA1E,CAAA,EAAA2E,CAAA;QAAA,OAAA3E,CAAA,GAAA2E,CAAA;MAAA,QAAAuF,WAAA,CAAA5J,MAAA;MACA,OAAAyE,IAAA,CAAAsF,IAAA,CAAAD,aAAA;IACA;IAEA,gBACAE,kBAAA,WAAAA,mBAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA,KAAAC,KAAA,CAAAC,OAAA,CAAAF,SAAA;QACA;MACA;MAEA,OAAAA,SAAA,CAAA7G,GAAA,WAAAgH,KAAA;QACA,IAAAC,IAAA,GAAAD,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;QACA,IAAAuD,KAAA,CAAApD,IAAA;UACAqD,IAAA,UAAAD,KAAA,CAAApD,IAAA;QACA;QACA,OAAAqD,IAAA;MACA,GAAAC,IAAA;IACA;IAEA,YACAxJ,kBAAA,WAAAA,mBAAA;MACA,IAAA/E,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,QAAAA,WAAA;QACA;UACA,KAAAY,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;QACA;UACA,KAAA9N,YAAA,IACA;YAAA4N,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAF,IAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA,EACA;UACA;MACA;IACA;IAEA,WACA1K,WAAA,WAAAA,YAAA;MACA,UAAAxE,KAAA,SAAAmB,SAAA,CAAAoD,MAAA;QACA;MACA;MAEA,IAAA4K,MAAA;MAEA,aAAAlP,SAAA;QACA;UACAkP,MAAA,QAAAC,kBAAA;UACA;QACA;UACAD,MAAA,QAAAE,iBAAA;UACA;QACA;UACAF,MAAA,QAAAG,qBAAA;UACA;QACA;UACAH,MAAA,QAAAI,mBAAA;UACA;QACA;UACAJ,MAAA,QAAAK,qBAAA;UACA;MACA;MAEA,KAAAxP,KAAA,CAAAyP,SAAA,CAAAN,MAAA;IACA;IAEA,cACAC,kBAAA,WAAAA,mBAAA;MACA;MACA,IAAA5O,WAAA,QAAAD,WAAA,CAAAC,WAAA;MAEA,IAAAA,WAAA;QACA;QACA;UACAkP,KAAA;YACAZ,IAAA,OAAA3O,UAAA;YACAwP,IAAA;UACA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA,WAAAA,UAAAC,MAAA;cACA,IAAA9C,MAAA,GAAA8C,MAAA,IAAAlQ,IAAA;cACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,IAAA4B,KAAA,CAAAmB,MAAA,GAAAnB,KAAA,CAAAoB,UAAA,UAAApB,KAAA,CAAA1K,KAAA;cACA;cACA,OAAA8I,MAAA;YACA;UACA;UACAiD,IAAA;YACAP,IAAA;YACAQ,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACA1M,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAA8B,IAAA;YAAA;UACA;UACAqD,KAAA;YACA3M,IAAA;YACA/D,IAAA;UACA;UACA2Q,MAAA;YACA3Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAqC,QAAA;YAAA;YACAgD,MAAA;YACAC,MAAA;YACAC,UAAA;UACA;QACA;MACA;QACA;QACA,IAAAC,IAAA;QAEA;UACAlB,KAAA;YACAZ,IAAA,OAAA3O,UAAA;YACAwP,IAAA;UACA;UACAC,OAAA;YACAC,OAAA;YACAgB,eAAA;YACAC,WAAA;YACAC,WAAA;YACAC,SAAA;cACAC,KAAA;cACAC,QAAA;YACA;YACAC,YAAA;YACArB,SAAA,WAAAA,UAAAC,MAAA;cACA,IAAAqB,SAAA,GAAArB,MAAA,IAAAqB,SAAA;cACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;cAEA,IAAAnE,MAAA,wIAAA7E,MAAA,CACA2H,MAAA,IAAAlQ,IAAA,uCACA;;cAEA;cACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;gBACA,IAAAoC,KAAA,GAAApC,KAAA,CAAAoC,KAAA;gBACAhE,MAAA,iLAAA7E,MAAA,CACA6I,KAAA,sHAAA7I,MAAA,CACAyG,KAAA,CAAAoB,UAAA,+GAAA7H,MAAA,CACAwI,IAAA,CAAAU,YAAA,CAAAzC,KAAA,CAAA1K,KAAA,6CACA;cACA;;cAEA;cACA,IAAAkN,WAAA;gBACApE,MAAA;gBAEA,IAAA2D,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,CAAAzM,YAAA;kBACAqI,MAAA;kBACA,IAAAoE,WAAA,CAAA5K,QAAA;oBACAwG,MAAA,0FAAA7E,MAAA,CAAAiJ,WAAA,CAAA5K,QAAA;kBACA;kBACA,IAAA4K,WAAA,CAAA1F,WAAA;oBACAsB,MAAA,0FAAA7E,MAAA,CAAAiJ,WAAA,CAAA1F,WAAA;kBACA;kBACA,IAAA0F,WAAA,CAAAzM,YAAA,IAAAyM,WAAA,CAAAzM,YAAA,CAAAL,MAAA;oBACA0I,MAAA;oBACAoE,WAAA,CAAAzM,YAAA,CAAA2M,KAAA,OAAAvE,OAAA,WAAA6B,KAAA;sBACA5B,MAAA,mHAAA7E,MAAA,CACAyG,KAAA,CAAAxD,SAAA,wCAAAjD,MAAA,CAAAwI,IAAA,CAAAU,YAAA,CAAAzC,KAAA,CAAAvD,UAAA,gDAAAlD,MAAA,CACAyG,KAAA,CAAApD,IAAA,uCAAAoD,KAAA,CAAApD,IAAA,4DACA;oBACA;oBACA,IAAA4F,WAAA,CAAAzM,YAAA,CAAAL,MAAA;sBACA0I,MAAA,6IAAA7E,MAAA,CACAiJ,WAAA,CAAAzM,YAAA,CAAAL,MAAA,kEACA;oBACA;kBACA;gBACA,WAAAqM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,mBAAA6Q,WAAA,CAAA7I,QAAA;kBACAyE,MAAA;kBACAA,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7I,QAAA;kBACA,IAAA6I,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA,WAAAoH,IAAA,CAAArQ,WAAA,CAAAC,WAAA,mBAAA6Q,WAAA,CAAAjH,QAAA;kBACA6C,MAAA;kBACAA,MAAA,qHAAA7E,MAAA,CAAAiJ,WAAA,CAAAjH,QAAA,oBAAAiH,WAAA,CAAAjH,QAAA,uCAAAhC,MAAA,CAAAiJ,WAAA,CAAAjH,QAAA;kBACA,IAAAiH,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA,WAAAoH,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,CAAAjF,SAAA;kBACAa,MAAA;kBACAA,MAAA,qHAAA7E,MAAA,CAAAiJ,WAAA,CAAAjF,SAAA,oBAAAiF,WAAA,CAAAjF,SAAA,uCAAAhE,MAAA,CAAAiJ,WAAA,CAAAjF,SAAA;kBACA,IAAAiF,WAAA,CAAA7H,SAAA;oBACAyD,MAAA,gGAAA7E,MAAA,CAAAiJ,WAAA,CAAA7H,SAAA;kBACA;gBACA;gBAEAyD,MAAA;cACA;cAEA,OAAAA,MAAA;YACA;UACA;UACAuE,MAAA;YACAC,GAAA;YACA3R,IAAA;UACA;UACAoQ,IAAA;YACAP,IAAA;YACAQ,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACA1M,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAvL,IAAA;YAAA;UACA;UACA0Q,KAAA;YACA3M,IAAA;YACA/D,IAAA;UACA;UACA2Q,MAAA,GACA;YACA3Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAA1C,WAAA;YAAA;YACA+H,MAAA;YACAC,MAAA;YACAC,UAAA;UACA,GACA;YACA9Q,IAAA;YACA+D,IAAA;YACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;cAAA,OAAAA,IAAA,CAAAtC,OAAA;YAAA;YACA2H,MAAA;YACAC,MAAA;YACAC,UAAA;UACA;QAEA;MACA;IACA;IAEA,cACAtB,iBAAA,WAAAA,kBAAA;MACA,IAAAuB,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACA6B,WAAA;YACA9N,IAAA;UACA;UACAkM,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqB,SAAA,GAAArB,MAAA,IAAAqB,SAAA;YACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,GAAA8C,MAAA,IAAAlQ,IAAA;;YAEA;YACAkQ,MAAA,CAAA/C,OAAA,WAAA6B,KAAA;cACA5B,MAAA,IAAA4B,KAAA,CAAAmB,MAAA,GAAAnB,KAAA,CAAAoB,UAAA,UAAApB,KAAA,CAAA1K,KAAA;YACA;;YAEA;YACA,IAAAyM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAuE,MAAA;UACAC,GAAA;UACA3R,IAAA;QACA;QACAoQ,IAAA;UACAP,IAAA;UACAQ,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACA1M,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAvL,IAAA;UAAA;UACA8R,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA;QACAtB,KAAA;UACA3M,IAAA;UACA/D,IAAA;QACA;QACA2Q,MAAA,GACA;UACA3Q,IAAA;UACA+D,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAA1C,WAAA;UAAA;UACAoJ,QAAA;UACAC,SAAA;YACAd,KAAA;UACA;QACA,GACA;UACApR,IAAA;UACA+D,IAAA;UACA9D,IAAA,OAAAqB,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAtC,OAAA;UAAA;UACAgJ,QAAA;UACAC,SAAA;YACAd,KAAA;UACA;QACA;MAEA;IACA;IAEA,cACA3B,qBAAA,WAAAA,sBAAA;MACA,IAAAsB,IAAA;;MAEA;MACA,IAAAoB,WAAA,QAAA7Q,SAAA,CAAA0G,GAAA,WAAAuD,IAAA,EAAA6G,KAAA;QAAA,QACAlK,UAAA,CAAAqD,IAAA,CAAA2C,aAAA,IAAA3C,IAAA,CAAA1C,WAAA,QACAX,UAAA,CAAAqD,IAAA,CAAAjD,SAAA,IAAAiD,IAAA,CAAAtC,OAAA,QACAsC,IAAA,CAAAvL,IAAA;QAAA;QACAoS,KAAA;QAAA,CACA;MAAA;MAEA;QACAvC,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAmC,YAAA,OAAAnF,eAAA,CAAArK,OAAA,EAAAqN,MAAA,CAAAjQ,IAAA;cAAAqS,WAAA,GAAAD,YAAA;cAAAE,OAAA,GAAAF,YAAA;cAAArS,IAAA,GAAAqS,YAAA;cAAAd,SAAA,GAAAc,YAAA;YACA,IAAAb,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,MAAA7E,MAAA,CAAAvI,IAAA,qCAAAuI,MAAA,CAAA+J,WAAA,+BAAA/J,MAAA,CAAAgK,OAAA,yBAAAhK,MAAA,CAAAY,IAAA,CAAAqB,GAAA,CAAA8H,WAAA,GAAAC,OAAA,EAAAvJ,OAAA;;YAEA;YACA,IAAA+H,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAqD,KAAA;UACA1M,IAAA;UACA/D,IAAA;UACAwS,KAAA;UACAV,SAAA;YACA7B,SAAA;UACA;QACA;QACAS,KAAA;UACA3M,IAAA;UACA/D,IAAA;UACAwS,KAAA;UACAV,SAAA;YACA7B,SAAA;UACA;QACA;QACAU,MAAA;UACA3Q,IAAA;UACA+D,IAAA;UACA9D,IAAA,EAAAkS,WAAA;UACArB,UAAA;UACAoB,SAAA;YACAd,KAAA;UACA;QACA;UACApR,IAAA;UACA+D,IAAA;UACA9D,IAAA,YAAAkJ,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsP,WAAA,CAAAnK,GAAA,WAAA4B,CAAA;YAAA,OAAAA,CAAA;UAAA,MAAAT,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsP,WAAA,CAAAnK,GAAA,WAAA4B,CAAA;YAAA,OAAAA,CAAA;UAAA;UACA6I,SAAA;YACArB,KAAA;YACArN,IAAA;UACA;UACA8M,MAAA;QACA;MACA;IACA;IAEA,cACAnB,mBAAA,WAAAA,oBAAA;MACA;MACA,IAAAgD,UAAA,IACA;QAAA1S,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,GACA;QAAApJ,IAAA;QAAAoJ,GAAA;MAAA,EACA;MAEA,IAAAuJ,SAAA,QAAArR,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;QACA,IAAA1C,WAAA,GAAAX,UAAA,CAAAqD,IAAA,CAAA1C,WAAA;QACA,IAAAI,OAAA,GAAAf,UAAA,CAAAqD,IAAA,CAAAtC,OAAA;QACA,IAAAU,SAAA,GAAAiJ,QAAA,CAAArH,IAAA,CAAA5B,SAAA;QACA,IAAAY,QAAA,GAAArC,UAAA,CAAAqD,IAAA,CAAAhB,QAAA;QACA,IAAAgC,SAAA,GAAArE,UAAA,CAAAqD,IAAA,CAAAgB,SAAA;QAEA;UACAvM,IAAA,EAAAuL,IAAA,CAAAvL,IAAA;UACAsE,KAAA,GACA6E,IAAA,CAAAM,GAAA,CAAAZ,WAAA,QACAM,IAAA,CAAAM,GAAA,CAAAR,OAAA,QACAE,IAAA,CAAAM,GAAA,CAAAE,SAAA,OACAR,IAAA,CAAAM,GAAA,CAAAc,QAAA,QACApB,IAAA,CAAAM,GAAA,CAAA8C,SAAA;QAEA;MACA;MAEA,IAAAwE,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqB,SAAA,GAAArB,MAAA,CAAAqB,SAAA;YACA,IAAAC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAiQ,SAAA;YAEA,IAAAnE,MAAA,GAAA8C,MAAA,CAAAlQ,IAAA;;YAEA;YACA,IAAA0S,UAAA;YACAxC,MAAA,CAAA5L,KAAA,CAAA6I,OAAA,WAAA7I,KAAA,EAAA8N,KAAA;cACAhF,MAAA,IAAAsF,UAAA,CAAAN,KAAA,WAAA9N,KAAA;YACA;;YAEA;YACA,IAAAyM,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAuE,MAAA;UACAC,GAAA;UACA3R,IAAA,EAAA0S,SAAA,CAAA3K,GAAA,WAAAuD,IAAA;YAAA,OAAAA,IAAA,CAAAvL,IAAA;UAAA;QACA;QACA6S,KAAA;UACAC,SAAA,EAAAJ,UAAA;UACAK,MAAA;QACA;QACApC,MAAA;UACA5M,IAAA;UACA9D,IAAA,EAAA0S;QACA;MACA;IACA;IAEA,cACAhD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAqD,SAAA,OAAA1J,mBAAA,CAAAzG,OAAA,MAAAoQ,GAAA,MAAA3R,SAAA,CAAA0G,GAAA,WAAAuD,IAAA;QAAA,OAAAA,IAAA,CAAAvL,IAAA;MAAA;MACA,IAAAkT,SAAA;MAEA,IAAAC,WAAA;MACA,KAAA7R,SAAA,CAAA6L,OAAA,WAAA5B,IAAA,EAAA6H,MAAA;QACA,IAAAvK,WAAA,GAAAX,UAAA,CAAAqD,IAAA,CAAA1C,WAAA;QACA,IAAAI,OAAA,GAAAf,UAAA,CAAAqD,IAAA,CAAAtC,OAAA;QACA,IAAA8C,SAAA,GAAA5C,IAAA,CAAAqB,GAAA,CAAA3B,WAAA,GAAAI,OAAA;QAEAkK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAAvK,WAAA;QACAsK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAAnK,OAAA;QACAkK,WAAA,CAAA1K,IAAA,EAAA2K,MAAA,KAAArH,SAAA;MACA;MAEA,IAAAsH,QAAA,GAAAlK,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAzG,OAAA,EAAAsQ,WAAA,CAAAnL,GAAA,WAAA4B,CAAA;QAAA,OAAAA,CAAA;MAAA;MAEA,IAAAmH,IAAA;MAEA;QACAlB,KAAA;UACAZ,IAAA,OAAA3O,UAAA;UACAwP,IAAA;QACA;QACAC,OAAA;UACAuD,QAAA;UACArD,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAqD,aAAA,OAAArG,eAAA,CAAArK,OAAA,EAAAqN,MAAA,CAAAjQ,IAAA;cAAAuT,CAAA,GAAAD,aAAA;cAAAE,CAAA,GAAAF,aAAA;cAAAjP,KAAA,GAAAiP,aAAA;YACA,IAAAG,MAAA,GAAAV,SAAA,CAAAQ,CAAA;YACA,IAAAG,MAAA,GAAAT,SAAA,CAAAO,CAAA;YACA,IAAAjC,WAAA,GAAAT,IAAA,CAAAzP,SAAA,CAAAkS,CAAA;YAEA,IAAApG,MAAA,MAAA7E,MAAA,CAAAmL,MAAA,WAAAnL,MAAA,CAAAoL,MAAA,QAAApL,MAAA,CAAAjE,KAAA,CAAA0E,OAAA;;YAEA;YACA,IAAA+H,IAAA,CAAArQ,WAAA,CAAAC,WAAA,sBAAA6Q,WAAA,IAAAA,WAAA,CAAAzM,YAAA;cACAqI,MAAA;cACA,IAAAoE,WAAA,CAAA5K,QAAA;gBACAwG,MAAA,YAAAoE,WAAA,CAAA5K,QAAA;cACA;cACA,IAAA4K,WAAA,CAAA1F,WAAA;gBACAsB,MAAA,YAAAoE,WAAA,CAAA1F,WAAA;cACA;cACAsB,MAAA;cAEAoE,WAAA,CAAAzM,YAAA,CAAAoI,OAAA,WAAA6B,KAAA;gBACA5B,MAAA,WAAA4B,KAAA,CAAAxD,SAAA,UAAAwD,KAAA,CAAAvD,UAAA;gBACA,IAAAuD,KAAA,CAAApD,IAAA;kBACAwB,MAAA,UAAA4B,KAAA,CAAApD,IAAA;gBACA;gBACAwB,MAAA;cACA;YACA;YAEA,OAAAA,MAAA;UACA;QACA;QACAiD,IAAA;UACAuD,MAAA;UACAhC,GAAA;QACA;QACAnB,KAAA;UACA1M,IAAA;UACA9D,IAAA,EAAA+S,SAAA;UACAlB,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA;QACAtB,KAAA;UACA3M,IAAA;UACA9D,IAAA,EAAAiT;QACA;QACAW,SAAA;UACApK,GAAA;UACAL,GAAA,EAAAiK,QAAA;UACAS,UAAA;UACAC,MAAA;UACAjE,IAAA;UACAS,MAAA;UACAyD,OAAA;YACA5C,KAAA;UACA;QACA;QACAT,MAAA;UACA5M,IAAA;UACA9D,IAAA,EAAAkT,WAAA;UACA/D,KAAA;YACA6E,IAAA;YACAhE,SAAA,WAAAA,UAAAC,MAAA;cACA,OAAAA,MAAA,CAAAjQ,IAAA,IAAA+I,OAAA;YACA;UACA;UACAkL,QAAA;YACAhC,SAAA;cACAiC,UAAA;cACAC,WAAA;YACA;UACA;QACA;MACA;IACA;IAEA,WACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAA5T,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,YAAA;MACA;MACA,KAAAsD,gBAAA;IACA;IAEA,WACAgQ,YAAA,WAAAA,aAAA;MACA,SAAAjT,SAAA,CAAAoD,MAAA;QACA,KAAAC,WAAA;MACA;IACA;IAEA,WACA6P,WAAA,WAAAA,YAAA;MACA,UAAArU,KAAA;QACA,KAAA0F,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,IAAA2O,GAAA,QAAAtU,KAAA,CAAAuU,UAAA;QACA3Q,IAAA;QACA4Q,UAAA;QACA3D,eAAA;MACA;MAEA,IAAA4D,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,IAAA,CAAAI,QAAA,MAAAzM,MAAA,MAAAjI,UAAA,OAAAiI,MAAA,KAAA0M,IAAA,GAAAC,OAAA;MACAN,IAAA,CAAAO,KAAA;IACA;IAEA,cACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA7U,aAAA,SAAAA,aAAA;MACA,UAAAA,aAAA,SAAAL,KAAA;QACA,KAAAmV,SAAA;UACAD,MAAA,CAAAlV,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,eACA6S,oBAAA,WAAAA,qBAAA;MACA,KAAA3T,kBAAA,SAAAA,kBAAA;IACA;IAEA,aACA4T,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,SAAAhV,YAAA;QACA,KAAAJ,WAAA;QACA,KAAAI,YAAA;MACA;QACA,KAAAJ,WAAA,GAAAmC,MAAA,CAAAkT,WAAA;QACA,KAAAjV,YAAA;MACA;MAEA,KAAA6U,SAAA;QACA,IAAAG,MAAA,CAAAtV,KAAA;UACAsV,MAAA,CAAAtV,KAAA,CAAAuC,MAAA;QACA;MACA;IACA;IAEA,aACAiT,aAAA,WAAAA,cAAA;MACA,KAAAjU,iBAAA;IACA;IAEA,cACA+P,YAAA,WAAAA,aAAAnN,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAoH,SAAA,IAAArD,KAAA,CAAA/D,KAAA;QACA;MACA;MACA,IAAAsR,GAAA,GAAA1N,UAAA,CAAA5D,KAAA;MACA,IAAAsR,GAAA;MACA,IAAAzM,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,QAAAA,GAAA,YAAA5M,OAAA;MACA,WAAAG,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,QAAAA,GAAA,SAAA5M,OAAA;MACA,WAAAG,IAAA,CAAAqB,GAAA,CAAAoL,GAAA;QACA,OAAAA,GAAA,CAAA5M,OAAA;MACA;QACA,OAAA4M,GAAA,CAAA5M,OAAA;MACA;IACA;IAEA,+BACA6M,gBAAA,WAAAA,iBAAAvR,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAoH,SAAA,IAAApH,KAAA;QACA;MACA;;MAEA;MACA,WAAAA,KAAA;QACA;QACA,IAAAsR,GAAA,GAAA1N,UAAA,CAAA5D,KAAA;QACA,KAAA+D,KAAA,CAAAuN,GAAA;UACA;UACA,OAAAA,GAAA,CAAA5M,OAAA,IAAA8M,OAAA;QACA;QACA;QACA,OAAAxR,KAAA;MACA;;MAEA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA,CAAA0E,OAAA,IAAA8M,OAAA;MACA;MAEA,OAAAnK,MAAA,CAAArH,KAAA;IACA;IAEA,eACAyR,eAAA,WAAAA,gBAAA/G,KAAA;MACA,KAAAA,KAAA,CAAAvD,UAAA;MACA,IAAAnH,KAAA,GAAA4D,UAAA,CAAA8G,KAAA,CAAAvD,UAAA;MACA,IAAApD,KAAA,CAAA/D,KAAA;;MAEA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA,aACA0R,eAAA,WAAAA,gBAAAhH,KAAA;MACA,KAAAlN,kBAAA,GAAAkN,KAAA;MACA,KAAAnN,wBAAA;IACA;IAEA,eACA8D,0BAAA,WAAAA,2BAAA;MAAA,IAAAsQ,OAAA;MACA;MACA,IAAAtV,WAAA,QAAAD,WAAA,CAAAC,WAAA;MACA,KAAAa,oBAAA;MAEA,IAAAb,WAAA,wBAAAD,WAAA,CAAAG,aAAA,CAAA6D,MAAA;QACA,KAAAhE,WAAA,CAAAG,aAAA,CAAAsM,OAAA,WAAA1G,UAAA;UACA,IAAAG,QAAA,GAAAqP,OAAA,CAAA9U,eAAA,CAAAwG,IAAA,WAAA5E,CAAA;YAAA,OAAAA,CAAA,CAAA0D,UAAA,KAAAA,UAAA;UAAA;UACA,IAAAG,QAAA;YACAqP,OAAA,CAAAzU,oBAAA,CAAAiH,IAAA;cACAzI,IAAA,EAAA4G,QAAA,CAAA8B,YAAA;cACAA,YAAA,EAAA9B,QAAA,CAAA8B,YAAA;cACAE,YAAA,EAAAhC,QAAA,CAAAgC,YAAA;cACAkD,WAAA,EAAAlF,QAAA,CAAAkF,WAAA;cACAoK,SAAA,EAAAtP,QAAA,CAAAsP,SAAA;cACAC,UAAA,EAAAvP,QAAA,CAAAuP;YACA;UACA;QACA;MACA,WAAAxV,WAAA,wBAAAD,WAAA,CAAAI,aAAA,CAAA4D,MAAA;QACA,KAAAhE,WAAA,CAAAI,aAAA,CAAAqM,OAAA,WAAAxE,QAAA;UACAsN,OAAA,CAAAzU,oBAAA,CAAAiH,IAAA;YACAzI,IAAA,EAAA2I,QAAA;YACAC,YAAA,EAAAD,QAAA;YACAuN,SAAA;UACA;QACA;MACA,WAAAvV,WAAA,2BAAAD,WAAA,CAAAE,YAAA,CAAA8D,MAAA;QACA,KAAAhE,WAAA,CAAAE,YAAA,CAAAuM,OAAA,WAAAiJ,OAAA;UACA,IAAApH,KAAA,GAAAiH,OAAA,CAAA/U,kBAAA,CAAAyG,IAAA,WAAAnE,CAAA;YAAA,OAAAA,CAAA,CAAAuE,OAAA,KAAAqO,OAAA;UAAA;UACA,IAAApH,KAAA;YACAiH,OAAA,CAAAzU,oBAAA,CAAAiH,IAAA;cACAzI,IAAA,EAAAgP,KAAA,CAAAnD,WAAA;cACAA,WAAA,EAAAmD,KAAA,CAAAnD,WAAA;cACAnD,YAAA,EAAAsG,KAAA,CAAAtG,YAAA;cACAoD,WAAA,EAAAkD,KAAA,CAAAlD,WAAA;cACAuK,UAAA,EAAArH,KAAA,CAAA7D,UAAA;cACA+K,SAAA,EAAAlH,KAAA,CAAAkH,SAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}