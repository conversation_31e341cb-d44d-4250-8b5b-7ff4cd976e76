-- 测试方案选项查询测试脚本
-- 用于验证修复后的选项查询功能

-- 1. 测试方案编号选项查询
SELECT '=== 方案编号选项查询测试 ===' as '测试项目';
SELECT DISTINCT plan_code FROM test_plans
WHERE plan_code IS NOT NULL AND plan_code != ''
ORDER BY plan_code;

-- 2. 性能类型选项查询
SELECT '=== 性能类型选项查询测试 ===' as '测试项目';
SELECT DISTINCT performance_type FROM test_plans
WHERE performance_type IS NOT NULL AND performance_type != ''
ORDER BY performance_type;

-- 3. 测试设备选项查询
SELECT '=== 测试设备选项查询测试 ===' as '测试项目';
SELECT DISTINCT test_equipment FROM test_plans
WHERE test_equipment IS NOT NULL AND test_equipment != ''
ORDER BY test_equipment;

-- 4. 验证数据完整性
SELECT '=== 数据完整性验证 ===' as '测试项目';
SELECT 
    COUNT(*) as '总记录数',
    COUNT(DISTINCT plan_code) as '不同方案编号数',
    COUNT(DISTINCT performance_type) as '不同性能类型数',
    COUNT(DISTINCT test_equipment) as '不同测试设备数'
FROM test_plans;

-- 5. 检查空值情况
SELECT '=== 空值检查 ===' as '测试项目';
SELECT 
    SUM(CASE WHEN plan_code IS NULL OR plan_code = '' THEN 1 ELSE 0 END) as '方案编号空值数',
    SUM(CASE WHEN performance_type IS NULL OR performance_type = '' THEN 1 ELSE 0 END) as '性能类型空值数',
    SUM(CASE WHEN test_equipment IS NULL OR test_equipment = '' THEN 1 ELSE 0 END) as '测试设备空值数'
FROM test_plans;
