{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue?vue&type=style&index=0&id=067df850&scoped=true&lang=css", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue", "mtime": 1754278951915}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753339847609}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753339890164}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753339854740}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7DQp9DQoNCi8qIOmhtemdouWktOmDqOagt+W8jyAqLw0KLnBhZ2UtaGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCn0NCg0KLnBhZ2UtdGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDI0cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnBhZ2UtdGl0bGUgaSB7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMjhweDsNCn0NCg0KLnBhZ2UtZGVzY3JpcHRpb24gcCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLyog5aKe5by65Y2h54mH5qC35byPICovDQouZW5oYW5jZWQtY2FyZCB7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQp9DQoNCi5lbmhhbmNlZC1jYXJkOmhvdmVyIHsNCiAgYm94LXNoYWRvdzogMCA4cHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMTIpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQp9DQoNCi8qIOWNoeeJh+WktOmDqOagt+W8jyAqLw0KLmNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAwOw0KfQ0KDQouaGVhZGVyLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEycHg7DQp9DQoNCi5oZWFkZXItbGVmdCBpIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMThweDsNCn0NCg0KLmhlYWRlci10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQouaXRlbS1jb3VudC1iYWRnZSB7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQp9DQoNCi5oZWFkZXItcmlnaHQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCn0NCg0KLmhlYWRlci1yaWdodCAuZWwtYnV0dG9uIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouY29sdW1uLXNldHRpbmctYnRuIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgYm9yZGVyOiBub25lOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5jb2x1bW4tc2V0dGluZy1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNzY0YmEyIDAlLCAjNjY3ZWVhIDEwMCUpOw0KfQ0KDQovKiDmkJzntKLljLrln5/moLflvI8gKi8NCi5zZWFyY2gtc2VjdGlvbiB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnNlYXJjaC1mb3JtIHsNCiAgbWFyZ2luOiAwOw0KfQ0KDQouZm9ybS1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmZvcm0tcm93IC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tcmlnaHQ6IDA7DQogIG1hcmdpbi1ib3R0b206IDA7DQogIGZsZXg6IDE7DQogIG1pbi13aWR0aDogMjUwcHg7DQp9DQoNCi5mb3JtLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiAxMHB4Ow0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KfQ0KDQouc2VhcmNoLWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlcjogbm9uZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBwYWRkaW5nOiA4cHggMjBweDsNCn0NCg0KLnJlc2V0LWJ0biB7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgcGFkZGluZzogOHB4IDIwcHg7DQp9DQoNCi8qIOmAmueUqOagt+W8jyAqLw0KLmNsZWFyZml4OmJlZm9yZSwNCi5jbGVhcmZpeDphZnRlciB7DQogIGRpc3BsYXk6IHRhYmxlOw0KICBjb250ZW50OiAiIjsNCn0NCg0KLmNsZWFyZml4OmFmdGVyIHsNCiAgY2xlYXI6IGJvdGg7DQp9DQoNCi5kaWFsb2ctZm9vdGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouZWwtdXBsb2FkX190aXAgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tdG9wOiA3cHg7DQp9DQoNCi5wYXJhbS1kZXRhaWwtY2FyZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmRldGFpbC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZGV0YWlsLWl0ZW0gbGFiZWwgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBtaW4td2lkdGg6IDgwcHg7DQp9DQoNCi5kZXRhaWwtaXRlbSBzcGFuIHsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZsZXg6IDE7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5hcHAtY29udGFpbmVyIHsNCiAgICBwYWRkaW5nOiAxMHB4Ow0KICB9DQoNCiAgLnBhZ2UtaGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLmhlYWRlci1yaWdodCB7DQogICAgZmxleC13cmFwOiB3cmFwOw0KICB9DQoNCiAgLmZvcm0tcm93IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICB9DQoNCiAgLmZvcm0tcm93IC5lbC1mb3JtLWl0ZW0gew0KICAgIG1pbi13aWR0aDogMTAwJTsNCiAgfQ0KDQogIC5zZWFyY2gtc2VjdGlvbiB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KfQ0KDQovKiDnu5/kuIDmjInpkq7moLflvI8gKi8NCi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtYnV0dG9uLS1zdWNjZXNzIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzU2YWIyZiAwJSwgI2E4ZTZjZiAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0taW5mbyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxN2EyYjggMCUsICMxMzg0OTYgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5lbC1idXR0b24tLXdhcm5pbmcgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjA5M2ZiIDAlLCAjZjU1NzZjIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtYnV0dG9uLS1kYW5nZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjZiIDAlLCAjZWU1YTI0IDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8jDA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/testResult", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-edit-outline\"></i>\r\n        <span>测试结果数据录入</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📊 录入和管理测试结果数据，支持多维度筛选和批量操作</p>\r\n        <el-alert\r\n          title=\"使用提示：先选择筛选条件，然后点击查询按钮查看数据，支持列设置自定义显示\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"result-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-edit-outline\"></i>\r\n          <span class=\"header-title\">测试结果管理</span>\r\n          <el-badge :value=\"total\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAdd\">\r\n            <span>新增结果</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"multiple\" @click=\"handleDelete\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExport\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"info\" icon=\"el-icon-setting\" size=\"small\" @click=\"handleColumnSetting\" class=\"column-setting-btn\">\r\n            <span>列设置</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\" class=\"search-form\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramNumber\"\r\n                  :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n                  placeholder=\"请输入参数编号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleParamNumberFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-tickets\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试方案组\" prop=\"testPlanCode\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testPlanCode\"\r\n                  :fetch-suggestions=\"queryTestPlanGroupSuggestions\"\r\n                  placeholder=\"请输入测试方案组\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestPlanGroupFocus\"\r\n                  @select=\"handleTestPlanGroupSelect\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-document\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试参数\" prop=\"paramName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramName\"\r\n                  :fetch-suggestions=\"queryTestParamSuggestions\"\r\n                  placeholder=\"请输入测试参数\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestParamFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-data-line\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialName\"\r\n                  :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n                  placeholder=\"请输入材料名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.supplierName\"\r\n                  :fetch-suggestions=\"querySupplierNameSuggestions\"\r\n                  placeholder=\"请输入供应商名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleSupplierNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-office-building\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialModel\"\r\n                  :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n                  placeholder=\"请输入材料型号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialModelFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-goods\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.processType\"\r\n                  :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n                  placeholder=\"请输入工艺类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleProcessTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-setting\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.performanceType\"\r\n                  :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n                  placeholder=\"请输入性能类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handlePerformanceTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-lightning\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testEquipment\"\r\n                  :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n                  placeholder=\"请输入测试设备\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestEquipmentFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-cpu\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\" style=\"text-align: right;\">\r\n              <el-form-item>\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\" class=\"search-btn\">\r\n                  <span>搜索</span>\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\" class=\"reset-btn\">\r\n                  <span>重置</span>\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"testResultList\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"multipleTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n\r\n        <!-- 动态列显示 -->\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialName\"\r\n          prop=\"materialName\"\r\n          label=\"材料名称\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierName\"\r\n          prop=\"supplierName\"\r\n          label=\"供应商\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialModel\"\r\n          prop=\"materialModel\"\r\n          label=\"材料型号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.processType\"\r\n          prop=\"processType\"\r\n          label=\"工艺类型\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.paramNumber\"\r\n          prop=\"paramNumber\"\r\n          label=\"参数编号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceType\"\r\n          prop=\"performanceType\"\r\n          label=\"性能类型\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceName\"\r\n          prop=\"performanceName\"\r\n          label=\"性能名称\"\r\n          min-width=\"150\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testEquipment\"\r\n          prop=\"testEquipment\"\r\n          label=\"测试设备\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierDatasheetVal\"\r\n          prop=\"supplierDatasheetVal\"\r\n          label=\"供应商数据\"\r\n          width=\"120\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.supplierDatasheetVal) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testValue\"\r\n          prop=\"testValue\"\r\n          label=\"测试值\"\r\n          width=\"100\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.testValue) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createBy\"\r\n          prop=\"createBy\"\r\n          label=\"创建人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createTime\"\r\n          prop=\"createTime\"\r\n          label=\"创建时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateBy\"\r\n          prop=\"updateBy\"\r\n          label=\"更新人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateTime\"\r\n          prop=\"updateTime\"\r\n          label=\"更新时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body v-drag>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数编号\" prop=\"groupId\">\r\n              <el-select\r\n                v-model=\"form.groupId\"\r\n                placeholder=\"请选择参数编号\"\r\n                style=\"width: 100%;\"\r\n                filterable\r\n                @change=\"handleParamGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in filteredParamGroupOptions\"\r\n                  :key=\"group.groupId\"\r\n                  :label=\"group.paramNumber + ' - ' + (group.materialName || '') + ' (' + (group.supplierName || '') + ')'\"\r\n                  :value=\"group.groupId\"\r\n                >\r\n                  <div style=\"display: flex; justify-content: space-between;\">\r\n                    <span>{{ group.paramNumber || 'N/A' }}</span>\r\n                    <span style=\"color: #8492a6; font-size: 13px;\">{{ (group.materialName || 'N/A') + ' - ' + (group.supplierName || 'N/A') }}</span>\r\n                  </div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试方案组\" prop=\"planGroupId\">\r\n              <el-select\r\n                v-model=\"form.planGroupId\"\r\n                placeholder=\"请选择测试方案组\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                @change=\"handleFormPlanGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in testPlanGroupOptions\"\r\n                  :key=\"group.planGroupId\"\r\n                  :label=\"group.planCode + ' - ' + group.performanceName\"\r\n                  :value=\"group.planGroupId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试参数\" prop=\"testParamId\">\r\n              <el-select\r\n                v-model=\"form.testParamId\"\r\n                placeholder=\"请选择测试参数\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"!form.planGroupId\"\r\n                @change=\"handleFormTestParamChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"param in testParamOptions\"\r\n                  :key=\"param.testParamId\"\r\n                  :label=\"param.paramName + (param.paramValue ? ' (' + param.paramValue + param.unit + ')' : '')\"\r\n                  :value=\"param.testParamId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- 空列，保持布局 -->\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 工艺参数信息展示 -->\r\n        <el-card v-if=\"selectedParamDetail\" class=\"param-detail-card\" style=\"margin-bottom: 15px;\" :key=\"selectedParamDetail.groupId + '_' + (selectedParamDetail.paramItems ? selectedParamDetail.paramItems.length : 0)\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-setting\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">工艺参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"材料名称\">{{ selectedParamDetail.materialName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"供应商\">{{ selectedParamDetail.supplierName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"工艺类型\">{{ selectedParamDetail.processType || '-' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n          <div v-if=\"selectedParamDetail.paramItems && selectedParamDetail.paramItems.length > 0\" style=\"margin-top: 15px;\">\r\n            <div style=\"margin-bottom: 10px; font-weight: bold; color: #606266;\">\r\n              <i class=\"el-icon-data-line\"></i>\r\n              <span style=\"margin-left: 5px;\">参数明细：</span>\r\n            </div>\r\n            <el-table :data=\"selectedParamDetail.paramItems\" size=\"mini\" style=\"width: 100%;\" border>\r\n              <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n              <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n            </el-table>\r\n          </div>\r\n          <div v-else style=\"margin-top: 15px; text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\"></i>\r\n            <span style=\"margin-left: 5px;\">暂无参数明细信息</span>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 测试方案参数信息展示 -->\r\n        <el-card v-if=\"selectedTestPlanDetail\" class=\"test-plan-detail-card\" style=\"margin-bottom: 15px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-document\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">测试方案参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"方案编号\">{{ selectedTestPlanDetail.planCode || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能类型\">{{ selectedTestPlanDetail.performanceType || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能名称\">{{ selectedTestPlanDetail.performanceName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"测试设备\">{{ selectedTestPlanDetail.testEquipment || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数名称\">{{ selectedTestPlanDetail.paramName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数数值\">{{ selectedTestPlanDetail.paramValue || '-' }} {{ selectedTestPlanDetail.unit || '' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"供应商数据\" prop=\"supplierDatasheetVal\">\r\n              <el-input v-model=\"form.supplierDatasheetVal\" placeholder=\"请输入供应商Datasheet值\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"实际测试值\" prop=\"testValue\">\r\n              <el-input-number v-model=\"form.testValue\" :precision=\"6\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"upload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"fileList\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :before-upload=\"beforeUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" clearable />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"测试结果详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body v-drag>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"材料名称\">{{ detailData.materialName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供应商\">{{ detailData.supplierName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"材料型号\">{{ detailData.materialModel || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"工艺类型\">{{ detailData.processType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"参数编号\">{{ detailData.paramNumber || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能类型\">{{ detailData.performanceType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能名称\">{{ detailData.performanceName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试设备\">{{ detailData.testEquipment || '-' }}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"供应商数据\">{{ formatDecimal(detailData.supplierDatasheetVal) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试值\">{{ formatDecimal(detailData.testValue) }} {{ detailData.paramUnit || '' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建人\">{{ detailData.createBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建时间\">{{ parseTime(detailData.createTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新人\">{{ detailData.updateBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新时间\">{{ parseTime(detailData.updateTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ detailData.remark || '-' }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 参数明细信息 -->\r\n      <el-card v-if=\"detailParamItems && detailParamItems.length > 0\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">材料参数明细信息</span>\r\n        </div>\r\n        <el-table :data=\"detailParamItems\" style=\"width: 100%\" size=\"small\">\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n          <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n        </el-table>\r\n      </el-card>\r\n\r\n      <!-- 测试方案参数信息 -->\r\n      <el-card v-if=\"detailTestPlanParams\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">测试方案参数信息</span>\r\n        </div>\r\n        <el-descriptions :column=\"2\" border size=\"small\" style=\"margin-bottom: 20px;\">\r\n          <el-descriptions-item label=\"方案编号\">{{ detailTestPlanParams.planCode || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能类型\">{{ detailTestPlanParams.performanceType || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能名称\">{{ detailTestPlanParams.performanceName || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"测试设备\">{{ detailTestPlanParams.testEquipment || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 测试参数列表 -->\r\n        <div v-if=\"detailTestPlanParams.testParams && detailTestPlanParams.testParams.length > 0\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">📋 测试参数明细</h4>\r\n          <el-table :data=\"detailTestPlanParams.testParams\" size=\"small\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n            <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip />\r\n            <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.paramValue || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.unit || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.remark || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        <div v-else style=\"text-align: center; color: #909399; padding: 20px;\">\r\n          <i class=\"el-icon-info\"></i> 该测试方案组暂无测试参数\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 附件信息 -->\r\n      <el-card v-if=\"detailData.attachments\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">附件信息</span>\r\n        </div>\r\n        <el-button size=\"mini\" type=\"text\" @click=\"handleViewAttachments(detailData.attachments)\">查看附件</el-button>\r\n      </el-card>\r\n    </el-dialog>\r\n\r\n    <!-- 列设置对话框 -->\r\n    <el-dialog title=\"列设置\" :visible.sync=\"columnSettingVisible\" width=\"500px\" append-to-body v-drag>\r\n      <el-checkbox-group v-model=\"selectedColumns\">\r\n        <el-row>\r\n          <el-col :span=\"12\" v-for=\"(label, key) in columnOptions\" :key=\"key\">\r\n            <el-checkbox :label=\"key\">{{ label }}</el-checkbox>\r\n          </el-col>\r\n        </el-row>\r\n      </el-checkbox-group>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleColumnConfirm\">确 定</el-button>\r\n        <el-button @click=\"columnSettingVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" />\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"downloadAttachment(scope.row.url, scope.row.name)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"attachmentDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestResult, getTestResult, delTestResult, addTestResult, updateTestResult,\r\n  getTestResultOptions\r\n} from \"@/api/material/testResult\";\r\nimport { listTestPlanGroup } from \"@/api/material/testPlanGroup\";\r\nimport { listTestParamItem } from \"@/api/material/testParamItem\";\r\nimport { listProcessParamGroup, getProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestResult\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 测试结果表格数据\r\n      testResultList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 测试方案组选项\r\n      testPlanGroupOptions: [],\r\n\r\n      // 参数组选项\r\n      paramGroupOptions: [],\r\n      // 过滤后的参数组选项\r\n      filteredParamGroupOptions: [],\r\n      // 选中的参数详情\r\n      selectedParamDetail: null,\r\n      // 选中的测试方案详情\r\n      selectedTestPlanDetail: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        testPlanCode: null,\r\n\r\n        groupId: null,\r\n        paramNumber: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        processType: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        groupId: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"change\" }\r\n        ],\r\n        planGroupId: [\r\n          { required: true, message: \"测试方案组不能为空\", trigger: \"change\" }\r\n        ],\r\n\r\n        testValue: [\r\n          { required: true, message: \"实际测试值不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 详情对话框\r\n      detailDialogVisible: false,\r\n      detailData: {},\r\n      detailParamItems: [],\r\n      detailTestPlanParams: null,\r\n\r\n      // 列设置相关\r\n      columnSettingVisible: false,\r\n      columnOptions: {\r\n        materialName: '材料名称',\r\n        supplierName: '供应商',\r\n        materialModel: '材料型号',\r\n        processType: '工艺类型',\r\n        paramNumber: '参数编号',\r\n        performanceType: '性能类型',\r\n        performanceName: '性能名称',\r\n\r\n        testEquipment: '测试设备',\r\n        supplierDatasheetVal: '供应商数据',\r\n        testValue: '测试值',\r\n        createBy: '创建人',\r\n        createTime: '创建时间',\r\n        updateBy: '更新人',\r\n        updateTime: '更新时间'\r\n      },\r\n      selectedColumns: ['materialName', 'supplierName', 'materialModel', 'processType', 'paramNumber', 'performanceType', 'performanceName', 'testEquipment', 'supplierDatasheetVal', 'testValue', 'createBy', 'createTime', 'updateBy', 'updateTime'],\r\n      visibleColumns: {},\r\n\r\n      // 搜索建议数据\r\n      paramNumberSuggestions: [],\r\n      materialNameSuggestions: [],\r\n      supplierNameSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      testPlanGroupSuggestions: [],\r\n\r\n      // 附件相关\r\n      fileList: [],\r\n      attachmentList: [],\r\n      attachmentDialogVisible: false,\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getTestPlanGroupOptions();\r\n    this.getParamGroupOptions();\r\n    this.loadSuggestions();\r\n    this.initVisibleColumns();\r\n  },\r\n  methods: {\r\n    /** 初始化可见列 */\r\n    initVisibleColumns() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取参数编号建议\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料名称建议\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商名称建议\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取性能类型建议\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试设备建议\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试方案建议\r\n      getTestResultOptions({ type: 'planCode' }).then(response => {\r\n        this.testPlanSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商名称搜索建议 */\r\n    querySupplierNameSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 性能类型搜索建议 */\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 测试设备搜索建议 */\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商名称焦点事件 */\r\n    handleSupplierNameFocus() {\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 性能类型焦点事件 */\r\n    handlePerformanceTypeFocus() {\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试设备焦点事件 */\r\n    handleTestEquipmentFocus() {\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组搜索建议 */\r\n    queryTestPlanGroupSuggestions(queryString, cb) {\r\n      let suggestions = this.testPlanGroupSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.testPlanGroupSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 测试方案组焦点事件 */\r\n    handleTestPlanGroupFocus() {\r\n      getTestResultOptions({ type: 'testPlanGroup' }).then(response => {\r\n        this.testPlanGroupSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组选择事件 */\r\n    handleTestPlanGroupSelect(item) {\r\n      this.queryParams.testPlanCode = item.value;\r\n    },\r\n\r\n    /** 表单测试方案改变事件 */\r\n    handleFormTestPlanChange(value) {\r\n      // 当测试方案改变时，可以重新加载参数详情\r\n      if (value && this.form.groupId) {\r\n        this.handleParamGroupChange(this.form.groupId);\r\n      }\r\n    },\r\n\r\n    /** 查询测试结果列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTestResult(this.queryParams).then(response => {\r\n        this.testResultList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 获取测试方案组选项 */\r\n    getTestPlanGroupOptions() {\r\n      listTestPlanGroup().then(response => {\r\n        this.testPlanGroupOptions = response.rows || [];\r\n      }).catch(() => {\r\n        this.testPlanGroupOptions = [];\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 处理测试方案组变化 */\r\n    handleFormPlanGroupChange(planGroupId) {\r\n      this.form.testParamId = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.testParamOptions = [];\r\n\r\n      if (!planGroupId) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试方案组详情\r\n      const selectedGroup = this.testPlanGroupOptions.find(group => group.planGroupId === planGroupId);\r\n      if (selectedGroup) {\r\n        // 立即显示测试方案组基本信息\r\n        this.selectedTestPlanDetail = {\r\n          planCode: selectedGroup.planCode,\r\n          performanceType: selectedGroup.performanceType,\r\n          performanceName: selectedGroup.performanceName,\r\n          testEquipment: selectedGroup.testEquipment,\r\n          paramName: '',\r\n          paramValue: '',\r\n          unit: ''\r\n        };\r\n\r\n        // 加载该测试方案组下的测试参数选项\r\n        this.getTestParamOptionsByPlanGroupId(planGroupId);\r\n      }\r\n    },\r\n\r\n    /** 处理测试参数变化 */\r\n    handleFormTestParamChange(testParamId) {\r\n      if (!testParamId || !this.selectedTestPlanDetail) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试参数详情\r\n      const selectedParam = this.testParamOptions.find(param => param.testParamId === testParamId);\r\n      if (selectedParam) {\r\n        this.selectedTestPlanDetail.paramName = selectedParam.paramName;\r\n        this.selectedTestPlanDetail.paramValue = selectedParam.paramValue;\r\n        this.selectedTestPlanDetail.unit = selectedParam.unit;\r\n      }\r\n    },\r\n\r\n\r\n\r\n    /** 获取参数组选项 */\r\n    getParamGroupOptions() {\r\n      listProcessParamGroup().then(response => {\r\n        this.paramGroupOptions = response.rows;\r\n        this.filteredParamGroupOptions = response.rows;\r\n      });\r\n    },\r\n\r\n    /** 方案改变事件 */\r\n    handlePlanChange(value) {\r\n      // 可以根据方案过滤参数组\r\n      this.queryParams.groupId = null;\r\n      this.handleQuery();\r\n    },\r\n\r\n\r\n\r\n    /** 参数组改变事件 */\r\n    handleParamGroupChange(value) {\r\n      if (value) {\r\n        // 先清空之前的数据\r\n        this.selectedParamDetail = null;\r\n\r\n        // 获取参数组详情\r\n        getProcessParamGroup(value).then(response => {\r\n          this.selectedParamDetail = response.data;\r\n          console.log('获取到的参数组详情：', this.selectedParamDetail);\r\n\r\n          // 立即获取参数明细，不需要等待测试方案选择\r\n          listProcessParamItem({ groupId: value }).then(paramResponse => {\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = paramResponse.rows || [];\r\n              // 为每个参数项添加显示文本，包含参数值\r\n              this.selectedParamDetail.paramItems.forEach(item => {\r\n                let displayText = item.paramName || 'N/A';\r\n                if (item.paramValue !== null && item.paramValue !== undefined) {\r\n                  displayText += `: ${this.formatDecimal(item.paramValue)}`;\r\n                }\r\n                if (item.unit) {\r\n                  displayText += ` ${item.unit}`;\r\n                }\r\n                item.displayText = displayText;\r\n              });\r\n              console.log('获取到的参数明细：', this.selectedParamDetail.paramItems);\r\n\r\n              // 强制更新视图\r\n              this.$forceUpdate();\r\n            }\r\n          }).catch(error => {\r\n            console.error('获取参数明细失败：', error);\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = [];\r\n            }\r\n          });\r\n        }).catch(error => {\r\n          console.error('获取参数组详情失败：', error);\r\n          this.selectedParamDetail = null;\r\n        });\r\n      } else {\r\n        this.selectedParamDetail = null;\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        testResultId: null,\r\n        planGroupId: null,\r\n        groupId: null,\r\n        supplierDatasheetVal: null,\r\n        testValue: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.fileList = [];\r\n      this.selectedParamDetail = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.testResultId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    // 行点击选择\r\n    handleRowClick(row) {\r\n      this.$refs.multipleTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加测试结果\";\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    handleEdit(row) {\r\n      this.reset();\r\n      const testResultId = row.testResultId || this.ids[0];\r\n      getTestResult(testResultId).then(response => {\r\n        console.log('编辑获取的数据：', response.data);\r\n        this.form = response.data;\r\n        // 解析附件数据\r\n        this.fileList = this.parseAttachments(response.data.attachments);\r\n        console.log('编辑时解析的文件列表：', this.fileList);\r\n        this.open = true;\r\n        this.title = \"修改测试结果\";\r\n\r\n        // 触发参数组改变事件以加载材料参数详情\r\n        if (this.form.groupId) {\r\n          this.handleParamGroupChange(this.form.groupId);\r\n        }\r\n\r\n        // 触发测试方案组改变事件以加载测试方案参数详情\r\n        if (this.form.planGroupId) {\r\n          this.handleFormPlanGroupChange(this.form.planGroupId);\r\n\r\n          // 如果有测试参数ID，在加载完测试参数选项后设置选中的参数\r\n          if (this.form.testParamId) {\r\n            this.$nextTick(() => {\r\n              this.handleFormTestParamChange(this.form.testParamId);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      this.detailData = row;\r\n      this.detailParamItems = [];\r\n      this.detailTestPlanParams = null;\r\n\r\n      // 如果有参数组ID，获取参数明细\r\n      if (row.groupId) {\r\n        listProcessParamItem({ groupId: row.groupId }).then(response => {\r\n          this.detailParamItems = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('获取参数明细失败：', error);\r\n          this.detailParamItems = [];\r\n        });\r\n      }\r\n\r\n      // 如果有测试方案组ID，获取测试方案参数信息\r\n      if (row.planGroupId) {\r\n        // 从当前的测试方案组选项中查找对应的方案信息\r\n        const planGroup = this.testPlanGroupOptions.find(group => group.planGroupId === row.planGroupId);\r\n        if (planGroup) {\r\n          // 获取测试方案组下的所有测试参数\r\n          listTestParamItem({ planGroupId: row.planGroupId }).then(response => {\r\n            this.detailTestPlanParams = {\r\n              planCode: planGroup.planCode,\r\n              performanceType: planGroup.performanceType,\r\n              performanceName: planGroup.performanceName,\r\n              testEquipment: planGroup.testEquipment,\r\n              testParams: response.rows || []\r\n            };\r\n          }).catch(error => {\r\n            console.error('获取测试方案参数失败：', error);\r\n          });\r\n        }\r\n      }\r\n\r\n      this.detailDialogVisible = true;\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 将附件列表转换为逗号分隔的URL字符串\r\n          if (this.fileList && this.fileList.length > 0) {\r\n            this.form.attachments = this.fileList.map(file => file.url).join(',');\r\n          } else {\r\n            this.form.attachments = '';\r\n          }\r\n\r\n          // testPlanId已经直接选择，无需转换\r\n\r\n          // 设置创建人和更新人\r\n          if (this.form.testResultId != null) {\r\n            // 更新操作，设置更新人\r\n            this.form.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.form.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          console.log('提交的表单数据：', this.form);\r\n\r\n          if (this.form.testResultId != null) {\r\n            updateTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const testResultIds = row.testResultId || this.ids;\r\n      this.$modal.confirm('是否确认删除测试结果编号为\"' + testResultIds + '\"的数据项？').then(function() {\r\n        return delTestResult(testResultIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('material/testResult/export', {\r\n        ...this.queryParams\r\n      }, `test_result_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 列设置 */\r\n    handleColumnSetting() {\r\n      this.columnSettingVisible = true;\r\n    },\r\n\r\n    /** 列设置确认 */\r\n    handleColumnConfirm() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n      this.columnSettingVisible = false;\r\n    },\r\n\r\n    /** 附件上传成功 */\r\n    handleUploadSuccess(response, file, fileList) {\r\n      console.log('上传成功回调：', { response, file, fileList });\r\n      if (response.code === 200) {\r\n        // 确保fileList是数组\r\n        if (Array.isArray(fileList)) {\r\n          this.fileList = fileList.map(item => ({\r\n            name: item.name,\r\n            url: item.response ? item.response.url : item.url,\r\n            size: this.formatFileSize(item.size || item.raw?.size),\r\n            uid: item.uid,\r\n            status: 'success'\r\n          }));\r\n        } else {\r\n          console.error('fileList不是数组：', fileList);\r\n          this.fileList = [];\r\n        }\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 附件移除 */\r\n    handleFileRemove(file, fileList) {\r\n      console.log('附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组\r\n      if (Array.isArray(fileList)) {\r\n        this.fileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.fileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 附件上传前检查 */\r\n    beforeUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      console.log('查看附件被调用，附件数据：', attachments, '类型：', typeof attachments);\r\n\r\n      this.attachmentList = [];\r\n\r\n      if (!attachments) {\r\n        console.log('附件数据为空');\r\n        this.attachmentDialogVisible = true;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            console.log('附件字符串为空');\r\n            this.attachmentDialogVisible = true;\r\n            return;\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                this.attachmentList = parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  size: item.size || '未知大小'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n              // 按逗号分割处理\r\n              this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n                const cleanUrl = url.trim();\r\n                const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n                return {\r\n                  name: fileName,\r\n                  url: cleanUrl,\r\n                  size: '未知大小'\r\n                };\r\n              });\r\n            }\r\n          } else {\r\n            // 按逗号分割处理\r\n            this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const cleanUrl = url.trim();\r\n              const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: cleanUrl,\r\n                size: '未知大小'\r\n              };\r\n            });\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            size: item.size || '未知大小'\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n        this.attachmentList = [];\r\n      }\r\n\r\n      console.log('解析后的附件列表：', this.attachmentList);\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, fileName) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      console.log('解析附件数据：', attachments, '类型：', typeof attachments);\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        // 如果已经是数组，直接返回\r\n        if (Array.isArray(attachments)) {\r\n          return attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            uid: item.uid || Date.now() + index,\r\n            status: 'success'\r\n          }));\r\n        }\r\n\r\n        // 如果是字符串，尝试解析\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            return [];\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                return parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  uid: item.uid || Date.now() + index,\r\n                  status: 'success'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n            }\r\n          }\r\n\r\n          // 按逗号分割处理\r\n          const urls = trimmed.split(',').filter(url => url.trim());\r\n          console.log('分割后的URL列表：', urls);\r\n          return urls.map((url, index) => {\r\n            const cleanUrl = url.trim();\r\n            const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: cleanUrl,\r\n              uid: Date.now() + index,\r\n              status: 'success'\r\n            };\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '未知大小';\r\n      const units = ['B', 'KB', 'MB', 'GB'];\r\n      let index = 0;\r\n      while (size >= 1024 && index < units.length - 1) {\r\n        size /= 1024;\r\n        index++;\r\n      }\r\n      return Math.round(size * 100) / 100 + ' ' + units[index];\r\n    },\r\n\r\n    /** 格式化小数 */\r\n    formatDecimal(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (isNaN(num)) {\r\n        return value;\r\n      }\r\n      // 如果是整数，直接返回，不添加.00\r\n      if (num % 1 === 0) {\r\n        return num.toString();\r\n      }\r\n      // 保留两位小数\r\n      return num.toFixed(2);\r\n    }\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.column-setting-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.column-setting-btn:hover {\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-row .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n  flex: 1;\r\n  min-width: 250px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n.param-detail-card {\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .form-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .form-row .el-form-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .search-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}