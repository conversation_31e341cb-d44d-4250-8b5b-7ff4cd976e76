{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754285582786}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnOw0KaW1wb3J0IHsgbGlzdFRlc3RSZXN1bHQsIGdldFRlc3RSZXN1bHRPcHRpb25zIH0gZnJvbSAiQC9hcGkvbWF0ZXJpYWwvdGVzdFJlc3VsdCI7DQppbXBvcnQgeyBsaXN0TWF0ZXJpYWwgfSBmcm9tICJAL2FwaS9tYXRlcmlhbC9tYXRlcmlhbCI7DQppbXBvcnQgeyBsaXN0UHJvY2Vzc1BhcmFtR3JvdXAgfSBmcm9tICJAL2FwaS9tYXRlcmlhbC9wcm9jZXNzUGFyYW1Hcm91cCI7DQppbXBvcnQgeyBsaXN0UHJvY2Vzc1BhcmFtSXRlbSB9IGZyb20gIkAvYXBpL21hdGVyaWFsL3Byb2Nlc3NQYXJhbUl0ZW0iOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJNYXRlcmlhbFRyZW5kIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5Yqg6L2954q25oCBDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOWbvuihqOWunuS+iw0KICAgICAgY2hhcnQ6IG51bGwsDQogICAgICAvLyDlm77ooajnsbvlnosNCiAgICAgIGNoYXJ0VHlwZTogJ2xpbmUnLA0KICAgICAgLy8g5Zu+6KGo6auY5bqmDQogICAgICBjaGFydEhlaWdodDogNDAwLA0KICAgICAgLy8g5Zu+6KGo5qCH6aKYDQogICAgICBjaGFydFRpdGxlOiAn5pWw5o2u6LaL5Yq/5a+55q+U5YiG5p6QJywNCiAgICAgIC8vIOWbvuihqOWKoOi9veeKtuaAgQ0KICAgICAgY2hhcnRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuaVsOaNruihqA0KICAgICAgc2hvd0RhdGFUYWJsZTogZmFsc2UsDQogICAgICAvLyDmmK/lkKblhajlsY8NCiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UsDQoNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgY29tcGFyZVR5cGU6ICdtYXRlcmlhbCcsDQogICAgICAgIHBhcmFtTnVtYmVyczogW10sDQogICAgICAgIG1hdGVyaWFsTmFtZXM6IFtdLA0KICAgICAgICBzdXBwbGllck5hbWVzOiBbXSwNCiAgICAgICAgcHJvY2Vzc1R5cGVzOiBbXSwNCiAgICAgICAgZGF0ZVJhbmdlOiBudWxsLA0KICAgICAgICBjb21wYXJlUGFyYW06IG51bGwNCiAgICAgIH0sDQoNCiAgICAgIC8vIOmAiemhueaVsOaNrg0KICAgICAgcGFyYW1OdW1iZXJPcHRpb25zOiBbXSwNCiAgICAgIG1hdGVyaWFsT3B0aW9uczogW10sDQogICAgICBzdXBwbGllck9wdGlvbnM6IFtdLA0KICAgICAgcHJvY2Vzc1R5cGVPcHRpb25zOiBbXSwNCg0KICAgICAgLy8g5Zu+6KGo5pWw5o2uDQogICAgICBjaGFydERhdGE6IFtdLA0KICAgICAgdGFibGVDb2x1bW5zOiBbXSwNCg0KICAgICAgLy8g5Y+C5pWw6K+m5oOFDQogICAgICBzZWxlY3RlZFBhcmFtRGV0YWlsczogW10sDQoNCiAgICAgIC8vIOe7n+iuoeaVsOaNrg0KICAgICAgc3RhdGlzdGljc0RhdGE6IFtdLA0KDQogICAgICAvLyDluK7liqnlr7nor53moYYNCiAgICAgIGhlbHBEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCg0KICAgICAgLy8g5L2/55So5oyH5Y2X5pi+56S654q25oCBDQogICAgICBzaG93VXNhZ2VHdWlkZTogZmFsc2UsDQoNCiAgICAgIC8vIOmhueebruivpuaDheaYvuekuueKtuaAgQ0KICAgICAgc2hvd1Byb2plY3REZXRhaWxzOiBmYWxzZSwNCg0KICAgICAgLy8g5Y+C5pWw6K+m5oOF5a+56K+d5qGGDQogICAgICBwYXJhbURldGFpbERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY3VycmVudFBhcmFtRGV0YWlsOiBudWxsDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXRDaGFydCgpOw0KICAgIHRoaXMubG9hZE9wdGlvbnMoKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICBpZiAodGhpcy5jaGFydCkgew0KICAgICAgdGhpcy5jaGFydC5kaXNwb3NlKCk7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWIneWni+WMluWbvuihqCAqLw0KICAgIGluaXRDaGFydCgpIHsNCiAgICAgIHRoaXMuY2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5jaGFydCk7DQoNCiAgICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlg0KICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsICgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuY2hhcnQpIHsNCiAgICAgICAgICB0aGlzLmNoYXJ0LnJlc2l6ZSgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWKoOi9vemAiemhueaVsOaNriAqLw0KICAgIGFzeW5jIGxvYWRPcHRpb25zKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5Yqg6L295Y+C5pWw57yW5Y+36YCJ6aG5DQogICAgICAgIGNvbnN0IHBhcmFtUmVzcG9uc2UgPSBhd2FpdCBsaXN0UHJvY2Vzc1BhcmFtR3JvdXAoe30pOw0KICAgICAgICB0aGlzLnBhcmFtTnVtYmVyT3B0aW9ucyA9IHBhcmFtUmVzcG9uc2Uucm93cyB8fCBbXTsNCg0KICAgICAgICAvLyDliqDovb3mnZDmlpnpgInpobkNCiAgICAgICAgY29uc3QgbWF0ZXJpYWxSZXNwb25zZSA9IGF3YWl0IGxpc3RNYXRlcmlhbCh7fSk7DQogICAgICAgIHRoaXMubWF0ZXJpYWxPcHRpb25zID0gbWF0ZXJpYWxSZXNwb25zZS5yb3dzIHx8IFtdOw0KDQogICAgICAgIC8vIOWKoOi9veS+m+W6lOWVhumAiemhuQ0KICAgICAgICBjb25zdCBzdXBwbGllclJlc3BvbnNlID0gYXdhaXQgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAnc3VwcGxpZXJOYW1lJyB9KTsNCiAgICAgICAgdGhpcy5zdXBwbGllck9wdGlvbnMgPSBzdXBwbGllclJlc3BvbnNlLmRhdGEgfHwgW107DQoNCiAgICAgICAgLy8g5Yqg6L295bel6Im657G75Z6L6YCJ6aG5DQogICAgICAgIGNvbnN0IHByb2Nlc3NSZXNwb25zZSA9IGF3YWl0IGdldFRlc3RSZXN1bHRPcHRpb25zKHsgdHlwZTogJ3Byb2Nlc3NUeXBlJyB9KTsNCiAgICAgICAgdGhpcy5wcm9jZXNzVHlwZU9wdGlvbnMgPSBwcm9jZXNzUmVzcG9uc2UuZGF0YSB8fCBbXTsNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296YCJ6aG55pWw5o2u5aSx6LSl77yaJywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5Yqg6L296YCJ6aG55pWw5o2u5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlr7nmr5TnsbvlnovmlLnlj5ggKi8NCiAgICBoYW5kbGVDb21wYXJlVHlwZUNoYW5nZSh2YWx1ZSkgew0KICAgICAgLy8g6YeN572u55u45YWz5Y+C5pWwDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtTnVtYmVycyA9IFtdOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5tYXRlcmlhbE5hbWVzID0gW107DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBsaWVyTmFtZXMgPSBbXTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvY2Vzc1R5cGVzID0gW107DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRhdGVSYW5nZSA9IG51bGw7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhcmVQYXJhbSA9IG51bGw7DQoNCiAgICAgIC8vIOa4heepuumAieS4reeahOWPguaVsOivpuaDheWSjOWbvuihqOaVsOaNrg0KICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlscyA9IFtdOw0KICAgICAgdGhpcy5jaGFydERhdGEgPSBbXTsNCiAgICAgIHRoaXMuc3RhdGlzdGljc0RhdGEgPSBbXTsNCg0KICAgICAgLy8g5pu05paw5Zu+6KGo5qCH6aKYDQogICAgICB0aGlzLnVwZGF0ZUNoYXJ0VGl0bGUoKTsNCiAgICB9LA0KDQogICAgLyoqIOabtOaWsOWbvuihqOagh+mimCAqLw0KICAgIHVwZGF0ZUNoYXJ0VGl0bGUoKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAncGFyYW1OdW1iZXInOiAn5Y+C5pWw57yW5Y+35a+55q+U5YiG5p6QJywNCiAgICAgICAgJ21hdGVyaWFsJzogJ+adkOaWmeaAp+iDveWvueavlOWIhuaekCcsDQogICAgICAgICdzdXBwbGllcic6ICfkvpvlupTllYbotKjph4/lr7nmr5TliIbmnpAnLA0KICAgICAgICAncHJvY2Vzc1R5cGUnOiAn5bel6Im657G75Z6L5pWI5p6c5a+55q+U5YiG5p6QJywNCiAgICAgICAgJ3RpbWVUcmVuZCc6ICfml7bpl7Totovlir/lr7nmr5TliIbmnpAnDQogICAgICB9Ow0KICAgICAgdGhpcy5jaGFydFRpdGxlID0gdHlwZU1hcFt0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlXSB8fCAn5a+55q+U5YiG5p6Q5Zu+JzsNCiAgICB9LA0KDQogICAgLyoqIOWbvuihqOexu+Wei+aUueWPmCAqLw0KICAgIGhhbmRsZUNoYXJ0VHlwZUNoYW5nZSh0eXBlKSB7DQogICAgICB0aGlzLmNoYXJ0VHlwZSA9IHR5cGU7DQogICAgICBpZiAodGhpcy5jaGFydERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnJlbmRlckNoYXJ0KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6LmlbDmja4gKi8NCiAgICBhc3luYyBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGlmICghdGhpcy52YWxpZGF0ZVF1ZXJ5KCkpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSB0cnVlOw0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDmoLnmja7lr7nmr5Tnsbvlnovojrflj5bkuI3lkIznmoTmlbDmja4NCiAgICAgICAgbGV0IGNoYXJ0RGF0YSA9IFtdOw0KICAgICAgICBsZXQgcGFyYW1EZXRhaWxzID0gW107DQoNCiAgICAgICAgc3dpdGNoICh0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlKSB7DQogICAgICAgICAgY2FzZSAnbWF0ZXJpYWwnOg0KICAgICAgICAgICAgY2hhcnREYXRhID0gYXdhaXQgdGhpcy5nZXRNYXRlcmlhbENvbXBhcmVEYXRhKCk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdzdXBwbGllcic6DQogICAgICAgICAgICBjaGFydERhdGEgPSBhd2FpdCB0aGlzLmdldFN1cHBsaWVyQ29tcGFyZURhdGEoKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgJ3BhcmFtTnVtYmVyJzoNCiAgICAgICAgICAgIGNoYXJ0RGF0YSA9IGF3YWl0IHRoaXMuZ2V0UGFyYW1OdW1iZXJDb21wYXJlRGF0YSgpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAncHJvY2Vzc1R5cGUnOg0KICAgICAgICAgICAgY2hhcnREYXRhID0gYXdhaXQgdGhpcy5nZXRQcm9jZXNzVHlwZUNvbXBhcmVEYXRhKCk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICd0aW1lVHJlbmQnOg0KICAgICAgICAgICAgY2hhcnREYXRhID0gYXdhaXQgdGhpcy5nZXRUaW1lVHJlbmREYXRhKCk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdzdXBwbGllclZzVGVzdCc6DQogICAgICAgICAgICBjaGFydERhdGEgPSBhd2FpdCB0aGlzLmdldFN1cHBsaWVyVnNUZXN0RGF0YSgpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCg0KICAgICAgICB0aGlzLmNoYXJ0RGF0YSA9IGNoYXJ0RGF0YTsNCiAgICAgICAgdGhpcy51cGRhdGVUYWJsZUNvbHVtbnMoKTsNCiAgICAgICAgdGhpcy5yZW5kZXJDaGFydCgpOw0KDQogICAgICAgIC8vIOabtOaWsOmAieS4reWPguaVsOivpuaDhQ0KICAgICAgICB0aGlzLnVwZGF0ZVNlbGVjdGVkUGFyYW1EZXRhaWxzKCk7DQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWvueavlOaVsOaNruWksei0pe+8micsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+iOt+WPluWvueavlOaVsOaNruWksei0pScpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDpqozor4Hmn6Xor6LmnaHku7YgKi8NCiAgICB2YWxpZGF0ZVF1ZXJ5KCkgew0KICAgICAgY29uc3QgeyBjb21wYXJlVHlwZSB9ID0gdGhpcy5xdWVyeVBhcmFtczsNCg0KICAgICAgaWYgKGNvbXBhcmVUeXBlID09PSAncGFyYW1OdW1iZXInICYmIHRoaXMucXVlcnlQYXJhbXMucGFyYW1OdW1iZXJzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeiHs+WwkeS4gOS4quWPguaVsOe8luWPtycpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmIChjb21wYXJlVHlwZSA9PT0gJ21hdGVyaWFsJyAmJiB0aGlzLnF1ZXJ5UGFyYW1zLm1hdGVyaWFsTmFtZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6Iez5bCR5LiA5Liq5p2Q5paZJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgaWYgKGNvbXBhcmVUeXBlID09PSAnc3VwcGxpZXInICYmIHRoaXMucXVlcnlQYXJhbXMuc3VwcGxpZXJOYW1lcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6noh7PlsJHkuIDkuKrkvpvlupTllYYnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICBpZiAoY29tcGFyZVR5cGUgPT09ICdwcm9jZXNzVHlwZScgJiYgdGhpcy5xdWVyeVBhcmFtcy5wcm9jZXNzVHlwZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6Iez5bCR5LiA5Liq5bel6Im657G75Z6LJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgaWYgKGNvbXBhcmVUeXBlID09PSAndGltZVRyZW5kJyAmJiAhdGhpcy5xdWVyeVBhcmFtcy5kYXRlUmFuZ2UpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nml7bpl7TojIPlm7QnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICBpZiAoY29tcGFyZVR5cGUgPT09ICdzdXBwbGllclZzVGVzdCcgJiYgIXRoaXMucXVlcnlQYXJhbXMuY29tcGFyZVBhcmFtKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5a+55q+U55qE5Y+C5pWwJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmnZDmlpnlr7nmr5TmlbDmja4gKi8NCiAgICBhc3luYyBnZXRNYXRlcmlhbENvbXBhcmVEYXRhKCkgew0KICAgICAgY29uc3QgbWF0ZXJpYWxJZHMgPSB0aGlzLnF1ZXJ5UGFyYW1zLm1hdGVyaWFsTmFtZXMgfHwgW107DQogICAgICBjb25zdCBjb21wYXJlRGF0YSA9IFtdOw0KDQogICAgICBpZiAobWF0ZXJpYWxJZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBjb21wYXJlRGF0YTsNCiAgICAgIH0NCg0KICAgICAgZm9yIChjb25zdCBtYXRlcmlhbElkIG9mIG1hdGVyaWFsSWRzKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g6YCa6L+H5p2Q5paZSUTmn6Xmib7lr7nlupTnmoTlj4LmlbDnu4TvvIznhLblkI7mn6Xmib7mtYvor5Xnu5PmnpwNCiAgICAgICAgICBjb25zdCBwYXJhbUdyb3VwUmVzcG9uc2UgPSBhd2FpdCBsaXN0UHJvY2Vzc1BhcmFtR3JvdXAoew0KICAgICAgICAgICAgbWF0ZXJpYWxJZDogbWF0ZXJpYWxJZCwNCiAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMA0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgY29uc3QgcGFyYW1Hcm91cHMgPSBwYXJhbUdyb3VwUmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICBjb25zdCBtYXRlcmlhbCA9IHRoaXMubWF0ZXJpYWxPcHRpb25zLmZpbmQobSA9PiBtLm1hdGVyaWFsSWQgPT09IG1hdGVyaWFsSWQpOw0KDQogICAgICAgICAgbGV0IGFsbFN1cHBsaWVyVmFsdWVzID0gW107DQogICAgICAgICAgbGV0IGFsbFRlc3RWYWx1ZXMgPSBbXTsNCg0KICAgICAgICAgIC8vIOmBjeWOhuivpeadkOaWmeeahOaJgOacieWPguaVsOe7hO+8jOiOt+WPlua1i+ivlee7k+aenA0KICAgICAgICAgIGZvciAoY29uc3QgZ3JvdXAgb2YgcGFyYW1Hcm91cHMpIHsNCiAgICAgICAgICAgIGNvbnN0IHRlc3RSZXNwb25zZSA9IGF3YWl0IGxpc3RUZXN0UmVzdWx0KHsNCiAgICAgICAgICAgICAgZ3JvdXBJZDogZ3JvdXAuZ3JvdXBJZCwNCiAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgcGFnZVNpemU6IDEwMDANCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICBjb25zdCB0ZXN0UmVzdWx0cyA9IHRlc3RSZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICAgICAgY29uc3Qgc3VwcGxpZXJWYWx1ZXMgPSB0ZXN0UmVzdWx0cy5tYXAociA9PiBwYXJzZUZsb2F0KHIuc3VwcGxpZXJEYXRhc2hlZXRWYWwpKS5maWx0ZXIodiA9PiAhaXNOYU4odikpOw0KICAgICAgICAgICAgY29uc3QgdGVzdFZhbHVlcyA9IHRlc3RSZXN1bHRzLm1hcChyID0+IHBhcnNlRmxvYXQoci50ZXN0VmFsdWUpKS5maWx0ZXIodiA9PiAhaXNOYU4odikpOw0KDQogICAgICAgICAgICBhbGxTdXBwbGllclZhbHVlcyA9IGFsbFN1cHBsaWVyVmFsdWVzLmNvbmNhdChzdXBwbGllclZhbHVlcyk7DQogICAgICAgICAgICBhbGxUZXN0VmFsdWVzID0gYWxsVGVzdFZhbHVlcy5jb25jYXQodGVzdFZhbHVlcyk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29tcGFyZURhdGEucHVzaCh7DQogICAgICAgICAgICBuYW1lOiBtYXRlcmlhbCA/IG1hdGVyaWFsLm1hdGVyaWFsTmFtZSA6IGDmnZDmlpkke21hdGVyaWFsSWR9YCwNCiAgICAgICAgICAgIHN1cHBsaWVyOiBtYXRlcmlhbCA/IG1hdGVyaWFsLnN1cHBsaWVyTmFtZSA6ICcnLA0KICAgICAgICAgICAgc3VwcGxpZXJBdmc6IGFsbFN1cHBsaWVyVmFsdWVzLmxlbmd0aCA+IDAgPyAoYWxsU3VwcGxpZXJWYWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkgLyBhbGxTdXBwbGllclZhbHVlcy5sZW5ndGgpLnRvRml4ZWQoMikgOiAwLA0KICAgICAgICAgICAgdGVzdEF2ZzogYWxsVGVzdFZhbHVlcy5sZW5ndGggPiAwID8gKGFsbFRlc3RWYWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkgLyBhbGxUZXN0VmFsdWVzLmxlbmd0aCkudG9GaXhlZCgyKSA6IDAsDQogICAgICAgICAgICBzdXBwbGllck1heDogYWxsU3VwcGxpZXJWYWx1ZXMubGVuZ3RoID4gMCA/IE1hdGgubWF4KC4uLmFsbFN1cHBsaWVyVmFsdWVzKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIHRlc3RNYXg6IGFsbFRlc3RWYWx1ZXMubGVuZ3RoID4gMCA/IE1hdGgubWF4KC4uLmFsbFRlc3RWYWx1ZXMpLnRvRml4ZWQoMikgOiAwLA0KICAgICAgICAgICAgc3VwcGxpZXJNaW46IGFsbFN1cHBsaWVyVmFsdWVzLmxlbmd0aCA+IDAgPyBNYXRoLm1pbiguLi5hbGxTdXBwbGllclZhbHVlcykudG9GaXhlZCgyKSA6IDAsDQogICAgICAgICAgICB0ZXN0TWluOiBhbGxUZXN0VmFsdWVzLmxlbmd0aCA+IDAgPyBNYXRoLm1pbiguLi5hbGxUZXN0VmFsdWVzKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIGRhdGFDb3VudDogYWxsVGVzdFZhbHVlcy5sZW5ndGgNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKGDojrflj5bmnZDmlpkke21hdGVyaWFsSWR95pWw5o2u5aSx6LSl77yaYCwgZXJyb3IpOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHJldHVybiBjb21wYXJlRGF0YTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluS+m+W6lOWVhuWvueavlOaVsOaNriAqLw0KICAgIGFzeW5jIGdldFN1cHBsaWVyQ29tcGFyZURhdGEoKSB7DQogICAgICBjb25zdCBzdXBwbGllcnMgPSB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBsaWVyTmFtZXMgfHwgW107DQogICAgICBjb25zdCBjb21wYXJlRGF0YSA9IFtdOw0KDQogICAgICBpZiAoc3VwcGxpZXJzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gY29tcGFyZURhdGE7DQogICAgICB9DQoNCiAgICAgIGZvciAoY29uc3Qgc3VwcGxpZXIgb2Ygc3VwcGxpZXJzKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsaXN0VGVzdFJlc3VsdCh7DQogICAgICAgICAgICBzdXBwbGllck5hbWU6IHN1cHBsaWVyLA0KICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICBjb25zdCB0ZXN0UmVzdWx0cyA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgICAgY29uc3Qgc3VwcGxpZXJWYWx1ZXMgPSB0ZXN0UmVzdWx0cy5tYXAociA9PiBwYXJzZUZsb2F0KHIuc3VwcGxpZXJEYXRhc2hlZXRWYWwpKS5maWx0ZXIodiA9PiAhaXNOYU4odikpOw0KICAgICAgICAgIGNvbnN0IHRlc3RWYWx1ZXMgPSB0ZXN0UmVzdWx0cy5tYXAociA9PiBwYXJzZUZsb2F0KHIudGVzdFZhbHVlKSkuZmlsdGVyKHYgPT4gIWlzTmFOKHYpKTsNCg0KICAgICAgICAgIGNvbXBhcmVEYXRhLnB1c2goew0KICAgICAgICAgICAgbmFtZTogc3VwcGxpZXIsDQogICAgICAgICAgICBzdXBwbGllckF2Zzogc3VwcGxpZXJWYWx1ZXMubGVuZ3RoID4gMCA/IChzdXBwbGllclZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHN1cHBsaWVyVmFsdWVzLmxlbmd0aCkudG9GaXhlZCgyKSA6IDAsDQogICAgICAgICAgICB0ZXN0QXZnOiB0ZXN0VmFsdWVzLmxlbmd0aCA+IDAgPyAodGVzdFZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHRlc3RWYWx1ZXMubGVuZ3RoKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIGFjY3VyYWN5OiBzdXBwbGllclZhbHVlcy5sZW5ndGggPiAwICYmIHRlc3RWYWx1ZXMubGVuZ3RoID4gMCA/DQogICAgICAgICAgICAgICgxMDAgLSBNYXRoLmFicygoc3VwcGxpZXJWYWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkgLyBzdXBwbGllclZhbHVlcy5sZW5ndGgpIC0NCiAgICAgICAgICAgICAgKHRlc3RWYWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkgLyB0ZXN0VmFsdWVzLmxlbmd0aCkpIC8NCiAgICAgICAgICAgICAgKHRlc3RWYWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkgLyB0ZXN0VmFsdWVzLmxlbmd0aCkgKiAxMDApLnRvRml4ZWQoMikgOiAwLA0KICAgICAgICAgICAgZGF0YUNvdW50OiB0ZXN0UmVzdWx0cy5sZW5ndGgNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKGDojrflj5bkvpvlupTllYYke3N1cHBsaWVyfeaVsOaNruWksei0pe+8mmAsIGVycm9yKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gY29tcGFyZURhdGE7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blj4LmlbDnvJblj7flr7nmr5TmlbDmja4gKi8NCiAgICBhc3luYyBnZXRQYXJhbU51bWJlckNvbXBhcmVEYXRhKCkgew0KICAgICAgY29uc3QgcGFyYW1Hcm91cElkcyA9IHRoaXMucXVlcnlQYXJhbXMucGFyYW1OdW1iZXJzIHx8IFtdOw0KICAgICAgY29uc3QgY29tcGFyZURhdGEgPSBbXTsNCg0KICAgICAgaWYgKHBhcmFtR3JvdXBJZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBjb21wYXJlRGF0YTsNCiAgICAgIH0NCg0KICAgICAgZm9yIChjb25zdCBncm91cElkIG9mIHBhcmFtR3JvdXBJZHMpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICAvLyDojrflj5bmtYvor5Xnu5PmnpzmlbDmja4NCiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxpc3RUZXN0UmVzdWx0KHsNCiAgICAgICAgICAgIGdyb3VwSWQ6IGdyb3VwSWQsDQogICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgcGFnZVNpemU6IDEwMDANCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOiOt+WPluWPguaVsOaYjue7huaVsOaNrg0KICAgICAgICAgIGNvbnN0IHBhcmFtSXRlbVJlc3BvbnNlID0gYXdhaXQgbGlzdFByb2Nlc3NQYXJhbUl0ZW0oew0KICAgICAgICAgICAgZ3JvdXBJZDogZ3JvdXBJZCwNCiAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMA0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgY29uc3QgcGFyYW1Hcm91cCA9IHRoaXMucGFyYW1OdW1iZXJPcHRpb25zLmZpbmQocCA9PiBwLmdyb3VwSWQgPT09IGdyb3VwSWQpOw0KICAgICAgICAgIGNvbnN0IHRlc3RSZXN1bHRzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICBjb25zdCBwYXJhbUl0ZW1zID0gcGFyYW1JdGVtUmVzcG9uc2Uucm93cyB8fCBbXTsNCg0KICAgICAgICAgIGNvbnN0IHN1cHBsaWVyVmFsdWVzID0gdGVzdFJlc3VsdHMubWFwKHIgPT4gcGFyc2VGbG9hdChyLnN1cHBsaWVyRGF0YXNoZWV0VmFsKSkuZmlsdGVyKHYgPT4gIWlzTmFOKHYpKTsNCiAgICAgICAgICBjb25zdCB0ZXN0VmFsdWVzID0gdGVzdFJlc3VsdHMubWFwKHIgPT4gcGFyc2VGbG9hdChyLnRlc3RWYWx1ZSkpLmZpbHRlcih2ID0+ICFpc05hTih2KSk7DQoNCiAgICAgICAgICAvLyDmoLzlvI/ljJblj4LmlbDmmI7nu4bkv6Hmga8NCiAgICAgICAgICBjb25zdCBwYXJhbURldGFpbHMgPSBwYXJhbUl0ZW1zLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgICBwYXJhbU5hbWU6IGl0ZW0ucGFyYW1OYW1lIHx8ICdOL0EnLA0KICAgICAgICAgICAgcGFyYW1WYWx1ZTogaXRlbS5wYXJhbVZhbHVlICE9PSBudWxsICYmIGl0ZW0ucGFyYW1WYWx1ZSAhPT0gdW5kZWZpbmVkID8NCiAgICAgICAgICAgICAgU3RyaW5nKGl0ZW0ucGFyYW1WYWx1ZSkgOiAnTi9BJywNCiAgICAgICAgICAgIHVuaXQ6IGl0ZW0udW5pdCB8fCAnJw0KICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIGNvbXBhcmVEYXRhLnB1c2goew0KICAgICAgICAgICAgbmFtZTogcGFyYW1Hcm91cCA/IHBhcmFtR3JvdXAucGFyYW1OdW1iZXIgOiBg5Y+C5pWwJHtncm91cElkfWAsDQogICAgICAgICAgICBtYXRlcmlhbDogcGFyYW1Hcm91cCA/IHBhcmFtR3JvdXAubWF0ZXJpYWxOYW1lIDogJycsDQogICAgICAgICAgICBwcm9jZXNzVHlwZTogcGFyYW1Hcm91cCA/IHBhcmFtR3JvdXAucHJvY2Vzc1R5cGUgOiAnJywNCiAgICAgICAgICAgIHN1cHBsaWVyQXZnOiBzdXBwbGllclZhbHVlcy5sZW5ndGggPiAwID8gKHN1cHBsaWVyVmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gc3VwcGxpZXJWYWx1ZXMubGVuZ3RoKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIHRlc3RBdmc6IHRlc3RWYWx1ZXMubGVuZ3RoID4gMCA/ICh0ZXN0VmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gdGVzdFZhbHVlcy5sZW5ndGgpLnRvRml4ZWQoMikgOiAwLA0KICAgICAgICAgICAgZGV2aWF0aW9uOiBzdXBwbGllclZhbHVlcy5sZW5ndGggPiAwICYmIHRlc3RWYWx1ZXMubGVuZ3RoID4gMCA/DQogICAgICAgICAgICAgIE1hdGguYWJzKChzdXBwbGllclZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHN1cHBsaWVyVmFsdWVzLmxlbmd0aCkgLQ0KICAgICAgICAgICAgICAodGVzdFZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHRlc3RWYWx1ZXMubGVuZ3RoKSkudG9GaXhlZCgyKSA6IDAsDQogICAgICAgICAgICBkYXRhQ291bnQ6IHRlc3RSZXN1bHRzLmxlbmd0aCwNCiAgICAgICAgICAgIHBhcmFtRGV0YWlsczogcGFyYW1EZXRhaWxzLCAvLyDmt7vliqDlj4LmlbDmmI7nu4bkv6Hmga8NCiAgICAgICAgICAgIGdyb3VwSWQ6IGdyb3VwSWQgLy8g5L+d5a2YZ3JvdXBJZOeUqOS6juWQjue7reS9v+eUqA0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOiOt+WPluWPguaVsOe7hCR7Z3JvdXBJZH3mlbDmja7lpLHotKXvvJpgLCBlcnJvcik7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGNvbXBhcmVEYXRhOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5bel6Im657G75Z6L5a+55q+U5pWw5o2uICovDQogICAgYXN5bmMgZ2V0UHJvY2Vzc1R5cGVDb21wYXJlRGF0YSgpIHsNCiAgICAgIGNvbnN0IHByb2Nlc3NUeXBlcyA9IHRoaXMucXVlcnlQYXJhbXMucHJvY2Vzc1R5cGVzIHx8IFtdOw0KICAgICAgY29uc3QgY29tcGFyZURhdGEgPSBbXTsNCg0KICAgICAgaWYgKHByb2Nlc3NUeXBlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIGNvbXBhcmVEYXRhOw0KICAgICAgfQ0KDQogICAgICBmb3IgKGNvbnN0IHByb2Nlc3NUeXBlIG9mIHByb2Nlc3NUeXBlcykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbGlzdFRlc3RSZXN1bHQoew0KICAgICAgICAgICAgcHJvY2Vzc1R5cGU6IHByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICBjb25zdCB0ZXN0UmVzdWx0cyA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgICAgY29uc3Qgc3VwcGxpZXJWYWx1ZXMgPSB0ZXN0UmVzdWx0cy5tYXAociA9PiBwYXJzZUZsb2F0KHIuc3VwcGxpZXJEYXRhc2hlZXRWYWwpKS5maWx0ZXIodiA9PiAhaXNOYU4odikpOw0KICAgICAgICAgIGNvbnN0IHRlc3RWYWx1ZXMgPSB0ZXN0UmVzdWx0cy5tYXAociA9PiBwYXJzZUZsb2F0KHIudGVzdFZhbHVlKSkuZmlsdGVyKHYgPT4gIWlzTmFOKHYpKTsNCg0KICAgICAgICAgIGNvbXBhcmVEYXRhLnB1c2goew0KICAgICAgICAgICAgbmFtZTogcHJvY2Vzc1R5cGUsDQogICAgICAgICAgICBzdXBwbGllckF2Zzogc3VwcGxpZXJWYWx1ZXMubGVuZ3RoID4gMCA/IChzdXBwbGllclZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHN1cHBsaWVyVmFsdWVzLmxlbmd0aCkudG9GaXhlZCgyKSA6IDAsDQogICAgICAgICAgICB0ZXN0QXZnOiB0ZXN0VmFsdWVzLmxlbmd0aCA+IDAgPyAodGVzdFZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHRlc3RWYWx1ZXMubGVuZ3RoKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIHN0YWJpbGl0eTogdGVzdFZhbHVlcy5sZW5ndGggPiAxID8gdGhpcy5jYWxjdWxhdGVTdGFuZGFyZERldmlhdGlvbih0ZXN0VmFsdWVzKS50b0ZpeGVkKDIpIDogMCwNCiAgICAgICAgICAgIGRhdGFDb3VudDogdGVzdFJlc3VsdHMubGVuZ3RoDQogICAgICAgICAgfSk7DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcihg6I635Y+W5bel6Im657G75Z6LJHtwcm9jZXNzVHlwZX3mlbDmja7lpLHotKXvvJpgLCBlcnJvcik7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGNvbXBhcmVEYXRhOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5pe26Ze06LaL5Yq/5pWw5o2uICovDQogICAgYXN5bmMgZ2V0VGltZVRyZW5kRGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy5kYXRlUmFuZ2UgfHwgdGhpcy5xdWVyeVBhcmFtcy5kYXRlUmFuZ2UubGVuZ3RoICE9PSAyKSB7DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgW3N0YXJ0RGF0ZSwgZW5kRGF0ZV0gPSB0aGlzLnF1ZXJ5UGFyYW1zLmRhdGVSYW5nZTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsaXN0VGVzdFJlc3VsdCh7DQogICAgICAgICAgc3RhcnREYXRlOiBzdGFydERhdGUsDQogICAgICAgICAgZW5kRGF0ZTogZW5kRGF0ZSwNCiAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgIHBhZ2VTaXplOiAxMDAwDQogICAgICAgIH0pOw0KDQogICAgICAgIGNvbnN0IHRlc3RSZXN1bHRzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgY29uc3QgdHJlbmREYXRhID0gW107DQoNCiAgICAgICAgLy8g5oyJ5pel5pyf5YiG57uEDQogICAgICAgIGNvbnN0IGRhdGVHcm91cHMgPSB7fTsNCiAgICAgICAgdGVzdFJlc3VsdHMuZm9yRWFjaChyZXN1bHQgPT4gew0KICAgICAgICAgIGNvbnN0IGRhdGUgPSByZXN1bHQuY3JlYXRlVGltZSA/IHJlc3VsdC5jcmVhdGVUaW1lLnNwbGl0KCcgJylbMF0gOiAnJzsNCiAgICAgICAgICBpZiAoZGF0ZSAmJiAhZGF0ZUdyb3Vwc1tkYXRlXSkgew0KICAgICAgICAgICAgZGF0ZUdyb3Vwc1tkYXRlXSA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoZGF0ZSkgew0KICAgICAgICAgICAgZGF0ZUdyb3Vwc1tkYXRlXS5wdXNoKHJlc3VsdCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDorqHnrpfmr4/ml6XlubPlnYflgLwNCiAgICAgICAgT2JqZWN0LmtleXMoZGF0ZUdyb3Vwcykuc29ydCgpLmZvckVhY2goZGF0ZSA9PiB7DQogICAgICAgICAgY29uc3QgZGF5UmVzdWx0cyA9IGRhdGVHcm91cHNbZGF0ZV07DQogICAgICAgICAgY29uc3QgdGVzdFZhbHVlcyA9IGRheVJlc3VsdHMubWFwKHIgPT4gcGFyc2VGbG9hdChyLnRlc3RWYWx1ZSkpLmZpbHRlcih2ID0+ICFpc05hTih2KSk7DQoNCiAgICAgICAgICB0cmVuZERhdGEucHVzaCh7DQogICAgICAgICAgICBkYXRlOiBkYXRlLA0KICAgICAgICAgICAgYXZnVmFsdWU6IHRlc3RWYWx1ZXMubGVuZ3RoID4gMCA/ICh0ZXN0VmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gdGVzdFZhbHVlcy5sZW5ndGgpLnRvRml4ZWQoMikgOiAwLA0KICAgICAgICAgICAgY291bnQ6IGRheVJlc3VsdHMubGVuZ3RoDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KDQogICAgICAgIHJldHVybiB0cmVuZERhdGE7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bml7bpl7Totovlir/mlbDmja7lpLHotKXvvJonLCBlcnJvcik7DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluS+m+W6lOWVhnZz5rWL6K+V5YC85a+55q+U5pWw5o2uICovDQogICAgYXN5bmMgZ2V0U3VwcGxpZXJWc1Rlc3REYXRhKCkgew0KICAgICAgY29uc3QgZ3JvdXBJZCA9IHRoaXMucXVlcnlQYXJhbXMuY29tcGFyZVBhcmFtOw0KDQogICAgICBpZiAoIWdyb3VwSWQpIHsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxpc3RUZXN0UmVzdWx0KHsNCiAgICAgICAgICBncm91cElkOiBncm91cElkLA0KICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgcGFnZVNpemU6IDEwMDANCiAgICAgICAgfSk7DQoNCiAgICAgICAgY29uc3QgdGVzdFJlc3VsdHMgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICBjb25zdCBjb21wYXJlRGF0YSA9IHRlc3RSZXN1bHRzLm1hcChyZXN1bHQgPT4gKHsNCiAgICAgICAgICBuYW1lOiByZXN1bHQubWF0ZXJpYWxOYW1lIHx8ICfmnKrnn6XmnZDmlpknLA0KICAgICAgICAgIHN1cHBsaWVyOiByZXN1bHQuc3VwcGxpZXJOYW1lIHx8ICfmnKrnn6XkvpvlupTllYYnLA0KICAgICAgICAgIHN1cHBsaWVyVmFsdWU6IHBhcnNlRmxvYXQocmVzdWx0LnN1cHBsaWVyRGF0YXNoZWV0VmFsKSB8fCAwLA0KICAgICAgICAgIHRlc3RWYWx1ZTogcGFyc2VGbG9hdChyZXN1bHQudGVzdFZhbHVlKSB8fCAwLA0KICAgICAgICAgIGRpZmZlcmVuY2U6IE1hdGguYWJzKChwYXJzZUZsb2F0KHJlc3VsdC5zdXBwbGllckRhdGFzaGVldFZhbCkgfHwgMCkgLSAocGFyc2VGbG9hdChyZXN1bHQudGVzdFZhbHVlKSB8fCAwKSkudG9GaXhlZCgyKSwNCiAgICAgICAgICBjcmVhdGVUaW1lOiByZXN1bHQuY3JlYXRlVGltZQ0KICAgICAgICB9KSk7DQoNCiAgICAgICAgcmV0dXJuIGNvbXBhcmVEYXRhOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5L6b5bqU5ZWGdnPmtYvor5XlgLzmlbDmja7lpLHotKXvvJonLCBlcnJvcik7DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiuoeeul+agh+WHhuW3riAqLw0KICAgIGNhbGN1bGF0ZVN0YW5kYXJkRGV2aWF0aW9uKHZhbHVlcykgew0KICAgICAgY29uc3QgYXZnID0gdmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gdmFsdWVzLmxlbmd0aDsNCiAgICAgIGNvbnN0IHNxdWFyZURpZmZzID0gdmFsdWVzLm1hcCh2YWx1ZSA9PiBNYXRoLnBvdyh2YWx1ZSAtIGF2ZywgMikpOw0KICAgICAgY29uc3QgYXZnU3F1YXJlRGlmZiA9IHNxdWFyZURpZmZzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gc3F1YXJlRGlmZnMubGVuZ3RoOw0KICAgICAgcmV0dXJuIE1hdGguc3FydChhdmdTcXVhcmVEaWZmKTsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluWPguaVsOaYjue7huaYvuekuiAqLw0KICAgIGZvcm1hdFBhcmFtRGV0YWlscyhyb3csIGNvbHVtbiwgY2VsbFZhbHVlKSB7DQogICAgICBpZiAoIWNlbGxWYWx1ZSB8fCAhQXJyYXkuaXNBcnJheShjZWxsVmFsdWUpKSB7DQogICAgICAgIHJldHVybiAn5pqC5peg5Y+C5pWwJzsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGNlbGxWYWx1ZS5tYXAocGFyYW0gPT4gew0KICAgICAgICBsZXQgdGV4dCA9IHBhcmFtLnBhcmFtTmFtZSArICc6ICcgKyBwYXJhbS5wYXJhbVZhbHVlOw0KICAgICAgICBpZiAocGFyYW0udW5pdCkgew0KICAgICAgICAgIHRleHQgKz0gJyAnICsgcGFyYW0udW5pdDsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGV4dDsNCiAgICAgIH0pLmpvaW4oJzsgJyk7DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDooajmoLzliJcgKi8NCiAgICB1cGRhdGVUYWJsZUNvbHVtbnMoKSB7DQogICAgICBjb25zdCB7IGNvbXBhcmVUeXBlIH0gPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KDQogICAgICBzd2l0Y2ggKGNvbXBhcmVUeXBlKSB7DQogICAgICAgIGNhc2UgJ21hdGVyaWFsJzoNCiAgICAgICAgICB0aGlzLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAgIHsgcHJvcDogJ25hbWUnLCBsYWJlbDogJ+adkOaWmeWQjeensCcsIHdpZHRoOiAxNTAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ3N1cHBsaWVyJywgbGFiZWw6ICfkvpvlupTllYYnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdzdXBwbGllckF2ZycsIGxhYmVsOiAn5L6b5bqU5ZWG5bmz5Z2H5YC8Jywgd2lkdGg6IDEyMCB9LA0KICAgICAgICAgICAgeyBwcm9wOiAndGVzdEF2ZycsIGxhYmVsOiAn5rWL6K+V5bmz5Z2H5YC8Jywgd2lkdGg6IDEyMCB9LA0KICAgICAgICAgICAgeyBwcm9wOiAnZGF0YUNvdW50JywgbGFiZWw6ICfmlbDmja7ph48nLCB3aWR0aDogODAgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ3N1cHBsaWVyJzoNCiAgICAgICAgICB0aGlzLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAgIHsgcHJvcDogJ25hbWUnLCBsYWJlbDogJ+S+m+W6lOWVhicsIHdpZHRoOiAxNTAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ3N1cHBsaWVyQXZnJywgbGFiZWw6ICfkvpvlupTllYblubPlnYflgLwnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICd0ZXN0QXZnJywgbGFiZWw6ICfmtYvor5XlubPlnYflgLwnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdhY2N1cmFjeScsIGxhYmVsOiAn5YeG56Gu546HKCUpJywgd2lkdGg6IDEwMCB9LA0KICAgICAgICAgICAgeyBwcm9wOiAnZGF0YUNvdW50JywgbGFiZWw6ICfmlbDmja7ph48nLCB3aWR0aDogODAgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ3BhcmFtTnVtYmVyJzoNCiAgICAgICAgICB0aGlzLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAgIHsgcHJvcDogJ25hbWUnLCBsYWJlbDogJ+WPguaVsOe8luWPtycsIHdpZHRoOiAxMjAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ21hdGVyaWFsJywgbGFiZWw6ICfmnZDmlpnlkI3np7AnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdwcm9jZXNzVHlwZScsIGxhYmVsOiAn5bel6Im657G75Z6LJywgd2lkdGg6IDEwMCB9LA0KICAgICAgICAgICAgeyBwcm9wOiAnc3VwcGxpZXJBdmcnLCBsYWJlbDogJ+S+m+W6lOWVhuW5s+Wdh+WAvCcsIHdpZHRoOiAxMjAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ3Rlc3RBdmcnLCBsYWJlbDogJ+a1i+ivleW5s+Wdh+WAvCcsIHdpZHRoOiAxMjAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ2RldmlhdGlvbicsIGxhYmVsOiAn5YGP5beuJywgd2lkdGg6IDgwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdwYXJhbURldGFpbHMnLCBsYWJlbDogJ+WPguaVsOaYjue7hicsIHdpZHRoOiAyMDAgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ3Byb2Nlc3NUeXBlJzoNCiAgICAgICAgICB0aGlzLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAgIHsgcHJvcDogJ25hbWUnLCBsYWJlbDogJ+W3peiJuuexu+WeiycsIHdpZHRoOiAxNTAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ3N1cHBsaWVyQXZnJywgbGFiZWw6ICfkvpvlupTllYblubPlnYflgLwnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICd0ZXN0QXZnJywgbGFiZWw6ICfmtYvor5XlubPlnYflgLwnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdzdGFiaWxpdHknLCBsYWJlbDogJ+eos+WumuaApycsIHdpZHRoOiAxMDAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ2RhdGFDb3VudCcsIGxhYmVsOiAn5pWw5o2u6YePJywgd2lkdGg6IDgwIH0NCiAgICAgICAgICBdOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICd0aW1lVHJlbmQnOg0KICAgICAgICAgIHRoaXMudGFibGVDb2x1bW5zID0gWw0KICAgICAgICAgICAgeyBwcm9wOiAnZGF0ZScsIGxhYmVsOiAn5pel5pyfJywgd2lkdGg6IDEyMCB9LA0KICAgICAgICAgICAgeyBwcm9wOiAnYXZnVmFsdWUnLCBsYWJlbDogJ+W5s+Wdh+WAvCcsIHdpZHRoOiAxMDAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ2NvdW50JywgbGFiZWw6ICfmlbDmja7ph48nLCB3aWR0aDogODAgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ3N1cHBsaWVyVnNUZXN0JzoNCiAgICAgICAgICB0aGlzLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAgIHsgcHJvcDogJ25hbWUnLCBsYWJlbDogJ+adkOaWmeWQjeensCcsIHdpZHRoOiAxNTAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ3N1cHBsaWVyJywgbGFiZWw6ICfkvpvlupTllYYnLCB3aWR0aDogMTIwIH0sDQogICAgICAgICAgICB7IHByb3A6ICdzdXBwbGllclZhbHVlJywgbGFiZWw6ICfkvpvlupTllYblgLwnLCB3aWR0aDogMTAwIH0sDQogICAgICAgICAgICB7IHByb3A6ICd0ZXN0VmFsdWUnLCBsYWJlbDogJ+a1i+ivleWAvCcsIHdpZHRoOiAxMDAgfSwNCiAgICAgICAgICAgIHsgcHJvcDogJ2RpZmZlcmVuY2UnLCBsYWJlbDogJ+W3ruWAvCcsIHdpZHRoOiA4MCB9DQogICAgICAgICAgXTsNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOa4suafk+WbvuihqCAqLw0KICAgIHJlbmRlckNoYXJ0KCkgew0KICAgICAgaWYgKCF0aGlzLmNoYXJ0IHx8IHRoaXMuY2hhcnREYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGxldCBvcHRpb24gPSB7fTsNCg0KICAgICAgc3dpdGNoICh0aGlzLmNoYXJ0VHlwZSkgew0KICAgICAgICBjYXNlICdsaW5lJzoNCiAgICAgICAgICBvcHRpb24gPSB0aGlzLmdldExpbmVDaGFydE9wdGlvbigpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdiYXInOg0KICAgICAgICAgIG9wdGlvbiA9IHRoaXMuZ2V0QmFyQ2hhcnRPcHRpb24oKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnc2NhdHRlcic6DQogICAgICAgICAgb3B0aW9uID0gdGhpcy5nZXRTY2F0dGVyQ2hhcnRPcHRpb24oKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAncmFkYXInOg0KICAgICAgICAgIG9wdGlvbiA9IHRoaXMuZ2V0UmFkYXJDaGFydE9wdGlvbigpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdoZWF0bWFwJzoNCiAgICAgICAgICBvcHRpb24gPSB0aGlzLmdldEhlYXRtYXBDaGFydE9wdGlvbigpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmNoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5oqY57q/5Zu+6YWN572uICovDQogICAgZ2V0TGluZUNoYXJ0T3B0aW9uKCkgew0KICAgICAgLy8g5qC55o2u5a+55q+U57G75Z6L55Sf5oiQ5LiN5ZCM55qE5Zu+6KGo6YWN572uDQogICAgICBjb25zdCB7IGNvbXBhcmVUeXBlIH0gPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KDQogICAgICBpZiAoY29tcGFyZVR5cGUgPT09ICd0aW1lVHJlbmQnKSB7DQogICAgICAgIC8vIOaXtumXtOi2i+WKv+Wbvg0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgICB0ZXh0OiB0aGlzLmNoYXJ0VGl0bGUsDQogICAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICAgIH0sDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgICAgcGFyYW1zLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSBwYXJhbS5tYXJrZXIgKyBwYXJhbS5zZXJpZXNOYW1lICsgJzogJyArIHBhcmFtLnZhbHVlICsgJzxici8+JzsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBncmlkOiB7DQogICAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgICBib3R0b206ICczJScsDQogICAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgICB9LA0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogdGhpcy5jaGFydERhdGEubWFwKGl0ZW0gPT4gaXRlbS5kYXRlKQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBuYW1lOiAn5bmz5Z2H5YC8Jw0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+W5s+Wdh+WAvCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiB0aGlzLmNoYXJ0RGF0YS5tYXAoaXRlbSA9PiBpdGVtLmF2Z1ZhbHVlKSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgfV0NCiAgICAgICAgfTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWFtuS7luWvueavlOexu+Wei+eahOaKmOe6v+Wbvg0KICAgICAgICBjb25zdCBzZWxmID0gdGhpczsNCg0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgICB0ZXh0OiB0aGlzLmNoYXJ0VGl0bGUsDQogICAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICAgIH0sDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSg1MCwgNTAsIDUwLCAwLjk1KScsDQogICAgICAgICAgICBib3JkZXJDb2xvcjogJyM0MDlFRkYnLA0KICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJywNCiAgICAgICAgICAgICAgZm9udFNpemU6IDEyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZXh0cmFDc3NUZXh0OiAnYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMyk7IGJvcmRlci1yYWRpdXM6IDhweDsgcGFkZGluZzogMTJweDsnLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgY29uc3QgZGF0YUluZGV4ID0gcGFyYW1zWzBdLmRhdGFJbmRleDsNCiAgICAgICAgICAgICAgY29uc3QgY3VycmVudERhdGEgPSBzZWxmLmNoYXJ0RGF0YVtkYXRhSW5kZXhdOw0KDQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBgPGRpdiBzdHlsZT0iZm9udC1zaXplOiAxNHB4OyBmb250LXdlaWdodDogYm9sZDsgY29sb3I6ICM0MDlFRkY7IG1hcmdpbi1ib3R0b206IDhweDsiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfk4ogJHtwYXJhbXNbMF0ubmFtZX0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+YDsNCg0KICAgICAgICAgICAgICAvLyDmmL7npLrln7rmnKzlr7nmr5TmlbDmja4NCiAgICAgICAgICAgICAgcGFyYW1zLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IGNvbG9yID0gcGFyYW0uY29sb3I7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9IGA8ZGl2IHN0eWxlPSJtYXJnaW46IDRweCAwOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHdpZHRoOiAxMHB4OyBoZWlnaHQ6IDEwcHg7IGJhY2tncm91bmQ6ICR7Y29sb3J9OyBib3JkZXItcmFkaXVzOiA1MCU7IG1hcmdpbi1yaWdodDogOHB4OyI+PC9zcGFuPg0KICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IDUwMDsiPiR7cGFyYW0uc2VyaWVzTmFtZX06PC9zcGFuPg0KICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0ibWFyZ2luLWxlZnQ6IDhweDsgY29sb3I6ICM2N0MyM0E7IGZvbnQtd2VpZ2h0OiBib2xkOyI+JHtzZWxmLmZvcm1hdE51bWJlcihwYXJhbS52YWx1ZSl9PC9zcGFuPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+YDsNCiAgICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgICAgLy8g5qC55o2u5a+55q+U57G75Z6L5pi+56S66K+m57uG5L+h5oGvDQogICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YSkgew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnPGRpdiBzdHlsZT0iYm9yZGVyLXRvcDogMXB4IHNvbGlkICM2NjY7IG1hcmdpbjogOHB4IDA7IHBhZGRpbmctdG9wOiA4cHg7Ij4nOw0KDQogICAgICAgICAgICAgICAgaWYgKHNlbGYucXVlcnlQYXJhbXMuY29tcGFyZVR5cGUgPT09ICdwYXJhbU51bWJlcicgJiYgY3VycmVudERhdGEucGFyYW1EZXRhaWxzKSB7DQogICAgICAgICAgICAgICAgICByZXN1bHQgKz0gJzxkaXYgc3R5bGU9ImNvbG9yOiAjRTZBMjNDOyBmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogNnB4OyI+8J+TiyDlj4LmlbDmmI7nu4bkv6Hmga88L2Rpdj4nOw0KICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnREYXRhLm1hdGVyaWFsKSB7DQogICAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSBgPGRpdiBzdHlsZT0ibWFyZ2luOiAycHggMDsiPjxzcGFuIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsiPuadkOaWmTo8L3NwYW4+ICR7Y3VycmVudERhdGEubWF0ZXJpYWx9PC9kaXY+YDsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YS5wcm9jZXNzVHlwZSkgew0KICAgICAgICAgICAgICAgICAgICByZXN1bHQgKz0gYDxkaXYgc3R5bGU9Im1hcmdpbjogMnB4IDA7Ij48c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7Ij7lt6Xoibo6PC9zcGFuPiAke2N1cnJlbnREYXRhLnByb2Nlc3NUeXBlfTwvZGl2PmA7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEucGFyYW1EZXRhaWxzICYmIGN1cnJlbnREYXRhLnBhcmFtRGV0YWlscy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnPGRpdiBzdHlsZT0ibWFyZ2luOiA0cHggMDsgY29sb3I6ICM5MDkzOTk7Ij7lj4LmlbDliJfooag6PC9kaXY+JzsNCiAgICAgICAgICAgICAgICAgICAgY3VycmVudERhdGEucGFyYW1EZXRhaWxzLnNsaWNlKDAsIDUpLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSBgPGRpdiBzdHlsZT0ibWFyZ2luOiAxcHggMDsgcGFkZGluZy1sZWZ0OiAxMnB4OyBmb250LXNpemU6IDExcHg7Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg4oCiICR7cGFyYW0ucGFyYW1OYW1lfTogPHNwYW4gc3R5bGU9ImNvbG9yOiAjNjdDMjNBOyI+JHtzZWxmLmZvcm1hdE51bWJlcihwYXJhbS5wYXJhbVZhbHVlKX08L3NwYW4+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICR7cGFyYW0udW5pdCA/ICcgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyI+JyArIHBhcmFtLnVuaXQgKyAnPC9zcGFuPicgOiAnJ30NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PmA7DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEucGFyYW1EZXRhaWxzLmxlbmd0aCA+IDUpIHsNCiAgICAgICAgICAgICAgICAgICAgICByZXN1bHQgKz0gYDxkaXYgc3R5bGU9Im1hcmdpbjogMnB4IDA7IHBhZGRpbmctbGVmdDogMTJweDsgY29sb3I6ICM5MDkzOTk7IGZvbnQtc2l6ZTogMTFweDsiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4g6L+Y5pyJICR7Y3VycmVudERhdGEucGFyYW1EZXRhaWxzLmxlbmd0aCAtIDV9IOS4quWPguaVsA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+YDsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoc2VsZi5xdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ21hdGVyaWFsJyAmJiBjdXJyZW50RGF0YS5zdXBwbGllcikgew0KICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9ICc8ZGl2IHN0eWxlPSJjb2xvcjogI0U2QTIzQzsgZm9udC13ZWlnaHQ6IGJvbGQ7IG1hcmdpbi1ib3R0b206IDZweDsiPvCfj60g5L6b5bqU5ZWG5L+h5oGvPC9kaXY+JzsNCiAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSBgPGRpdiBzdHlsZT0ibWFyZ2luOiAycHggMDsiPjxzcGFuIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsiPuS+m+W6lOWVhjo8L3NwYW4+ICR7Y3VycmVudERhdGEuc3VwcGxpZXJ9PC9kaXY+YDsNCiAgICAgICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YS5kYXRhQ291bnQpIHsNCiAgICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9IGA8ZGl2IHN0eWxlPSJtYXJnaW46IDJweCAwOyI+PHNwYW4gc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyI+5pWw5o2u6YePOjwvc3Bhbj4gJHtjdXJyZW50RGF0YS5kYXRhQ291bnR9IOadoTwvZGl2PmA7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChzZWxmLnF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlID09PSAnc3VwcGxpZXInICYmIGN1cnJlbnREYXRhLmFjY3VyYWN5KSB7DQogICAgICAgICAgICAgICAgICByZXN1bHQgKz0gJzxkaXYgc3R5bGU9ImNvbG9yOiAjRTZBMjNDOyBmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogNnB4OyI+8J+TiCDotKjph4/mjIfmoIc8L2Rpdj4nOw0KICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9IGA8ZGl2IHN0eWxlPSJtYXJnaW46IDJweCAwOyI+PHNwYW4gc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyI+5YeG56Gu546HOjwvc3Bhbj4gPHNwYW4gc3R5bGU9ImNvbG9yOiAke2N1cnJlbnREYXRhLmFjY3VyYWN5ID4gOTAgPyAnIzY3QzIzQScgOiBjdXJyZW50RGF0YS5hY2N1cmFjeSA+IDgwID8gJyNFNkEyM0MnIDogJyNGNTZDNkMnfTsiPiR7Y3VycmVudERhdGEuYWNjdXJhY3l9JTwvc3Bhbj48L2Rpdj5gOw0KICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnREYXRhLmRhdGFDb3VudCkgew0KICAgICAgICAgICAgICAgICAgICByZXN1bHQgKz0gYDxkaXYgc3R5bGU9Im1hcmdpbjogMnB4IDA7Ij48c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7Ij7mlbDmja7ph486PC9zcGFuPiAke2N1cnJlbnREYXRhLmRhdGFDb3VudH0g5p2hPC9kaXY+YDsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHNlbGYucXVlcnlQYXJhbXMuY29tcGFyZVR5cGUgPT09ICdwcm9jZXNzVHlwZScgJiYgY3VycmVudERhdGEuc3RhYmlsaXR5KSB7DQogICAgICAgICAgICAgICAgICByZXN1bHQgKz0gJzxkaXYgc3R5bGU9ImNvbG9yOiAjRTZBMjNDOyBmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogNnB4OyI+4pqZ77iPIOW3peiJuuaMh+aghzwvZGl2Pic7DQogICAgICAgICAgICAgICAgICByZXN1bHQgKz0gYDxkaXYgc3R5bGU9Im1hcmdpbjogMnB4IDA7Ij48c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7Ij7nqLPlrprmgKc6PC9zcGFuPiA8c3BhbiBzdHlsZT0iY29sb3I6ICR7Y3VycmVudERhdGEuc3RhYmlsaXR5ID4gOTAgPyAnIzY3QzIzQScgOiBjdXJyZW50RGF0YS5zdGFiaWxpdHkgPiA4MCA/ICcjRTZBMjNDJyA6ICcjRjU2QzZDJ307Ij4ke2N1cnJlbnREYXRhLnN0YWJpbGl0eX0lPC9zcGFuPjwvZGl2PmA7DQogICAgICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEuZGF0YUNvdW50KSB7DQogICAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSBgPGRpdiBzdHlsZT0ibWFyZ2luOiAycHggMDsiPjxzcGFuIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsiPuaVsOaNrumHjzo8L3NwYW4+ICR7Y3VycmVudERhdGEuZGF0YUNvdW50fSDmnaE8L2Rpdj5gOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnPC9kaXY+JzsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICAgIHRvcDogJzEwJScsDQogICAgICAgICAgICBkYXRhOiBbJ+S+m+W6lOWVhuaVsOaNricsICfmtYvor5XmlbDmja4nXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZ3JpZDogew0KICAgICAgICAgICAgbGVmdDogJzMlJywNCiAgICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgICAgYm90dG9tOiAnMyUnLA0KICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkNCiAgICAgICAgICB9LA0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+aVsOWAvCcNCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn5L6b5bqU5ZWG5pWw5o2uJywNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiB0aGlzLmNoYXJ0RGF0YS5tYXAoaXRlbSA9PiBpdGVtLnN1cHBsaWVyQXZnIHx8IDApLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICfmtYvor5XmlbDmja4nLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIGRhdGE6IHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0udGVzdEF2ZyB8fCAwKSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLA0KICAgICAgICAgICAgICBzeW1ib2w6ICd0cmlhbmdsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH07DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmn7Hnirblm77phY3nva4gKi8NCiAgICBnZXRCYXJDaGFydE9wdGlvbigpIHsNCiAgICAgIGNvbnN0IHNlbGYgPSB0aGlzOw0KDQogICAgICByZXR1cm4gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6IHRoaXMuY2hhcnRUaXRsZSwNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7DQogICAgICAgICAgICB0eXBlOiAnc2hhZG93Jw0KICAgICAgICAgIH0sDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGFJbmRleCA9IHBhcmFtc1swXS5kYXRhSW5kZXg7DQogICAgICAgICAgICBjb25zdCBjdXJyZW50RGF0YSA9IHNlbGYuY2hhcnREYXRhW2RhdGFJbmRleF07DQoNCiAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQoNCiAgICAgICAgICAgIC8vIOaYvuekuuWfuuacrOWvueavlOaVsOaNrg0KICAgICAgICAgICAgcGFyYW1zLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICByZXN1bHQgKz0gcGFyYW0ubWFya2VyICsgcGFyYW0uc2VyaWVzTmFtZSArICc6ICcgKyBwYXJhbS52YWx1ZSArICc8YnIvPic7DQogICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5Y+C5pWw57yW5Y+35a+55q+U5LiU5pyJ5Y+C5pWw5piO57uG77yM5pi+56S65Y+C5pWw5piO57uG5L+h5oGvDQogICAgICAgICAgICBpZiAoc2VsZi5xdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ3BhcmFtTnVtYmVyJyAmJiBjdXJyZW50RGF0YSAmJiBjdXJyZW50RGF0YS5wYXJhbURldGFpbHMpIHsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9ICc8YnIvPjxzdHJvbmc+5Y+C5pWw5piO57uG5L+h5oGv77yaPC9zdHJvbmc+PGJyLz4nOw0KICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEubWF0ZXJpYWwpIHsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJ+adkOaWme+8micgKyBjdXJyZW50RGF0YS5tYXRlcmlhbCArICc8YnIvPic7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgaWYgKGN1cnJlbnREYXRhLnByb2Nlc3NUeXBlKSB7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9ICflt6XoibrvvJonICsgY3VycmVudERhdGEucHJvY2Vzc1R5cGUgKyAnPGJyLz4nOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIHJlc3VsdCArPSAn5Y+C5pWw5YiX6KGo77yaPGJyLz4nOw0KDQogICAgICAgICAgICAgIGN1cnJlbnREYXRhLnBhcmFtRGV0YWlscy5mb3JFYWNoKHBhcmFtID0+IHsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJ+KAoiAnICsgcGFyYW0ucGFyYW1OYW1lICsgJzogJyArIHBhcmFtLnBhcmFtVmFsdWU7DQogICAgICAgICAgICAgICAgaWYgKHBhcmFtLnVuaXQpIHsNCiAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnICcgKyBwYXJhbS51bml0Ow0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJzxici8+JzsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICB0b3A6ICcxMCUnLA0KICAgICAgICAgIGRhdGE6IFsn5L6b5bqU5ZWG5pWw5o2uJywgJ+a1i+ivleaVsOaNriddDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSksDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICByb3RhdGU6IDQ1LA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDANCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5pWw5YC8Jw0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5L6b5bqU5ZWG5pWw5o2uJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogdGhpcy5jaGFydERhdGEubWFwKGl0ZW0gPT4gaXRlbS5zdXBwbGllckF2ZyB8fCAwKSwNCiAgICAgICAgICAgIGJhcldpZHRoOiAnMzAlJywNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM1NDcwYzYnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5rWL6K+V5pWw5o2uJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogdGhpcy5jaGFydERhdGEubWFwKGl0ZW0gPT4gaXRlbS50ZXN0QXZnIHx8IDApLA0KICAgICAgICAgICAgYmFyV2lkdGg6ICczMCUnLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzkxY2M3NScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmlaPngrnlm77phY3nva4gKi8NCiAgICBnZXRTY2F0dGVyQ2hhcnRPcHRpb24oKSB7DQogICAgICBjb25zdCBzZWxmID0gdGhpczsNCg0KICAgICAgLy8g5pWj54K55Zu+5Li76KaB55So5LqO5L6b5bqU5ZWGdnPmtYvor5XlgLzlr7nmr5QNCiAgICAgIGNvbnN0IHNjYXR0ZXJEYXRhID0gdGhpcy5jaGFydERhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gWw0KICAgICAgICBwYXJzZUZsb2F0KGl0ZW0uc3VwcGxpZXJWYWx1ZSB8fCBpdGVtLnN1cHBsaWVyQXZnKSB8fCAwLA0KICAgICAgICBwYXJzZUZsb2F0KGl0ZW0udGVzdFZhbHVlIHx8IGl0ZW0udGVzdEF2ZykgfHwgMCwNCiAgICAgICAgaXRlbS5uYW1lLCAvLyDnlKjkuo50b29sdGlw5pi+56S6DQogICAgICAgIGluZGV4IC8vIOaVsOaNrue0ouW8le+8jOeUqOS6juiOt+WPluivpue7huS/oeaBrw0KICAgICAgXSk7DQoNCiAgICAgIHJldHVybiB7DQogICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgdGV4dDogdGhpcy5jaGFydFRpdGxlLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInDQogICAgICAgIH0sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIGNvbnN0IFtzdXBwbGllclZhbCwgdGVzdFZhbCwgbmFtZSwgZGF0YUluZGV4XSA9IHBhcmFtcy5kYXRhOw0KICAgICAgICAgICAgY29uc3QgY3VycmVudERhdGEgPSBzZWxmLmNoYXJ0RGF0YVtkYXRhSW5kZXhdOw0KDQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gYCR7bmFtZX08YnIvPuS+m+W6lOWVhuWAvDogJHtzdXBwbGllclZhbH08YnIvPua1i+ivleWAvDogJHt0ZXN0VmFsfTxici8+5beu5YC8OiAke01hdGguYWJzKHN1cHBsaWVyVmFsIC0gdGVzdFZhbCkudG9GaXhlZCgyKX1gOw0KDQogICAgICAgICAgICAvLyDlpoLmnpzmmK/lj4LmlbDnvJblj7flr7nmr5TkuJTmnInlj4LmlbDmmI7nu4bvvIzmmL7npLrlj4LmlbDmmI7nu4bkv6Hmga8NCiAgICAgICAgICAgIGlmIChzZWxmLnF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlID09PSAncGFyYW1OdW1iZXInICYmIGN1cnJlbnREYXRhICYmIGN1cnJlbnREYXRhLnBhcmFtRGV0YWlscykgew0KICAgICAgICAgICAgICByZXN1bHQgKz0gJzxici8+PGJyLz48c3Ryb25nPuWPguaVsOaYjue7huS/oeaBr++8mjwvc3Ryb25nPjxici8+JzsNCiAgICAgICAgICAgICAgaWYgKGN1cnJlbnREYXRhLm1hdGVyaWFsKSB7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9ICfmnZDmlpnvvJonICsgY3VycmVudERhdGEubWF0ZXJpYWwgKyAnPGJyLz4nOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YS5wcm9jZXNzVHlwZSkgew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAn5bel6Im677yaJyArIGN1cnJlbnREYXRhLnByb2Nlc3NUeXBlICsgJzxici8+JzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXN1bHQgKz0gJ+WPguaVsOWIl+ihqO+8mjxici8+JzsNCg0KICAgICAgICAgICAgICBjdXJyZW50RGF0YS5wYXJhbURldGFpbHMuZm9yRWFjaChwYXJhbSA9PiB7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9ICfigKIgJyArIHBhcmFtLnBhcmFtTmFtZSArICc6ICcgKyBwYXJhbS5wYXJhbVZhbHVlOw0KICAgICAgICAgICAgICAgIGlmIChwYXJhbS51bml0KSB7DQogICAgICAgICAgICAgICAgICByZXN1bHQgKz0gJyAnICsgcGFyYW0udW5pdDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9ICc8YnIvPic7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6ICfkvpvlupTllYbmlbDmja4nLA0KICAgICAgICAgIHNjYWxlOiB0cnVlLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9ybWF0dGVyOiAne3ZhbHVlfScNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5rWL6K+V5pWw5o2uJywNCiAgICAgICAgICBzY2FsZTogdHJ1ZSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGZvcm1hdHRlcjogJ3t2YWx1ZX0nDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+aVsOaNruWvueavlCcsDQogICAgICAgICAgdHlwZTogJ3NjYXR0ZXInLA0KICAgICAgICAgIGRhdGE6IHNjYXR0ZXJEYXRhLA0KICAgICAgICAgIHN5bWJvbFNpemU6IDgsDQogICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogJyM1NDcwYzYnDQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgbmFtZTogJ+eQhuaDs+e6vycsDQogICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgIGRhdGE6IFtbMCwgMF0sIFtNYXRoLm1heCguLi5zY2F0dGVyRGF0YS5tYXAoZCA9PiBkWzBdKSksIE1hdGgubWF4KC4uLnNjYXR0ZXJEYXRhLm1hcChkID0+IGRbMF0pKV1dLA0KICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgY29sb3I6ICcjZmY2YjZiJywNCiAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnDQogICAgICAgICAgfSwNCiAgICAgICAgICBzeW1ib2w6ICdub25lJw0KICAgICAgICB9XQ0KICAgICAgfTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlumbt+i+vuWbvumFjee9riAqLw0KICAgIGdldFJhZGFyQ2hhcnRPcHRpb24oKSB7DQogICAgICAvLyDpm7fovr7lm77nlKjkuo7lpJrnu7Tluqblr7nmr5TvvIzln7rkuo5jaGFydERhdGHnlJ/miJDmjIfmoIcNCiAgICAgIGNvbnN0IGluZGljYXRvcnMgPSBbDQogICAgICAgIHsgbmFtZTogJ+S+m+W6lOWVhuW5s+Wdh+WAvCcsIG1heDogMTAwIH0sDQogICAgICAgIHsgbmFtZTogJ+a1i+ivleW5s+Wdh+WAvCcsIG1heDogMTAwIH0sDQogICAgICAgIHsgbmFtZTogJ+aVsOaNrumHjycsIG1heDogNTAgfSwNCiAgICAgICAgeyBuYW1lOiAn5YeG56Gu546HJywgbWF4OiAxMDAgfSwNCiAgICAgICAgeyBuYW1lOiAn56iz5a6a5oCnJywgbWF4OiAxMCB9DQogICAgICBdOw0KDQogICAgICBjb25zdCByYWRhckRhdGEgPSB0aGlzLmNoYXJ0RGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgIGNvbnN0IHN1cHBsaWVyQXZnID0gcGFyc2VGbG9hdChpdGVtLnN1cHBsaWVyQXZnKSB8fCAwOw0KICAgICAgICBjb25zdCB0ZXN0QXZnID0gcGFyc2VGbG9hdChpdGVtLnRlc3RBdmcpIHx8IDA7DQogICAgICAgIGNvbnN0IGRhdGFDb3VudCA9IHBhcnNlSW50KGl0ZW0uZGF0YUNvdW50KSB8fCAwOw0KICAgICAgICBjb25zdCBhY2N1cmFjeSA9IHBhcnNlRmxvYXQoaXRlbS5hY2N1cmFjeSkgfHwgMDsNCiAgICAgICAgY29uc3Qgc3RhYmlsaXR5ID0gcGFyc2VGbG9hdChpdGVtLnN0YWJpbGl0eSkgfHwgMDsNCg0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgICB2YWx1ZTogWw0KICAgICAgICAgICAgTWF0aC5taW4oc3VwcGxpZXJBdmcsIDEwMCksDQogICAgICAgICAgICBNYXRoLm1pbih0ZXN0QXZnLCAxMDApLA0KICAgICAgICAgICAgTWF0aC5taW4oZGF0YUNvdW50LCA1MCksDQogICAgICAgICAgICBNYXRoLm1pbihhY2N1cmFjeSwgMTAwKSwNCiAgICAgICAgICAgIE1hdGgubWluKHN0YWJpbGl0eSwgMTApDQogICAgICAgICAgXQ0KICAgICAgICB9Ow0KICAgICAgfSk7DQoNCiAgICAgIGNvbnN0IHNlbGYgPSB0aGlzOw0KDQogICAgICByZXR1cm4gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6IHRoaXMuY2hhcnRUaXRsZSwNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBjb25zdCBkYXRhSW5kZXggPSBwYXJhbXMuZGF0YUluZGV4Ow0KICAgICAgICAgICAgY29uc3QgY3VycmVudERhdGEgPSBzZWxmLmNoYXJ0RGF0YVtkYXRhSW5kZXhdOw0KDQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zLm5hbWUgKyAnPGJyLz4nOw0KDQogICAgICAgICAgICAvLyDmmL7npLrpm7fovr7lm77mlbDmja4NCiAgICAgICAgICAgIGNvbnN0IGluZGljYXRvcnMgPSBbJ+S+m+W6lOWVhuW5s+Wdh+WAvCcsICfmtYvor5XlubPlnYflgLwnLCAn5pWw5o2u6YePJywgJ+WHhuehrueOhycsICfnqLPlrprmgKcnXTsNCiAgICAgICAgICAgIHBhcmFtcy52YWx1ZS5mb3JFYWNoKCh2YWx1ZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9IGluZGljYXRvcnNbaW5kZXhdICsgJzogJyArIHZhbHVlICsgJzxici8+JzsNCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAvLyDlpoLmnpzmmK/lj4LmlbDnvJblj7flr7nmr5TkuJTmnInlj4LmlbDmmI7nu4bvvIzmmL7npLrlj4LmlbDmmI7nu4bkv6Hmga8NCiAgICAgICAgICAgIGlmIChzZWxmLnF1ZXJ5UGFyYW1zLmNvbXBhcmVUeXBlID09PSAncGFyYW1OdW1iZXInICYmIGN1cnJlbnREYXRhICYmIGN1cnJlbnREYXRhLnBhcmFtRGV0YWlscykgew0KICAgICAgICAgICAgICByZXN1bHQgKz0gJzxici8+PHN0cm9uZz7lj4LmlbDmmI7nu4bkv6Hmga/vvJo8L3N0cm9uZz48YnIvPic7DQogICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YS5tYXRlcmlhbCkgew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAn5p2Q5paZ77yaJyArIGN1cnJlbnREYXRhLm1hdGVyaWFsICsgJzxici8+JzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEucHJvY2Vzc1R5cGUpIHsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJ+W3peiJuu+8micgKyBjdXJyZW50RGF0YS5wcm9jZXNzVHlwZSArICc8YnIvPic7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgcmVzdWx0ICs9ICflj4LmlbDliJfooajvvJo8YnIvPic7DQoNCiAgICAgICAgICAgICAgY3VycmVudERhdGEucGFyYW1EZXRhaWxzLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAn4oCiICcgKyBwYXJhbS5wYXJhbU5hbWUgKyAnOiAnICsgcGFyYW0ucGFyYW1WYWx1ZTsNCiAgICAgICAgICAgICAgICBpZiAocGFyYW0udW5pdCkgew0KICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9ICcgJyArIHBhcmFtLnVuaXQ7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnPGJyLz4nOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIHRvcDogJzEwJScsDQogICAgICAgICAgZGF0YTogcmFkYXJEYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkNCiAgICAgICAgfSwNCiAgICAgICAgcmFkYXI6IHsNCiAgICAgICAgICBpbmRpY2F0b3I6IGluZGljYXRvcnMsDQogICAgICAgICAgcmFkaXVzOiAnNjAlJw0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgdHlwZTogJ3JhZGFyJywNCiAgICAgICAgICBkYXRhOiByYWRhckRhdGENCiAgICAgICAgfV0NCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bng63lipvlm77phY3nva4gKi8NCiAgICBnZXRIZWF0bWFwQ2hhcnRPcHRpb24oKSB7DQogICAgICAvLyDng63lipvlm77nlKjkuo7lsZXnpLrmlbDmja7lr4bluqblkozliIbluIMNCiAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFsuLi5uZXcgU2V0KHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkpXTsNCiAgICAgIGNvbnN0IHlBeGlzRGF0YSA9IFsn5L6b5bqU5ZWG5pWw5o2uJywgJ+a1i+ivleaVsOaNricsICflgY/lt64nXTsNCg0KICAgICAgY29uc3QgaGVhdG1hcERhdGEgPSBbXTsNCiAgICAgIHRoaXMuY2hhcnREYXRhLmZvckVhY2goKGl0ZW0sIHhJbmRleCkgPT4gew0KICAgICAgICBjb25zdCBzdXBwbGllckF2ZyA9IHBhcnNlRmxvYXQoaXRlbS5zdXBwbGllckF2ZykgfHwgMDsNCiAgICAgICAgY29uc3QgdGVzdEF2ZyA9IHBhcnNlRmxvYXQoaXRlbS50ZXN0QXZnKSB8fCAwOw0KICAgICAgICBjb25zdCBkZXZpYXRpb24gPSBNYXRoLmFicyhzdXBwbGllckF2ZyAtIHRlc3RBdmcpOw0KDQogICAgICAgIGhlYXRtYXBEYXRhLnB1c2goW3hJbmRleCwgMCwgc3VwcGxpZXJBdmddKTsgLy8g5L6b5bqU5ZWG5pWw5o2uDQogICAgICAgIGhlYXRtYXBEYXRhLnB1c2goW3hJbmRleCwgMSwgdGVzdEF2Z10pOyAgICAgLy8g5rWL6K+V5pWw5o2uDQogICAgICAgIGhlYXRtYXBEYXRhLnB1c2goW3hJbmRleCwgMiwgZGV2aWF0aW9uXSk7ICAgLy8g5YGP5beuDQogICAgICB9KTsNCg0KICAgICAgY29uc3QgbWF4VmFsdWUgPSBNYXRoLm1heCguLi5oZWF0bWFwRGF0YS5tYXAoZCA9PiBkWzJdKSk7DQoNCiAgICAgIGNvbnN0IHNlbGYgPSB0aGlzOw0KDQogICAgICByZXR1cm4gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6IHRoaXMuY2hhcnRUaXRsZSwNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgcG9zaXRpb246ICd0b3AnLA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBjb25zdCBbeCwgeSwgdmFsdWVdID0gcGFyYW1zLmRhdGE7DQogICAgICAgICAgICBjb25zdCB4TGFiZWwgPSB4QXhpc0RhdGFbeF07DQogICAgICAgICAgICBjb25zdCB5TGFiZWwgPSB5QXhpc0RhdGFbeV07DQogICAgICAgICAgICBjb25zdCBjdXJyZW50RGF0YSA9IHNlbGYuY2hhcnREYXRhW3hdOw0KDQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gYCR7eExhYmVsfTxici8+JHt5TGFiZWx9OiAke3ZhbHVlLnRvRml4ZWQoMil9YDsNCg0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5Y+C5pWw57yW5Y+35a+55q+U5LiU5pyJ5Y+C5pWw5piO57uG77yM5pi+56S65Y+C5pWw5piO57uG5L+h5oGvDQogICAgICAgICAgICBpZiAoc2VsZi5xdWVyeVBhcmFtcy5jb21wYXJlVHlwZSA9PT0gJ3BhcmFtTnVtYmVyJyAmJiBjdXJyZW50RGF0YSAmJiBjdXJyZW50RGF0YS5wYXJhbURldGFpbHMpIHsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9ICc8YnIvPjxici8+PHN0cm9uZz7lj4LmlbDmmI7nu4bkv6Hmga/vvJo8L3N0cm9uZz48YnIvPic7DQogICAgICAgICAgICAgIGlmIChjdXJyZW50RGF0YS5tYXRlcmlhbCkgew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAn5p2Q5paZ77yaJyArIGN1cnJlbnREYXRhLm1hdGVyaWFsICsgJzxici8+JzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoY3VycmVudERhdGEucHJvY2Vzc1R5cGUpIHsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJ+W3peiJuu+8micgKyBjdXJyZW50RGF0YS5wcm9jZXNzVHlwZSArICc8YnIvPic7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgcmVzdWx0ICs9ICflj4LmlbDliJfooajvvJo8YnIvPic7DQoNCiAgICAgICAgICAgICAgY3VycmVudERhdGEucGFyYW1EZXRhaWxzLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAn4oCiICcgKyBwYXJhbS5wYXJhbU5hbWUgKyAnOiAnICsgcGFyYW0ucGFyYW1WYWx1ZTsNCiAgICAgICAgICAgICAgICBpZiAocGFyYW0udW5pdCkgew0KICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9ICcgJyArIHBhcmFtLnVuaXQ7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSAnPGJyLz4nOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBoZWlnaHQ6ICc1MCUnLA0KICAgICAgICAgIHRvcDogJzE1JScNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IHhBeGlzRGF0YSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIHJvdGF0ZTogNDUsDQogICAgICAgICAgICBpbnRlcnZhbDogMA0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IHlBeGlzRGF0YQ0KICAgICAgICB9LA0KICAgICAgICB2aXN1YWxNYXA6IHsNCiAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgbWF4OiBtYXhWYWx1ZSB8fCAxMDAsDQogICAgICAgICAgY2FsY3VsYWJsZTogdHJ1ZSwNCiAgICAgICAgICBvcmllbnQ6ICdob3Jpem9udGFsJywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICBib3R0b206ICc1JScsDQogICAgICAgICAgaW5SYW5nZTogew0KICAgICAgICAgICAgY29sb3I6IFsnIzUwYTNiYScsICcjZWFjNzM2JywgJyNkOTRlNWQnXQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgIHR5cGU6ICdoZWF0bWFwJywNCiAgICAgICAgICBkYXRhOiBoZWF0bWFwRGF0YSwNCiAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICAgIHJldHVybiBwYXJhbXMuZGF0YVsyXS50b0ZpeGVkKDEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwNCiAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuNSknDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9XQ0KICAgICAgfTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruafpeivoiAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBjb21wYXJlVHlwZTogJ21hdGVyaWFsJywNCiAgICAgICAgcGFyYW1OdW1iZXJzOiBbXSwNCiAgICAgICAgbWF0ZXJpYWxOYW1lczogW10sDQogICAgICAgIHN1cHBsaWVyTmFtZXM6IFtdLA0KICAgICAgICBwcm9jZXNzVHlwZXM6IFtdLA0KICAgICAgICBkYXRlUmFuZ2U6IG51bGwsDQogICAgICAgIGNvbXBhcmVQYXJhbTogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMudXBkYXRlQ2hhcnRUaXRsZSgpOw0KICAgIH0sDQoNCiAgICAvKiog5Yi35paw5Zu+6KGoICovDQogICAgcmVmcmVzaENoYXJ0KCkgew0KICAgICAgaWYgKHRoaXMuY2hhcnREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5yZW5kZXJDaGFydCgpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5a+85Ye65Zu+6KGoICovDQogICAgZXhwb3J0Q2hhcnQoKSB7DQogICAgICBpZiAoIXRoaXMuY2hhcnQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjnlJ/miJDlm77ooagnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB1cmwgPSB0aGlzLmNoYXJ0LmdldERhdGFVUkwoew0KICAgICAgICB0eXBlOiAncG5nJywNCiAgICAgICAgcGl4ZWxSYXRpbzogMiwNCiAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZicNCiAgICAgIH0pOw0KDQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IGAke3RoaXMuY2hhcnRUaXRsZX1fJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ucG5nYDsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICB9LA0KDQogICAgLyoqIOWIh+aNouaVsOaNruihqOaYvuekuiAqLw0KICAgIHRvZ2dsZURhdGFUYWJsZSgpIHsNCiAgICAgIHRoaXMuc2hvd0RhdGFUYWJsZSA9ICF0aGlzLnNob3dEYXRhVGFibGU7DQogICAgICBpZiAoIXRoaXMuc2hvd0RhdGFUYWJsZSAmJiB0aGlzLmNoYXJ0KSB7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmNoYXJ0LnJlc2l6ZSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWIh+aNoumhueebruivpuaDheaYvuekuiAqLw0KICAgIHRvZ2dsZVByb2plY3REZXRhaWxzKCkgew0KICAgICAgdGhpcy5zaG93UHJvamVjdERldGFpbHMgPSAhdGhpcy5zaG93UHJvamVjdERldGFpbHM7DQogICAgfSwNCg0KICAgIC8qKiDliIfmjaLlhajlsY/mmL7npLogKi8NCiAgICB0b2dnbGVGdWxsc2NyZWVuKCkgew0KICAgICAgaWYgKHRoaXMuaXNGdWxsc2NyZWVuKSB7DQogICAgICAgIHRoaXMuY2hhcnRIZWlnaHQgPSA0MDA7DQogICAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gZmFsc2U7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmNoYXJ0SGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0IC0gMjAwOw0KICAgICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IHRydWU7DQogICAgICB9DQoNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuY2hhcnQpIHsNCiAgICAgICAgICB0aGlzLmNoYXJ0LnJlc2l6ZSgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOaYvuekuuWbvuihqOW4ruWKqSAqLw0KICAgIHNob3dDaGFydEhlbHAoKSB7DQogICAgICB0aGlzLmhlbHBEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluaVsOWtl+aYvuekuiAqLw0KICAgIGZvcm1hdE51bWJlcih2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQgfHwgaXNOYU4odmFsdWUpKSB7DQogICAgICAgIHJldHVybiAnTi9BJzsNCiAgICAgIH0NCiAgICAgIGNvbnN0IG51bSA9IHBhcnNlRmxvYXQodmFsdWUpOw0KICAgICAgaWYgKG51bSA9PT0gMCkgcmV0dXJuICcwJzsNCiAgICAgIGlmIChNYXRoLmFicyhudW0pID49IDEwMDAwMDApIHsNCiAgICAgICAgcmV0dXJuIChudW0gLyAxMDAwMDAwKS50b0ZpeGVkKDIpICsgJ00nOw0KICAgICAgfSBlbHNlIGlmIChNYXRoLmFicyhudW0pID49IDEwMDApIHsNCiAgICAgICAgcmV0dXJuIChudW0gLyAxMDAwKS50b0ZpeGVkKDIpICsgJ0snOw0KICAgICAgfSBlbHNlIGlmIChNYXRoLmFicyhudW0pIDwgMSkgew0KICAgICAgICByZXR1cm4gbnVtLnRvRml4ZWQoNCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gbnVtLnRvRml4ZWQoMik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJblj4LmlbDlgLzmmL7npLrvvIjmlK/mjIHlrZfnrKbkuLLnsbvlnovvvIzmmL7npLrlrozmlbTmlbDlgLzvvIkgKi8NCiAgICBmb3JtYXRQYXJhbVZhbHVlKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICctJzsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy57G75Z6L77yM55u05o6l6L+U5Zue5a6M5pW05a2X56ym5LiyDQogICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgew0KICAgICAgICAvLyDlsJ3or5Xop6PmnpDkuLrmlbDlrZcNCiAgICAgICAgY29uc3QgbnVtID0gcGFyc2VGbG9hdCh2YWx1ZSk7DQogICAgICAgIGlmICghaXNOYU4obnVtKSkgew0KICAgICAgICAgIC8vIOWmguaenOaYr+aVsOWtl+Wtl+espuS4su+8jOS/neeVmTbkvY3lsI/mlbDlubbljrvpmaTlsL7pmo/pm7YNCiAgICAgICAgICByZXR1cm4gbnVtLnRvRml4ZWQoNikucmVwbGFjZSgvXC4/MCskLywgJycpOw0KICAgICAgICB9DQogICAgICAgIC8vIOWmguaenOS4jeaYr+aVsOWtl+Wtl+espuS4su+8jOebtOaOpei/lOWbng0KICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYr+aVsOWtl+exu+Wei++8jOS/neeVmTbkvY3lsI/mlbDlubbljrvpmaTlsL7pmo/pm7YNCiAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSB7DQogICAgICAgIHJldHVybiB2YWx1ZS50b0ZpeGVkKDYpLnJlcGxhY2UoL1wuPzArJC8sICcnKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFN0cmluZyh2YWx1ZSk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blj4LmlbDmoIfnrb7nsbvlnosgKi8NCiAgICBnZXRQYXJhbVRhZ1R5cGUocGFyYW0pIHsNCiAgICAgIGlmICghcGFyYW0ucGFyYW1WYWx1ZSkgcmV0dXJuICcnOw0KICAgICAgY29uc3QgdmFsdWUgPSBwYXJzZUZsb2F0KHBhcmFtLnBhcmFtVmFsdWUpOw0KICAgICAgaWYgKGlzTmFOKHZhbHVlKSkgcmV0dXJuICcnOw0KDQogICAgICAvLyDmoLnmja7lj4LmlbDlgLzojIPlm7Torr7nva7kuI3lkIzpopzoibINCiAgICAgIGlmICh2YWx1ZSA+IDEwMCkgcmV0dXJuICdkYW5nZXInOw0KICAgICAgaWYgKHZhbHVlID4gNTApIHJldHVybiAnd2FybmluZyc7DQogICAgICBpZiAodmFsdWUgPiAxMCkgcmV0dXJuICdzdWNjZXNzJzsNCiAgICAgIHJldHVybiAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8qKiDmmL7npLrlj4LmlbDor6bmg4UgKi8NCiAgICBzaG93UGFyYW1EZXRhaWwocGFyYW0pIHsNCiAgICAgIHRoaXMuY3VycmVudFBhcmFtRGV0YWlsID0gcGFyYW07DQogICAgICB0aGlzLnBhcmFtRGV0YWlsRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDpgInkuK3lj4LmlbDor6bmg4UgKi8NCiAgICB1cGRhdGVTZWxlY3RlZFBhcmFtRGV0YWlscygpIHsNCiAgICAgIC8vIOagueaNruW9k+WJjemAieaLqeeahOWvueavlOexu+Wei+WSjOmAiemhue+8jOabtOaWsOWPguaVsOivpuaDheS/oeaBrw0KICAgICAgY29uc3QgeyBjb21wYXJlVHlwZSB9ID0gdGhpcy5xdWVyeVBhcmFtczsNCiAgICAgIHRoaXMuc2VsZWN0ZWRQYXJhbURldGFpbHMgPSBbXTsNCg0KICAgICAgaWYgKGNvbXBhcmVUeXBlID09PSAnbWF0ZXJpYWwnICYmIHRoaXMucXVlcnlQYXJhbXMubWF0ZXJpYWxOYW1lcy5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubWF0ZXJpYWxOYW1lcy5mb3JFYWNoKG1hdGVyaWFsSWQgPT4gew0KICAgICAgICAgIGNvbnN0IG1hdGVyaWFsID0gdGhpcy5tYXRlcmlhbE9wdGlvbnMuZmluZChtID0+IG0ubWF0ZXJpYWxJZCA9PT0gbWF0ZXJpYWxJZCk7DQogICAgICAgICAgaWYgKG1hdGVyaWFsKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWxzLnB1c2goew0KICAgICAgICAgICAgICBuYW1lOiBtYXRlcmlhbC5tYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWwubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICBzdXBwbGllck5hbWU6IG1hdGVyaWFsLnN1cHBsaWVyTmFtZSwNCiAgICAgICAgICAgICAgcHJvY2Vzc1R5cGU6IG1hdGVyaWFsLnByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgICB0ZXN0Q291bnQ6IG1hdGVyaWFsLnRlc3RDb3VudCB8fCAwLA0KICAgICAgICAgICAgICBzdGF0aXN0aWNzOiBtYXRlcmlhbC5zdGF0aXN0aWNzDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIGlmIChjb21wYXJlVHlwZSA9PT0gJ3N1cHBsaWVyJyAmJiB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBsaWVyTmFtZXMubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBsaWVyTmFtZXMuZm9yRWFjaChzdXBwbGllciA9PiB7DQogICAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlscy5wdXNoKHsNCiAgICAgICAgICAgIG5hbWU6IHN1cHBsaWVyLA0KICAgICAgICAgICAgc3VwcGxpZXJOYW1lOiBzdXBwbGllciwNCiAgICAgICAgICAgIHRlc3RDb3VudDogMCAvLyDov5nph4zlj6/ku6Xku45BUEnojrflj5blrp7pmYXmlbDmja4NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2UgaWYgKGNvbXBhcmVUeXBlID09PSAncGFyYW1OdW1iZXInICYmIHRoaXMucXVlcnlQYXJhbXMucGFyYW1OdW1iZXJzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbU51bWJlcnMuZm9yRWFjaChwYXJhbUlkID0+IHsNCiAgICAgICAgICBjb25zdCBwYXJhbSA9IHRoaXMucGFyYW1OdW1iZXJPcHRpb25zLmZpbmQocCA9PiBwLmdyb3VwSWQgPT09IHBhcmFtSWQpOw0KICAgICAgICAgIGlmIChwYXJhbSkgew0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlscy5wdXNoKHsNCiAgICAgICAgICAgICAgbmFtZTogcGFyYW0ucGFyYW1OdW1iZXIsDQogICAgICAgICAgICAgIHBhcmFtTnVtYmVyOiBwYXJhbS5wYXJhbU51bWJlciwNCiAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBwYXJhbS5tYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgIHByb2Nlc3NUeXBlOiBwYXJhbS5wcm9jZXNzVHlwZSwNCiAgICAgICAgICAgICAgbWFpblBhcmFtczogcGFyYW0ucGFyYW1JdGVtcyB8fCBbXSwNCiAgICAgICAgICAgICAgdGVzdENvdW50OiBwYXJhbS50ZXN0Q291bnQgfHwgMA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAojBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/trend", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-data-line\"></i>\r\n        <span>趋势对比分析</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <p>📈 多维度数据趋势对比分析，支持材料性能、供应商质量等多种对比维度</p>\r\n          <el-button type=\"text\" @click=\"showUsageGuide = true\" style=\"color: #409EFF;\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <span>使用指南</span>\r\n          </el-button>\r\n        </div>\r\n        <el-alert\r\n          title=\"使用提示：选择对比维度 → 配置筛选条件 → 生成图表分析\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用指南卡片 -->\r\n    <el-card class=\"usage-guide-card enhanced-card\" style=\"margin-bottom: 20px;\" v-if=\"showUsageGuide\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"header-title\">📊 趋势对比分析使用指南</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"text\" @click=\"showUsageGuide = false\" class=\"close-guide-btn\">\r\n            <i class=\"el-icon-close\"></i>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>🎯 对比维度说明</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>材料对比：</strong>按材料名称分组，计算每种材料的供应商数据和测试数据平均值</li>\r\n              <li><strong>供应商对比：</strong>按供应商分组，计算准确率（供应商数据与测试数据的偏差）</li>\r\n              <li><strong>参数编号对比：</strong>按参数组分组，展示工艺参数明细和测试结果统计</li>\r\n              <li><strong>工艺类型对比：</strong>按工艺类型分组，计算稳定性（测试数据标准差）</li>\r\n              <li><strong>时间趋势：</strong>按日期分组，展示测试数据随时间的变化趋势</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>📊 数据来源详解</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>供应商平均值：</strong>选中项目下所有测试记录的供应商数据平均值</li>\r\n              <li><strong>测试平均值：</strong>选中项目下所有测试记录的实际测试值平均值</li>\r\n              <li><strong>准确率：</strong>100% - |供应商平均值 - 测试平均值| / 供应商平均值 × 100%</li>\r\n              <li><strong>稳定性：</strong>基于测试数据标准差计算，数值越小越稳定</li>\r\n              <li><strong>数据量：</strong>参与计算的测试记录总数</li>\r\n              <li><strong>参数明细：</strong>来自工艺参数配置中的具体参数项</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row style=\"margin-top: 15px;\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"guide-item\">\r\n            <h4>💡 使用建议</h4>\r\n            <div class=\"usage-suggestion\">\r\n              <p>1. 选择对比维度 → 2. 配置筛选条件（支持多选） → 3. 点击\"生成图表\"分析 → 4. 切换图表类型查看不同视角 → 5. 查看详细数据表获取具体数值</p>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <el-card class=\"trend-analysis-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">数据趋势对比分析</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"info\" icon=\"el-icon-question\" size=\"small\" @click=\"showUsageGuide = !showUsageGuide\" class=\"guide-btn\">\r\n            <span>使用指南</span>\r\n          </el-button>\r\n          <el-button type=\"primary\" icon=\"el-icon-refresh\" size=\"small\" @click=\"refreshChart\" class=\"refresh-btn\">\r\n            <span>刷新数据</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"exportChart\" class=\"export-btn\">\r\n            <span>导出图表</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 筛选条件 -->\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"对比维度\" prop=\"compareType\">\r\n          <el-select v-model=\"queryParams.compareType\" placeholder=\"请选择对比维度\" style=\"width: 250px;\" clearable @change=\"handleCompareTypeChange\">\r\n            <el-option label=\"📊 材料性能对比\" value=\"material\">\r\n              <span style=\"float: left\">📊 材料性能对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同材料性能</span>\r\n            </el-option>\r\n            <el-option label=\"🏭 供应商数据对比\" value=\"supplier\">\r\n              <span style=\"float: left\">🏭 供应商数据对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较供应商质量</span>\r\n            </el-option>\r\n            <el-option label=\"🔢 参数编号对比\" value=\"paramNumber\">\r\n              <span style=\"float: left\">🔢 参数编号对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同参数值</span>\r\n            </el-option>\r\n            <el-option label=\"⚙️ 工艺类型对比\" value=\"processType\">\r\n              <span style=\"float: left\">⚙️ 工艺类型对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较工艺效果</span>\r\n            </el-option>\r\n            <el-option label=\"📈 时间趋势分析\" value=\"timeTrend\">\r\n              <span style=\"float: left\">📈 时间趋势分析</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">查看时间变化</span>\r\n            </el-option>\r\n            <el-option label=\"⚖️ 供应商vs测试值\" value=\"supplierVsTest\">\r\n              <span style=\"float: left\">⚖️ 供应商vs测试值</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">对比数据差异</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 材料性能对比 -->\r\n        <el-form-item label=\"选择材料\" prop=\"materialNames\" v-if=\"queryParams.compareType === 'material'\">\r\n          <el-select\r\n            v-model=\"queryParams.materialNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的材料\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"material in materialOptions\"\r\n              :key=\"material.materialId\"\r\n              :label=\"material.materialName + ' (' + material.supplierName + ')'\"\r\n              :value=\"material.materialId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 供应商数据对比 -->\r\n        <el-form-item label=\"选择供应商\" prop=\"supplierNames\" v-if=\"queryParams.compareType === 'supplier'\">\r\n          <el-select\r\n            v-model=\"queryParams.supplierNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的供应商\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"supplier in supplierOptions\"\r\n              :key=\"supplier\"\r\n              :label=\"supplier\"\r\n              :value=\"supplier\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 参数编号对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"paramNumbers\" v-if=\"queryParams.compareType === 'paramNumber'\">\r\n          <el-select\r\n            v-model=\"queryParams.paramNumbers\"\r\n            multiple\r\n            placeholder=\"请选择要对比的参数编号\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 工艺类型对比 -->\r\n        <el-form-item label=\"选择工艺\" prop=\"processTypes\" v-if=\"queryParams.compareType === 'processType'\">\r\n          <el-select\r\n            v-model=\"queryParams.processTypes\"\r\n            multiple\r\n            placeholder=\"请选择要对比的工艺类型\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"type in processTypeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 时间趋势分析 -->\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\" v-if=\"queryParams.compareType === 'timeTrend'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            style=\"width: 300px;\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 供应商vs测试值对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"compareParam\" v-if=\"queryParams.compareType === 'supplierVsTest'\">\r\n          <el-select\r\n            v-model=\"queryParams.compareParam\"\r\n            placeholder=\"请选择要对比的参数\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\" :loading=\"loading\">生成对比图表</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 图表类型选择 -->\r\n      <el-row style=\"margin-bottom: 20px;\">\r\n        <el-col :span=\"24\">\r\n          <el-radio-group v-model=\"chartType\" @change=\"handleChartTypeChange\">\r\n            <el-radio-button label=\"line\">折线图</el-radio-button>\r\n            <el-radio-button label=\"bar\">柱状图</el-radio-button>\r\n            <el-radio-button label=\"scatter\">散点图</el-radio-button>\r\n            <el-radio-button label=\"radar\">雷达图</el-radio-button>\r\n            <el-radio-button label=\"heatmap\">热力图</el-radio-button>\r\n          </el-radio-group>\r\n          <el-button-group style=\"margin-left: 20px;\">\r\n            <el-button size=\"small\" @click=\"toggleDataTable\">{{ showDataTable ? '隐藏' : '显示' }}数据表</el-button>\r\n            <el-button size=\"small\" @click=\"toggleProjectDetails\" :disabled=\"selectedParamDetails.length === 0\">\r\n              {{ showProjectDetails ? '隐藏' : '显示' }}项目详情\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"toggleFullscreen\">全屏显示</el-button>\r\n          </el-button-group>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 参数详情信息卡片 -->\r\n    <el-card v-if=\"selectedParamDetails.length > 0 && showProjectDetails\" class=\"box-card param-details-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">📋 选中项目详情信息</span>\r\n        <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 10px;\">{{ selectedParamDetails.length }}项</el-tag>\r\n        <el-button type=\"text\" @click=\"showProjectDetails = false\" style=\"float: right; color: #909399;\">\r\n          <i class=\"el-icon-close\"></i>\r\n        </el-button>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\" v-for=\"(detail, index) in selectedParamDetails\" :key=\"index\">\r\n          <el-card class=\"param-detail-card\" shadow=\"hover\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span style=\"font-weight: bold; color: #409EFF;\">\r\n                <i class=\"el-icon-data-line\"></i>\r\n                {{ detail.paramNumber || detail.name }}\r\n              </span>\r\n              <el-tag size=\"mini\" type=\"success\" style=\"float: right;\" v-if=\"detail.testCount\">\r\n                {{ detail.testCount }}次测试\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 基本信息 -->\r\n            <div class=\"detail-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-info\"></i>\r\n                基本信息\r\n              </div>\r\n              <el-descriptions :column=\"2\" border size=\"small\">\r\n                <el-descriptions-item label=\"材料名称\" v-if=\"detail.materialName\">\r\n                  <el-tag type=\"primary\" size=\"mini\">{{ detail.materialName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"供应商\" v-if=\"detail.supplierName\">\r\n                  <el-tag type=\"success\" size=\"mini\">{{ detail.supplierName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"材料型号\" v-if=\"detail.materialModel\">\r\n                  <span>{{ detail.materialModel }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"工艺类型\" v-if=\"detail.processType\">\r\n                  <el-tag type=\"warning\" size=\"mini\">{{ detail.processType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"性能类型\" v-if=\"detail.performanceType\">\r\n                  <el-tag type=\"info\" size=\"mini\">{{ detail.performanceType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"参数编号\" v-if=\"detail.paramNumber\">\r\n                  <span>{{ detail.paramNumber }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"数据量\" v-if=\"detail.dataCount !== undefined\">\r\n                  <el-tag type=\"danger\" size=\"mini\">{{ detail.dataCount }}条</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"创建时间\" v-if=\"detail.createTime\">\r\n                  <span>{{ detail.createTime }}</span>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n\r\n            <!-- 统计信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.statistics\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                统计信息\r\n              </div>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.avgValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">平均值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.avgValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.maxValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最大值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.maxValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.minValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最小值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.minValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.stdDev !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">标准差</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.stdDev) }}</div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 参数明细 -->\r\n            <div class=\"detail-section\" v-if=\"detail.mainParams && detail.mainParams.length > 0\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-menu\"></i>\r\n                参数明细\r\n                <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 5px;\">{{ detail.mainParams.length }}个</el-tag>\r\n              </div>\r\n              <div class=\"params-container\">\r\n                <el-tooltip\r\n                  v-for=\"param in detail.mainParams\"\r\n                  :key=\"param.paramName\"\r\n                  :content=\"`${param.paramName}: ${param.paramValue || 'N/A'} ${param.unit || ''}`\"\r\n                  placement=\"top\"\r\n                >\r\n                  <el-tag\r\n                    size=\"mini\"\r\n                    :type=\"getParamTagType(param)\"\r\n                    style=\"margin-right: 5px; margin-bottom: 3px; cursor: pointer;\"\r\n                    @click=\"showParamDetail(param)\"\r\n                  >\r\n                    {{ param.paramName }}\r\n                    <span v-if=\"param.paramValue\" style=\"margin-left: 3px; opacity: 0.8;\">\r\n                      ({{ formatNumber(param.paramValue) }})\r\n                    </span>\r\n                  </el-tag>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 测试方案信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.testPlanInfo\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-document\"></i>\r\n                测试方案\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <label>方案编号：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.planCode }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" v-if=\"detail.testPlanInfo.testEquipment\">\r\n                <label>测试设备：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.testEquipment }}</span>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 图表区域 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">{{ chartTitle }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-tooltip content=\"图表说明\" placement=\"top\">\r\n            <el-button type=\"text\" icon=\"el-icon-question\" @click=\"showChartHelp\" />\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"chartLoading\" element-loading-text=\"正在生成图表...\">\r\n        <div\r\n          ref=\"chart\"\r\n          :style=\"{ height: chartHeight + 'px', width: '100%' }\"\r\n          v-show=\"!showDataTable\"\r\n        ></div>\r\n\r\n        <!-- 数据表格 -->\r\n        <el-table\r\n          v-show=\"showDataTable\"\r\n          :data=\"chartData\"\r\n          style=\"width: 100%\"\r\n          :max-height=\"chartHeight\"\r\n          class=\"trend-data-table\"\r\n        >\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column\r\n            v-for=\"column in tableColumns\"\r\n            :key=\"column.prop\"\r\n            :prop=\"column.prop\"\r\n            :label=\"column.label\"\r\n            :width=\"column.width\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"column.prop === 'paramDetails'\">\r\n                <div v-if=\"scope.row.paramDetails && scope.row.paramDetails.length > 0\" class=\"param-details-container\">\r\n                  <div\r\n                    v-for=\"(param, index) in scope.row.paramDetails\"\r\n                    :key=\"index\"\r\n                    class=\"param-detail-item\"\r\n                  >\r\n                    <span class=\"param-name\">{{ param.paramName }}:</span>\r\n                    <span class=\"param-value\">{{ formatParamValue(param.paramValue) }}</span>\r\n                    <span class=\"param-unit\" v-if=\"param.unit\">{{ param.unit }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else class=\"empty-data\">暂无参数</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'paramNumber'\">\r\n                <el-tag type=\"primary\" size=\"small\" v-if=\"scope.row.paramNumber\">\r\n                  {{ scope.row.paramNumber }}\r\n                </el-tag>\r\n                <span v-else class=\"empty-data\">-</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'materialName'\">\r\n                <div class=\"material-info\">\r\n                  <i class=\"el-icon-box\"></i>\r\n                  <span>{{ scope.row.materialName || '-' }}</span>\r\n                </div>\r\n              </div>\r\n              <div v-else>\r\n                {{ scope.row[column.prop] || '-' }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\" v-if=\"statisticsData.length > 0\">\r\n      <el-col :span=\"6\" v-for=\"(stat, index) in statisticsData\" :key=\"index\">\r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-content\">\r\n            <div class=\"statistics-title\">{{ stat.title }}</div>\r\n            <div class=\"statistics-value\">{{ stat.value }}</div>\r\n            <div class=\"statistics-desc\">{{ stat.description }}</div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表说明对话框 -->\r\n    <el-dialog title=\"📊 图表说明\" :visible.sync=\"helpDialogVisible\" width=\"700px\" append-to-body>\r\n      <div class=\"chart-help-content\">\r\n        <h4>🎯 图表类型说明：</h4>\r\n        <ul>\r\n          <li><strong>📈 折线图：</strong>适用于展示数据随时间或其他连续变量的变化趋势，清晰显示数据走向</li>\r\n          <li><strong>📊 柱状图：</strong>适用于比较不同类别之间的数值大小，直观对比差异</li>\r\n          <li><strong>🔵 散点图：</strong>适用于展示两个变量之间的相关关系，发现数据规律</li>\r\n          <li><strong>🕸️ 雷达图：</strong>适用于多维度数据的综合对比，全面评估性能</li>\r\n          <li><strong>🌡️ 热力图：</strong>适用于展示数据的分布密度和相关性，识别热点区域</li>\r\n        </ul>\r\n\r\n        <h4>🔍 对比维度说明：</h4>\r\n        <ul>\r\n          <li><strong>📊 材料性能对比：</strong>比较不同材料的性能表现，识别最优材料</li>\r\n          <li><strong>🏭 供应商数据对比：</strong>比较不同供应商材料的质量差异，评估供应商可靠性</li>\r\n          <li><strong>🔢 参数编号对比：</strong>比较不同参数编号下的测试值趋势，分析参数影响</li>\r\n          <li><strong>⚙️ 工艺类型对比：</strong>比较不同工艺类型的效果，优化工艺流程</li>\r\n          <li><strong>📈 时间趋势分析：</strong>展示测试数据随时间的变化规律，预测发展趋势</li>\r\n          <li><strong>⚖️ 供应商vs测试值：</strong>对比供应商提供数据与实际测试结果的差异</li>\r\n        </ul>\r\n\r\n        <h4>💡 使用技巧：</h4>\r\n        <ul>\r\n          <li>将鼠标悬停在图表数据点上可查看详细信息和参数明细</li>\r\n          <li>点击参数标签可查看该参数的详细信息</li>\r\n          <li>使用\"显示数据表\"功能可查看原始数据</li>\r\n          <li>选择合适的图表类型能更好地展示数据特征</li>\r\n          <li>多选对比项目可进行横向比较分析</li>\r\n        </ul>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数详情对话框 -->\r\n    <el-dialog\r\n      title=\"📋 参数详细信息\"\r\n      :visible.sync=\"paramDetailDialogVisible\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      v-if=\"currentParamDetail\"\r\n    >\r\n      <div class=\"param-detail-content\">\r\n        <el-descriptions :column=\"2\" border size=\"small\">\r\n          <el-descriptions-item label=\"参数名称\">\r\n            <el-tag type=\"primary\">{{ currentParamDetail.paramName }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数数值\">\r\n            <span style=\"font-weight: bold; color: #67C23A;\">\r\n              {{ formatNumber(currentParamDetail.paramValue) }}\r\n            </span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数单位\" v-if=\"currentParamDetail.unit\">\r\n            {{ currentParamDetail.unit }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"数据类型\">\r\n            {{ typeof currentParamDetail.paramValue === 'number' ? '数值型' : '文本型' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\" v-if=\"currentParamDetail.createTime\">\r\n            {{ currentParamDetail.createTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\" v-if=\"currentParamDetail.updateTime\">\r\n            {{ currentParamDetail.updateTime }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <div v-if=\"currentParamDetail.remark\" style=\"margin-top: 15px;\">\r\n          <h4 style=\"color: #409EFF; margin-bottom: 8px;\">📝 备注信息：</h4>\r\n          <p style=\"background: #f5f7fa; padding: 10px; border-radius: 4px; margin: 0;\">\r\n            {{ currentParamDetail.remark }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { listTestResult, getTestResultOptions } from \"@/api/material/testResult\";\r\nimport { listMaterial } from \"@/api/material/material\";\r\nimport { listProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\n\r\nexport default {\r\n  name: \"MaterialTrend\",\r\n  data() {\r\n    return {\r\n      // 加载状态\r\n      loading: false,\r\n      // 图表实例\r\n      chart: null,\r\n      // 图表类型\r\n      chartType: 'line',\r\n      // 图表高度\r\n      chartHeight: 400,\r\n      // 图表标题\r\n      chartTitle: '数据趋势对比分析',\r\n      // 图表加载状态\r\n      chartLoading: false,\r\n      // 是否显示数据表\r\n      showDataTable: false,\r\n      // 是否全屏\r\n      isFullscreen: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      },\r\n\r\n      // 选项数据\r\n      paramNumberOptions: [],\r\n      materialOptions: [],\r\n      supplierOptions: [],\r\n      processTypeOptions: [],\r\n\r\n      // 图表数据\r\n      chartData: [],\r\n      tableColumns: [],\r\n\r\n      // 参数详情\r\n      selectedParamDetails: [],\r\n\r\n      // 统计数据\r\n      statisticsData: [],\r\n\r\n      // 帮助对话框\r\n      helpDialogVisible: false,\r\n\r\n      // 使用指南显示状态\r\n      showUsageGuide: false,\r\n\r\n      // 项目详情显示状态\r\n      showProjectDetails: false,\r\n\r\n      // 参数详情对话框\r\n      paramDetailDialogVisible: false,\r\n      currentParamDetail: null\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n    this.loadOptions();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化图表 */\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart);\r\n\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', () => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 加载选项数据 */\r\n    async loadOptions() {\r\n      try {\r\n        // 加载参数编号选项\r\n        const paramResponse = await listProcessParamGroup({});\r\n        this.paramNumberOptions = paramResponse.rows || [];\r\n\r\n        // 加载材料选项\r\n        const materialResponse = await listMaterial({});\r\n        this.materialOptions = materialResponse.rows || [];\r\n\r\n        // 加载供应商选项\r\n        const supplierResponse = await getTestResultOptions({ type: 'supplierName' });\r\n        this.supplierOptions = supplierResponse.data || [];\r\n\r\n        // 加载工艺类型选项\r\n        const processResponse = await getTestResultOptions({ type: 'processType' });\r\n        this.processTypeOptions = processResponse.data || [];\r\n\r\n      } catch (error) {\r\n        console.error('加载选项数据失败：', error);\r\n        this.$modal.msgError('加载选项数据失败');\r\n      }\r\n    },\r\n\r\n    /** 对比类型改变 */\r\n    handleCompareTypeChange(value) {\r\n      // 重置相关参数\r\n      this.queryParams.paramNumbers = [];\r\n      this.queryParams.materialNames = [];\r\n      this.queryParams.supplierNames = [];\r\n      this.queryParams.processTypes = [];\r\n      this.queryParams.dateRange = null;\r\n      this.queryParams.compareParam = null;\r\n\r\n      // 清空选中的参数详情和图表数据\r\n      this.selectedParamDetails = [];\r\n      this.chartData = [];\r\n      this.statisticsData = [];\r\n\r\n      // 更新图表标题\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 更新图表标题 */\r\n    updateChartTitle() {\r\n      const typeMap = {\r\n        'paramNumber': '参数编号对比分析',\r\n        'material': '材料性能对比分析',\r\n        'supplier': '供应商质量对比分析',\r\n        'processType': '工艺类型效果对比分析',\r\n        'timeTrend': '时间趋势对比分析'\r\n      };\r\n      this.chartTitle = typeMap[this.queryParams.compareType] || '对比分析图';\r\n    },\r\n\r\n    /** 图表类型改变 */\r\n    handleChartTypeChange(type) {\r\n      this.chartType = type;\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 查询数据 */\r\n    async handleQuery() {\r\n      if (!this.validateQuery()) {\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      this.chartLoading = true;\r\n\r\n      try {\r\n        // 根据对比类型获取不同的数据\r\n        let chartData = [];\r\n        let paramDetails = [];\r\n\r\n        switch (this.queryParams.compareType) {\r\n          case 'material':\r\n            chartData = await this.getMaterialCompareData();\r\n            break;\r\n          case 'supplier':\r\n            chartData = await this.getSupplierCompareData();\r\n            break;\r\n          case 'paramNumber':\r\n            chartData = await this.getParamNumberCompareData();\r\n            break;\r\n          case 'processType':\r\n            chartData = await this.getProcessTypeCompareData();\r\n            break;\r\n          case 'timeTrend':\r\n            chartData = await this.getTimeTrendData();\r\n            break;\r\n          case 'supplierVsTest':\r\n            chartData = await this.getSupplierVsTestData();\r\n            break;\r\n        }\r\n\r\n        this.chartData = chartData;\r\n        this.updateTableColumns();\r\n        this.renderChart();\r\n\r\n        // 更新选中参数详情\r\n        this.updateSelectedParamDetails();\r\n\r\n      } catch (error) {\r\n        console.error('获取对比数据失败：', error);\r\n        this.$modal.msgError('获取对比数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n        this.chartLoading = false;\r\n      }\r\n    },\r\n\r\n    /** 验证查询条件 */\r\n    validateQuery() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length === 0) {\r\n        this.$message.warning('请选择至少一个参数编号');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length === 0) {\r\n        this.$message.warning('请选择至少一个材料');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplier' && this.queryParams.supplierNames.length === 0) {\r\n        this.$message.warning('请选择至少一个供应商');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'processType' && this.queryParams.processTypes.length === 0) {\r\n        this.$message.warning('请选择至少一个工艺类型');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'timeTrend' && !this.queryParams.dateRange) {\r\n        this.$message.warning('请选择时间范围');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplierVsTest' && !this.queryParams.compareParam) {\r\n        this.$message.warning('请选择要对比的参数');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 获取材料对比数据 */\r\n    async getMaterialCompareData() {\r\n      const materialIds = this.queryParams.materialNames || [];\r\n      const compareData = [];\r\n\r\n      if (materialIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const materialId of materialIds) {\r\n        try {\r\n          // 通过材料ID查找对应的参数组，然后查找测试结果\r\n          const paramGroupResponse = await listProcessParamGroup({\r\n            materialId: materialId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroups = paramGroupResponse.rows || [];\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n\r\n          let allSupplierValues = [];\r\n          let allTestValues = [];\r\n\r\n          // 遍历该材料的所有参数组，获取测试结果\r\n          for (const group of paramGroups) {\r\n            const testResponse = await listTestResult({\r\n              groupId: group.groupId,\r\n              pageNum: 1,\r\n              pageSize: 1000\r\n            });\r\n\r\n            const testResults = testResponse.rows || [];\r\n            const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n            const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n            allSupplierValues = allSupplierValues.concat(supplierValues);\r\n            allTestValues = allTestValues.concat(testValues);\r\n          }\r\n\r\n          compareData.push({\r\n            name: material ? material.materialName : `材料${materialId}`,\r\n            supplier: material ? material.supplierName : '',\r\n            supplierAvg: allSupplierValues.length > 0 ? (allSupplierValues.reduce((a, b) => a + b, 0) / allSupplierValues.length).toFixed(2) : 0,\r\n            testAvg: allTestValues.length > 0 ? (allTestValues.reduce((a, b) => a + b, 0) / allTestValues.length).toFixed(2) : 0,\r\n            supplierMax: allSupplierValues.length > 0 ? Math.max(...allSupplierValues).toFixed(2) : 0,\r\n            testMax: allTestValues.length > 0 ? Math.max(...allTestValues).toFixed(2) : 0,\r\n            supplierMin: allSupplierValues.length > 0 ? Math.min(...allSupplierValues).toFixed(2) : 0,\r\n            testMin: allTestValues.length > 0 ? Math.min(...allTestValues).toFixed(2) : 0,\r\n            dataCount: allTestValues.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取材料${materialId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取供应商对比数据 */\r\n    async getSupplierCompareData() {\r\n      const suppliers = this.queryParams.supplierNames || [];\r\n      const compareData = [];\r\n\r\n      if (suppliers.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const supplier of suppliers) {\r\n        try {\r\n          const response = await listTestResult({\r\n            supplierName: supplier,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: supplier,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            accuracy: supplierValues.length > 0 && testValues.length > 0 ?\r\n              (100 - Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)) /\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length) * 100).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取供应商${supplier}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取参数编号对比数据 */\r\n    async getParamNumberCompareData() {\r\n      const paramGroupIds = this.queryParams.paramNumbers || [];\r\n      const compareData = [];\r\n\r\n      if (paramGroupIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const groupId of paramGroupIds) {\r\n        try {\r\n          // 获取测试结果数据\r\n          const response = await listTestResult({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          // 获取参数明细数据\r\n          const paramItemResponse = await listProcessParamItem({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroup = this.paramNumberOptions.find(p => p.groupId === groupId);\r\n          const testResults = response.rows || [];\r\n          const paramItems = paramItemResponse.rows || [];\r\n\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          // 格式化参数明细信息\r\n          const paramDetails = paramItems.map(item => ({\r\n            paramName: item.paramName || 'N/A',\r\n            paramValue: item.paramValue !== null && item.paramValue !== undefined ?\r\n              String(item.paramValue) : 'N/A',\r\n            unit: item.unit || ''\r\n          }));\r\n\r\n          compareData.push({\r\n            name: paramGroup ? paramGroup.paramNumber : `参数${groupId}`,\r\n            material: paramGroup ? paramGroup.materialName : '',\r\n            processType: paramGroup ? paramGroup.processType : '',\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            deviation: supplierValues.length > 0 && testValues.length > 0 ?\r\n              Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)).toFixed(2) : 0,\r\n            dataCount: testResults.length,\r\n            paramDetails: paramDetails, // 添加参数明细信息\r\n            groupId: groupId // 保存groupId用于后续使用\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取参数组${groupId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取工艺类型对比数据 */\r\n    async getProcessTypeCompareData() {\r\n      const processTypes = this.queryParams.processTypes || [];\r\n      const compareData = [];\r\n\r\n      if (processTypes.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const processType of processTypes) {\r\n        try {\r\n          const response = await listTestResult({\r\n            processType: processType,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: processType,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            stability: testValues.length > 1 ? this.calculateStandardDeviation(testValues).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取工艺类型${processType}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取时间趋势数据 */\r\n    async getTimeTrendData() {\r\n      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const [startDate, endDate] = this.queryParams.dateRange;\r\n        const response = await listTestResult({\r\n          startDate: startDate,\r\n          endDate: endDate,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const trendData = [];\r\n\r\n        // 按日期分组\r\n        const dateGroups = {};\r\n        testResults.forEach(result => {\r\n          const date = result.createTime ? result.createTime.split(' ')[0] : '';\r\n          if (date && !dateGroups[date]) {\r\n            dateGroups[date] = [];\r\n          }\r\n          if (date) {\r\n            dateGroups[date].push(result);\r\n          }\r\n        });\r\n\r\n        // 计算每日平均值\r\n        Object.keys(dateGroups).sort().forEach(date => {\r\n          const dayResults = dateGroups[date];\r\n          const testValues = dayResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          trendData.push({\r\n            date: date,\r\n            avgValue: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            count: dayResults.length\r\n          });\r\n        });\r\n\r\n        return trendData;\r\n      } catch (error) {\r\n        console.error('获取时间趋势数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 获取供应商vs测试值对比数据 */\r\n    async getSupplierVsTestData() {\r\n      const groupId = this.queryParams.compareParam;\r\n\r\n      if (!groupId) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const response = await listTestResult({\r\n          groupId: groupId,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const compareData = testResults.map(result => ({\r\n          name: result.materialName || '未知材料',\r\n          supplier: result.supplierName || '未知供应商',\r\n          supplierValue: parseFloat(result.supplierDatasheetVal) || 0,\r\n          testValue: parseFloat(result.testValue) || 0,\r\n          difference: Math.abs((parseFloat(result.supplierDatasheetVal) || 0) - (parseFloat(result.testValue) || 0)).toFixed(2),\r\n          createTime: result.createTime\r\n        }));\r\n\r\n        return compareData;\r\n      } catch (error) {\r\n        console.error('获取供应商vs测试值数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 计算标准差 */\r\n    calculateStandardDeviation(values) {\r\n      const avg = values.reduce((a, b) => a + b, 0) / values.length;\r\n      const squareDiffs = values.map(value => Math.pow(value - avg, 2));\r\n      const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;\r\n      return Math.sqrt(avgSquareDiff);\r\n    },\r\n\r\n    /** 格式化参数明细显示 */\r\n    formatParamDetails(row, column, cellValue) {\r\n      if (!cellValue || !Array.isArray(cellValue)) {\r\n        return '暂无参数';\r\n      }\r\n\r\n      return cellValue.map(param => {\r\n        let text = param.paramName + ': ' + param.paramValue;\r\n        if (param.unit) {\r\n          text += ' ' + param.unit;\r\n        }\r\n        return text;\r\n      }).join('; ');\r\n    },\r\n\r\n    /** 更新表格列 */\r\n    updateTableColumns() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      switch (compareType) {\r\n        case 'material':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplier':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '供应商', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'accuracy', label: '准确率(%)', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'paramNumber':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '参数编号', width: 120 },\r\n            { prop: 'material', label: '材料名称', width: 120 },\r\n            { prop: 'processType', label: '工艺类型', width: 100 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'deviation', label: '偏差', width: 80 },\r\n            { prop: 'paramDetails', label: '参数明细', width: 200 }\r\n          ];\r\n          break;\r\n        case 'processType':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '工艺类型', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'stability', label: '稳定性', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'timeTrend':\r\n          this.tableColumns = [\r\n            { prop: 'date', label: '日期', width: 120 },\r\n            { prop: 'avgValue', label: '平均值', width: 100 },\r\n            { prop: 'count', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplierVsTest':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierValue', label: '供应商值', width: 100 },\r\n            { prop: 'testValue', label: '测试值', width: 100 },\r\n            { prop: 'difference', label: '差值', width: 80 }\r\n          ];\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 渲染图表 */\r\n    renderChart() {\r\n      if (!this.chart || this.chartData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      let option = {};\r\n\r\n      switch (this.chartType) {\r\n        case 'line':\r\n          option = this.getLineChartOption();\r\n          break;\r\n        case 'bar':\r\n          option = this.getBarChartOption();\r\n          break;\r\n        case 'scatter':\r\n          option = this.getScatterChartOption();\r\n          break;\r\n        case 'radar':\r\n          option = this.getRadarChartOption();\r\n          break;\r\n        case 'heatmap':\r\n          option = this.getHeatmapChartOption();\r\n          break;\r\n      }\r\n\r\n      this.chart.setOption(option, true);\r\n    },\r\n\r\n    /** 获取折线图配置 */\r\n    getLineChartOption() {\r\n      // 根据对比类型生成不同的图表配置\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'timeTrend') {\r\n        // 时间趋势图\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(param => {\r\n                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.date)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '平均值'\r\n          },\r\n          series: [{\r\n            name: '平均值',\r\n            type: 'line',\r\n            data: this.chartData.map(item => item.avgValue),\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }]\r\n        };\r\n      } else {\r\n        // 其他对比类型的折线图\r\n        const self = this;\r\n\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            backgroundColor: 'rgba(50, 50, 50, 0.95)',\r\n            borderColor: '#409EFF',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); border-radius: 8px; padding: 12px;',\r\n            formatter: function(params) {\r\n              const dataIndex = params[0].dataIndex;\r\n              const currentData = self.chartData[dataIndex];\r\n\r\n              let result = `<div style=\"font-size: 14px; font-weight: bold; color: #409EFF; margin-bottom: 8px;\">\r\n                            📊 ${params[0].name}\r\n                          </div>`;\r\n\r\n              // 显示基本对比数据\r\n              params.forEach(param => {\r\n                const color = param.color;\r\n                result += `<div style=\"margin: 4px 0; display: flex; align-items: center;\">\r\n                          <span style=\"display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;\"></span>\r\n                          <span style=\"font-weight: 500;\">${param.seriesName}:</span>\r\n                          <span style=\"margin-left: 8px; color: #67C23A; font-weight: bold;\">${self.formatNumber(param.value)}</span>\r\n                        </div>`;\r\n              });\r\n\r\n              // 根据对比类型显示详细信息\r\n              if (currentData) {\r\n                result += '<div style=\"border-top: 1px solid #666; margin: 8px 0; padding-top: 8px;\">';\r\n\r\n                if (self.queryParams.compareType === 'paramNumber' && currentData.paramDetails) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📋 参数明细信息</div>';\r\n                  if (currentData.material) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">材料:</span> ${currentData.material}</div>`;\r\n                  }\r\n                  if (currentData.processType) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">工艺:</span> ${currentData.processType}</div>`;\r\n                  }\r\n                  if (currentData.paramDetails && currentData.paramDetails.length > 0) {\r\n                    result += '<div style=\"margin: 4px 0; color: #909399;\">参数列表:</div>';\r\n                    currentData.paramDetails.slice(0, 5).forEach(param => {\r\n                      result += `<div style=\"margin: 1px 0; padding-left: 12px; font-size: 11px;\">\r\n                                • ${param.paramName}: <span style=\"color: #67C23A;\">${self.formatNumber(param.paramValue)}</span>\r\n                                ${param.unit ? ' <span style=\"color: #909399;\">' + param.unit + '</span>' : ''}\r\n                              </div>`;\r\n                    });\r\n                    if (currentData.paramDetails.length > 5) {\r\n                      result += `<div style=\"margin: 2px 0; padding-left: 12px; color: #909399; font-size: 11px;\">\r\n                                ... 还有 ${currentData.paramDetails.length - 5} 个参数\r\n                              </div>`;\r\n                    }\r\n                  }\r\n                } else if (self.queryParams.compareType === 'material' && currentData.supplier) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">🏭 供应商信息</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">供应商:</span> ${currentData.supplier}</div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'supplier' && currentData.accuracy) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📈 质量指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">准确率:</span> <span style=\"color: ${currentData.accuracy > 90 ? '#67C23A' : currentData.accuracy > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.accuracy}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'processType' && currentData.stability) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">⚙️ 工艺指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">稳定性:</span> <span style=\"color: ${currentData.stability > 90 ? '#67C23A' : currentData.stability > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.stability}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                }\r\n\r\n                result += '</div>';\r\n              }\r\n\r\n              return result;\r\n            }\r\n          },\r\n          legend: {\r\n            top: '10%',\r\n            data: ['供应商数据', '测试数据']\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.name)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '数值'\r\n          },\r\n          series: [\r\n            {\r\n              name: '供应商数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.supplierAvg || 0),\r\n              smooth: true,\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '测试数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.testAvg || 0),\r\n              smooth: true,\r\n              symbol: 'triangle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    /** 获取柱状图配置 */\r\n    getBarChartOption() {\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params[0].name + '<br/>';\r\n\r\n            // 显示基本对比数据\r\n            params.forEach(param => {\r\n              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: ['供应商数据', '测试数据']\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.chartData.map(item => item.name),\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数值'\r\n        },\r\n        series: [\r\n          {\r\n            name: '供应商数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.supplierAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          {\r\n            name: '测试数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.testAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#91cc75'\r\n            }\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    /** 获取散点图配置 */\r\n    getScatterChartOption() {\r\n      const self = this;\r\n\r\n      // 散点图主要用于供应商vs测试值对比\r\n      const scatterData = this.chartData.map((item, index) => [\r\n        parseFloat(item.supplierValue || item.supplierAvg) || 0,\r\n        parseFloat(item.testValue || item.testAvg) || 0,\r\n        item.name, // 用于tooltip显示\r\n        index // 数据索引，用于获取详细信息\r\n      ]);\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const [supplierVal, testVal, name, dataIndex] = params.data;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = `${name}<br/>供应商值: ${supplierVal}<br/>测试值: ${testVal}<br/>差值: ${Math.abs(supplierVal - testVal).toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '供应商数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '测试数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        series: [{\r\n          name: '数据对比',\r\n          type: 'scatter',\r\n          data: scatterData,\r\n          symbolSize: 8,\r\n          itemStyle: {\r\n            color: '#5470c6'\r\n          }\r\n        }, {\r\n          name: '理想线',\r\n          type: 'line',\r\n          data: [[0, 0], [Math.max(...scatterData.map(d => d[0])), Math.max(...scatterData.map(d => d[0]))]],\r\n          lineStyle: {\r\n            color: '#ff6b6b',\r\n            type: 'dashed'\r\n          },\r\n          symbol: 'none'\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取雷达图配置 */\r\n    getRadarChartOption() {\r\n      // 雷达图用于多维度对比，基于chartData生成指标\r\n      const indicators = [\r\n        { name: '供应商平均值', max: 100 },\r\n        { name: '测试平均值', max: 100 },\r\n        { name: '数据量', max: 50 },\r\n        { name: '准确率', max: 100 },\r\n        { name: '稳定性', max: 10 }\r\n      ];\r\n\r\n      const radarData = this.chartData.map(item => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const dataCount = parseInt(item.dataCount) || 0;\r\n        const accuracy = parseFloat(item.accuracy) || 0;\r\n        const stability = parseFloat(item.stability) || 0;\r\n\r\n        return {\r\n          name: item.name,\r\n          value: [\r\n            Math.min(supplierAvg, 100),\r\n            Math.min(testAvg, 100),\r\n            Math.min(dataCount, 50),\r\n            Math.min(accuracy, 100),\r\n            Math.min(stability, 10)\r\n          ]\r\n        };\r\n      });\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const dataIndex = params.dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params.name + '<br/>';\r\n\r\n            // 显示雷达图数据\r\n            const indicators = ['供应商平均值', '测试平均值', '数据量', '准确率', '稳定性'];\r\n            params.value.forEach((value, index) => {\r\n              result += indicators[index] + ': ' + value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: radarData.map(item => item.name)\r\n        },\r\n        radar: {\r\n          indicator: indicators,\r\n          radius: '60%'\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          data: radarData\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取热力图配置 */\r\n    getHeatmapChartOption() {\r\n      // 热力图用于展示数据密度和分布\r\n      const xAxisData = [...new Set(this.chartData.map(item => item.name))];\r\n      const yAxisData = ['供应商数据', '测试数据', '偏差'];\r\n\r\n      const heatmapData = [];\r\n      this.chartData.forEach((item, xIndex) => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const deviation = Math.abs(supplierAvg - testAvg);\r\n\r\n        heatmapData.push([xIndex, 0, supplierAvg]); // 供应商数据\r\n        heatmapData.push([xIndex, 1, testAvg]);     // 测试数据\r\n        heatmapData.push([xIndex, 2, deviation]);   // 偏差\r\n      });\r\n\r\n      const maxValue = Math.max(...heatmapData.map(d => d[2]));\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          position: 'top',\r\n          formatter: function(params) {\r\n            const [x, y, value] = params.data;\r\n            const xLabel = xAxisData[x];\r\n            const yLabel = yAxisData[y];\r\n            const currentData = self.chartData[x];\r\n\r\n            let result = `${xLabel}<br/>${yLabel}: ${value.toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        grid: {\r\n          height: '50%',\r\n          top: '15%'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: yAxisData\r\n        },\r\n        visualMap: {\r\n          min: 0,\r\n          max: maxValue || 100,\r\n          calculable: true,\r\n          orient: 'horizontal',\r\n          left: 'center',\r\n          bottom: '5%',\r\n          inRange: {\r\n            color: ['#50a3ba', '#eac736', '#d94e5d']\r\n          }\r\n        },\r\n        series: [{\r\n          type: 'heatmap',\r\n          data: heatmapData,\r\n          label: {\r\n            show: true,\r\n            formatter: function(params) {\r\n              return params.data[2].toFixed(1);\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          }\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 重置查询 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams = {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      };\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 刷新图表 */\r\n    refreshChart() {\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 导出图表 */\r\n    exportChart() {\r\n      if (!this.chart) {\r\n        this.$message.warning('请先生成图表');\r\n        return;\r\n      }\r\n\r\n      const url = this.chart.getDataURL({\r\n        type: 'png',\r\n        pixelRatio: 2,\r\n        backgroundColor: '#fff'\r\n      });\r\n\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${this.chartTitle}_${new Date().getTime()}.png`;\r\n      link.click();\r\n    },\r\n\r\n    /** 切换数据表显示 */\r\n    toggleDataTable() {\r\n      this.showDataTable = !this.showDataTable;\r\n      if (!this.showDataTable && this.chart) {\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 切换项目详情显示 */\r\n    toggleProjectDetails() {\r\n      this.showProjectDetails = !this.showProjectDetails;\r\n    },\r\n\r\n    /** 切换全屏显示 */\r\n    toggleFullscreen() {\r\n      if (this.isFullscreen) {\r\n        this.chartHeight = 400;\r\n        this.isFullscreen = false;\r\n      } else {\r\n        this.chartHeight = window.innerHeight - 200;\r\n        this.isFullscreen = true;\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 显示图表帮助 */\r\n    showChartHelp() {\r\n      this.helpDialogVisible = true;\r\n    },\r\n\r\n    /** 格式化数字显示 */\r\n    formatNumber(value) {\r\n      if (value === null || value === undefined || isNaN(value)) {\r\n        return 'N/A';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (num === 0) return '0';\r\n      if (Math.abs(num) >= 1000000) {\r\n        return (num / 1000000).toFixed(2) + 'M';\r\n      } else if (Math.abs(num) >= 1000) {\r\n        return (num / 1000).toFixed(2) + 'K';\r\n      } else if (Math.abs(num) < 1) {\r\n        return num.toFixed(4);\r\n      } else {\r\n        return num.toFixed(2);\r\n      }\r\n    },\r\n\r\n    /** 格式化参数值显示（支持字符串类型，显示完整数值） */\r\n    formatParamValue(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 如果是字符串类型，直接返回完整字符串\r\n      if (typeof value === 'string') {\r\n        // 尝试解析为数字\r\n        const num = parseFloat(value);\r\n        if (!isNaN(num)) {\r\n          // 如果是数字字符串，保留6位小数并去除尾随零\r\n          return num.toFixed(6).replace(/\\.?0+$/, '');\r\n        }\r\n        // 如果不是数字字符串，直接返回\r\n        return value;\r\n      }\r\n\r\n      // 如果是数字类型，保留6位小数并去除尾随零\r\n      if (typeof value === 'number') {\r\n        return value.toFixed(6).replace(/\\.?0+$/, '');\r\n      }\r\n\r\n      return String(value);\r\n    },\r\n\r\n    /** 获取参数标签类型 */\r\n    getParamTagType(param) {\r\n      if (!param.paramValue) return '';\r\n      const value = parseFloat(param.paramValue);\r\n      if (isNaN(value)) return '';\r\n\r\n      // 根据参数值范围设置不同颜色\r\n      if (value > 100) return 'danger';\r\n      if (value > 50) return 'warning';\r\n      if (value > 10) return 'success';\r\n      return 'info';\r\n    },\r\n\r\n    /** 显示参数详情 */\r\n    showParamDetail(param) {\r\n      this.currentParamDetail = param;\r\n      this.paramDetailDialogVisible = true;\r\n    },\r\n\r\n    /** 更新选中参数详情 */\r\n    updateSelectedParamDetails() {\r\n      // 根据当前选择的对比类型和选项，更新参数详情信息\r\n      const { compareType } = this.queryParams;\r\n      this.selectedParamDetails = [];\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length > 0) {\r\n        this.queryParams.materialNames.forEach(materialId => {\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n          if (material) {\r\n            this.selectedParamDetails.push({\r\n              name: material.materialName,\r\n              materialName: material.materialName,\r\n              supplierName: material.supplierName,\r\n              processType: material.processType,\r\n              testCount: material.testCount || 0,\r\n              statistics: material.statistics\r\n            });\r\n          }\r\n        });\r\n      } else if (compareType === 'supplier' && this.queryParams.supplierNames.length > 0) {\r\n        this.queryParams.supplierNames.forEach(supplier => {\r\n          this.selectedParamDetails.push({\r\n            name: supplier,\r\n            supplierName: supplier,\r\n            testCount: 0 // 这里可以从API获取实际数据\r\n          });\r\n        });\r\n      } else if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length > 0) {\r\n        this.queryParams.paramNumbers.forEach(paramId => {\r\n          const param = this.paramNumberOptions.find(p => p.groupId === paramId);\r\n          if (param) {\r\n            this.selectedParamDetails.push({\r\n              name: param.paramNumber,\r\n              paramNumber: param.paramNumber,\r\n              materialName: param.materialName,\r\n              processType: param.processType,\r\n              mainParams: param.paramItems || [],\r\n              testCount: param.testCount || 0\r\n            });\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-guide-btn {\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-guide-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.guide-btn {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n}\r\n\r\n.export-btn {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\r\n  border: none;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n}\r\n\r\n.statistics-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.statistics-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.statistics-content {\r\n  padding: 20px;\r\n}\r\n\r\n.statistics-title {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.statistics-desc {\r\n  font-size: 12px;\r\n  color: #C0C4CC;\r\n}\r\n\r\n.chart-help-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.chart-help-content h4 {\r\n  color: #409EFF;\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.chart-help-content ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.chart-help-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.chart-help-content strong {\r\n  color: #303133;\r\n}\r\n\r\n/* 使用指南样式 */\r\n.usage-guide-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.usage-guide-card .el-card__header {\r\n  background: transparent;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.guide-item h4 {\r\n  color: #fff;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.guide-item p {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n}\r\n\r\n/* 参数详情卡片样式 */\r\n.param-details-card {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background: white;\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.param-detail-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.detail-section:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.section-title {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-icon {\r\n  color: #909399;\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 60px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 8px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 10px;\r\n  color: #909399;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.params-container {\r\n  max-height: 80px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.params-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* 使用建议样式优化 */\r\n.usage-suggestion {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.usage-suggestion p {\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据表格样式优化 */\r\n.trend-data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.trend-data-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.trend-data-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.param-details-container {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 5px;\r\n}\r\n\r\n.param-detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n  padding: 2px 6px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.param-name {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 5px;\r\n  min-width: 80px;\r\n}\r\n\r\n.param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n  font-family: 'Courier New', monospace;\r\n  margin-right: 5px;\r\n}\r\n\r\n.param-unit {\r\n  color: #909399;\r\n  font-size: 11px;\r\n}\r\n\r\n.material-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.material-info i {\r\n  color: #409EFF;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}