-- 基于实际数据库结构的正确修复脚本
-- 根据codebuddy.sql中的实际表结构进行修改

-- ======== 第一步：安全检查 ========
SET FOREIGN_KEY_CHECKS = 0;

-- 检查当前表结构
SELECT 'Current table structure check:' as info;
SHOW TABLES LIKE '%material%';
SHOW TABLES LIKE '%process%';
SHOW TABLES LIKE '%test%';

-- ======== 第二步：修正字段类型不一致问题 ========

-- 2.1 将test_plan_group和test_param_item的ID字段改为int，与其他表保持一致
ALTER TABLE `test_plan_group` 
MODIFY COLUMN `plan_group_id` int NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）';

ALTER TABLE `test_param_item` 
MODIFY COLUMN `test_param_id` int NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）';

ALTER TABLE `test_param_item` 
MODIFY COLUMN `plan_group_id` int NOT NULL COMMENT '所属测试方案组ID';

-- ======== 第三步：清理数据，确保外键约束可以建立 ========

-- 3.1 清理test_results表中无效的外键数据
DELETE FROM `test_results` 
WHERE `plan_group_id` NOT IN (
    SELECT `plan_group_id` FROM `test_plan_group` WHERE `plan_group_id` IS NOT NULL
);

DELETE FROM `test_results` 
WHERE `group_id` NOT IN (
    SELECT `group_id` FROM `process_param_group` WHERE `group_id` IS NOT NULL
);

-- 3.2 清理process_param_item表中无效的外键数据
DELETE FROM `process_param_item` 
WHERE `group_id` NOT IN (
    SELECT `group_id` FROM `process_param_group` WHERE `group_id` IS NOT NULL
);

-- 3.3 清理process_param_group表中无效的外键数据
DELETE FROM `process_param_group` 
WHERE `material_id` NOT IN (
    SELECT `material_id` FROM `materials` WHERE `material_id` IS NOT NULL
);

-- 3.4 清理test_param_item表中无效的外键数据
DELETE FROM `test_param_item` 
WHERE `plan_group_id` NOT IN (
    SELECT `plan_group_id` FROM `test_plan_group` WHERE `plan_group_id` IS NOT NULL
);

-- ======== 第四步：添加外键约束 ========

-- 4.1 工艺参数组 -> 材料
ALTER TABLE `process_param_group` 
ADD CONSTRAINT `fk_group_material`
  FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 4.2 工艺参数明细 -> 工艺参数组
ALTER TABLE `process_param_item` 
ADD CONSTRAINT `fk_item_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 4.3 测试参数明细 -> 测试方案组
ALTER TABLE `test_param_item` 
ADD CONSTRAINT `fk_test_param_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 4.4 测试结果 -> 测试方案组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 4.5 测试结果 -> 工艺参数组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_param_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- ======== 第五步：插入示例数据（如果表为空） ========

-- 5.1 检查并插入测试方案组示例数据
INSERT INTO `test_plan_group` (`plan_code`, `performance_type`, `performance_name`, `test_equipment`, `create_by`) 
SELECT * FROM (
  SELECT 'TP001' as plan_code, '力学性能' as performance_type, '拉伸强度测试' as performance_name, 'Instron 5985' as test_equipment, 'admin' as create_by
  UNION ALL
  SELECT 'TP002', '力学性能', '弯曲强度测试', 'Instron 5985', 'admin'
  UNION ALL
  SELECT 'TP003', '力学性能', '冲击韧性测试', 'Charpy冲击试验机', 'admin'
  UNION ALL
  SELECT 'TP004', '热学性能', '热膨胀系数测试', 'TMA热机械分析仪', 'admin'
  UNION ALL
  SELECT 'TP005', '热学性能', '导热系数测试', '激光导热仪', 'admin'
  UNION ALL
  SELECT 'TP006', '电学性能', '介电常数测试', '阻抗分析仪', 'admin'
  UNION ALL
  SELECT 'TP007', '化学性能', '耐腐蚀性测试', '盐雾试验箱', 'admin'
  UNION ALL
  SELECT 'TP008', '物理性能', '密度测试', '密度计', 'admin'
) AS tmp
WHERE NOT EXISTS (SELECT 1 FROM `test_plan_group` WHERE `plan_code` = tmp.plan_code);

-- 5.2 为测试方案组添加测试参数明细
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `unit`, `create_by`) 
SELECT tpg.plan_group_id, param_name, param_value, unit, 'admin'
FROM `test_plan_group` tpg
CROSS JOIN (
  SELECT '试样长度' as param_name, '250' as param_value, 'mm' as unit, '力学性能' as performance_type
  UNION ALL
  SELECT '试样宽度', '25', 'mm', '力学性能'
  UNION ALL
  SELECT '拉伸速度', '2', 'mm/min', '力学性能'
  UNION ALL
  SELECT '试样尺寸', '25x25', 'mm', '热学性能'
  UNION ALL
  SELECT '温度范围', '25-200', '°C', '热学性能'
  UNION ALL
  SELECT '升温速率', '5', '°C/min', '热学性能'
  UNION ALL
  SELECT '试样厚度', '1', 'mm', '电学性能'
  UNION ALL
  SELECT '测试频率', '1000', 'Hz', '电学性能'
  UNION ALL
  SELECT '测试电压', '1', 'V', '电学性能'
  UNION ALL
  SELECT '试样体积', '1', 'cm³', '物理性能'
  UNION ALL
  SELECT '测试温度', '23', '°C', '物理性能'
  UNION ALL
  SELECT '盐雾浓度', '5', '%', '化学性能'
  UNION ALL
  SELECT '测试时间', '168', 'h', '化学性能'
) AS params
WHERE tpg.performance_type = params.performance_type
AND NOT EXISTS (
  SELECT 1 FROM `test_param_item` tpi 
  WHERE tpi.plan_group_id = tpg.plan_group_id 
  AND tpi.param_name = params.param_name
);

-- ======== 第六步：创建索引优化性能 ========
CREATE INDEX IF NOT EXISTS `idx_material_name` ON `materials` (`material_name`);
CREATE INDEX IF NOT EXISTS `idx_process_type` ON `process_param_group` (`process_type`);
CREATE INDEX IF NOT EXISTS `idx_param_number` ON `process_param_group` (`param_number`);
CREATE INDEX IF NOT EXISTS `idx_performance_type` ON `test_plan_group` (`performance_type`);
CREATE INDEX IF NOT EXISTS `idx_plan_code` ON `test_plan_group` (`plan_code`);
CREATE INDEX IF NOT EXISTS `idx_test_result_create_time` ON `test_results` (`create_time`);

SET FOREIGN_KEY_CHECKS = 1;

-- ======== 第七步：验证修改结果 ========
SELECT 'Database structure verification:' as info;

SELECT 'materials table:' as table_name;
SELECT COUNT(*) as record_count FROM `materials`;

SELECT 'process_param_group table:' as table_name;
SELECT COUNT(*) as record_count FROM `process_param_group`;

SELECT 'process_param_item table:' as table_name;
SELECT COUNT(*) as record_count FROM `process_param_item`;

SELECT 'test_plan_group table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_plan_group`;

SELECT 'test_param_item table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_param_item`;

SELECT 'test_results table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_results`;

SELECT 'Foreign key constraints:' as info;
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
  TABLE_NAME, CONSTRAINT_NAME;

SELECT 'Field type verification:' as info;
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  COLUMN_TYPE
FROM 
  INFORMATION_SCHEMA.COLUMNS 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('materials', 'process_param_group', 'process_param_item', 'test_plan_group', 'test_param_item', 'test_results')
  AND COLUMN_NAME LIKE '%_id'
ORDER BY 
  TABLE_NAME, COLUMN_NAME;

SELECT 'Modification completed successfully!' as status;
