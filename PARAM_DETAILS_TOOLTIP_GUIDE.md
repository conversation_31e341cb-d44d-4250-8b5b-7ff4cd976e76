# 参数编号对比 - 参数明细悬停显示功能说明

## 🎯 功能概述

在趋势对比模块的"参数编号对比"维度中，当鼠标悬停在图表上时，会显示该参数编号下的所有参数明细信息，包括参数名称、数值及单位。

## 📊 支持的图表类型

### 1. 柱状图 📊
**显示内容：**
- 基本对比数据（供应商数据、测试数据）
- 材料名称
- 工艺类型
- 完整的参数明细列表

**悬停效果示例：**
```
CF-001
供应商数据: 3420.5
测试数据: 3380.2

参数明细信息：
材料：碳纤维复合材料
工艺：预浸料成型
参数列表：
• 成型温度: 180.00 °C
• 成型压力: 0.70 MPa
• 固化时间: 120.00 min
```

### 2. 折线图 📈
**显示内容：**
- 趋势对比数据
- 参数明细信息
- 工艺参数详情

**适用场景：**
- 多个参数编号的性能趋势对比
- 工艺参数优化分析

### 3. 散点图 🔵
**显示内容：**
- 供应商值 vs 测试值对比
- 数据偏差分析
- 详细的参数明细信息

**适用场景：**
- 数据准确性分析
- 参数相关性研究

### 4. 雷达图 🕸️
**显示内容：**
- 多维度性能指标
- 综合评估数据
- 完整参数明细

**适用场景：**
- 综合性能对比
- 多指标评估

### 5. 热力图 🌡️
**显示内容：**
- 数据密度分布
- 性能热点分析
- 参数明细信息

**适用场景：**
- 参数优化区间识别
- 性能分布分析

## 🔧 技术实现

### 数据获取增强
```javascript
// 获取参数明细数据
const paramItemResponse = await listProcessParamItem({
  groupId: groupId,
  pageNum: 1,
  pageSize: 1000
});

// 格式化参数明细信息
const paramDetails = paramItems.map(item => ({
  paramName: item.paramName || 'N/A',
  paramValue: item.paramValue !== null ? parseFloat(item.paramValue).toFixed(2) : 'N/A',
  unit: item.unit || ''
}));
```

### Tooltip配置
```javascript
tooltip: {
  trigger: 'axis', // 或 'item'
  formatter: function(params) {
    const dataIndex = params[0].dataIndex;
    const currentData = self.chartData[dataIndex];
    
    let result = params[0].name + '<br/>';
    
    // 显示基本对比数据
    params.forEach(param => {
      result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
    });
    
    // 显示参数明细信息
    if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {
      result += '<br/><strong>参数明细信息：</strong><br/>';
      result += '材料：' + currentData.material + '<br/>';
      result += '工艺：' + currentData.processType + '<br/>';
      result += '参数列表：<br/>';
      
      currentData.paramDetails.forEach(param => {
        result += '• ' + param.paramName + ': ' + param.paramValue;
        if (param.unit) {
          result += ' ' + param.unit;
        }
        result += '<br/>';
      });
    }
    
    return result;
  }
}
```

## 📋 使用步骤

### 1. 选择对比维度
- 在趋势对比模块中选择"参数编号对比"

### 2. 选择参数编号
推荐测试组合：
- **碳纤维工艺对比**：CF-001 + CF-002 + CF-003
- **玻璃纤维工艺对比**：GF-001 + GF-002 + GF-003
- **金属材料工艺对比**：AL-001 + AL-002

### 3. 生成图表
- 点击"生成对比图表"按钮

### 4. 查看参数明细
- 将鼠标悬停在图表的任意数据点上
- 自动显示该参数编号的完整参数明细信息

## 🎨 显示效果

### 基本信息
- **参数编号**：如 CF-001
- **材料名称**：如 碳纤维复合材料
- **工艺类型**：如 预浸料成型

### 参数明细列表
每个参数明细包含：
- **参数名称**：如 成型温度
- **参数数值**：如 180.00
- **参数单位**：如 °C

### 格式示例
```
• 成型温度: 180.00 °C
• 成型压力: 0.70 MPa
• 固化时间: 120.00 min
```

## 📊 数据表格增强

在数据表格中也新增了"参数明细"列，显示格式：
```
成型温度: 180.00 °C; 成型压力: 0.70 MPa; 固化时间: 120.00 min
```

## 🔍 实际应用场景

### 1. 工艺参数优化
**使用方式：**
- 选择同一材料的不同工艺参数编号
- 通过悬停查看具体的工艺参数设置
- 对比性能数据与工艺参数的关系

### 2. 材料性能分析
**使用方式：**
- 选择不同材料的相似工艺
- 查看各材料的具体参数设置
- 分析参数对性能的影响

### 3. 质量控制
**使用方式：**
- 监控关键工艺参数
- 通过悬停快速查看参数详情
- 识别异常参数设置

### 4. 工艺标准化
**使用方式：**
- 对比不同批次的工艺参数
- 识别最优参数组合
- 建立工艺标准

## ⚠️ 注意事项

1. **数据完整性**：确保参数明细数据已正确录入
2. **悬停位置**：鼠标需要悬停在具体的数据点上
3. **图表类型**：所有图表类型都支持参数明细显示
4. **数据更新**：参数明细信息会实时从数据库获取

## 🚀 优势特点

1. **信息丰富**：一次悬停获取完整参数信息
2. **操作便捷**：无需额外点击，悬停即显示
3. **格式清晰**：结构化显示，易于阅读
4. **实时更新**：数据始终保持最新状态
5. **全图表支持**：所有图表类型都支持此功能

通过这个功能，用户可以在进行参数编号对比时，快速了解每个参数编号背后的具体工艺参数设置，大大提升了数据分析的效率和深度！
