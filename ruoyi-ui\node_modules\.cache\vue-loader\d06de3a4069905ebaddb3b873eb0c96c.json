{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testResult\\index.vue", "mtime": 1754278951915}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0VGVzdFJlc3VsdCwgZ2V0VGVzdFJlc3VsdCwgZGVsVGVzdFJlc3VsdCwgYWRkVGVzdFJlc3VsdCwgdXBkYXRlVGVzdFJlc3VsdCwNCiAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMNCn0gZnJvbSAiQC9hcGkvbWF0ZXJpYWwvdGVzdFJlc3VsdCI7DQppbXBvcnQgeyBsaXN0VGVzdFBsYW5Hcm91cCB9IGZyb20gIkAvYXBpL21hdGVyaWFsL3Rlc3RQbGFuR3JvdXAiOw0KaW1wb3J0IHsgbGlzdFRlc3RQYXJhbUl0ZW0gfSBmcm9tICJAL2FwaS9tYXRlcmlhbC90ZXN0UGFyYW1JdGVtIjsNCmltcG9ydCB7IGxpc3RQcm9jZXNzUGFyYW1Hcm91cCwgZ2V0UHJvY2Vzc1BhcmFtR3JvdXAgfSBmcm9tICJAL2FwaS9tYXRlcmlhbC9wcm9jZXNzUGFyYW1Hcm91cCI7DQppbXBvcnQgeyBsaXN0UHJvY2Vzc1BhcmFtSXRlbSB9IGZyb20gIkAvYXBpL21hdGVyaWFsL3Byb2Nlc3NQYXJhbUl0ZW0iOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJUZXN0UmVzdWx0IiwNCiAgZGlyZWN0aXZlczogew0KICAgIC8vIOaLluaLveaMh+S7pA0KICAgIGRyYWc6IHsNCiAgICAgIGJpbmQoZWwpIHsNCiAgICAgICAgY29uc3QgZGlhbG9nSGVhZGVyRWwgPSBlbC5xdWVyeVNlbGVjdG9yKCcuZWwtZGlhbG9nX19oZWFkZXInKTsNCiAgICAgICAgY29uc3QgZHJhZ0RvbSA9IGVsLnF1ZXJ5U2VsZWN0b3IoJy5lbC1kaWFsb2cnKTsNCiAgICAgICAgZGlhbG9nSGVhZGVyRWwuc3R5bGUuY3Vyc29yID0gJ21vdmUnOw0KDQogICAgICAgIC8vIOiOt+WPluWOn+acieWxnuaApyBpZSBkb23lhYPntKAuY3VycmVudFN0eWxlIOeBq+eLkOiwt+atjCB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShkb23lhYPntKAsIG51bGwpOw0KICAgICAgICBjb25zdCBzdHkgPSBkcmFnRG9tLmN1cnJlbnRTdHlsZSB8fCB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShkcmFnRG9tLCBudWxsKTsNCg0KICAgICAgICBkaWFsb2dIZWFkZXJFbC5vbm1vdXNlZG93biA9IChlKSA9PiB7DQogICAgICAgICAgLy8g6byg5qCH5oyJ5LiL77yM6K6h566X5b2T5YmN5YWD57Sg6Led56a75Y+v6KeG5Yy655qE6Led56a7DQogICAgICAgICAgY29uc3QgZGlzWCA9IGUuY2xpZW50WCAtIGRpYWxvZ0hlYWRlckVsLm9mZnNldExlZnQ7DQogICAgICAgICAgY29uc3QgZGlzWSA9IGUuY2xpZW50WSAtIGRpYWxvZ0hlYWRlckVsLm9mZnNldFRvcDsNCg0KICAgICAgICAgIC8vIOiOt+WPluWIsOeahOWAvOW4pnB4IOato+WImeWMuemFjeabv+aNog0KICAgICAgICAgIGxldCBzdHlMLCBzdHlUOw0KDQogICAgICAgICAgLy8g5rOo5oSP5ZyoaWXkuK0g56ys5LiA5qyh6I635Y+W5Yiw55qE5YC85Li657uE5Lu26Ieq5bimNTAlIOenu+WKqOS5i+WQjui1i+WAvOS4unB4DQogICAgICAgICAgaWYgKHN0eS5sZWZ0LmluY2x1ZGVzKCclJykpIHsNCiAgICAgICAgICAgIHN0eUwgPSArZG9jdW1lbnQuYm9keS5jbGllbnRXaWR0aCAqICgrc3R5LmxlZnQucmVwbGFjZSgvXCUvZywgJycpIC8gMTAwKTsNCiAgICAgICAgICAgIHN0eVQgPSArZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQgKiAoK3N0eS50b3AucmVwbGFjZSgvXCUvZywgJycpIC8gMTAwKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgc3R5TCA9ICtzdHkubGVmdC5yZXBsYWNlKC9weC9nLCAnJyk7DQogICAgICAgICAgICBzdHlUID0gK3N0eS50b3AucmVwbGFjZSgvcHgvZywgJycpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGRvY3VtZW50Lm9ubW91c2Vtb3ZlID0gZnVuY3Rpb24gKGUpIHsNCiAgICAgICAgICAgIC8vIOmAmui/h+S6i+S7tuWnlOaJmO+8jOiuoeeul+enu+WKqOeahOi3neemuw0KICAgICAgICAgICAgY29uc3QgbCA9IGUuY2xpZW50WCAtIGRpc1g7DQogICAgICAgICAgICBjb25zdCB0ID0gZS5jbGllbnRZIC0gZGlzWTsNCg0KICAgICAgICAgICAgLy8g56e75Yqo5b2T5YmN5YWD57SgDQogICAgICAgICAgICBkcmFnRG9tLnN0eWxlLmxlZnQgPSBgJHtsICsgc3R5TH1weGA7DQogICAgICAgICAgICBkcmFnRG9tLnN0eWxlLnRvcCA9IGAke3QgKyBzdHlUfXB4YDsNCg0KICAgICAgICAgICAgLy8g5bCG5q2k5pe255qE5L2N572u5Lyg5Ye65Y67DQogICAgICAgICAgICAvLyBiaW5kaW5nLnZhbHVlKHt4OmUucGFnZVgseTplLnBhZ2VZfSkNCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgZG9jdW1lbnQub25tb3VzZXVwID0gZnVuY3Rpb24gKGUpIHsNCiAgICAgICAgICAgIGRvY3VtZW50Lm9ubW91c2Vtb3ZlID0gbnVsbDsNCiAgICAgICAgICAgIGRvY3VtZW50Lm9ubW91c2V1cCA9IG51bGw7DQogICAgICAgICAgfTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KDQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5rWL6K+V57uT5p6c6KGo5qC85pWw5o2uDQogICAgICB0ZXN0UmVzdWx0TGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmtYvor5XmlrnmoYjnu4TpgInpobkNCiAgICAgIHRlc3RQbGFuR3JvdXBPcHRpb25zOiBbXSwNCg0KICAgICAgLy8g5Y+C5pWw57uE6YCJ6aG5DQogICAgICBwYXJhbUdyb3VwT3B0aW9uczogW10sDQogICAgICAvLyDov4fmu6TlkI7nmoTlj4LmlbDnu4TpgInpobkNCiAgICAgIGZpbHRlcmVkUGFyYW1Hcm91cE9wdGlvbnM6IFtdLA0KICAgICAgLy8g6YCJ5Lit55qE5Y+C5pWw6K+m5oOFDQogICAgICBzZWxlY3RlZFBhcmFtRGV0YWlsOiBudWxsLA0KICAgICAgLy8g6YCJ5Lit55qE5rWL6K+V5pa55qGI6K+m5oOFDQogICAgICBzZWxlY3RlZFRlc3RQbGFuRGV0YWlsOiBudWxsLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHRlc3RQbGFuQ29kZTogbnVsbCwNCg0KICAgICAgICBncm91cElkOiBudWxsLA0KICAgICAgICBwYXJhbU51bWJlcjogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxOYW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllck5hbWU6IG51bGwsDQogICAgICAgIG1hdGVyaWFsTW9kZWw6IG51bGwsDQogICAgICAgIHByb2Nlc3NUeXBlOiBudWxsLA0KICAgICAgICBwZXJmb3JtYW5jZVR5cGU6IG51bGwsDQogICAgICAgIHRlc3RFcXVpcG1lbnQ6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBncm91cElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPguaVsOe8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGxhbkdyb3VwSWQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5rWL6K+V5pa55qGI57uE5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KDQogICAgICAgIHRlc3RWYWx1ZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrp7pmYXmtYvor5XlgLzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyDor6bmg4Xlr7nor53moYYNCiAgICAgIGRldGFpbERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZGV0YWlsRGF0YToge30sDQogICAgICBkZXRhaWxQYXJhbUl0ZW1zOiBbXSwNCiAgICAgIGRldGFpbFRlc3RQbGFuUGFyYW1zOiBudWxsLA0KDQogICAgICAvLyDliJforr7nva7nm7jlhbMNCiAgICAgIGNvbHVtblNldHRpbmdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNvbHVtbk9wdGlvbnM6IHsNCiAgICAgICAgbWF0ZXJpYWxOYW1lOiAn5p2Q5paZ5ZCN56ewJywNCiAgICAgICAgc3VwcGxpZXJOYW1lOiAn5L6b5bqU5ZWGJywNCiAgICAgICAgbWF0ZXJpYWxNb2RlbDogJ+adkOaWmeWei+WPtycsDQogICAgICAgIHByb2Nlc3NUeXBlOiAn5bel6Im657G75Z6LJywNCiAgICAgICAgcGFyYW1OdW1iZXI6ICflj4LmlbDnvJblj7cnLA0KICAgICAgICBwZXJmb3JtYW5jZVR5cGU6ICfmgKfog73nsbvlnosnLA0KICAgICAgICBwZXJmb3JtYW5jZU5hbWU6ICfmgKfog73lkI3np7AnLA0KDQogICAgICAgIHRlc3RFcXVpcG1lbnQ6ICfmtYvor5Xorr7lpIcnLA0KICAgICAgICBzdXBwbGllckRhdGFzaGVldFZhbDogJ+S+m+W6lOWVhuaVsOaNricsDQogICAgICAgIHRlc3RWYWx1ZTogJ+a1i+ivleWAvCcsDQogICAgICAgIGNyZWF0ZUJ5OiAn5Yib5bu65Lq6JywNCiAgICAgICAgY3JlYXRlVGltZTogJ+WIm+W7uuaXtumXtCcsDQogICAgICAgIHVwZGF0ZUJ5OiAn5pu05paw5Lq6JywNCiAgICAgICAgdXBkYXRlVGltZTogJ+abtOaWsOaXtumXtCcNCiAgICAgIH0sDQogICAgICBzZWxlY3RlZENvbHVtbnM6IFsnbWF0ZXJpYWxOYW1lJywgJ3N1cHBsaWVyTmFtZScsICdtYXRlcmlhbE1vZGVsJywgJ3Byb2Nlc3NUeXBlJywgJ3BhcmFtTnVtYmVyJywgJ3BlcmZvcm1hbmNlVHlwZScsICdwZXJmb3JtYW5jZU5hbWUnLCAndGVzdEVxdWlwbWVudCcsICdzdXBwbGllckRhdGFzaGVldFZhbCcsICd0ZXN0VmFsdWUnLCAnY3JlYXRlQnknLCAnY3JlYXRlVGltZScsICd1cGRhdGVCeScsICd1cGRhdGVUaW1lJ10sDQogICAgICB2aXNpYmxlQ29sdW1uczoge30sDQoNCiAgICAgIC8vIOaQnOe0ouW7uuiuruaVsOaNrg0KICAgICAgcGFyYW1OdW1iZXJTdWdnZXN0aW9uczogW10sDQogICAgICBtYXRlcmlhbE5hbWVTdWdnZXN0aW9uczogW10sDQogICAgICBzdXBwbGllck5hbWVTdWdnZXN0aW9uczogW10sDQogICAgICBtYXRlcmlhbE1vZGVsU3VnZ2VzdGlvbnM6IFtdLA0KICAgICAgcHJvY2Vzc1R5cGVTdWdnZXN0aW9uczogW10sDQogICAgICBwZXJmb3JtYW5jZVR5cGVTdWdnZXN0aW9uczogW10sDQogICAgICB0ZXN0RXF1aXBtZW50U3VnZ2VzdGlvbnM6IFtdLA0KICAgICAgdGVzdFBsYW5Hcm91cFN1Z2dlc3Rpb25zOiBbXSwNCg0KICAgICAgLy8g6ZmE5Lu255u45YWzDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICBhdHRhY2htZW50TGlzdDogW10sDQogICAgICBhdHRhY2htZW50RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2NvbW1vbi91cGxvYWQiLA0KICAgICAgdXBsb2FkSGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0VGVzdFBsYW5Hcm91cE9wdGlvbnMoKTsNCiAgICB0aGlzLmdldFBhcmFtR3JvdXBPcHRpb25zKCk7DQogICAgdGhpcy5sb2FkU3VnZ2VzdGlvbnMoKTsNCiAgICB0aGlzLmluaXRWaXNpYmxlQ29sdW1ucygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWIneWni+WMluWPr+ingeWIlyAqLw0KICAgIGluaXRWaXNpYmxlQ29sdW1ucygpIHsNCiAgICAgIHRoaXMudmlzaWJsZUNvbHVtbnMgPSB7fTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRDb2x1bW5zLmZvckVhY2goY29sID0+IHsNCiAgICAgICAgdGhpcy52aXNpYmxlQ29sdW1uc1tjb2xdID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L295pCc57Si5bu66K6u5pWw5o2uICovDQogICAgbG9hZFN1Z2dlc3Rpb25zKCkgew0KICAgICAgLy8g6I635Y+W5Y+C5pWw57yW5Y+35bu66K6uDQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdwYXJhbU51bWJlcicgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KDQogICAgICAvLyDojrflj5bmnZDmlpnlkI3np7Dlu7rorq4NCiAgICAgIGdldFRlc3RSZXN1bHRPcHRpb25zKHsgdHlwZTogJ21hdGVyaWFsTmFtZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxOYW1lU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCg0KICAgICAgLy8g6I635Y+W5L6b5bqU5ZWG5ZCN56ew5bu66K6uDQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdzdXBwbGllck5hbWUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN1cHBsaWVyTmFtZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQoNCiAgICAgIC8vIOiOt+WPluadkOaWmeWei+WPt+W7uuiurg0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAnbWF0ZXJpYWxNb2RlbCcgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxNb2RlbFN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQoNCiAgICAgIC8vIOiOt+WPluW3peiJuuexu+Wei+W7uuiurg0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAncHJvY2Vzc1R5cGUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnByb2Nlc3NUeXBlU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCg0KICAgICAgLy8g6I635Y+W5oCn6IO957G75Z6L5bu66K6uDQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdwZXJmb3JtYW5jZVR5cGUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBlcmZvcm1hbmNlVHlwZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQoNCiAgICAgIC8vIOiOt+WPlua1i+ivleiuvuWkh+W7uuiurg0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAndGVzdEVxdWlwbWVudCcgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudGVzdEVxdWlwbWVudFN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQoNCiAgICAgIC8vIOiOt+WPlua1i+ivleaWueahiOW7uuiurg0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAncGxhbkNvZGUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnRlc3RQbGFuU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe8luWPt+aQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5UGFyYW1OdW1iZXJTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZyAmJiBzdWdnZXN0aW9ucyAmJiBzdWdnZXN0aW9ucy5sZW5ndGggPiAwKSB7DQogICAgICAgIHN1Z2dlc3Rpb25zID0gdGhpcy5wYXJhbU51bWJlclN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbSAmJiBpdGVtLnZhbHVlICYmIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyB8fCBbXSk7DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnlkI3np7DmkJzntKLlu7rorq4gKi8NCiAgICBxdWVyeU1hdGVyaWFsTmFtZVN1Z2dlc3Rpb25zKHF1ZXJ5U3RyaW5nLCBjYikgew0KICAgICAgbGV0IHN1Z2dlc3Rpb25zID0gdGhpcy5tYXRlcmlhbE5hbWVTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMubWF0ZXJpYWxOYW1lU3VnZ2VzdGlvbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiBpdGVtLnZhbHVlLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihxdWVyeVN0cmluZy50b0xvd2VyQ2FzZSgpKSAhPT0gLTE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY2Ioc3VnZ2VzdGlvbnMpOw0KICAgIH0sDQoNCiAgICAvKiog5L6b5bqU5ZWG5ZCN56ew5pCc57Si5bu66K6uICovDQogICAgcXVlcnlTdXBwbGllck5hbWVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMuc3VwcGxpZXJOYW1lU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnN1cHBsaWVyTmFtZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmeWei+WPt+aQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5TWF0ZXJpYWxNb2RlbFN1Z2dlc3Rpb25zKHF1ZXJ5U3RyaW5nLCBjYikgew0KICAgICAgbGV0IHN1Z2dlc3Rpb25zID0gdGhpcy5tYXRlcmlhbE1vZGVsU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLm1hdGVyaWFsTW9kZWxTdWdnZXN0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyk7DQogICAgfSwNCg0KICAgIC8qKiDlt6XoibrnsbvlnovmkJzntKLlu7rorq4gKi8NCiAgICBxdWVyeVByb2Nlc3NUeXBlU3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLnByb2Nlc3NUeXBlU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnByb2Nlc3NUeXBlU3VnZ2VzdGlvbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiBpdGVtLnZhbHVlLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihxdWVyeVN0cmluZy50b0xvd2VyQ2FzZSgpKSAhPT0gLTE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY2Ioc3VnZ2VzdGlvbnMpOw0KICAgIH0sDQoNCiAgICAvKiog5oCn6IO957G75Z6L5pCc57Si5bu66K6uICovDQogICAgcXVlcnlQZXJmb3JtYW5jZVR5cGVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGVyZm9ybWFuY2VUeXBlU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnBlcmZvcm1hbmNlVHlwZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleiuvuWkh+aQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5VGVzdEVxdWlwbWVudFN1Z2dlc3Rpb25zKHF1ZXJ5U3RyaW5nLCBjYikgew0KICAgICAgbGV0IHN1Z2dlc3Rpb25zID0gdGhpcy50ZXN0RXF1aXBtZW50U3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnRlc3RFcXVpcG1lbnRTdWdnZXN0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyk7DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDnvJblj7fnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVQYXJhbU51bWJlckZvY3VzKCkgew0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAncGFyYW1OdW1iZXInIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBhcmFtTnVtYmVyU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmeWQjeensOeEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZU1hdGVyaWFsTmFtZUZvY3VzKCkgew0KICAgICAgZ2V0VGVzdFJlc3VsdE9wdGlvbnMoeyB0eXBlOiAnbWF0ZXJpYWxOYW1lJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbE5hbWVTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5L6b5bqU5ZWG5ZCN56ew54Sm54K55LqL5Lu2ICovDQogICAgaGFuZGxlU3VwcGxpZXJOYW1lRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdzdXBwbGllck5hbWUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN1cHBsaWVyTmFtZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnlnovlj7fnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVNYXRlcmlhbE1vZGVsRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdtYXRlcmlhbE1vZGVsJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbE1vZGVsU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOW3peiJuuexu+Wei+eEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZVByb2Nlc3NUeXBlRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdwcm9jZXNzVHlwZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHJvY2Vzc1R5cGVTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5oCn6IO957G75Z6L54Sm54K55LqL5Lu2ICovDQogICAgaGFuZGxlUGVyZm9ybWFuY2VUeXBlRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICdwZXJmb3JtYW5jZVR5cGUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBlcmZvcm1hbmNlVHlwZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCg0KICAgIC8qKiDmtYvor5Xorr7lpIfnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVUZXN0RXF1aXBtZW50Rm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICd0ZXN0RXF1aXBtZW50JyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50ZXN0RXF1aXBtZW50U3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleaWueahiOe7hOaQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5VGVzdFBsYW5Hcm91cFN1Z2dlc3Rpb25zKHF1ZXJ5U3RyaW5nLCBjYikgew0KICAgICAgbGV0IHN1Z2dlc3Rpb25zID0gdGhpcy50ZXN0UGxhbkdyb3VwU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcgJiYgc3VnZ2VzdGlvbnMgJiYgc3VnZ2VzdGlvbnMubGVuZ3RoID4gMCkgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMudGVzdFBsYW5Hcm91cFN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbSAmJiBpdGVtLnZhbHVlICYmIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyB8fCBbXSk7DQogICAgfSwNCg0KICAgIC8qKiDmtYvor5XmlrnmoYjnu4TnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVUZXN0UGxhbkdyb3VwRm9jdXMoKSB7DQogICAgICBnZXRUZXN0UmVzdWx0T3B0aW9ucyh7IHR5cGU6ICd0ZXN0UGxhbkdyb3VwJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50ZXN0UGxhbkdyb3VwU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleaWueahiOe7hOmAieaLqeS6i+S7tiAqLw0KICAgIGhhbmRsZVRlc3RQbGFuR3JvdXBTZWxlY3QoaXRlbSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy50ZXN0UGxhbkNvZGUgPSBpdGVtLnZhbHVlOw0KICAgIH0sDQoNCiAgICAvKiog6KGo5Y2V5rWL6K+V5pa55qGI5pS55Y+Y5LqL5Lu2ICovDQogICAgaGFuZGxlRm9ybVRlc3RQbGFuQ2hhbmdlKHZhbHVlKSB7DQogICAgICAvLyDlvZPmtYvor5XmlrnmoYjmlLnlj5jml7bvvIzlj6/ku6Xph43mlrDliqDovb3lj4LmlbDor6bmg4UNCiAgICAgIGlmICh2YWx1ZSAmJiB0aGlzLmZvcm0uZ3JvdXBJZCkgew0KICAgICAgICB0aGlzLmhhbmRsZVBhcmFtR3JvdXBDaGFuZ2UodGhpcy5mb3JtLmdyb3VwSWQpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5rWL6K+V57uT5p6c5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0VGVzdFJlc3VsdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50ZXN0UmVzdWx0TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlua1i+ivleaWueahiOe7hOmAiemhuSAqLw0KICAgIGdldFRlc3RQbGFuR3JvdXBPcHRpb25zKCkgew0KICAgICAgbGlzdFRlc3RQbGFuR3JvdXAoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50ZXN0UGxhbkdyb3VwT3B0aW9ucyA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMudGVzdFBsYW5Hcm91cE9wdGlvbnMgPSBbXTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCg0KDQogICAgLyoqIOWkhOeQhua1i+ivleaWueahiOe7hOWPmOWMliAqLw0KICAgIGhhbmRsZUZvcm1QbGFuR3JvdXBDaGFuZ2UocGxhbkdyb3VwSWQpIHsNCiAgICAgIHRoaXMuZm9ybS50ZXN0UGFyYW1JZCA9IG51bGw7DQogICAgICB0aGlzLnNlbGVjdGVkVGVzdFBsYW5EZXRhaWwgPSBudWxsOw0KICAgICAgdGhpcy50ZXN0UGFyYW1PcHRpb25zID0gW107DQoNCiAgICAgIGlmICghcGxhbkdyb3VwSWQpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDojrflj5bpgInkuK3nmoTmtYvor5XmlrnmoYjnu4Tor6bmg4UNCiAgICAgIGNvbnN0IHNlbGVjdGVkR3JvdXAgPSB0aGlzLnRlc3RQbGFuR3JvdXBPcHRpb25zLmZpbmQoZ3JvdXAgPT4gZ3JvdXAucGxhbkdyb3VwSWQgPT09IHBsYW5Hcm91cElkKTsNCiAgICAgIGlmIChzZWxlY3RlZEdyb3VwKSB7DQogICAgICAgIC8vIOeri+WNs+aYvuekuua1i+ivleaWueahiOe7hOWfuuacrOS/oeaBrw0KICAgICAgICB0aGlzLnNlbGVjdGVkVGVzdFBsYW5EZXRhaWwgPSB7DQogICAgICAgICAgcGxhbkNvZGU6IHNlbGVjdGVkR3JvdXAucGxhbkNvZGUsDQogICAgICAgICAgcGVyZm9ybWFuY2VUeXBlOiBzZWxlY3RlZEdyb3VwLnBlcmZvcm1hbmNlVHlwZSwNCiAgICAgICAgICBwZXJmb3JtYW5jZU5hbWU6IHNlbGVjdGVkR3JvdXAucGVyZm9ybWFuY2VOYW1lLA0KICAgICAgICAgIHRlc3RFcXVpcG1lbnQ6IHNlbGVjdGVkR3JvdXAudGVzdEVxdWlwbWVudCwNCiAgICAgICAgICBwYXJhbU5hbWU6ICcnLA0KICAgICAgICAgIHBhcmFtVmFsdWU6ICcnLA0KICAgICAgICAgIHVuaXQ6ICcnDQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5Yqg6L296K+l5rWL6K+V5pa55qGI57uE5LiL55qE5rWL6K+V5Y+C5pWw6YCJ6aG5DQogICAgICAgIHRoaXMuZ2V0VGVzdFBhcmFtT3B0aW9uc0J5UGxhbkdyb3VwSWQocGxhbkdyb3VwSWQpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5rWL6K+V5Y+C5pWw5Y+Y5YyWICovDQogICAgaGFuZGxlRm9ybVRlc3RQYXJhbUNoYW5nZSh0ZXN0UGFyYW1JZCkgew0KICAgICAgaWYgKCF0ZXN0UGFyYW1JZCB8fCAhdGhpcy5zZWxlY3RlZFRlc3RQbGFuRGV0YWlsKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g6I635Y+W6YCJ5Lit55qE5rWL6K+V5Y+C5pWw6K+m5oOFDQogICAgICBjb25zdCBzZWxlY3RlZFBhcmFtID0gdGhpcy50ZXN0UGFyYW1PcHRpb25zLmZpbmQocGFyYW0gPT4gcGFyYW0udGVzdFBhcmFtSWQgPT09IHRlc3RQYXJhbUlkKTsNCiAgICAgIGlmIChzZWxlY3RlZFBhcmFtKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRUZXN0UGxhbkRldGFpbC5wYXJhbU5hbWUgPSBzZWxlY3RlZFBhcmFtLnBhcmFtTmFtZTsNCiAgICAgICAgdGhpcy5zZWxlY3RlZFRlc3RQbGFuRGV0YWlsLnBhcmFtVmFsdWUgPSBzZWxlY3RlZFBhcmFtLnBhcmFtVmFsdWU7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRUZXN0UGxhbkRldGFpbC51bml0ID0gc2VsZWN0ZWRQYXJhbS51bml0Ow0KICAgICAgfQ0KICAgIH0sDQoNCg0KDQogICAgLyoqIOiOt+WPluWPguaVsOe7hOmAiemhuSAqLw0KICAgIGdldFBhcmFtR3JvdXBPcHRpb25zKCkgew0KICAgICAgbGlzdFByb2Nlc3NQYXJhbUdyb3VwKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1Hcm91cE9wdGlvbnMgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLmZpbHRlcmVkUGFyYW1Hcm91cE9wdGlvbnMgPSByZXNwb25zZS5yb3dzOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmlrnmoYjmlLnlj5jkuovku7YgKi8NCiAgICBoYW5kbGVQbGFuQ2hhbmdlKHZhbHVlKSB7DQogICAgICAvLyDlj6/ku6XmoLnmja7mlrnmoYjov4fmu6Tlj4LmlbDnu4QNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZ3JvdXBJZCA9IG51bGw7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCg0KDQoNCiAgICAvKiog5Y+C5pWw57uE5pS55Y+Y5LqL5Lu2ICovDQogICAgaGFuZGxlUGFyYW1Hcm91cENoYW5nZSh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlKSB7DQogICAgICAgIC8vIOWFiOa4heepuuS5i+WJjeeahOaVsOaNrg0KICAgICAgICB0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWwgPSBudWxsOw0KDQogICAgICAgIC8vIOiOt+WPluWPguaVsOe7hOivpuaDhQ0KICAgICAgICBnZXRQcm9jZXNzUGFyYW1Hcm91cCh2YWx1ZSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlsID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W5Yiw55qE5Y+C5pWw57uE6K+m5oOF77yaJywgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlsKTsNCg0KICAgICAgICAgIC8vIOeri+WNs+iOt+WPluWPguaVsOaYjue7hu+8jOS4jemcgOimgeetieW+hea1i+ivleaWueahiOmAieaLqQ0KICAgICAgICAgIGxpc3RQcm9jZXNzUGFyYW1JdGVtKHsgZ3JvdXBJZDogdmFsdWUgfSkudGhlbihwYXJhbVJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWwpIHsNCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlsLnBhcmFtSXRlbXMgPSBwYXJhbVJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgICAgICAgIC8vIOS4uuavj+S4quWPguaVsOmhuea3u+WKoOaYvuekuuaWh+acrO+8jOWMheWQq+WPguaVsOWAvA0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWwucGFyYW1JdGVtcy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGxldCBkaXNwbGF5VGV4dCA9IGl0ZW0ucGFyYW1OYW1lIHx8ICdOL0EnOw0KICAgICAgICAgICAgICAgIGlmIChpdGVtLnBhcmFtVmFsdWUgIT09IG51bGwgJiYgaXRlbS5wYXJhbVZhbHVlICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgIGRpc3BsYXlUZXh0ICs9IGA6ICR7dGhpcy5mb3JtYXREZWNpbWFsKGl0ZW0ucGFyYW1WYWx1ZSl9YDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKGl0ZW0udW5pdCkgew0KICAgICAgICAgICAgICAgICAgZGlzcGxheVRleHQgKz0gYCAke2l0ZW0udW5pdH1gOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBpdGVtLmRpc3BsYXlUZXh0ID0gZGlzcGxheVRleHQ7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W5Yiw55qE5Y+C5pWw5piO57uG77yaJywgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlsLnBhcmFtSXRlbXMpOw0KDQogICAgICAgICAgICAgIC8vIOW8uuWItuabtOaWsOinhuWbvg0KICAgICAgICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWPguaVsOaYjue7huWksei0pe+8micsIGVycm9yKTsNCiAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWwpIHsNCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFBhcmFtRGV0YWlsLnBhcmFtSXRlbXMgPSBbXTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWPguaVsOe7hOivpuaDheWksei0pe+8micsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkUGFyYW1EZXRhaWwgPSBudWxsOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRQYXJhbURldGFpbCA9IG51bGw7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQoNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgdGVzdFJlc3VsdElkOiBudWxsLA0KICAgICAgICBwbGFuR3JvdXBJZDogbnVsbCwNCiAgICAgICAgZ3JvdXBJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJEYXRhc2hlZXRWYWw6IG51bGwsDQogICAgICAgIHRlc3RWYWx1ZTogbnVsbCwNCiAgICAgICAgYXR0YWNobWVudHM6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRQYXJhbURldGFpbCA9IG51bGw7DQogICAgICB0aGlzLnNlbGVjdGVkVGVzdFBsYW5EZXRhaWwgPSBudWxsOw0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS50ZXN0UmVzdWx0SWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQoNCiAgICAvLyDooYzngrnlh7vpgInmi6kNCiAgICBoYW5kbGVSb3dDbGljayhyb3cpIHsNCiAgICAgIHRoaXMuJHJlZnMubXVsdGlwbGVUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93KTsNCiAgICB9LA0KDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOa1i+ivlee7k+aenCI7DQogICAgfSwNCg0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgdGVzdFJlc3VsdElkID0gcm93LnRlc3RSZXN1bHRJZCB8fCB0aGlzLmlkc1swXTsNCiAgICAgIGdldFRlc3RSZXN1bHQodGVzdFJlc3VsdElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ+e8lui+keiOt+WPlueahOaVsOaNru+8micsIHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDop6PmnpDpmYTku7bmlbDmja4NCiAgICAgICAgdGhpcy5maWxlTGlzdCA9IHRoaXMucGFyc2VBdHRhY2htZW50cyhyZXNwb25zZS5kYXRhLmF0dGFjaG1lbnRzKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+e8lui+keaXtuino+aekOeahOaWh+S7tuWIl+ihqO+8micsIHRoaXMuZmlsZUxpc3QpOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuea1i+ivlee7k+aenCI7DQoNCiAgICAgICAgLy8g6Kem5Y+R5Y+C5pWw57uE5pS55Y+Y5LqL5Lu25Lul5Yqg6L295p2Q5paZ5Y+C5pWw6K+m5oOFDQogICAgICAgIGlmICh0aGlzLmZvcm0uZ3JvdXBJZCkgew0KICAgICAgICAgIHRoaXMuaGFuZGxlUGFyYW1Hcm91cENoYW5nZSh0aGlzLmZvcm0uZ3JvdXBJZCk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDop6blj5HmtYvor5XmlrnmoYjnu4TmlLnlj5jkuovku7bku6XliqDovb3mtYvor5XmlrnmoYjlj4LmlbDor6bmg4UNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuR3JvdXBJZCkgew0KICAgICAgICAgIHRoaXMuaGFuZGxlRm9ybVBsYW5Hcm91cENoYW5nZSh0aGlzLmZvcm0ucGxhbkdyb3VwSWQpOw0KDQogICAgICAgICAgLy8g5aaC5p6c5pyJ5rWL6K+V5Y+C5pWwSUTvvIzlnKjliqDovb3lrozmtYvor5Xlj4LmlbDpgInpobnlkI7orr7nva7pgInkuK3nmoTlj4LmlbANCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnRlc3RQYXJhbUlkKSB7DQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlRm9ybVRlc3RQYXJhbUNoYW5nZSh0aGlzLmZvcm0udGVzdFBhcmFtSWQpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIHRoaXMuZGV0YWlsRGF0YSA9IHJvdzsNCiAgICAgIHRoaXMuZGV0YWlsUGFyYW1JdGVtcyA9IFtdOw0KICAgICAgdGhpcy5kZXRhaWxUZXN0UGxhblBhcmFtcyA9IG51bGw7DQoNCiAgICAgIC8vIOWmguaenOacieWPguaVsOe7hElE77yM6I635Y+W5Y+C5pWw5piO57uGDQogICAgICBpZiAocm93Lmdyb3VwSWQpIHsNCiAgICAgICAgbGlzdFByb2Nlc3NQYXJhbUl0ZW0oeyBncm91cElkOiByb3cuZ3JvdXBJZCB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmRldGFpbFBhcmFtSXRlbXMgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Y+C5pWw5piO57uG5aSx6LSl77yaJywgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuZGV0YWlsUGFyYW1JdGVtcyA9IFtdOw0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5rWL6K+V5pa55qGI57uESUTvvIzojrflj5bmtYvor5XmlrnmoYjlj4LmlbDkv6Hmga8NCiAgICAgIGlmIChyb3cucGxhbkdyb3VwSWQpIHsNCiAgICAgICAgLy8g5LuO5b2T5YmN55qE5rWL6K+V5pa55qGI57uE6YCJ6aG55Lit5p+l5om+5a+55bqU55qE5pa55qGI5L+h5oGvDQogICAgICAgIGNvbnN0IHBsYW5Hcm91cCA9IHRoaXMudGVzdFBsYW5Hcm91cE9wdGlvbnMuZmluZChncm91cCA9PiBncm91cC5wbGFuR3JvdXBJZCA9PT0gcm93LnBsYW5Hcm91cElkKTsNCiAgICAgICAgaWYgKHBsYW5Hcm91cCkgew0KICAgICAgICAgIC8vIOiOt+WPlua1i+ivleaWueahiOe7hOS4i+eahOaJgOaciea1i+ivleWPguaVsA0KICAgICAgICAgIGxpc3RUZXN0UGFyYW1JdGVtKHsgcGxhbkdyb3VwSWQ6IHJvdy5wbGFuR3JvdXBJZCB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuZGV0YWlsVGVzdFBsYW5QYXJhbXMgPSB7DQogICAgICAgICAgICAgIHBsYW5Db2RlOiBwbGFuR3JvdXAucGxhbkNvZGUsDQogICAgICAgICAgICAgIHBlcmZvcm1hbmNlVHlwZTogcGxhbkdyb3VwLnBlcmZvcm1hbmNlVHlwZSwNCiAgICAgICAgICAgICAgcGVyZm9ybWFuY2VOYW1lOiBwbGFuR3JvdXAucGVyZm9ybWFuY2VOYW1lLA0KICAgICAgICAgICAgICB0ZXN0RXF1aXBtZW50OiBwbGFuR3JvdXAudGVzdEVxdWlwbWVudCwNCiAgICAgICAgICAgICAgdGVzdFBhcmFtczogcmVzcG9uc2Uucm93cyB8fCBbXQ0KICAgICAgICAgICAgfTsNCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmtYvor5XmlrnmoYjlj4LmlbDlpLHotKXvvJonLCBlcnJvcik7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAvLyDlsIbpmYTku7bliJfooajovazmjaLkuLrpgJflj7fliIbpmpTnmoRVUkzlrZfnrKbkuLINCiAgICAgICAgICBpZiAodGhpcy5maWxlTGlzdCAmJiB0aGlzLmZpbGVMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5hdHRhY2htZW50cyA9IHRoaXMuZmlsZUxpc3QubWFwKGZpbGUgPT4gZmlsZS51cmwpLmpvaW4oJywnKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRzID0gJyc7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8gdGVzdFBsYW5JZOW3sue7j+ebtOaOpemAieaLqe+8jOaXoOmcgOi9rOaNog0KDQogICAgICAgICAgLy8g6K6+572u5Yib5bu65Lq65ZKM5pu05paw5Lq6DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS50ZXN0UmVzdWx0SWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgLy8g5pu05paw5pON5L2c77yM6K6+572u5pu05paw5Lq6DQogICAgICAgICAgICB0aGlzLmZvcm0udXBkYXRlQnkgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWU7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaWsOWinuaTjeS9nO+8jOiuvue9ruWIm+W7uuS6ug0KICAgICAgICAgICAgdGhpcy5mb3JtLmNyZWF0ZUJ5ID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTnmoTooajljZXmlbDmja7vvJonLCB0aGlzLmZvcm0pOw0KDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS50ZXN0UmVzdWx0SWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlVGVzdFJlc3VsdCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFRlc3RSZXN1bHQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCB0ZXN0UmVzdWx0SWRzID0gcm93LnRlc3RSZXN1bHRJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOa1i+ivlee7k+aenOe8luWPt+S4uiInICsgdGVzdFJlc3VsdElkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFRlc3RSZXN1bHQodGVzdFJlc3VsdElkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWF0ZXJpYWwvdGVzdFJlc3VsdC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGB0ZXN0X3Jlc3VsdF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCk7DQogICAgfSwNCg0KICAgIC8qKiDliJforr7nva4gKi8NCiAgICBoYW5kbGVDb2x1bW5TZXR0aW5nKCkgew0KICAgICAgdGhpcy5jb2x1bW5TZXR0aW5nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDliJforr7nva7noa7orqQgKi8NCiAgICBoYW5kbGVDb2x1bW5Db25maXJtKCkgew0KICAgICAgdGhpcy52aXNpYmxlQ29sdW1ucyA9IHt9Ow0KICAgICAgdGhpcy5zZWxlY3RlZENvbHVtbnMuZm9yRWFjaChjb2wgPT4gew0KICAgICAgICB0aGlzLnZpc2libGVDb2x1bW5zW2NvbF0gPSB0cnVlOw0KICAgICAgfSk7DQogICAgICB0aGlzLmNvbHVtblNldHRpbmdWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8qKiDpmYTku7bkuIrkvKDmiJDlip8gKi8NCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgY29uc29sZS5sb2coJ+S4iuS8oOaIkOWKn+Wbnuiwg++8micsIHsgcmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0IH0pOw0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAvLyDnoa7kv51maWxlTGlzdOaYr+aVsOe7hA0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmaWxlTGlzdCkpIHsNCiAgICAgICAgICB0aGlzLmZpbGVMaXN0ID0gZmlsZUxpc3QubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgICAgIHVybDogaXRlbS5yZXNwb25zZSA/IGl0ZW0ucmVzcG9uc2UudXJsIDogaXRlbS51cmwsDQogICAgICAgICAgICBzaXplOiB0aGlzLmZvcm1hdEZpbGVTaXplKGl0ZW0uc2l6ZSB8fCBpdGVtLnJhdz8uc2l6ZSksDQogICAgICAgICAgICB1aWQ6IGl0ZW0udWlkLA0KICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcignZmlsZUxpc3TkuI3mmK/mlbDnu4TvvJonLCBmaWxlTGlzdCk7DQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS4iuS8oOaIkOWKnyIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnIHx8ICLkuIrkvKDlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOmZhOS7tuenu+mZpCAqLw0KICAgIGhhbmRsZUZpbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfpmYTku7bnp7vpmaTlm57osIPvvJonLCB7IGZpbGUsIGZpbGVMaXN0IH0pOw0KICAgICAgLy8g56Gu5L+dZmlsZUxpc3TmmK/mlbDnu4QNCiAgICAgIGlmIChBcnJheS5pc0FycmF5KGZpbGVMaXN0KSkgew0KICAgICAgICB0aGlzLmZpbGVMaXN0ID0gZmlsZUxpc3QubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsDQogICAgICAgICAgdXJsOiBpdGVtLnJlc3BvbnNlID8gaXRlbS5yZXNwb25zZS51cmwgOiBpdGVtLnVybCwNCiAgICAgICAgICBzaXplOiB0aGlzLmZvcm1hdEZpbGVTaXplKGl0ZW0uc2l6ZSB8fCBpdGVtLnJhdz8uc2l6ZSksDQogICAgICAgICAgdWlkOiBpdGVtLnVpZCwNCiAgICAgICAgICBzdGF0dXM6IGl0ZW0uc3RhdHVzIHx8ICdzdWNjZXNzJw0KICAgICAgICB9KSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmVycm9yKCdmaWxlTGlzdOS4jeaYr+aVsOe7hO+8micsIGZpbGVMaXN0KTsNCiAgICAgICAgdGhpcy5maWxlTGlzdCA9IFtdOw0KICAgICAgfQ0KICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6ZmE5Lu25Yig6Zmk5oiQ5YqfIik7DQogICAgfSwNCg0KICAgIC8qKiDpmYTku7bkuIrkvKDliY3mo4Dmn6UgKi8NCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTA7DQogICAgICBpZiAoIWlzTHQxME0pIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzTHQxME07DQogICAgfSwNCg0KICAgIC8qKiDmn6XnnIvpmYTku7YgKi8NCiAgICBoYW5kbGVWaWV3QXR0YWNobWVudHMoYXR0YWNobWVudHMpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmn6XnnIvpmYTku7booqvosIPnlKjvvIzpmYTku7bmlbDmja7vvJonLCBhdHRhY2htZW50cywgJ+exu+Wei++8micsIHR5cGVvZiBhdHRhY2htZW50cyk7DQoNCiAgICAgIHRoaXMuYXR0YWNobWVudExpc3QgPSBbXTsNCg0KICAgICAgaWYgKCFhdHRhY2htZW50cykgew0KICAgICAgICBjb25zb2xlLmxvZygn6ZmE5Lu25pWw5o2u5Li656m6Jyk7DQogICAgICAgIHRoaXMuYXR0YWNobWVudERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICh0eXBlb2YgYXR0YWNobWVudHMgPT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgY29uc3QgdHJpbW1lZCA9IGF0dGFjaG1lbnRzLnRyaW0oKTsNCiAgICAgICAgICBpZiAoIXRyaW1tZWQpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfpmYTku7blrZfnrKbkuLLkuLrnqbonKTsNCiAgICAgICAgICAgIHRoaXMuYXR0YWNobWVudERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT07moLzlvI8NCiAgICAgICAgICBpZiAodHJpbW1lZC5zdGFydHNXaXRoKCdbJykgJiYgdHJpbW1lZC5lbmRzV2l0aCgnXScpKSB7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHRyaW1tZWQpOw0KICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShwYXJzZWQpKSB7DQogICAgICAgICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IHBhcnNlZC5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lIHx8IGDpmYTku7Yke2luZGV4ICsgMX1gLA0KICAgICAgICAgICAgICAgICAgdXJsOiBpdGVtLnVybCB8fCBpdGVtLA0KICAgICAgICAgICAgICAgICAgc2l6ZTogaXRlbS5zaXplIHx8ICfmnKrnn6XlpKflsI8nDQogICAgICAgICAgICAgICAgfSkpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChqc29uRXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdKU09O6Kej5p6Q5aSx6LSl77yM5bCd6K+V5oyJ6YCX5Y+35YiG5Ymy77yaJywganNvbkVycm9yKTsNCiAgICAgICAgICAgICAgLy8g5oyJ6YCX5Y+35YiG5Ymy5aSE55CGDQogICAgICAgICAgICAgIHRoaXMuYXR0YWNobWVudExpc3QgPSB0cmltbWVkLnNwbGl0KCcsJykuZmlsdGVyKHVybCA9PiB1cmwudHJpbSgpKS5tYXAoKHVybCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCBjbGVhblVybCA9IHVybC50cmltKCk7DQogICAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBjbGVhblVybC5zdWJzdHJpbmcoY2xlYW5VcmwubGFzdEluZGV4T2YoJy8nKSArIDEpIHx8IGDpmYTku7Yke2luZGV4ICsgMX1gOw0KICAgICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgICAgIHVybDogY2xlYW5VcmwsDQogICAgICAgICAgICAgICAgICBzaXplOiAn5pyq55+l5aSn5bCPJw0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmjInpgJflj7fliIblibLlpITnkIYNCiAgICAgICAgICAgIHRoaXMuYXR0YWNobWVudExpc3QgPSB0cmltbWVkLnNwbGl0KCcsJykuZmlsdGVyKHVybCA9PiB1cmwudHJpbSgpKS5tYXAoKHVybCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY2xlYW5VcmwgPSB1cmwudHJpbSgpOw0KICAgICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGNsZWFuVXJsLnN1YnN0cmluZyhjbGVhblVybC5sYXN0SW5kZXhPZignLycpICsgMSkgfHwgYOmZhOS7tiR7aW5kZXggKyAxfWA7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiBjbGVhblVybCwNCiAgICAgICAgICAgICAgICBzaXplOiAn5pyq55+l5aSn5bCPJw0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoYXR0YWNobWVudHMpKSB7DQogICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IGF0dGFjaG1lbnRzLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUgfHwgYOmZhOS7tiR7aW5kZXggKyAxfWAsDQogICAgICAgICAgICB1cmw6IGl0ZW0udXJsIHx8IGl0ZW0sDQogICAgICAgICAgICBzaXplOiBpdGVtLnNpemUgfHwgJ+acquefpeWkp+WwjycNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOmZhOS7tuaVsOaNruaXtuWPkeeUn+mUmeivr++8micsIGVycm9yKTsNCiAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IFtdOw0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn6Kej5p6Q5ZCO55qE6ZmE5Lu25YiX6KGo77yaJywgdGhpcy5hdHRhY2htZW50TGlzdCk7DQogICAgICB0aGlzLmF0dGFjaG1lbnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOS4i+i9vemZhOS7tiAqLw0KICAgIGRvd25sb2FkQXR0YWNobWVudCh1cmwsIGZpbGVOYW1lKSB7DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lOw0KICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7DQogICAgfSwNCg0KICAgIC8qKiDop6PmnpDpmYTku7bmlbDmja4gKi8NCiAgICBwYXJzZUF0dGFjaG1lbnRzKGF0dGFjaG1lbnRzKSB7DQogICAgICBjb25zb2xlLmxvZygn6Kej5p6Q6ZmE5Lu25pWw5o2u77yaJywgYXR0YWNobWVudHMsICfnsbvlnovvvJonLCB0eXBlb2YgYXR0YWNobWVudHMpOw0KICAgICAgaWYgKCFhdHRhY2htZW50cykgew0KICAgICAgICByZXR1cm4gW107DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWmguaenOW3sue7j+aYr+aVsOe7hO+8jOebtOaOpei/lOWbng0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShhdHRhY2htZW50cykpIHsNCiAgICAgICAgICByZXR1cm4gYXR0YWNobWVudHMubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSB8fCBg6ZmE5Lu2JHtpbmRleCArIDF9YCwNCiAgICAgICAgICAgIHVybDogaXRlbS51cmwgfHwgaXRlbSwNCiAgICAgICAgICAgIHVpZDogaXRlbS51aWQgfHwgRGF0ZS5ub3coKSArIGluZGV4LA0KICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpoLmnpzmmK/lrZfnrKbkuLLvvIzlsJ3or5Xop6PmnpANCiAgICAgICAgaWYgKHR5cGVvZiBhdHRhY2htZW50cyA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICBjb25zdCB0cmltbWVkID0gYXR0YWNobWVudHMudHJpbSgpOw0KICAgICAgICAgIGlmICghdHJpbW1lZCkgew0KICAgICAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT07moLzlvI8NCiAgICAgICAgICBpZiAodHJpbW1lZC5zdGFydHNXaXRoKCdbJykgJiYgdHJpbW1lZC5lbmRzV2l0aCgnXScpKSB7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHRyaW1tZWQpOw0KICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShwYXJzZWQpKSB7DQogICAgICAgICAgICAgICAgcmV0dXJuIHBhcnNlZC5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lIHx8IGDpmYTku7Yke2luZGV4ICsgMX1gLA0KICAgICAgICAgICAgICAgICAgdXJsOiBpdGVtLnVybCB8fCBpdGVtLA0KICAgICAgICAgICAgICAgICAgdWlkOiBpdGVtLnVpZCB8fCBEYXRlLm5vdygpICsgaW5kZXgsDQogICAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICAgIH0pKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUud2FybignSlNPTuino+aekOWksei0pe+8jOWwneivleaMiemAl+WPt+WIhuWJsu+8micsIGpzb25FcnJvcik7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5oyJ6YCX5Y+35YiG5Ymy5aSE55CGDQogICAgICAgICAgY29uc3QgdXJscyA9IHRyaW1tZWQuc3BsaXQoJywnKS5maWx0ZXIodXJsID0+IHVybC50cmltKCkpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCfliIblibLlkI7nmoRVUkzliJfooajvvJonLCB1cmxzKTsNCiAgICAgICAgICByZXR1cm4gdXJscy5tYXAoKHVybCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGNsZWFuVXJsID0gdXJsLnRyaW0oKTsNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gY2xlYW5Vcmwuc3Vic3RyaW5nKGNsZWFuVXJsLmxhc3RJbmRleE9mKCcvJykgKyAxKSB8fCBg6ZmE5Lu2JHtpbmRleCArIDF9YDsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICB1cmw6IGNsZWFuVXJsLA0KICAgICAgICAgICAgICB1aWQ6IERhdGUubm93KCkgKyBpbmRleCwNCiAgICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOmZhOS7tuaVsOaNruaXtuWPkeeUn+mUmeivr++8micsIGVycm9yKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFtdOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5paH5Lu25aSn5bCPICovDQogICAgZm9ybWF0RmlsZVNpemUoc2l6ZSkgew0KICAgICAgaWYgKCFzaXplKSByZXR1cm4gJ+acquefpeWkp+Wwjyc7DQogICAgICBjb25zdCB1bml0cyA9IFsnQicsICdLQicsICdNQicsICdHQiddOw0KICAgICAgbGV0IGluZGV4ID0gMDsNCiAgICAgIHdoaWxlIChzaXplID49IDEwMjQgJiYgaW5kZXggPCB1bml0cy5sZW5ndGggLSAxKSB7DQogICAgICAgIHNpemUgLz0gMTAyNDsNCiAgICAgICAgaW5kZXgrKzsNCiAgICAgIH0NCiAgICAgIHJldHVybiBNYXRoLnJvdW5kKHNpemUgKiAxMDApIC8gMTAwICsgJyAnICsgdW5pdHNbaW5kZXhdOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5bCP5pWwICovDQogICAgZm9ybWF0RGVjaW1hbCh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQgfHwgdmFsdWUgPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnLSc7DQogICAgICB9DQogICAgICBjb25zdCBudW0gPSBwYXJzZUZsb2F0KHZhbHVlKTsNCiAgICAgIGlmIChpc05hTihudW0pKSB7DQogICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgIH0NCiAgICAgIC8vIOWmguaenOaYr+aVtOaVsO+8jOebtOaOpei/lOWbnu+8jOS4jea3u+WKoC4wMA0KICAgICAgaWYgKG51bSAlIDEgPT09IDApIHsNCiAgICAgICAgcmV0dXJuIG51bS50b1N0cmluZygpOw0KICAgICAgfQ0KICAgICAgLy8g5L+d55WZ5Lik5L2N5bCP5pWwDQogICAgICByZXR1cm4gbnVtLnRvRml4ZWQoMik7DQogICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5LiL6L296ZmE5Lu2ICovDQogICAgZG93bmxvYWRBdHRhY2htZW50KHVybCwgbmFtZSkgew0KICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsNCiAgICAgIGxpbmsuaHJlZiA9IHVybDsNCiAgICAgIGxpbmsuZG93bmxvYWQgPSBuYW1lOw0KICAgICAgbGluay5jbGljaygpOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5paH5Lu25aSn5bCPICovDQogICAgZm9ybWF0RmlsZVNpemUoc2l6ZSkgew0KICAgICAgaWYgKHNpemUgPCAxMDI0KSB7DQogICAgICAgIHJldHVybiBzaXplICsgJyBCJzsNCiAgICAgIH0gZWxzZSBpZiAoc2l6ZSA8IDEwMjQgKiAxMDI0KSB7DQogICAgICAgIHJldHVybiAoc2l6ZSAvIDEwMjQpLnRvRml4ZWQoMikgKyAnIEtCJzsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAoc2l6ZSAvIDEwMjQgLyAxMDI0KS50b0ZpeGVkKDIpICsgJyBNQic7DQogICAgICB9DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6nBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/testResult", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-edit-outline\"></i>\r\n        <span>测试结果数据录入</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📊 录入和管理测试结果数据，支持多维度筛选和批量操作</p>\r\n        <el-alert\r\n          title=\"使用提示：先选择筛选条件，然后点击查询按钮查看数据，支持列设置自定义显示\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"result-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-edit-outline\"></i>\r\n          <span class=\"header-title\">测试结果管理</span>\r\n          <el-badge :value=\"total\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAdd\">\r\n            <span>新增结果</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"multiple\" @click=\"handleDelete\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExport\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"info\" icon=\"el-icon-setting\" size=\"small\" @click=\"handleColumnSetting\" class=\"column-setting-btn\">\r\n            <span>列设置</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\" class=\"search-form\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramNumber\"\r\n                  :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n                  placeholder=\"请输入参数编号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleParamNumberFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-tickets\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试方案组\" prop=\"testPlanCode\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testPlanCode\"\r\n                  :fetch-suggestions=\"queryTestPlanGroupSuggestions\"\r\n                  placeholder=\"请输入测试方案组\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestPlanGroupFocus\"\r\n                  @select=\"handleTestPlanGroupSelect\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-document\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试参数\" prop=\"paramName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.paramName\"\r\n                  :fetch-suggestions=\"queryTestParamSuggestions\"\r\n                  placeholder=\"请输入测试参数\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestParamFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-data-line\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialName\"\r\n                  :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n                  placeholder=\"请输入材料名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.supplierName\"\r\n                  :fetch-suggestions=\"querySupplierNameSuggestions\"\r\n                  placeholder=\"请输入供应商名称\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleSupplierNameFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-office-building\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.materialModel\"\r\n                  :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n                  placeholder=\"请输入材料型号\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleMaterialModelFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-goods\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.processType\"\r\n                  :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n                  placeholder=\"请输入工艺类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleProcessTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-setting\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.performanceType\"\r\n                  :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n                  placeholder=\"请输入性能类型\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handlePerformanceTypeFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-lightning\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n                <el-autocomplete\r\n                  v-model=\"queryParams.testEquipment\"\r\n                  :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n                  placeholder=\"请输入测试设备\"\r\n                  clearable\r\n                  style=\"width: 100%;\"\r\n                  @focus=\"handleTestEquipmentFocus\"\r\n                  :trigger-on-focus=\"true\"\r\n                  prefix-icon=\"el-icon-cpu\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\" style=\"text-align: right;\">\r\n              <el-form-item>\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\" class=\"search-btn\">\r\n                  <span>搜索</span>\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\" class=\"reset-btn\">\r\n                  <span>重置</span>\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"testResultList\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"multipleTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n\r\n        <!-- 动态列显示 -->\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialName\"\r\n          prop=\"materialName\"\r\n          label=\"材料名称\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierName\"\r\n          prop=\"supplierName\"\r\n          label=\"供应商\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.materialModel\"\r\n          prop=\"materialModel\"\r\n          label=\"材料型号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.processType\"\r\n          prop=\"processType\"\r\n          label=\"工艺类型\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.paramNumber\"\r\n          prop=\"paramNumber\"\r\n          label=\"参数编号\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceType\"\r\n          prop=\"performanceType\"\r\n          label=\"性能类型\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.performanceName\"\r\n          prop=\"performanceName\"\r\n          label=\"性能名称\"\r\n          min-width=\"150\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testEquipment\"\r\n          prop=\"testEquipment\"\r\n          label=\"测试设备\"\r\n          min-width=\"120\"\r\n          show-overflow-tooltip\r\n        />\r\n\r\n        <el-table-column\r\n          v-if=\"visibleColumns.supplierDatasheetVal\"\r\n          prop=\"supplierDatasheetVal\"\r\n          label=\"供应商数据\"\r\n          width=\"120\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.supplierDatasheetVal) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.testValue\"\r\n          prop=\"testValue\"\r\n          label=\"测试值\"\r\n          width=\"100\"\r\n          align=\"right\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDecimal(scope.row.testValue) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createBy\"\r\n          prop=\"createBy\"\r\n          label=\"创建人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.createTime\"\r\n          prop=\"createTime\"\r\n          label=\"创建时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateBy\"\r\n          prop=\"updateBy\"\r\n          label=\"更新人\"\r\n          width=\"100\"\r\n        />\r\n        <el-table-column\r\n          v-if=\"visibleColumns.updateTime\"\r\n          prop=\"updateTime\"\r\n          label=\"更新时间\"\r\n          width=\"160\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body v-drag>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数编号\" prop=\"groupId\">\r\n              <el-select\r\n                v-model=\"form.groupId\"\r\n                placeholder=\"请选择参数编号\"\r\n                style=\"width: 100%;\"\r\n                filterable\r\n                @change=\"handleParamGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in filteredParamGroupOptions\"\r\n                  :key=\"group.groupId\"\r\n                  :label=\"group.paramNumber + ' - ' + (group.materialName || '') + ' (' + (group.supplierName || '') + ')'\"\r\n                  :value=\"group.groupId\"\r\n                >\r\n                  <div style=\"display: flex; justify-content: space-between;\">\r\n                    <span>{{ group.paramNumber || 'N/A' }}</span>\r\n                    <span style=\"color: #8492a6; font-size: 13px;\">{{ (group.materialName || 'N/A') + ' - ' + (group.supplierName || 'N/A') }}</span>\r\n                  </div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试方案组\" prop=\"planGroupId\">\r\n              <el-select\r\n                v-model=\"form.planGroupId\"\r\n                placeholder=\"请选择测试方案组\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                @change=\"handleFormPlanGroupChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"group in testPlanGroupOptions\"\r\n                  :key=\"group.planGroupId\"\r\n                  :label=\"group.planCode + ' - ' + group.performanceName\"\r\n                  :value=\"group.planGroupId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试参数\" prop=\"testParamId\">\r\n              <el-select\r\n                v-model=\"form.testParamId\"\r\n                placeholder=\"请选择测试参数\"\r\n                style=\"width: 100%;\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"!form.planGroupId\"\r\n                @change=\"handleFormTestParamChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"param in testParamOptions\"\r\n                  :key=\"param.testParamId\"\r\n                  :label=\"param.paramName + (param.paramValue ? ' (' + param.paramValue + param.unit + ')' : '')\"\r\n                  :value=\"param.testParamId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- 空列，保持布局 -->\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 工艺参数信息展示 -->\r\n        <el-card v-if=\"selectedParamDetail\" class=\"param-detail-card\" style=\"margin-bottom: 15px;\" :key=\"selectedParamDetail.groupId + '_' + (selectedParamDetail.paramItems ? selectedParamDetail.paramItems.length : 0)\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-setting\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">工艺参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"材料名称\">{{ selectedParamDetail.materialName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"供应商\">{{ selectedParamDetail.supplierName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"工艺类型\">{{ selectedParamDetail.processType || '-' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n          <div v-if=\"selectedParamDetail.paramItems && selectedParamDetail.paramItems.length > 0\" style=\"margin-top: 15px;\">\r\n            <div style=\"margin-bottom: 10px; font-weight: bold; color: #606266;\">\r\n              <i class=\"el-icon-data-line\"></i>\r\n              <span style=\"margin-left: 5px;\">参数明细：</span>\r\n            </div>\r\n            <el-table :data=\"selectedParamDetail.paramItems\" size=\"mini\" style=\"width: 100%;\" border>\r\n              <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n              <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n            </el-table>\r\n          </div>\r\n          <div v-else style=\"margin-top: 15px; text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\"></i>\r\n            <span style=\"margin-left: 5px;\">暂无参数明细信息</span>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 测试方案参数信息展示 -->\r\n        <el-card v-if=\"selectedTestPlanDetail\" class=\"test-plan-detail-card\" style=\"margin-bottom: 15px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <i class=\"el-icon-document\"></i>\r\n            <span style=\"font-weight: bold; margin-left: 8px;\">测试方案参数信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border size=\"small\">\r\n            <el-descriptions-item label=\"方案编号\">{{ selectedTestPlanDetail.planCode || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能类型\">{{ selectedTestPlanDetail.performanceType || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性能名称\">{{ selectedTestPlanDetail.performanceName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"测试设备\">{{ selectedTestPlanDetail.testEquipment || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数名称\">{{ selectedTestPlanDetail.paramName || '-' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"参数数值\">{{ selectedTestPlanDetail.paramValue || '-' }} {{ selectedTestPlanDetail.unit || '' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"供应商数据\" prop=\"supplierDatasheetVal\">\r\n              <el-input v-model=\"form.supplierDatasheetVal\" placeholder=\"请输入供应商Datasheet值\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"实际测试值\" prop=\"testValue\">\r\n              <el-input-number v-model=\"form.testValue\" :precision=\"6\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"upload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"fileList\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :before-upload=\"beforeUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" clearable />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"测试结果详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body v-drag>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"材料名称\">{{ detailData.materialName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供应商\">{{ detailData.supplierName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"材料型号\">{{ detailData.materialModel || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"工艺类型\">{{ detailData.processType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"参数编号\">{{ detailData.paramNumber || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能类型\">{{ detailData.performanceType || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性能名称\">{{ detailData.performanceName || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试设备\">{{ detailData.testEquipment || '-' }}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"供应商数据\">{{ formatDecimal(detailData.supplierDatasheetVal) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"测试值\">{{ formatDecimal(detailData.testValue) }} {{ detailData.paramUnit || '' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建人\">{{ detailData.createBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建时间\">{{ parseTime(detailData.createTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新人\">{{ detailData.updateBy || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"更新时间\">{{ parseTime(detailData.updateTime) || '-' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ detailData.remark || '-' }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 参数明细信息 -->\r\n      <el-card v-if=\"detailParamItems && detailParamItems.length > 0\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">材料参数明细信息</span>\r\n        </div>\r\n        <el-table :data=\"detailParamItems\" style=\"width: 100%\" size=\"small\">\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"120\" />\r\n          <el-table-column prop=\"paramValue\" label=\"参数值\" width=\"100\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ formatDecimal(scope.row.paramValue) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n        </el-table>\r\n      </el-card>\r\n\r\n      <!-- 测试方案参数信息 -->\r\n      <el-card v-if=\"detailTestPlanParams\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">测试方案参数信息</span>\r\n        </div>\r\n        <el-descriptions :column=\"2\" border size=\"small\" style=\"margin-bottom: 20px;\">\r\n          <el-descriptions-item label=\"方案编号\">{{ detailTestPlanParams.planCode || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能类型\">{{ detailTestPlanParams.performanceType || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"性能名称\">{{ detailTestPlanParams.performanceName || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"测试设备\">{{ detailTestPlanParams.testEquipment || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 测试参数列表 -->\r\n        <div v-if=\"detailTestPlanParams.testParams && detailTestPlanParams.testParams.length > 0\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">📋 测试参数明细</h4>\r\n          <el-table :data=\"detailTestPlanParams.testParams\" size=\"small\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n            <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip />\r\n            <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.paramValue || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.unit || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ scope.row.remark || '-' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        <div v-else style=\"text-align: center; color: #909399; padding: 20px;\">\r\n          <i class=\"el-icon-info\"></i> 该测试方案组暂无测试参数\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 附件信息 -->\r\n      <el-card v-if=\"detailData.attachments\" style=\"margin-top: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-weight: bold;\">附件信息</span>\r\n        </div>\r\n        <el-button size=\"mini\" type=\"text\" @click=\"handleViewAttachments(detailData.attachments)\">查看附件</el-button>\r\n      </el-card>\r\n    </el-dialog>\r\n\r\n    <!-- 列设置对话框 -->\r\n    <el-dialog title=\"列设置\" :visible.sync=\"columnSettingVisible\" width=\"500px\" append-to-body v-drag>\r\n      <el-checkbox-group v-model=\"selectedColumns\">\r\n        <el-row>\r\n          <el-col :span=\"12\" v-for=\"(label, key) in columnOptions\" :key=\"key\">\r\n            <el-checkbox :label=\"key\">{{ label }}</el-checkbox>\r\n          </el-col>\r\n        </el-row>\r\n      </el-checkbox-group>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleColumnConfirm\">确 定</el-button>\r\n        <el-button @click=\"columnSettingVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" />\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"downloadAttachment(scope.row.url, scope.row.name)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"attachmentDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestResult, getTestResult, delTestResult, addTestResult, updateTestResult,\r\n  getTestResultOptions\r\n} from \"@/api/material/testResult\";\r\nimport { listTestPlanGroup } from \"@/api/material/testPlanGroup\";\r\nimport { listTestParamItem } from \"@/api/material/testParamItem\";\r\nimport { listProcessParamGroup, getProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestResult\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 测试结果表格数据\r\n      testResultList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 测试方案组选项\r\n      testPlanGroupOptions: [],\r\n\r\n      // 参数组选项\r\n      paramGroupOptions: [],\r\n      // 过滤后的参数组选项\r\n      filteredParamGroupOptions: [],\r\n      // 选中的参数详情\r\n      selectedParamDetail: null,\r\n      // 选中的测试方案详情\r\n      selectedTestPlanDetail: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        testPlanCode: null,\r\n\r\n        groupId: null,\r\n        paramNumber: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        processType: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        groupId: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"change\" }\r\n        ],\r\n        planGroupId: [\r\n          { required: true, message: \"测试方案组不能为空\", trigger: \"change\" }\r\n        ],\r\n\r\n        testValue: [\r\n          { required: true, message: \"实际测试值不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 详情对话框\r\n      detailDialogVisible: false,\r\n      detailData: {},\r\n      detailParamItems: [],\r\n      detailTestPlanParams: null,\r\n\r\n      // 列设置相关\r\n      columnSettingVisible: false,\r\n      columnOptions: {\r\n        materialName: '材料名称',\r\n        supplierName: '供应商',\r\n        materialModel: '材料型号',\r\n        processType: '工艺类型',\r\n        paramNumber: '参数编号',\r\n        performanceType: '性能类型',\r\n        performanceName: '性能名称',\r\n\r\n        testEquipment: '测试设备',\r\n        supplierDatasheetVal: '供应商数据',\r\n        testValue: '测试值',\r\n        createBy: '创建人',\r\n        createTime: '创建时间',\r\n        updateBy: '更新人',\r\n        updateTime: '更新时间'\r\n      },\r\n      selectedColumns: ['materialName', 'supplierName', 'materialModel', 'processType', 'paramNumber', 'performanceType', 'performanceName', 'testEquipment', 'supplierDatasheetVal', 'testValue', 'createBy', 'createTime', 'updateBy', 'updateTime'],\r\n      visibleColumns: {},\r\n\r\n      // 搜索建议数据\r\n      paramNumberSuggestions: [],\r\n      materialNameSuggestions: [],\r\n      supplierNameSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      testPlanGroupSuggestions: [],\r\n\r\n      // 附件相关\r\n      fileList: [],\r\n      attachmentList: [],\r\n      attachmentDialogVisible: false,\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getTestPlanGroupOptions();\r\n    this.getParamGroupOptions();\r\n    this.loadSuggestions();\r\n    this.initVisibleColumns();\r\n  },\r\n  methods: {\r\n    /** 初始化可见列 */\r\n    initVisibleColumns() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取参数编号建议\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料名称建议\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商名称建议\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取性能类型建议\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试设备建议\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取测试方案建议\r\n      getTestResultOptions({ type: 'planCode' }).then(response => {\r\n        this.testPlanSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商名称搜索建议 */\r\n    querySupplierNameSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 性能类型搜索建议 */\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 测试设备搜索建议 */\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      getTestResultOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      getTestResultOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商名称焦点事件 */\r\n    handleSupplierNameFocus() {\r\n      getTestResultOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      getTestResultOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      getTestResultOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 性能类型焦点事件 */\r\n    handlePerformanceTypeFocus() {\r\n      getTestResultOptions({ type: 'performanceType' }).then(response => {\r\n        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试设备焦点事件 */\r\n    handleTestEquipmentFocus() {\r\n      getTestResultOptions({ type: 'testEquipment' }).then(response => {\r\n        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组搜索建议 */\r\n    queryTestPlanGroupSuggestions(queryString, cb) {\r\n      let suggestions = this.testPlanGroupSuggestions;\r\n      if (queryString && suggestions && suggestions.length > 0) {\r\n        suggestions = this.testPlanGroupSuggestions.filter(item => {\r\n          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions || []);\r\n    },\r\n\r\n    /** 测试方案组焦点事件 */\r\n    handleTestPlanGroupFocus() {\r\n      getTestResultOptions({ type: 'testPlanGroup' }).then(response => {\r\n        this.testPlanGroupSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 测试方案组选择事件 */\r\n    handleTestPlanGroupSelect(item) {\r\n      this.queryParams.testPlanCode = item.value;\r\n    },\r\n\r\n    /** 表单测试方案改变事件 */\r\n    handleFormTestPlanChange(value) {\r\n      // 当测试方案改变时，可以重新加载参数详情\r\n      if (value && this.form.groupId) {\r\n        this.handleParamGroupChange(this.form.groupId);\r\n      }\r\n    },\r\n\r\n    /** 查询测试结果列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTestResult(this.queryParams).then(response => {\r\n        this.testResultList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 获取测试方案组选项 */\r\n    getTestPlanGroupOptions() {\r\n      listTestPlanGroup().then(response => {\r\n        this.testPlanGroupOptions = response.rows || [];\r\n      }).catch(() => {\r\n        this.testPlanGroupOptions = [];\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 处理测试方案组变化 */\r\n    handleFormPlanGroupChange(planGroupId) {\r\n      this.form.testParamId = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.testParamOptions = [];\r\n\r\n      if (!planGroupId) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试方案组详情\r\n      const selectedGroup = this.testPlanGroupOptions.find(group => group.planGroupId === planGroupId);\r\n      if (selectedGroup) {\r\n        // 立即显示测试方案组基本信息\r\n        this.selectedTestPlanDetail = {\r\n          planCode: selectedGroup.planCode,\r\n          performanceType: selectedGroup.performanceType,\r\n          performanceName: selectedGroup.performanceName,\r\n          testEquipment: selectedGroup.testEquipment,\r\n          paramName: '',\r\n          paramValue: '',\r\n          unit: ''\r\n        };\r\n\r\n        // 加载该测试方案组下的测试参数选项\r\n        this.getTestParamOptionsByPlanGroupId(planGroupId);\r\n      }\r\n    },\r\n\r\n    /** 处理测试参数变化 */\r\n    handleFormTestParamChange(testParamId) {\r\n      if (!testParamId || !this.selectedTestPlanDetail) {\r\n        return;\r\n      }\r\n\r\n      // 获取选中的测试参数详情\r\n      const selectedParam = this.testParamOptions.find(param => param.testParamId === testParamId);\r\n      if (selectedParam) {\r\n        this.selectedTestPlanDetail.paramName = selectedParam.paramName;\r\n        this.selectedTestPlanDetail.paramValue = selectedParam.paramValue;\r\n        this.selectedTestPlanDetail.unit = selectedParam.unit;\r\n      }\r\n    },\r\n\r\n\r\n\r\n    /** 获取参数组选项 */\r\n    getParamGroupOptions() {\r\n      listProcessParamGroup().then(response => {\r\n        this.paramGroupOptions = response.rows;\r\n        this.filteredParamGroupOptions = response.rows;\r\n      });\r\n    },\r\n\r\n    /** 方案改变事件 */\r\n    handlePlanChange(value) {\r\n      // 可以根据方案过滤参数组\r\n      this.queryParams.groupId = null;\r\n      this.handleQuery();\r\n    },\r\n\r\n\r\n\r\n    /** 参数组改变事件 */\r\n    handleParamGroupChange(value) {\r\n      if (value) {\r\n        // 先清空之前的数据\r\n        this.selectedParamDetail = null;\r\n\r\n        // 获取参数组详情\r\n        getProcessParamGroup(value).then(response => {\r\n          this.selectedParamDetail = response.data;\r\n          console.log('获取到的参数组详情：', this.selectedParamDetail);\r\n\r\n          // 立即获取参数明细，不需要等待测试方案选择\r\n          listProcessParamItem({ groupId: value }).then(paramResponse => {\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = paramResponse.rows || [];\r\n              // 为每个参数项添加显示文本，包含参数值\r\n              this.selectedParamDetail.paramItems.forEach(item => {\r\n                let displayText = item.paramName || 'N/A';\r\n                if (item.paramValue !== null && item.paramValue !== undefined) {\r\n                  displayText += `: ${this.formatDecimal(item.paramValue)}`;\r\n                }\r\n                if (item.unit) {\r\n                  displayText += ` ${item.unit}`;\r\n                }\r\n                item.displayText = displayText;\r\n              });\r\n              console.log('获取到的参数明细：', this.selectedParamDetail.paramItems);\r\n\r\n              // 强制更新视图\r\n              this.$forceUpdate();\r\n            }\r\n          }).catch(error => {\r\n            console.error('获取参数明细失败：', error);\r\n            if (this.selectedParamDetail) {\r\n              this.selectedParamDetail.paramItems = [];\r\n            }\r\n          });\r\n        }).catch(error => {\r\n          console.error('获取参数组详情失败：', error);\r\n          this.selectedParamDetail = null;\r\n        });\r\n      } else {\r\n        this.selectedParamDetail = null;\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        testResultId: null,\r\n        planGroupId: null,\r\n        groupId: null,\r\n        supplierDatasheetVal: null,\r\n        testValue: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.fileList = [];\r\n      this.selectedParamDetail = null;\r\n      this.selectedTestPlanDetail = null;\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.testResultId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    // 行点击选择\r\n    handleRowClick(row) {\r\n      this.$refs.multipleTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加测试结果\";\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    handleEdit(row) {\r\n      this.reset();\r\n      const testResultId = row.testResultId || this.ids[0];\r\n      getTestResult(testResultId).then(response => {\r\n        console.log('编辑获取的数据：', response.data);\r\n        this.form = response.data;\r\n        // 解析附件数据\r\n        this.fileList = this.parseAttachments(response.data.attachments);\r\n        console.log('编辑时解析的文件列表：', this.fileList);\r\n        this.open = true;\r\n        this.title = \"修改测试结果\";\r\n\r\n        // 触发参数组改变事件以加载材料参数详情\r\n        if (this.form.groupId) {\r\n          this.handleParamGroupChange(this.form.groupId);\r\n        }\r\n\r\n        // 触发测试方案组改变事件以加载测试方案参数详情\r\n        if (this.form.planGroupId) {\r\n          this.handleFormPlanGroupChange(this.form.planGroupId);\r\n\r\n          // 如果有测试参数ID，在加载完测试参数选项后设置选中的参数\r\n          if (this.form.testParamId) {\r\n            this.$nextTick(() => {\r\n              this.handleFormTestParamChange(this.form.testParamId);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      this.detailData = row;\r\n      this.detailParamItems = [];\r\n      this.detailTestPlanParams = null;\r\n\r\n      // 如果有参数组ID，获取参数明细\r\n      if (row.groupId) {\r\n        listProcessParamItem({ groupId: row.groupId }).then(response => {\r\n          this.detailParamItems = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('获取参数明细失败：', error);\r\n          this.detailParamItems = [];\r\n        });\r\n      }\r\n\r\n      // 如果有测试方案组ID，获取测试方案参数信息\r\n      if (row.planGroupId) {\r\n        // 从当前的测试方案组选项中查找对应的方案信息\r\n        const planGroup = this.testPlanGroupOptions.find(group => group.planGroupId === row.planGroupId);\r\n        if (planGroup) {\r\n          // 获取测试方案组下的所有测试参数\r\n          listTestParamItem({ planGroupId: row.planGroupId }).then(response => {\r\n            this.detailTestPlanParams = {\r\n              planCode: planGroup.planCode,\r\n              performanceType: planGroup.performanceType,\r\n              performanceName: planGroup.performanceName,\r\n              testEquipment: planGroup.testEquipment,\r\n              testParams: response.rows || []\r\n            };\r\n          }).catch(error => {\r\n            console.error('获取测试方案参数失败：', error);\r\n          });\r\n        }\r\n      }\r\n\r\n      this.detailDialogVisible = true;\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 将附件列表转换为逗号分隔的URL字符串\r\n          if (this.fileList && this.fileList.length > 0) {\r\n            this.form.attachments = this.fileList.map(file => file.url).join(',');\r\n          } else {\r\n            this.form.attachments = '';\r\n          }\r\n\r\n          // testPlanId已经直接选择，无需转换\r\n\r\n          // 设置创建人和更新人\r\n          if (this.form.testResultId != null) {\r\n            // 更新操作，设置更新人\r\n            this.form.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.form.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          console.log('提交的表单数据：', this.form);\r\n\r\n          if (this.form.testResultId != null) {\r\n            updateTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestResult(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const testResultIds = row.testResultId || this.ids;\r\n      this.$modal.confirm('是否确认删除测试结果编号为\"' + testResultIds + '\"的数据项？').then(function() {\r\n        return delTestResult(testResultIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('material/testResult/export', {\r\n        ...this.queryParams\r\n      }, `test_result_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 列设置 */\r\n    handleColumnSetting() {\r\n      this.columnSettingVisible = true;\r\n    },\r\n\r\n    /** 列设置确认 */\r\n    handleColumnConfirm() {\r\n      this.visibleColumns = {};\r\n      this.selectedColumns.forEach(col => {\r\n        this.visibleColumns[col] = true;\r\n      });\r\n      this.columnSettingVisible = false;\r\n    },\r\n\r\n    /** 附件上传成功 */\r\n    handleUploadSuccess(response, file, fileList) {\r\n      console.log('上传成功回调：', { response, file, fileList });\r\n      if (response.code === 200) {\r\n        // 确保fileList是数组\r\n        if (Array.isArray(fileList)) {\r\n          this.fileList = fileList.map(item => ({\r\n            name: item.name,\r\n            url: item.response ? item.response.url : item.url,\r\n            size: this.formatFileSize(item.size || item.raw?.size),\r\n            uid: item.uid,\r\n            status: 'success'\r\n          }));\r\n        } else {\r\n          console.error('fileList不是数组：', fileList);\r\n          this.fileList = [];\r\n        }\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 附件移除 */\r\n    handleFileRemove(file, fileList) {\r\n      console.log('附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组\r\n      if (Array.isArray(fileList)) {\r\n        this.fileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.fileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 附件上传前检查 */\r\n    beforeUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      console.log('查看附件被调用，附件数据：', attachments, '类型：', typeof attachments);\r\n\r\n      this.attachmentList = [];\r\n\r\n      if (!attachments) {\r\n        console.log('附件数据为空');\r\n        this.attachmentDialogVisible = true;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            console.log('附件字符串为空');\r\n            this.attachmentDialogVisible = true;\r\n            return;\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                this.attachmentList = parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  size: item.size || '未知大小'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n              // 按逗号分割处理\r\n              this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n                const cleanUrl = url.trim();\r\n                const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n                return {\r\n                  name: fileName,\r\n                  url: cleanUrl,\r\n                  size: '未知大小'\r\n                };\r\n              });\r\n            }\r\n          } else {\r\n            // 按逗号分割处理\r\n            this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const cleanUrl = url.trim();\r\n              const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: cleanUrl,\r\n                size: '未知大小'\r\n              };\r\n            });\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            size: item.size || '未知大小'\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n        this.attachmentList = [];\r\n      }\r\n\r\n      console.log('解析后的附件列表：', this.attachmentList);\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, fileName) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      console.log('解析附件数据：', attachments, '类型：', typeof attachments);\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        // 如果已经是数组，直接返回\r\n        if (Array.isArray(attachments)) {\r\n          return attachments.map((item, index) => ({\r\n            name: item.name || `附件${index + 1}`,\r\n            url: item.url || item,\r\n            uid: item.uid || Date.now() + index,\r\n            status: 'success'\r\n          }));\r\n        }\r\n\r\n        // 如果是字符串，尝试解析\r\n        if (typeof attachments === 'string') {\r\n          const trimmed = attachments.trim();\r\n          if (!trimmed) {\r\n            return [];\r\n          }\r\n\r\n          // 尝试解析JSON格式\r\n          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {\r\n            try {\r\n              const parsed = JSON.parse(trimmed);\r\n              if (Array.isArray(parsed)) {\r\n                return parsed.map((item, index) => ({\r\n                  name: item.name || `附件${index + 1}`,\r\n                  url: item.url || item,\r\n                  uid: item.uid || Date.now() + index,\r\n                  status: 'success'\r\n                }));\r\n              }\r\n            } catch (jsonError) {\r\n              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);\r\n            }\r\n          }\r\n\r\n          // 按逗号分割处理\r\n          const urls = trimmed.split(',').filter(url => url.trim());\r\n          console.log('分割后的URL列表：', urls);\r\n          return urls.map((url, index) => {\r\n            const cleanUrl = url.trim();\r\n            const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: cleanUrl,\r\n              uid: Date.now() + index,\r\n              status: 'success'\r\n            };\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('解析附件数据时发生错误：', error);\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '未知大小';\r\n      const units = ['B', 'KB', 'MB', 'GB'];\r\n      let index = 0;\r\n      while (size >= 1024 && index < units.length - 1) {\r\n        size /= 1024;\r\n        index++;\r\n      }\r\n      return Math.round(size * 100) / 100 + ' ' + units[index];\r\n    },\r\n\r\n    /** 格式化小数 */\r\n    formatDecimal(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (isNaN(num)) {\r\n        return value;\r\n      }\r\n      // 如果是整数，直接返回，不添加.00\r\n      if (num % 1 === 0) {\r\n        return num.toString();\r\n      }\r\n      // 保留两位小数\r\n      return num.toFixed(2);\r\n    }\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.column-setting-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.column-setting-btn:hover {\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-row .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n  flex: 1;\r\n  min-width: 250px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  padding: 8px 20px;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n.param-detail-card {\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .form-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .form-row .el-form-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .search-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}