# 趋势对比模块详细使用教程

## 📋 前置准备

### 1. 执行测试数据脚本
首先在数据库中执行 `create_test_data.sql` 脚本，这将创建以下测试数据：
- **8种材料**：碳纤维、玻璃纤维、芳纶纤维、铝合金、钛合金、不锈钢、PEEK、聚酰亚胺
- **8个测试方案**：拉伸强度、弯曲强度、冲击韧性、热膨胀系数、导热系数、介电常数、耐腐蚀性、密度
- **18个参数组**：每种材料对应2-3种不同工艺
- **144条测试结果**：涵盖5种测试类型的完整数据

### 2. 数据结构说明
```
材料 → 工艺参数组 → 测试结果
     ↓
   参数明细
```

## 🎯 六大对比维度详细教程

### 1. 📊 材料性能对比

**使用场景：** 比较不同材料在相同测试条件下的性能表现

**操作步骤：**
1. 选择对比维度：**材料性能对比**
2. 选择材料：建议选择2-4种材料进行对比
   - 推荐组合1：碳纤维复合材料 + 玻璃纤维复合材料 + 芳纶纤维复合材料
   - 推荐组合2：铝合金 + 钛合金 + 不锈钢
   - 推荐组合3：PEEK + 聚酰亚胺（高性能塑料对比）

**推荐图表类型：**
- **柱状图**：直观对比各材料的平均性能
- **雷达图**：多维度性能对比（供应商数据、测试数据、数据量、准确率）
- **折线图**：查看性能趋势

**分析要点：**
- 供应商数据 vs 测试数据的差异
- 各材料的数据稳定性
- 性能排名和优劣势分析

### 2. 🏭 供应商数据对比

**使用场景：** 评估不同供应商提供数据的准确性和可靠性

**操作步骤：**
1. 选择对比维度：**供应商数据对比**
2. 选择供应商：建议选择3-5个供应商
   - 推荐选择：东丽工业 + 巨石集团 + 杜邦公司 + 中铝集团 + 宝钛集团

**推荐图表类型：**
- **柱状图**：对比各供应商的数据准确率
- **散点图**：查看供应商声明值与实际测试值的相关性
- **热力图**：展示数据偏差分布

**分析要点：**
- 准确率排名（越高越好）
- 数据偏差模式识别
- 供应商可信度评估

### 3. 🔢 参数编号对比

**使用场景：** 比较不同参数编号（工艺组合）的测试效果

**操作步骤：**
1. 选择对比维度：**参数编号对比**
2. 选择参数编号：建议选择同类材料的不同工艺
   - 推荐组合1：CF-001 + CF-002 + CF-003（碳纤维不同工艺）
   - 推荐组合2：GF-001 + GF-002 + GF-003（玻璃纤维不同工艺）
   - 推荐组合3：AL-001 + AL-002（铝合金不同工艺）

**推荐图表类型：**
- **柱状图**：对比不同工艺的性能表现
- **折线图**：查看工艺优化趋势
- **雷达图**：多维度工艺对比

**分析要点：**
- 最优工艺参数识别
- 工艺稳定性分析
- 成本效益评估

### 4. ⚙️ 工艺类型对比

**使用场景：** 比较不同工艺类型的整体效果

**操作步骤：**
1. 选择对比维度：**工艺类型对比**
2. 选择工艺类型：建议选择3-5种工艺
   - 推荐选择：预浸料成型 + 热压成型 + RTM成型 + 手糊成型 + 注塑成型

**推荐图表类型：**
- **柱状图**：对比各工艺类型的平均性能
- **雷达图**：多维度工艺评估
- **热力图**：工艺性能分布图

**分析要点：**
- 工艺适用性分析
- 稳定性指标对比
- 工艺选择建议

### 5. 📈 时间趋势分析

**使用场景：** 分析测试数据随时间的变化趋势

**操作步骤：**
1. 选择对比维度：**时间趋势分析**
2. 选择时间范围：建议选择1-3个月的数据
   - 推荐范围：2024-01-01 到 2024-02-28

**推荐图表类型：**
- **折线图**：展示时间趋势变化
- **面积图**：显示数据量变化

**分析要点：**
- 测试数据的时间规律
- 异常数据点识别
- 质量控制趋势

### 6. ⚖️ 供应商vs测试值对比

**使用场景：** 直观对比供应商声明值与实际测试值的差异

**操作步骤：**
1. 选择对比维度：**供应商vs测试值对比**
2. 选择参数：建议选择数据量较多的参数
   - 推荐选择：CF-001（碳纤维预浸料成型）或 GF-001（玻璃纤维手糊成型）

**推荐图表类型：**
- **散点图**：最佳选择，显示相关性和偏差
- **对比柱状图**：直观显示数值差异

**分析要点：**
- 数据相关性分析
- 系统性偏差识别
- 供应商数据可信度

## 🎨 图表类型选择指南

### 柱状图 📊
- **适用场景**：类别对比、排名分析
- **最佳用途**：材料性能对比、供应商准确率对比
- **优势**：直观清晰，易于理解

### 折线图 📈
- **适用场景**：趋势分析、时间序列
- **最佳用途**：时间趋势分析、性能变化趋势
- **优势**：趋势明显，连续性好

### 散点图 🔵
- **适用场景**：相关性分析、偏差分析
- **最佳用途**：供应商vs测试值对比
- **优势**：显示数据分布和相关性

### 雷达图 🕸️
- **适用场景**：多维度对比、综合评估
- **最佳用途**：材料综合性能对比
- **优势**：多指标同时展示

### 热力图 🌡️
- **适用场景**：数据密度分析、模式识别
- **最佳用途**：工艺参数优化、异常检测
- **优势**：直观显示数据分布

## 🔍 实际应用案例

### 案例1：新材料选型
**目标：** 为航空航天项目选择最佳复合材料
**操作：**
1. 选择材料性能对比
2. 对比：碳纤维 + 玻璃纤维 + 芳纶纤维
3. 使用雷达图进行综合评估
4. 重点关注拉伸强度和冲击韧性数据

### 案例2：供应商评估
**目标：** 评估供应商数据可靠性
**操作：**
1. 选择供应商数据对比
2. 对比所有主要供应商
3. 使用柱状图查看准确率排名
4. 使用散点图分析数据偏差模式

### 案例3：工艺优化
**目标：** 优化碳纤维成型工艺
**操作：**
1. 选择参数编号对比
2. 对比：CF-001 + CF-002 + CF-003
3. 使用柱状图对比性能
4. 使用热力图分析工艺参数影响

## ⚠️ 注意事项

1. **数据量要求**：建议每个对比项至少有3条以上测试数据
2. **时间范围**：时间趋势分析建议选择至少1个月的数据
3. **对比数量**：单次对比建议不超过8个项目，以保证图表清晰度
4. **图表选择**：根据分析目的选择合适的图表类型
5. **数据质量**：注意识别和处理异常数据点

## 🚀 高级技巧

1. **组合分析**：先用材料对比找出优势材料，再用工艺对比优化参数
2. **时间验证**：用时间趋势分析验证材料性能的稳定性
3. **供应商筛选**：定期使用供应商对比评估数据质量
4. **异常检测**：使用散点图和热力图识别异常数据
5. **决策支持**：结合多个维度的分析结果做出综合决策

通过以上教程，您可以充分利用趋势对比模块进行全面的材料性能分析和决策支持！
