{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue?vue&type=style&index=0&id=24309412&scoped=true&lang=css", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\trend\\index.vue", "mtime": 1754285582786}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753339847609}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753339890164}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753339854740}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y1ZjdmYSAwJSwgI2MzY2ZlMiAxMDAlKTsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOw0KfQ0KDQovKiDpobXpnaLlpLTpg6jmoLflvI8gKi8NCi5wYWdlLWhlYWRlciB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7DQp9DQoNCi5wYWdlLXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wYWdlLXRpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXNpemU6IDI4cHg7DQp9DQoNCi5wYWdlLWRlc2NyaXB0aW9uIHAgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW46IDA7DQp9DQoNCi8qIOWinuW8uuWNoeeJh+agt+W8jyAqLw0KLmVuaGFuY2VkLWNhcmQgew0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouZW5oYW5jZWQtY2FyZDpob3ZlciB7DQogIGJveC1zaGFkb3c6IDAgOHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjEyKTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KfQ0KDQovKiDljaHniYflpLTpg6jmoLflvI8gKi8NCi5jYXJkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMDsNCn0NCg0KLmhlYWRlci1sZWZ0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouaGVhZGVyLWxlZnQgaSB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXNpemU6IDE4cHg7DQp9DQoNCi5oZWFkZXItdGl0bGUgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLmhlYWRlci1yaWdodCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogOHB4Ow0KfQ0KDQouaGVhZGVyLXJpZ2h0IC5lbC1idXR0b24gew0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5jbG9zZS1ndWlkZS1idG4gew0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmNsb3NlLWd1aWRlLWJ0bjpob3ZlciB7DQogIGNvbG9yOiAjZjBmMGYwOw0KfQ0KDQouZ3VpZGUtYnRuIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzE3YTJiOCAwJSwgIzEzODQ5NiAxMDAlKTsNCiAgYm9yZGVyOiBub25lOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5yZWZyZXNoLWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlcjogbm9uZTsNCn0NCg0KLmV4cG9ydC1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNTZhYjJmIDAlLCAjYThlNmNmIDEwMCUpOw0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi5jbGVhcmZpeDpiZWZvcmUsDQouY2xlYXJmaXg6YWZ0ZXIgew0KICBkaXNwbGF5OiB0YWJsZTsNCiAgY29udGVudDogIiI7DQp9DQoNCi5jbGVhcmZpeDphZnRlciB7DQogIGNsZWFyOiBib3RoOw0KfQ0KDQouZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wYXJhbS1kZXRhaWwtY2FyZCB7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsNCn0NCg0KLmRldGFpbC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi5kZXRhaWwtaXRlbSBsYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5kZXRhaWwtaXRlbSBzcGFuIHsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5zdGF0aXN0aWNzLWNhcmQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQp9DQoNCi5zdGF0aXN0aWNzLWNhcmQ6aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouc3RhdGlzdGljcy1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLnN0YXRpc3RpY3MtdGl0bGUgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouc3RhdGlzdGljcy12YWx1ZSB7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQp9DQoNCi5zdGF0aXN0aWNzLWRlc2Mgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjQzBDNENDOw0KfQ0KDQouY2hhcnQtaGVscC1jb250ZW50IHsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCn0NCg0KLmNoYXJ0LWhlbHAtY29udGVudCBoNCB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouY2hhcnQtaGVscC1jb250ZW50IHVsIHsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KfQ0KDQouY2hhcnQtaGVscC1jb250ZW50IGxpIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQouY2hhcnQtaGVscC1jb250ZW50IHN0cm9uZyB7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQovKiDkvb/nlKjmjIfljZfmoLflvI8gKi8NCi51c2FnZS1ndWlkZS1jYXJkIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6IHdoaXRlOw0KfQ0KDQoudXNhZ2UtZ3VpZGUtY2FyZCAuZWwtY2FyZF9faGVhZGVyIHsNCiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQp9DQoNCi5ndWlkZS1pdGVtIGg0IHsNCiAgY29sb3I6ICNmZmY7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZ3VpZGUtaXRlbSBwIHsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBtYXJnaW46IDA7DQp9DQoNCi8qIOWPguaVsOivpuaDheWNoeeJh+agt+W8jyAqLw0KLnBhcmFtLWRldGFpbHMtY2FyZCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNjM2NmZTIgMTAwJSk7DQp9DQoNCi5wYXJhbS1kZXRhaWwtY2FyZCB7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi5wYXJhbS1kZXRhaWwtY2FyZDpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5kZXRhaWwtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmctYm90dG9tOiAxMHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLmRldGFpbC1zZWN0aW9uOmxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQouc2VjdGlvbi10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5zZWN0aW9uLXRpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCn0NCg0KLmRldGFpbC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogNnB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5kZXRhaWwtaWNvbiB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouZGV0YWlsLWl0ZW0gbGFiZWwgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIG1pbi13aWR0aDogNjBweDsNCn0NCg0KLmRldGFpbC12YWx1ZSB7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouc3RhdC1pdGVtIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiA4cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KfQ0KDQouc3RhdC1sYWJlbCB7DQogIGZvbnQtc2l6ZTogMTBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIG1hcmdpbi1ib3R0b206IDJweDsNCn0NCg0KLnN0YXQtdmFsdWUgew0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzQwOUVGRjsNCn0NCg0KLnBhcmFtcy1jb250YWluZXIgew0KICBtYXgtaGVpZ2h0OiA4MHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQoucGFyYW1zLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNHB4Ow0KfQ0KDQoucGFyYW1zLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiAjZjFmMWYxOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQoNCi5wYXJhbXMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6ICNjMWMxYzE7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLnBhcmFtcy1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogI2E4YThhODsNCn0NCg0KLyog5L2/55So5bu66K6u5qC35byP5LyY5YyWICovDQoudXNhZ2Utc3VnZ2VzdGlvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBtYXJnaW46IDEwcHggMDsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTsNCn0NCg0KLnVzYWdlLXN1Z2dlc3Rpb24gcCB7DQogIG1hcmdpbjogMDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLyog5pWw5o2u6KGo5qC85qC35byP5LyY5YyWICovDQoudHJlbmQtZGF0YS10YWJsZSB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLnRyZW5kLWRhdGEtdGFibGUgLmVsLXRhYmxlX19oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KfQ0KDQoudHJlbmQtZGF0YS10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB0aCB7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50Ow0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5wYXJhbS1kZXRhaWxzLWNvbnRhaW5lciB7DQogIG1heC1oZWlnaHQ6IDEyMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiA1cHg7DQp9DQoNCi5wYXJhbS1kZXRhaWwtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCiAgcGFkZGluZzogMnB4IDZweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5wYXJhbS1uYW1lIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIG1hcmdpbi1yaWdodDogNXB4Ow0KICBtaW4td2lkdGg6IDgwcHg7DQp9DQoNCi5wYXJhbS12YWx1ZSB7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjNjdDMjNBOw0KICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOw0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCn0NCg0KLnBhcmFtLXVuaXQgew0KICBjb2xvcjogIzkwOTM5OTsNCiAgZm9udC1zaXplOiAxMXB4Ow0KfQ0KDQoubWF0ZXJpYWwtaW5mbyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogNXB4Ow0KfQ0KDQoubWF0ZXJpYWwtaW5mbyBpIHsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5lbXB0eS1kYXRhIHsNCiAgY29sb3I6ICNDMEM0Q0M7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLyog57uf5LiA5oyJ6ZKu5qC35byPICovDQouZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0tc3VjY2VzcyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1NmFiMmYgMCUsICNhOGU2Y2YgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5lbC1idXR0b24tLWluZm8gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTdhMmI4IDAlLCAjMTM4NDk2IDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtYnV0dG9uLS13YXJuaW5nIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0tZGFuZ2VyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI2YiAwJSwgI2VlNWEyNCAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA44DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/trend", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-data-line\"></i>\r\n        <span>趋势对比分析</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <p>📈 多维度数据趋势对比分析，支持材料性能、供应商质量等多种对比维度</p>\r\n          <el-button type=\"text\" @click=\"showUsageGuide = true\" style=\"color: #409EFF;\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <span>使用指南</span>\r\n          </el-button>\r\n        </div>\r\n        <el-alert\r\n          title=\"使用提示：选择对比维度 → 配置筛选条件 → 生成图表分析\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用指南卡片 -->\r\n    <el-card class=\"usage-guide-card enhanced-card\" style=\"margin-bottom: 20px;\" v-if=\"showUsageGuide\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"header-title\">📊 趋势对比分析使用指南</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"text\" @click=\"showUsageGuide = false\" class=\"close-guide-btn\">\r\n            <i class=\"el-icon-close\"></i>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>🎯 对比维度说明</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>材料对比：</strong>按材料名称分组，计算每种材料的供应商数据和测试数据平均值</li>\r\n              <li><strong>供应商对比：</strong>按供应商分组，计算准确率（供应商数据与测试数据的偏差）</li>\r\n              <li><strong>参数编号对比：</strong>按参数组分组，展示工艺参数明细和测试结果统计</li>\r\n              <li><strong>工艺类型对比：</strong>按工艺类型分组，计算稳定性（测试数据标准差）</li>\r\n              <li><strong>时间趋势：</strong>按日期分组，展示测试数据随时间的变化趋势</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"guide-item\">\r\n            <h4>📊 数据来源详解</h4>\r\n            <ul style=\"margin: 10px 0; padding-left: 20px; line-height: 1.6;\">\r\n              <li><strong>供应商平均值：</strong>选中项目下所有测试记录的供应商数据平均值</li>\r\n              <li><strong>测试平均值：</strong>选中项目下所有测试记录的实际测试值平均值</li>\r\n              <li><strong>准确率：</strong>100% - |供应商平均值 - 测试平均值| / 供应商平均值 × 100%</li>\r\n              <li><strong>稳定性：</strong>基于测试数据标准差计算，数值越小越稳定</li>\r\n              <li><strong>数据量：</strong>参与计算的测试记录总数</li>\r\n              <li><strong>参数明细：</strong>来自工艺参数配置中的具体参数项</li>\r\n            </ul>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row style=\"margin-top: 15px;\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"guide-item\">\r\n            <h4>💡 使用建议</h4>\r\n            <div class=\"usage-suggestion\">\r\n              <p>1. 选择对比维度 → 2. 配置筛选条件（支持多选） → 3. 点击\"生成图表\"分析 → 4. 切换图表类型查看不同视角 → 5. 查看详细数据表获取具体数值</p>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <el-card class=\"trend-analysis-card enhanced-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">数据趋势对比分析</span>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"info\" icon=\"el-icon-question\" size=\"small\" @click=\"showUsageGuide = !showUsageGuide\" class=\"guide-btn\">\r\n            <span>使用指南</span>\r\n          </el-button>\r\n          <el-button type=\"primary\" icon=\"el-icon-refresh\" size=\"small\" @click=\"refreshChart\" class=\"refresh-btn\">\r\n            <span>刷新数据</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"exportChart\" class=\"export-btn\">\r\n            <span>导出图表</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 筛选条件 -->\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"对比维度\" prop=\"compareType\">\r\n          <el-select v-model=\"queryParams.compareType\" placeholder=\"请选择对比维度\" style=\"width: 250px;\" clearable @change=\"handleCompareTypeChange\">\r\n            <el-option label=\"📊 材料性能对比\" value=\"material\">\r\n              <span style=\"float: left\">📊 材料性能对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同材料性能</span>\r\n            </el-option>\r\n            <el-option label=\"🏭 供应商数据对比\" value=\"supplier\">\r\n              <span style=\"float: left\">🏭 供应商数据对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较供应商质量</span>\r\n            </el-option>\r\n            <el-option label=\"🔢 参数编号对比\" value=\"paramNumber\">\r\n              <span style=\"float: left\">🔢 参数编号对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较不同参数值</span>\r\n            </el-option>\r\n            <el-option label=\"⚙️ 工艺类型对比\" value=\"processType\">\r\n              <span style=\"float: left\">⚙️ 工艺类型对比</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">比较工艺效果</span>\r\n            </el-option>\r\n            <el-option label=\"📈 时间趋势分析\" value=\"timeTrend\">\r\n              <span style=\"float: left\">📈 时间趋势分析</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">查看时间变化</span>\r\n            </el-option>\r\n            <el-option label=\"⚖️ 供应商vs测试值\" value=\"supplierVsTest\">\r\n              <span style=\"float: left\">⚖️ 供应商vs测试值</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 12px\">对比数据差异</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 材料性能对比 -->\r\n        <el-form-item label=\"选择材料\" prop=\"materialNames\" v-if=\"queryParams.compareType === 'material'\">\r\n          <el-select\r\n            v-model=\"queryParams.materialNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的材料\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"material in materialOptions\"\r\n              :key=\"material.materialId\"\r\n              :label=\"material.materialName + ' (' + material.supplierName + ')'\"\r\n              :value=\"material.materialId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 供应商数据对比 -->\r\n        <el-form-item label=\"选择供应商\" prop=\"supplierNames\" v-if=\"queryParams.compareType === 'supplier'\">\r\n          <el-select\r\n            v-model=\"queryParams.supplierNames\"\r\n            multiple\r\n            placeholder=\"请选择要对比的供应商\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"supplier in supplierOptions\"\r\n              :key=\"supplier\"\r\n              :label=\"supplier\"\r\n              :value=\"supplier\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 参数编号对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"paramNumbers\" v-if=\"queryParams.compareType === 'paramNumber'\">\r\n          <el-select\r\n            v-model=\"queryParams.paramNumbers\"\r\n            multiple\r\n            placeholder=\"请选择要对比的参数编号\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 工艺类型对比 -->\r\n        <el-form-item label=\"选择工艺\" prop=\"processTypes\" v-if=\"queryParams.compareType === 'processType'\">\r\n          <el-select\r\n            v-model=\"queryParams.processTypes\"\r\n            multiple\r\n            placeholder=\"请选择要对比的工艺类型\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"type in processTypeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 时间趋势分析 -->\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\" v-if=\"queryParams.compareType === 'timeTrend'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            style=\"width: 300px;\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 供应商vs测试值对比 -->\r\n        <el-form-item label=\"选择参数\" prop=\"compareParam\" v-if=\"queryParams.compareType === 'supplierVsTest'\">\r\n          <el-select\r\n            v-model=\"queryParams.compareParam\"\r\n            placeholder=\"请选择要对比的参数\"\r\n            style=\"width: 300px;\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"param in paramNumberOptions\"\r\n              :key=\"param.groupId\"\r\n              :label=\"param.paramNumber + ' - ' + param.materialName\"\r\n              :value=\"param.groupId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\" :loading=\"loading\">生成对比图表</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 图表类型选择 -->\r\n      <el-row style=\"margin-bottom: 20px;\">\r\n        <el-col :span=\"24\">\r\n          <el-radio-group v-model=\"chartType\" @change=\"handleChartTypeChange\">\r\n            <el-radio-button label=\"line\">折线图</el-radio-button>\r\n            <el-radio-button label=\"bar\">柱状图</el-radio-button>\r\n            <el-radio-button label=\"scatter\">散点图</el-radio-button>\r\n            <el-radio-button label=\"radar\">雷达图</el-radio-button>\r\n            <el-radio-button label=\"heatmap\">热力图</el-radio-button>\r\n          </el-radio-group>\r\n          <el-button-group style=\"margin-left: 20px;\">\r\n            <el-button size=\"small\" @click=\"toggleDataTable\">{{ showDataTable ? '隐藏' : '显示' }}数据表</el-button>\r\n            <el-button size=\"small\" @click=\"toggleProjectDetails\" :disabled=\"selectedParamDetails.length === 0\">\r\n              {{ showProjectDetails ? '隐藏' : '显示' }}项目详情\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"toggleFullscreen\">全屏显示</el-button>\r\n          </el-button-group>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 参数详情信息卡片 -->\r\n    <el-card v-if=\"selectedParamDetails.length > 0 && showProjectDetails\" class=\"box-card param-details-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">📋 选中项目详情信息</span>\r\n        <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 10px;\">{{ selectedParamDetails.length }}项</el-tag>\r\n        <el-button type=\"text\" @click=\"showProjectDetails = false\" style=\"float: right; color: #909399;\">\r\n          <i class=\"el-icon-close\"></i>\r\n        </el-button>\r\n      </div>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\" v-for=\"(detail, index) in selectedParamDetails\" :key=\"index\">\r\n          <el-card class=\"param-detail-card\" shadow=\"hover\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span style=\"font-weight: bold; color: #409EFF;\">\r\n                <i class=\"el-icon-data-line\"></i>\r\n                {{ detail.paramNumber || detail.name }}\r\n              </span>\r\n              <el-tag size=\"mini\" type=\"success\" style=\"float: right;\" v-if=\"detail.testCount\">\r\n                {{ detail.testCount }}次测试\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 基本信息 -->\r\n            <div class=\"detail-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-info\"></i>\r\n                基本信息\r\n              </div>\r\n              <el-descriptions :column=\"2\" border size=\"small\">\r\n                <el-descriptions-item label=\"材料名称\" v-if=\"detail.materialName\">\r\n                  <el-tag type=\"primary\" size=\"mini\">{{ detail.materialName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"供应商\" v-if=\"detail.supplierName\">\r\n                  <el-tag type=\"success\" size=\"mini\">{{ detail.supplierName }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"材料型号\" v-if=\"detail.materialModel\">\r\n                  <span>{{ detail.materialModel }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"工艺类型\" v-if=\"detail.processType\">\r\n                  <el-tag type=\"warning\" size=\"mini\">{{ detail.processType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"性能类型\" v-if=\"detail.performanceType\">\r\n                  <el-tag type=\"info\" size=\"mini\">{{ detail.performanceType }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"参数编号\" v-if=\"detail.paramNumber\">\r\n                  <span>{{ detail.paramNumber }}</span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"数据量\" v-if=\"detail.dataCount !== undefined\">\r\n                  <el-tag type=\"danger\" size=\"mini\">{{ detail.dataCount }}条</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"创建时间\" v-if=\"detail.createTime\">\r\n                  <span>{{ detail.createTime }}</span>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n\r\n            <!-- 统计信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.statistics\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                统计信息\r\n              </div>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.avgValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">平均值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.avgValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.maxValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最大值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.maxValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.minValue !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">最小值</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.minValue) }}</div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" v-if=\"detail.statistics.stdDev !== undefined\">\r\n                  <div class=\"stat-item\">\r\n                    <div class=\"stat-label\">标准差</div>\r\n                    <div class=\"stat-value\">{{ formatNumber(detail.statistics.stdDev) }}</div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 参数明细 -->\r\n            <div class=\"detail-section\" v-if=\"detail.mainParams && detail.mainParams.length > 0\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-menu\"></i>\r\n                参数明细\r\n                <el-tag size=\"mini\" type=\"info\" style=\"margin-left: 5px;\">{{ detail.mainParams.length }}个</el-tag>\r\n              </div>\r\n              <div class=\"params-container\">\r\n                <el-tooltip\r\n                  v-for=\"param in detail.mainParams\"\r\n                  :key=\"param.paramName\"\r\n                  :content=\"`${param.paramName}: ${param.paramValue || 'N/A'} ${param.unit || ''}`\"\r\n                  placement=\"top\"\r\n                >\r\n                  <el-tag\r\n                    size=\"mini\"\r\n                    :type=\"getParamTagType(param)\"\r\n                    style=\"margin-right: 5px; margin-bottom: 3px; cursor: pointer;\"\r\n                    @click=\"showParamDetail(param)\"\r\n                  >\r\n                    {{ param.paramName }}\r\n                    <span v-if=\"param.paramValue\" style=\"margin-left: 3px; opacity: 0.8;\">\r\n                      ({{ formatNumber(param.paramValue) }})\r\n                    </span>\r\n                  </el-tag>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 测试方案信息 -->\r\n            <div class=\"detail-section\" v-if=\"detail.testPlanInfo\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-document\"></i>\r\n                测试方案\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <label>方案编号：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.planCode }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" v-if=\"detail.testPlanInfo.testEquipment\">\r\n                <label>测试设备：</label>\r\n                <span class=\"detail-value\">{{ detail.testPlanInfo.testEquipment }}</span>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 图表区域 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 14px;\">{{ chartTitle }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-tooltip content=\"图表说明\" placement=\"top\">\r\n            <el-button type=\"text\" icon=\"el-icon-question\" @click=\"showChartHelp\" />\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"chartLoading\" element-loading-text=\"正在生成图表...\">\r\n        <div\r\n          ref=\"chart\"\r\n          :style=\"{ height: chartHeight + 'px', width: '100%' }\"\r\n          v-show=\"!showDataTable\"\r\n        ></div>\r\n\r\n        <!-- 数据表格 -->\r\n        <el-table\r\n          v-show=\"showDataTable\"\r\n          :data=\"chartData\"\r\n          style=\"width: 100%\"\r\n          :max-height=\"chartHeight\"\r\n          class=\"trend-data-table\"\r\n        >\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column\r\n            v-for=\"column in tableColumns\"\r\n            :key=\"column.prop\"\r\n            :prop=\"column.prop\"\r\n            :label=\"column.label\"\r\n            :width=\"column.width\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"column.prop === 'paramDetails'\">\r\n                <div v-if=\"scope.row.paramDetails && scope.row.paramDetails.length > 0\" class=\"param-details-container\">\r\n                  <div\r\n                    v-for=\"(param, index) in scope.row.paramDetails\"\r\n                    :key=\"index\"\r\n                    class=\"param-detail-item\"\r\n                  >\r\n                    <span class=\"param-name\">{{ param.paramName }}:</span>\r\n                    <span class=\"param-value\">{{ formatParamValue(param.paramValue) }}</span>\r\n                    <span class=\"param-unit\" v-if=\"param.unit\">{{ param.unit }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else class=\"empty-data\">暂无参数</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'paramNumber'\">\r\n                <el-tag type=\"primary\" size=\"small\" v-if=\"scope.row.paramNumber\">\r\n                  {{ scope.row.paramNumber }}\r\n                </el-tag>\r\n                <span v-else class=\"empty-data\">-</span>\r\n              </div>\r\n              <div v-else-if=\"column.prop === 'materialName'\">\r\n                <div class=\"material-info\">\r\n                  <i class=\"el-icon-box\"></i>\r\n                  <span>{{ scope.row.materialName || '-' }}</span>\r\n                </div>\r\n              </div>\r\n              <div v-else>\r\n                {{ scope.row[column.prop] || '-' }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\" v-if=\"statisticsData.length > 0\">\r\n      <el-col :span=\"6\" v-for=\"(stat, index) in statisticsData\" :key=\"index\">\r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-content\">\r\n            <div class=\"statistics-title\">{{ stat.title }}</div>\r\n            <div class=\"statistics-value\">{{ stat.value }}</div>\r\n            <div class=\"statistics-desc\">{{ stat.description }}</div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表说明对话框 -->\r\n    <el-dialog title=\"📊 图表说明\" :visible.sync=\"helpDialogVisible\" width=\"700px\" append-to-body>\r\n      <div class=\"chart-help-content\">\r\n        <h4>🎯 图表类型说明：</h4>\r\n        <ul>\r\n          <li><strong>📈 折线图：</strong>适用于展示数据随时间或其他连续变量的变化趋势，清晰显示数据走向</li>\r\n          <li><strong>📊 柱状图：</strong>适用于比较不同类别之间的数值大小，直观对比差异</li>\r\n          <li><strong>🔵 散点图：</strong>适用于展示两个变量之间的相关关系，发现数据规律</li>\r\n          <li><strong>🕸️ 雷达图：</strong>适用于多维度数据的综合对比，全面评估性能</li>\r\n          <li><strong>🌡️ 热力图：</strong>适用于展示数据的分布密度和相关性，识别热点区域</li>\r\n        </ul>\r\n\r\n        <h4>🔍 对比维度说明：</h4>\r\n        <ul>\r\n          <li><strong>📊 材料性能对比：</strong>比较不同材料的性能表现，识别最优材料</li>\r\n          <li><strong>🏭 供应商数据对比：</strong>比较不同供应商材料的质量差异，评估供应商可靠性</li>\r\n          <li><strong>🔢 参数编号对比：</strong>比较不同参数编号下的测试值趋势，分析参数影响</li>\r\n          <li><strong>⚙️ 工艺类型对比：</strong>比较不同工艺类型的效果，优化工艺流程</li>\r\n          <li><strong>📈 时间趋势分析：</strong>展示测试数据随时间的变化规律，预测发展趋势</li>\r\n          <li><strong>⚖️ 供应商vs测试值：</strong>对比供应商提供数据与实际测试结果的差异</li>\r\n        </ul>\r\n\r\n        <h4>💡 使用技巧：</h4>\r\n        <ul>\r\n          <li>将鼠标悬停在图表数据点上可查看详细信息和参数明细</li>\r\n          <li>点击参数标签可查看该参数的详细信息</li>\r\n          <li>使用\"显示数据表\"功能可查看原始数据</li>\r\n          <li>选择合适的图表类型能更好地展示数据特征</li>\r\n          <li>多选对比项目可进行横向比较分析</li>\r\n        </ul>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数详情对话框 -->\r\n    <el-dialog\r\n      title=\"📋 参数详细信息\"\r\n      :visible.sync=\"paramDetailDialogVisible\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      v-if=\"currentParamDetail\"\r\n    >\r\n      <div class=\"param-detail-content\">\r\n        <el-descriptions :column=\"2\" border size=\"small\">\r\n          <el-descriptions-item label=\"参数名称\">\r\n            <el-tag type=\"primary\">{{ currentParamDetail.paramName }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数数值\">\r\n            <span style=\"font-weight: bold; color: #67C23A;\">\r\n              {{ formatNumber(currentParamDetail.paramValue) }}\r\n            </span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"参数单位\" v-if=\"currentParamDetail.unit\">\r\n            {{ currentParamDetail.unit }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"数据类型\">\r\n            {{ typeof currentParamDetail.paramValue === 'number' ? '数值型' : '文本型' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\" v-if=\"currentParamDetail.createTime\">\r\n            {{ currentParamDetail.createTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"更新时间\" v-if=\"currentParamDetail.updateTime\">\r\n            {{ currentParamDetail.updateTime }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <div v-if=\"currentParamDetail.remark\" style=\"margin-top: 15px;\">\r\n          <h4 style=\"color: #409EFF; margin-bottom: 8px;\">📝 备注信息：</h4>\r\n          <p style=\"background: #f5f7fa; padding: 10px; border-radius: 4px; margin: 0;\">\r\n            {{ currentParamDetail.remark }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { listTestResult, getTestResultOptions } from \"@/api/material/testResult\";\r\nimport { listMaterial } from \"@/api/material/material\";\r\nimport { listProcessParamGroup } from \"@/api/material/processParamGroup\";\r\nimport { listProcessParamItem } from \"@/api/material/processParamItem\";\r\n\r\nexport default {\r\n  name: \"MaterialTrend\",\r\n  data() {\r\n    return {\r\n      // 加载状态\r\n      loading: false,\r\n      // 图表实例\r\n      chart: null,\r\n      // 图表类型\r\n      chartType: 'line',\r\n      // 图表高度\r\n      chartHeight: 400,\r\n      // 图表标题\r\n      chartTitle: '数据趋势对比分析',\r\n      // 图表加载状态\r\n      chartLoading: false,\r\n      // 是否显示数据表\r\n      showDataTable: false,\r\n      // 是否全屏\r\n      isFullscreen: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      },\r\n\r\n      // 选项数据\r\n      paramNumberOptions: [],\r\n      materialOptions: [],\r\n      supplierOptions: [],\r\n      processTypeOptions: [],\r\n\r\n      // 图表数据\r\n      chartData: [],\r\n      tableColumns: [],\r\n\r\n      // 参数详情\r\n      selectedParamDetails: [],\r\n\r\n      // 统计数据\r\n      statisticsData: [],\r\n\r\n      // 帮助对话框\r\n      helpDialogVisible: false,\r\n\r\n      // 使用指南显示状态\r\n      showUsageGuide: false,\r\n\r\n      // 项目详情显示状态\r\n      showProjectDetails: false,\r\n\r\n      // 参数详情对话框\r\n      paramDetailDialogVisible: false,\r\n      currentParamDetail: null\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n    this.loadOptions();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化图表 */\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart);\r\n\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', () => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 加载选项数据 */\r\n    async loadOptions() {\r\n      try {\r\n        // 加载参数编号选项\r\n        const paramResponse = await listProcessParamGroup({});\r\n        this.paramNumberOptions = paramResponse.rows || [];\r\n\r\n        // 加载材料选项\r\n        const materialResponse = await listMaterial({});\r\n        this.materialOptions = materialResponse.rows || [];\r\n\r\n        // 加载供应商选项\r\n        const supplierResponse = await getTestResultOptions({ type: 'supplierName' });\r\n        this.supplierOptions = supplierResponse.data || [];\r\n\r\n        // 加载工艺类型选项\r\n        const processResponse = await getTestResultOptions({ type: 'processType' });\r\n        this.processTypeOptions = processResponse.data || [];\r\n\r\n      } catch (error) {\r\n        console.error('加载选项数据失败：', error);\r\n        this.$modal.msgError('加载选项数据失败');\r\n      }\r\n    },\r\n\r\n    /** 对比类型改变 */\r\n    handleCompareTypeChange(value) {\r\n      // 重置相关参数\r\n      this.queryParams.paramNumbers = [];\r\n      this.queryParams.materialNames = [];\r\n      this.queryParams.supplierNames = [];\r\n      this.queryParams.processTypes = [];\r\n      this.queryParams.dateRange = null;\r\n      this.queryParams.compareParam = null;\r\n\r\n      // 清空选中的参数详情和图表数据\r\n      this.selectedParamDetails = [];\r\n      this.chartData = [];\r\n      this.statisticsData = [];\r\n\r\n      // 更新图表标题\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 更新图表标题 */\r\n    updateChartTitle() {\r\n      const typeMap = {\r\n        'paramNumber': '参数编号对比分析',\r\n        'material': '材料性能对比分析',\r\n        'supplier': '供应商质量对比分析',\r\n        'processType': '工艺类型效果对比分析',\r\n        'timeTrend': '时间趋势对比分析'\r\n      };\r\n      this.chartTitle = typeMap[this.queryParams.compareType] || '对比分析图';\r\n    },\r\n\r\n    /** 图表类型改变 */\r\n    handleChartTypeChange(type) {\r\n      this.chartType = type;\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 查询数据 */\r\n    async handleQuery() {\r\n      if (!this.validateQuery()) {\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      this.chartLoading = true;\r\n\r\n      try {\r\n        // 根据对比类型获取不同的数据\r\n        let chartData = [];\r\n        let paramDetails = [];\r\n\r\n        switch (this.queryParams.compareType) {\r\n          case 'material':\r\n            chartData = await this.getMaterialCompareData();\r\n            break;\r\n          case 'supplier':\r\n            chartData = await this.getSupplierCompareData();\r\n            break;\r\n          case 'paramNumber':\r\n            chartData = await this.getParamNumberCompareData();\r\n            break;\r\n          case 'processType':\r\n            chartData = await this.getProcessTypeCompareData();\r\n            break;\r\n          case 'timeTrend':\r\n            chartData = await this.getTimeTrendData();\r\n            break;\r\n          case 'supplierVsTest':\r\n            chartData = await this.getSupplierVsTestData();\r\n            break;\r\n        }\r\n\r\n        this.chartData = chartData;\r\n        this.updateTableColumns();\r\n        this.renderChart();\r\n\r\n        // 更新选中参数详情\r\n        this.updateSelectedParamDetails();\r\n\r\n      } catch (error) {\r\n        console.error('获取对比数据失败：', error);\r\n        this.$modal.msgError('获取对比数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n        this.chartLoading = false;\r\n      }\r\n    },\r\n\r\n    /** 验证查询条件 */\r\n    validateQuery() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length === 0) {\r\n        this.$message.warning('请选择至少一个参数编号');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length === 0) {\r\n        this.$message.warning('请选择至少一个材料');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplier' && this.queryParams.supplierNames.length === 0) {\r\n        this.$message.warning('请选择至少一个供应商');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'processType' && this.queryParams.processTypes.length === 0) {\r\n        this.$message.warning('请选择至少一个工艺类型');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'timeTrend' && !this.queryParams.dateRange) {\r\n        this.$message.warning('请选择时间范围');\r\n        return false;\r\n      }\r\n\r\n      if (compareType === 'supplierVsTest' && !this.queryParams.compareParam) {\r\n        this.$message.warning('请选择要对比的参数');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 获取材料对比数据 */\r\n    async getMaterialCompareData() {\r\n      const materialIds = this.queryParams.materialNames || [];\r\n      const compareData = [];\r\n\r\n      if (materialIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const materialId of materialIds) {\r\n        try {\r\n          // 通过材料ID查找对应的参数组，然后查找测试结果\r\n          const paramGroupResponse = await listProcessParamGroup({\r\n            materialId: materialId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroups = paramGroupResponse.rows || [];\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n\r\n          let allSupplierValues = [];\r\n          let allTestValues = [];\r\n\r\n          // 遍历该材料的所有参数组，获取测试结果\r\n          for (const group of paramGroups) {\r\n            const testResponse = await listTestResult({\r\n              groupId: group.groupId,\r\n              pageNum: 1,\r\n              pageSize: 1000\r\n            });\r\n\r\n            const testResults = testResponse.rows || [];\r\n            const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n            const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n            allSupplierValues = allSupplierValues.concat(supplierValues);\r\n            allTestValues = allTestValues.concat(testValues);\r\n          }\r\n\r\n          compareData.push({\r\n            name: material ? material.materialName : `材料${materialId}`,\r\n            supplier: material ? material.supplierName : '',\r\n            supplierAvg: allSupplierValues.length > 0 ? (allSupplierValues.reduce((a, b) => a + b, 0) / allSupplierValues.length).toFixed(2) : 0,\r\n            testAvg: allTestValues.length > 0 ? (allTestValues.reduce((a, b) => a + b, 0) / allTestValues.length).toFixed(2) : 0,\r\n            supplierMax: allSupplierValues.length > 0 ? Math.max(...allSupplierValues).toFixed(2) : 0,\r\n            testMax: allTestValues.length > 0 ? Math.max(...allTestValues).toFixed(2) : 0,\r\n            supplierMin: allSupplierValues.length > 0 ? Math.min(...allSupplierValues).toFixed(2) : 0,\r\n            testMin: allTestValues.length > 0 ? Math.min(...allTestValues).toFixed(2) : 0,\r\n            dataCount: allTestValues.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取材料${materialId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取供应商对比数据 */\r\n    async getSupplierCompareData() {\r\n      const suppliers = this.queryParams.supplierNames || [];\r\n      const compareData = [];\r\n\r\n      if (suppliers.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const supplier of suppliers) {\r\n        try {\r\n          const response = await listTestResult({\r\n            supplierName: supplier,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: supplier,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            accuracy: supplierValues.length > 0 && testValues.length > 0 ?\r\n              (100 - Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)) /\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length) * 100).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取供应商${supplier}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取参数编号对比数据 */\r\n    async getParamNumberCompareData() {\r\n      const paramGroupIds = this.queryParams.paramNumbers || [];\r\n      const compareData = [];\r\n\r\n      if (paramGroupIds.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const groupId of paramGroupIds) {\r\n        try {\r\n          // 获取测试结果数据\r\n          const response = await listTestResult({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          // 获取参数明细数据\r\n          const paramItemResponse = await listProcessParamItem({\r\n            groupId: groupId,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const paramGroup = this.paramNumberOptions.find(p => p.groupId === groupId);\r\n          const testResults = response.rows || [];\r\n          const paramItems = paramItemResponse.rows || [];\r\n\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          // 格式化参数明细信息\r\n          const paramDetails = paramItems.map(item => ({\r\n            paramName: item.paramName || 'N/A',\r\n            paramValue: item.paramValue !== null && item.paramValue !== undefined ?\r\n              String(item.paramValue) : 'N/A',\r\n            unit: item.unit || ''\r\n          }));\r\n\r\n          compareData.push({\r\n            name: paramGroup ? paramGroup.paramNumber : `参数${groupId}`,\r\n            material: paramGroup ? paramGroup.materialName : '',\r\n            processType: paramGroup ? paramGroup.processType : '',\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            deviation: supplierValues.length > 0 && testValues.length > 0 ?\r\n              Math.abs((supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length) -\r\n              (testValues.reduce((a, b) => a + b, 0) / testValues.length)).toFixed(2) : 0,\r\n            dataCount: testResults.length,\r\n            paramDetails: paramDetails, // 添加参数明细信息\r\n            groupId: groupId // 保存groupId用于后续使用\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取参数组${groupId}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取工艺类型对比数据 */\r\n    async getProcessTypeCompareData() {\r\n      const processTypes = this.queryParams.processTypes || [];\r\n      const compareData = [];\r\n\r\n      if (processTypes.length === 0) {\r\n        return compareData;\r\n      }\r\n\r\n      for (const processType of processTypes) {\r\n        try {\r\n          const response = await listTestResult({\r\n            processType: processType,\r\n            pageNum: 1,\r\n            pageSize: 1000\r\n          });\r\n\r\n          const testResults = response.rows || [];\r\n          const supplierValues = testResults.map(r => parseFloat(r.supplierDatasheetVal)).filter(v => !isNaN(v));\r\n          const testValues = testResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          compareData.push({\r\n            name: processType,\r\n            supplierAvg: supplierValues.length > 0 ? (supplierValues.reduce((a, b) => a + b, 0) / supplierValues.length).toFixed(2) : 0,\r\n            testAvg: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            stability: testValues.length > 1 ? this.calculateStandardDeviation(testValues).toFixed(2) : 0,\r\n            dataCount: testResults.length\r\n          });\r\n        } catch (error) {\r\n          console.error(`获取工艺类型${processType}数据失败：`, error);\r\n        }\r\n      }\r\n\r\n      return compareData;\r\n    },\r\n\r\n    /** 获取时间趋势数据 */\r\n    async getTimeTrendData() {\r\n      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const [startDate, endDate] = this.queryParams.dateRange;\r\n        const response = await listTestResult({\r\n          startDate: startDate,\r\n          endDate: endDate,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const trendData = [];\r\n\r\n        // 按日期分组\r\n        const dateGroups = {};\r\n        testResults.forEach(result => {\r\n          const date = result.createTime ? result.createTime.split(' ')[0] : '';\r\n          if (date && !dateGroups[date]) {\r\n            dateGroups[date] = [];\r\n          }\r\n          if (date) {\r\n            dateGroups[date].push(result);\r\n          }\r\n        });\r\n\r\n        // 计算每日平均值\r\n        Object.keys(dateGroups).sort().forEach(date => {\r\n          const dayResults = dateGroups[date];\r\n          const testValues = dayResults.map(r => parseFloat(r.testValue)).filter(v => !isNaN(v));\r\n\r\n          trendData.push({\r\n            date: date,\r\n            avgValue: testValues.length > 0 ? (testValues.reduce((a, b) => a + b, 0) / testValues.length).toFixed(2) : 0,\r\n            count: dayResults.length\r\n          });\r\n        });\r\n\r\n        return trendData;\r\n      } catch (error) {\r\n        console.error('获取时间趋势数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 获取供应商vs测试值对比数据 */\r\n    async getSupplierVsTestData() {\r\n      const groupId = this.queryParams.compareParam;\r\n\r\n      if (!groupId) {\r\n        return [];\r\n      }\r\n\r\n      try {\r\n        const response = await listTestResult({\r\n          groupId: groupId,\r\n          pageNum: 1,\r\n          pageSize: 1000\r\n        });\r\n\r\n        const testResults = response.rows || [];\r\n        const compareData = testResults.map(result => ({\r\n          name: result.materialName || '未知材料',\r\n          supplier: result.supplierName || '未知供应商',\r\n          supplierValue: parseFloat(result.supplierDatasheetVal) || 0,\r\n          testValue: parseFloat(result.testValue) || 0,\r\n          difference: Math.abs((parseFloat(result.supplierDatasheetVal) || 0) - (parseFloat(result.testValue) || 0)).toFixed(2),\r\n          createTime: result.createTime\r\n        }));\r\n\r\n        return compareData;\r\n      } catch (error) {\r\n        console.error('获取供应商vs测试值数据失败：', error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    /** 计算标准差 */\r\n    calculateStandardDeviation(values) {\r\n      const avg = values.reduce((a, b) => a + b, 0) / values.length;\r\n      const squareDiffs = values.map(value => Math.pow(value - avg, 2));\r\n      const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;\r\n      return Math.sqrt(avgSquareDiff);\r\n    },\r\n\r\n    /** 格式化参数明细显示 */\r\n    formatParamDetails(row, column, cellValue) {\r\n      if (!cellValue || !Array.isArray(cellValue)) {\r\n        return '暂无参数';\r\n      }\r\n\r\n      return cellValue.map(param => {\r\n        let text = param.paramName + ': ' + param.paramValue;\r\n        if (param.unit) {\r\n          text += ' ' + param.unit;\r\n        }\r\n        return text;\r\n      }).join('; ');\r\n    },\r\n\r\n    /** 更新表格列 */\r\n    updateTableColumns() {\r\n      const { compareType } = this.queryParams;\r\n\r\n      switch (compareType) {\r\n        case 'material':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplier':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '供应商', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'accuracy', label: '准确率(%)', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'paramNumber':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '参数编号', width: 120 },\r\n            { prop: 'material', label: '材料名称', width: 120 },\r\n            { prop: 'processType', label: '工艺类型', width: 100 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'deviation', label: '偏差', width: 80 },\r\n            { prop: 'paramDetails', label: '参数明细', width: 200 }\r\n          ];\r\n          break;\r\n        case 'processType':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '工艺类型', width: 150 },\r\n            { prop: 'supplierAvg', label: '供应商平均值', width: 120 },\r\n            { prop: 'testAvg', label: '测试平均值', width: 120 },\r\n            { prop: 'stability', label: '稳定性', width: 100 },\r\n            { prop: 'dataCount', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'timeTrend':\r\n          this.tableColumns = [\r\n            { prop: 'date', label: '日期', width: 120 },\r\n            { prop: 'avgValue', label: '平均值', width: 100 },\r\n            { prop: 'count', label: '数据量', width: 80 }\r\n          ];\r\n          break;\r\n        case 'supplierVsTest':\r\n          this.tableColumns = [\r\n            { prop: 'name', label: '材料名称', width: 150 },\r\n            { prop: 'supplier', label: '供应商', width: 120 },\r\n            { prop: 'supplierValue', label: '供应商值', width: 100 },\r\n            { prop: 'testValue', label: '测试值', width: 100 },\r\n            { prop: 'difference', label: '差值', width: 80 }\r\n          ];\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 渲染图表 */\r\n    renderChart() {\r\n      if (!this.chart || this.chartData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      let option = {};\r\n\r\n      switch (this.chartType) {\r\n        case 'line':\r\n          option = this.getLineChartOption();\r\n          break;\r\n        case 'bar':\r\n          option = this.getBarChartOption();\r\n          break;\r\n        case 'scatter':\r\n          option = this.getScatterChartOption();\r\n          break;\r\n        case 'radar':\r\n          option = this.getRadarChartOption();\r\n          break;\r\n        case 'heatmap':\r\n          option = this.getHeatmapChartOption();\r\n          break;\r\n      }\r\n\r\n      this.chart.setOption(option, true);\r\n    },\r\n\r\n    /** 获取折线图配置 */\r\n    getLineChartOption() {\r\n      // 根据对比类型生成不同的图表配置\r\n      const { compareType } = this.queryParams;\r\n\r\n      if (compareType === 'timeTrend') {\r\n        // 时间趋势图\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(param => {\r\n                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.date)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '平均值'\r\n          },\r\n          series: [{\r\n            name: '平均值',\r\n            type: 'line',\r\n            data: this.chartData.map(item => item.avgValue),\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }]\r\n        };\r\n      } else {\r\n        // 其他对比类型的折线图\r\n        const self = this;\r\n\r\n        return {\r\n          title: {\r\n            text: this.chartTitle,\r\n            left: 'center'\r\n          },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            backgroundColor: 'rgba(50, 50, 50, 0.95)',\r\n            borderColor: '#409EFF',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); border-radius: 8px; padding: 12px;',\r\n            formatter: function(params) {\r\n              const dataIndex = params[0].dataIndex;\r\n              const currentData = self.chartData[dataIndex];\r\n\r\n              let result = `<div style=\"font-size: 14px; font-weight: bold; color: #409EFF; margin-bottom: 8px;\">\r\n                            📊 ${params[0].name}\r\n                          </div>`;\r\n\r\n              // 显示基本对比数据\r\n              params.forEach(param => {\r\n                const color = param.color;\r\n                result += `<div style=\"margin: 4px 0; display: flex; align-items: center;\">\r\n                          <span style=\"display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;\"></span>\r\n                          <span style=\"font-weight: 500;\">${param.seriesName}:</span>\r\n                          <span style=\"margin-left: 8px; color: #67C23A; font-weight: bold;\">${self.formatNumber(param.value)}</span>\r\n                        </div>`;\r\n              });\r\n\r\n              // 根据对比类型显示详细信息\r\n              if (currentData) {\r\n                result += '<div style=\"border-top: 1px solid #666; margin: 8px 0; padding-top: 8px;\">';\r\n\r\n                if (self.queryParams.compareType === 'paramNumber' && currentData.paramDetails) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📋 参数明细信息</div>';\r\n                  if (currentData.material) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">材料:</span> ${currentData.material}</div>`;\r\n                  }\r\n                  if (currentData.processType) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">工艺:</span> ${currentData.processType}</div>`;\r\n                  }\r\n                  if (currentData.paramDetails && currentData.paramDetails.length > 0) {\r\n                    result += '<div style=\"margin: 4px 0; color: #909399;\">参数列表:</div>';\r\n                    currentData.paramDetails.slice(0, 5).forEach(param => {\r\n                      result += `<div style=\"margin: 1px 0; padding-left: 12px; font-size: 11px;\">\r\n                                • ${param.paramName}: <span style=\"color: #67C23A;\">${self.formatNumber(param.paramValue)}</span>\r\n                                ${param.unit ? ' <span style=\"color: #909399;\">' + param.unit + '</span>' : ''}\r\n                              </div>`;\r\n                    });\r\n                    if (currentData.paramDetails.length > 5) {\r\n                      result += `<div style=\"margin: 2px 0; padding-left: 12px; color: #909399; font-size: 11px;\">\r\n                                ... 还有 ${currentData.paramDetails.length - 5} 个参数\r\n                              </div>`;\r\n                    }\r\n                  }\r\n                } else if (self.queryParams.compareType === 'material' && currentData.supplier) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">🏭 供应商信息</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">供应商:</span> ${currentData.supplier}</div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'supplier' && currentData.accuracy) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">📈 质量指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">准确率:</span> <span style=\"color: ${currentData.accuracy > 90 ? '#67C23A' : currentData.accuracy > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.accuracy}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                } else if (self.queryParams.compareType === 'processType' && currentData.stability) {\r\n                  result += '<div style=\"color: #E6A23C; font-weight: bold; margin-bottom: 6px;\">⚙️ 工艺指标</div>';\r\n                  result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">稳定性:</span> <span style=\"color: ${currentData.stability > 90 ? '#67C23A' : currentData.stability > 80 ? '#E6A23C' : '#F56C6C'};\">${currentData.stability}%</span></div>`;\r\n                  if (currentData.dataCount) {\r\n                    result += `<div style=\"margin: 2px 0;\"><span style=\"color: #909399;\">数据量:</span> ${currentData.dataCount} 条</div>`;\r\n                  }\r\n                }\r\n\r\n                result += '</div>';\r\n              }\r\n\r\n              return result;\r\n            }\r\n          },\r\n          legend: {\r\n            top: '10%',\r\n            data: ['供应商数据', '测试数据']\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '4%',\r\n            bottom: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: this.chartData.map(item => item.name)\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '数值'\r\n          },\r\n          series: [\r\n            {\r\n              name: '供应商数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.supplierAvg || 0),\r\n              smooth: true,\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '测试数据',\r\n              type: 'line',\r\n              data: this.chartData.map(item => item.testAvg || 0),\r\n              smooth: true,\r\n              symbol: 'triangle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    /** 获取柱状图配置 */\r\n    getBarChartOption() {\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params[0].name + '<br/>';\r\n\r\n            // 显示基本对比数据\r\n            params.forEach(param => {\r\n              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: ['供应商数据', '测试数据']\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.chartData.map(item => item.name),\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数值'\r\n        },\r\n        series: [\r\n          {\r\n            name: '供应商数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.supplierAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          {\r\n            name: '测试数据',\r\n            type: 'bar',\r\n            data: this.chartData.map(item => item.testAvg || 0),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#91cc75'\r\n            }\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    /** 获取散点图配置 */\r\n    getScatterChartOption() {\r\n      const self = this;\r\n\r\n      // 散点图主要用于供应商vs测试值对比\r\n      const scatterData = this.chartData.map((item, index) => [\r\n        parseFloat(item.supplierValue || item.supplierAvg) || 0,\r\n        parseFloat(item.testValue || item.testAvg) || 0,\r\n        item.name, // 用于tooltip显示\r\n        index // 数据索引，用于获取详细信息\r\n      ]);\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const [supplierVal, testVal, name, dataIndex] = params.data;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = `${name}<br/>供应商值: ${supplierVal}<br/>测试值: ${testVal}<br/>差值: ${Math.abs(supplierVal - testVal).toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '供应商数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '测试数据',\r\n          scale: true,\r\n          axisLabel: {\r\n            formatter: '{value}'\r\n          }\r\n        },\r\n        series: [{\r\n          name: '数据对比',\r\n          type: 'scatter',\r\n          data: scatterData,\r\n          symbolSize: 8,\r\n          itemStyle: {\r\n            color: '#5470c6'\r\n          }\r\n        }, {\r\n          name: '理想线',\r\n          type: 'line',\r\n          data: [[0, 0], [Math.max(...scatterData.map(d => d[0])), Math.max(...scatterData.map(d => d[0]))]],\r\n          lineStyle: {\r\n            color: '#ff6b6b',\r\n            type: 'dashed'\r\n          },\r\n          symbol: 'none'\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取雷达图配置 */\r\n    getRadarChartOption() {\r\n      // 雷达图用于多维度对比，基于chartData生成指标\r\n      const indicators = [\r\n        { name: '供应商平均值', max: 100 },\r\n        { name: '测试平均值', max: 100 },\r\n        { name: '数据量', max: 50 },\r\n        { name: '准确率', max: 100 },\r\n        { name: '稳定性', max: 10 }\r\n      ];\r\n\r\n      const radarData = this.chartData.map(item => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const dataCount = parseInt(item.dataCount) || 0;\r\n        const accuracy = parseFloat(item.accuracy) || 0;\r\n        const stability = parseFloat(item.stability) || 0;\r\n\r\n        return {\r\n          name: item.name,\r\n          value: [\r\n            Math.min(supplierAvg, 100),\r\n            Math.min(testAvg, 100),\r\n            Math.min(dataCount, 50),\r\n            Math.min(accuracy, 100),\r\n            Math.min(stability, 10)\r\n          ]\r\n        };\r\n      });\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            const dataIndex = params.dataIndex;\r\n            const currentData = self.chartData[dataIndex];\r\n\r\n            let result = params.name + '<br/>';\r\n\r\n            // 显示雷达图数据\r\n            const indicators = ['供应商平均值', '测试平均值', '数据量', '准确率', '稳定性'];\r\n            params.value.forEach((value, index) => {\r\n              result += indicators[index] + ': ' + value + '<br/>';\r\n            });\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        legend: {\r\n          top: '10%',\r\n          data: radarData.map(item => item.name)\r\n        },\r\n        radar: {\r\n          indicator: indicators,\r\n          radius: '60%'\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          data: radarData\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 获取热力图配置 */\r\n    getHeatmapChartOption() {\r\n      // 热力图用于展示数据密度和分布\r\n      const xAxisData = [...new Set(this.chartData.map(item => item.name))];\r\n      const yAxisData = ['供应商数据', '测试数据', '偏差'];\r\n\r\n      const heatmapData = [];\r\n      this.chartData.forEach((item, xIndex) => {\r\n        const supplierAvg = parseFloat(item.supplierAvg) || 0;\r\n        const testAvg = parseFloat(item.testAvg) || 0;\r\n        const deviation = Math.abs(supplierAvg - testAvg);\r\n\r\n        heatmapData.push([xIndex, 0, supplierAvg]); // 供应商数据\r\n        heatmapData.push([xIndex, 1, testAvg]);     // 测试数据\r\n        heatmapData.push([xIndex, 2, deviation]);   // 偏差\r\n      });\r\n\r\n      const maxValue = Math.max(...heatmapData.map(d => d[2]));\r\n\r\n      const self = this;\r\n\r\n      return {\r\n        title: {\r\n          text: this.chartTitle,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          position: 'top',\r\n          formatter: function(params) {\r\n            const [x, y, value] = params.data;\r\n            const xLabel = xAxisData[x];\r\n            const yLabel = yAxisData[y];\r\n            const currentData = self.chartData[x];\r\n\r\n            let result = `${xLabel}<br/>${yLabel}: ${value.toFixed(2)}`;\r\n\r\n            // 如果是参数编号对比且有参数明细，显示参数明细信息\r\n            if (self.queryParams.compareType === 'paramNumber' && currentData && currentData.paramDetails) {\r\n              result += '<br/><br/><strong>参数明细信息：</strong><br/>';\r\n              if (currentData.material) {\r\n                result += '材料：' + currentData.material + '<br/>';\r\n              }\r\n              if (currentData.processType) {\r\n                result += '工艺：' + currentData.processType + '<br/>';\r\n              }\r\n              result += '参数列表：<br/>';\r\n\r\n              currentData.paramDetails.forEach(param => {\r\n                result += '• ' + param.paramName + ': ' + param.paramValue;\r\n                if (param.unit) {\r\n                  result += ' ' + param.unit;\r\n                }\r\n                result += '<br/>';\r\n              });\r\n            }\r\n\r\n            return result;\r\n          }\r\n        },\r\n        grid: {\r\n          height: '50%',\r\n          top: '15%'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: yAxisData\r\n        },\r\n        visualMap: {\r\n          min: 0,\r\n          max: maxValue || 100,\r\n          calculable: true,\r\n          orient: 'horizontal',\r\n          left: 'center',\r\n          bottom: '5%',\r\n          inRange: {\r\n            color: ['#50a3ba', '#eac736', '#d94e5d']\r\n          }\r\n        },\r\n        series: [{\r\n          type: 'heatmap',\r\n          data: heatmapData,\r\n          label: {\r\n            show: true,\r\n            formatter: function(params) {\r\n              return params.data[2].toFixed(1);\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          }\r\n        }]\r\n      };\r\n    },\r\n\r\n    /** 重置查询 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams = {\r\n        compareType: 'material',\r\n        paramNumbers: [],\r\n        materialNames: [],\r\n        supplierNames: [],\r\n        processTypes: [],\r\n        dateRange: null,\r\n        compareParam: null\r\n      };\r\n      this.updateChartTitle();\r\n    },\r\n\r\n    /** 刷新图表 */\r\n    refreshChart() {\r\n      if (this.chartData.length > 0) {\r\n        this.renderChart();\r\n      }\r\n    },\r\n\r\n    /** 导出图表 */\r\n    exportChart() {\r\n      if (!this.chart) {\r\n        this.$message.warning('请先生成图表');\r\n        return;\r\n      }\r\n\r\n      const url = this.chart.getDataURL({\r\n        type: 'png',\r\n        pixelRatio: 2,\r\n        backgroundColor: '#fff'\r\n      });\r\n\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${this.chartTitle}_${new Date().getTime()}.png`;\r\n      link.click();\r\n    },\r\n\r\n    /** 切换数据表显示 */\r\n    toggleDataTable() {\r\n      this.showDataTable = !this.showDataTable;\r\n      if (!this.showDataTable && this.chart) {\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 切换项目详情显示 */\r\n    toggleProjectDetails() {\r\n      this.showProjectDetails = !this.showProjectDetails;\r\n    },\r\n\r\n    /** 切换全屏显示 */\r\n    toggleFullscreen() {\r\n      if (this.isFullscreen) {\r\n        this.chartHeight = 400;\r\n        this.isFullscreen = false;\r\n      } else {\r\n        this.chartHeight = window.innerHeight - 200;\r\n        this.isFullscreen = true;\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 显示图表帮助 */\r\n    showChartHelp() {\r\n      this.helpDialogVisible = true;\r\n    },\r\n\r\n    /** 格式化数字显示 */\r\n    formatNumber(value) {\r\n      if (value === null || value === undefined || isNaN(value)) {\r\n        return 'N/A';\r\n      }\r\n      const num = parseFloat(value);\r\n      if (num === 0) return '0';\r\n      if (Math.abs(num) >= 1000000) {\r\n        return (num / 1000000).toFixed(2) + 'M';\r\n      } else if (Math.abs(num) >= 1000) {\r\n        return (num / 1000).toFixed(2) + 'K';\r\n      } else if (Math.abs(num) < 1) {\r\n        return num.toFixed(4);\r\n      } else {\r\n        return num.toFixed(2);\r\n      }\r\n    },\r\n\r\n    /** 格式化参数值显示（支持字符串类型，显示完整数值） */\r\n    formatParamValue(value) {\r\n      if (value === null || value === undefined || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 如果是字符串类型，直接返回完整字符串\r\n      if (typeof value === 'string') {\r\n        // 尝试解析为数字\r\n        const num = parseFloat(value);\r\n        if (!isNaN(num)) {\r\n          // 如果是数字字符串，保留6位小数并去除尾随零\r\n          return num.toFixed(6).replace(/\\.?0+$/, '');\r\n        }\r\n        // 如果不是数字字符串，直接返回\r\n        return value;\r\n      }\r\n\r\n      // 如果是数字类型，保留6位小数并去除尾随零\r\n      if (typeof value === 'number') {\r\n        return value.toFixed(6).replace(/\\.?0+$/, '');\r\n      }\r\n\r\n      return String(value);\r\n    },\r\n\r\n    /** 获取参数标签类型 */\r\n    getParamTagType(param) {\r\n      if (!param.paramValue) return '';\r\n      const value = parseFloat(param.paramValue);\r\n      if (isNaN(value)) return '';\r\n\r\n      // 根据参数值范围设置不同颜色\r\n      if (value > 100) return 'danger';\r\n      if (value > 50) return 'warning';\r\n      if (value > 10) return 'success';\r\n      return 'info';\r\n    },\r\n\r\n    /** 显示参数详情 */\r\n    showParamDetail(param) {\r\n      this.currentParamDetail = param;\r\n      this.paramDetailDialogVisible = true;\r\n    },\r\n\r\n    /** 更新选中参数详情 */\r\n    updateSelectedParamDetails() {\r\n      // 根据当前选择的对比类型和选项，更新参数详情信息\r\n      const { compareType } = this.queryParams;\r\n      this.selectedParamDetails = [];\r\n\r\n      if (compareType === 'material' && this.queryParams.materialNames.length > 0) {\r\n        this.queryParams.materialNames.forEach(materialId => {\r\n          const material = this.materialOptions.find(m => m.materialId === materialId);\r\n          if (material) {\r\n            this.selectedParamDetails.push({\r\n              name: material.materialName,\r\n              materialName: material.materialName,\r\n              supplierName: material.supplierName,\r\n              processType: material.processType,\r\n              testCount: material.testCount || 0,\r\n              statistics: material.statistics\r\n            });\r\n          }\r\n        });\r\n      } else if (compareType === 'supplier' && this.queryParams.supplierNames.length > 0) {\r\n        this.queryParams.supplierNames.forEach(supplier => {\r\n          this.selectedParamDetails.push({\r\n            name: supplier,\r\n            supplierName: supplier,\r\n            testCount: 0 // 这里可以从API获取实际数据\r\n          });\r\n        });\r\n      } else if (compareType === 'paramNumber' && this.queryParams.paramNumbers.length > 0) {\r\n        this.queryParams.paramNumbers.forEach(paramId => {\r\n          const param = this.paramNumberOptions.find(p => p.groupId === paramId);\r\n          if (param) {\r\n            this.selectedParamDetails.push({\r\n              name: param.paramNumber,\r\n              paramNumber: param.paramNumber,\r\n              materialName: param.materialName,\r\n              processType: param.processType,\r\n              mainParams: param.paramItems || [],\r\n              testCount: param.testCount || 0\r\n            });\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-guide-btn {\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-guide-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.guide-btn {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n}\r\n\r\n.export-btn {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\r\n  border: none;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.detail-item span {\r\n  color: #303133;\r\n}\r\n\r\n.statistics-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.statistics-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.statistics-content {\r\n  padding: 20px;\r\n}\r\n\r\n.statistics-title {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.statistics-desc {\r\n  font-size: 12px;\r\n  color: #C0C4CC;\r\n}\r\n\r\n.chart-help-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.chart-help-content h4 {\r\n  color: #409EFF;\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.chart-help-content ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.chart-help-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.chart-help-content strong {\r\n  color: #303133;\r\n}\r\n\r\n/* 使用指南样式 */\r\n.usage-guide-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.usage-guide-card .el-card__header {\r\n  background: transparent;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.guide-item h4 {\r\n  color: #fff;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.guide-item p {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n}\r\n\r\n/* 参数详情卡片样式 */\r\n.param-details-card {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n\r\n.param-detail-card {\r\n  height: 100%;\r\n  background: white;\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.param-detail-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.detail-section:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.section-title {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.detail-item {\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-icon {\r\n  color: #909399;\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.detail-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 60px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 8px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 10px;\r\n  color: #909399;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.params-container {\r\n  max-height: 80px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.params-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 2px;\r\n}\r\n\r\n.params-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* 使用建议样式优化 */\r\n.usage-suggestion {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.usage-suggestion p {\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据表格样式优化 */\r\n.trend-data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.trend-data-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.trend-data-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.param-details-container {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 5px;\r\n}\r\n\r\n.param-detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n  padding: 2px 6px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.param-name {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 5px;\r\n  min-width: 80px;\r\n}\r\n\r\n.param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n  font-family: 'Courier New', monospace;\r\n  margin-right: 5px;\r\n}\r\n\r\n.param-unit {\r\n  color: #909399;\r\n  font-size: 11px;\r\n}\r\n\r\n.material-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.material-info i {\r\n  color: #409EFF;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}