-- 数据录入模块修复验证脚本
-- 用于快速验证修复是否成功

SELECT '======== 数据录入模块修复验证 ========' as info;

-- 1. 检查test_plans视图是否存在
SELECT '1. 检查test_plans视图' as step;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✅ test_plans视图存在，包含 ', COUNT(*), ' 条记录')
        ELSE '❌ test_plans视图不存在或无数据'
    END as result
FROM information_schema.views 
WHERE table_schema = DATABASE() AND table_name = 'test_plans';

-- 2. 检查新表结构数据
SELECT '2. 检查新表结构数据' as step;
SELECT 'test_plan_group表' as table_name, COUNT(*) as record_count FROM test_plan_group
UNION ALL
SELECT 'test_param_item表', COUNT(*) FROM test_param_item
UNION ALL
SELECT 'test_results表', COUNT(*) FROM test_results;

-- 3. 验证test_plans视图数据
SELECT '3. 验证test_plans视图数据' as step;
SELECT 
    test_plan_id,
    plan_code,
    performance_type,
    performance_name,
    test_equipment,
    CASE 
        WHEN test_parameter IS NULL THEN '无参数'
        ELSE CONCAT('参数: ', LEFT(test_parameter, 50), CASE WHEN LENGTH(test_parameter) > 50 THEN '...' ELSE '' END)
    END as test_parameter_info
FROM test_plans
ORDER BY plan_code
LIMIT 5;

-- 4. 检查关联查询是否正常
SELECT '4. 检查关联查询' as step;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✅ 关联查询正常，找到 ', COUNT(*), ' 条关联记录')
        ELSE '⚠️  暂无测试结果数据，但关联查询结构正常'
    END as result
FROM test_results tr
LEFT JOIN test_plan_group tpg ON tr.plan_group_id = tpg.plan_group_id
LEFT JOIN test_param_item tpi ON tr.test_param_id = tpi.test_param_id
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
LEFT JOIN materials m ON pg.material_id = m.material_id;

-- 5. 验证数据录入所需的选项数据
SELECT '5. 验证选项数据' as step;

-- 5.1 测试方案编号选项
SELECT '测试方案编号选项:' as option_type, COUNT(DISTINCT plan_code) as option_count 
FROM test_plan_group 
WHERE plan_code IS NOT NULL AND plan_code != '';

-- 5.2 性能类型选项
SELECT '性能类型选项:' as option_type, COUNT(DISTINCT performance_type) as option_count 
FROM test_plan_group 
WHERE performance_type IS NOT NULL AND performance_type != '';

-- 5.3 测试设备选项
SELECT '测试设备选项:' as option_type, COUNT(DISTINCT test_equipment) as option_count 
FROM test_plan_group 
WHERE test_equipment IS NOT NULL AND test_equipment != '';

-- 6. 显示具体的选项数据
SELECT '6. 具体选项数据预览' as step;

-- 6.1 方案编号选项
SELECT 'plan_code' as option_type, plan_code as option_value 
FROM test_plan_group 
WHERE plan_code IS NOT NULL AND plan_code != '' 
ORDER BY plan_code 
LIMIT 5;

-- 6.2 性能类型选项
SELECT 'performance_type' as option_type, performance_type as option_value 
FROM test_plan_group 
WHERE performance_type IS NOT NULL AND performance_type != '' 
GROUP BY performance_type 
ORDER BY performance_type 
LIMIT 5;

-- 6.3 测试设备选项
SELECT 'test_equipment' as option_type, test_equipment as option_value 
FROM test_plan_group 
WHERE test_equipment IS NOT NULL AND test_equipment != '' 
GROUP BY test_equipment 
ORDER BY test_equipment 
LIMIT 5;

-- 7. 检查测试参数明细
SELECT '7. 检查测试参数明细' as step;
SELECT 
    tpg.plan_code,
    tpg.performance_name,
    COUNT(tpi.test_param_id) as param_count,
    GROUP_CONCAT(tpi.param_name ORDER BY tpi.param_name SEPARATOR ', ') as param_list
FROM test_plan_group tpg
LEFT JOIN test_param_item tpi ON tpg.plan_group_id = tpi.plan_group_id
GROUP BY tpg.plan_group_id, tpg.plan_code, tpg.performance_name
ORDER BY tpg.plan_code
LIMIT 5;

-- 8. 验证数据录入详情视图
SELECT '8. 验证详情视图' as step;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ test_result_detail_view视图存在且可查询'
        ELSE '❌ test_result_detail_view视图不存在或查询失败'
    END as result
FROM information_schema.views 
WHERE table_schema = DATABASE() AND table_name = 'test_result_detail_view';

-- 9. 最终验证结果
SELECT '======== 修复验证结果 ========' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = DATABASE() AND table_name = 'test_plans') > 0
         AND (SELECT COUNT(*) FROM test_plan_group) > 0
         AND (SELECT COUNT(*) FROM test_param_item) > 0
        THEN '✅ 数据录入模块修复成功！可以正常使用数据录入功能。'
        ELSE '❌ 修复未完成，请检查上述步骤中的问题。'
    END as final_result;

-- 10. 提供下一步操作建议
SELECT '======== 下一步操作建议 ========' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM test_results) = 0 
        THEN '💡 建议：数据库修复完成，现在可以重启应用并测试数据录入功能。'
        ELSE '💡 建议：数据库和数据都正常，可以直接测试数据录入功能。'
    END as suggestion;

-- 11. 常用测试查询
SELECT '======== 常用测试查询 ========' as info;

-- 显示可用于测试的数据
SELECT '可用于测试的测试方案:' as test_data_info;
SELECT 
    CONCAT('方案编号: ', plan_code, ' | 性能类型: ', performance_type, ' | 设备: ', test_equipment) as test_plan_info
FROM test_plan_group 
ORDER BY plan_code 
LIMIT 3;

SELECT '验证脚本执行完成！' as completion_status;
