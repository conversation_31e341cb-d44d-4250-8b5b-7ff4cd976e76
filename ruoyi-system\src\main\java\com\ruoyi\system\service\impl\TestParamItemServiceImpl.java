package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TestParamItemMapper;
import com.ruoyi.system.domain.TestParamItem;
import com.ruoyi.system.service.ITestParamItemService;

/**
 * 测试参数明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class TestParamItemServiceImpl implements ITestParamItemService 
{
    @Autowired
    private TestParamItemMapper testParamItemMapper;

    /**
     * 查询测试参数明细
     * 
     * @param testParamId 测试参数明细主键
     * @return 测试参数明细
     */
    @Override
    public TestParamItem selectTestParamItemByTestParamId(Long testParamId)
    {
        return testParamItemMapper.selectTestParamItemByTestParamId(testParamId);
    }

    /**
     * 查询测试参数明细列表
     * 
     * @param testParamItem 测试参数明细
     * @return 测试参数明细
     */
    @Override
    public List<TestParamItem> selectTestParamItemList(TestParamItem testParamItem)
    {
        return testParamItemMapper.selectTestParamItemList(testParamItem);
    }

    /**
     * 根据测试方案组ID查询测试参数明细列表
     * 
     * @param planGroupId 测试方案组ID
     * @return 测试参数明细集合
     */
    @Override
    public List<TestParamItem> selectTestParamItemByPlanGroupId(Long planGroupId)
    {
        return testParamItemMapper.selectTestParamItemByPlanGroupId(planGroupId);
    }

    /**
     * 新增测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    @Override
    public int insertTestParamItem(TestParamItem testParamItem)
    {
        testParamItem.setCreateTime(DateUtils.getNowDate());
        return testParamItemMapper.insertTestParamItem(testParamItem);
    }

    /**
     * 修改测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    @Override
    public int updateTestParamItem(TestParamItem testParamItem)
    {
        testParamItem.setUpdateTime(DateUtils.getNowDate());
        return testParamItemMapper.updateTestParamItem(testParamItem);
    }

    /**
     * 批量删除测试参数明细
     * 
     * @param testParamIds 需要删除的测试参数明细主键
     * @return 结果
     */
    @Override
    public int deleteTestParamItemByTestParamIds(Long[] testParamIds)
    {
        return testParamItemMapper.deleteTestParamItemByTestParamIds(testParamIds);
    }

    /**
     * 删除测试参数明细信息
     * 
     * @param testParamId 测试参数明细主键
     * @return 结果
     */
    @Override
    public int deleteTestParamItemByTestParamId(Long testParamId)
    {
        return testParamItemMapper.deleteTestParamItemByTestParamId(testParamId);
    }

    /**
     * 获取测试参数明细选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectTestParamItemOptions(String type)
    {
        if ("paramName".equals(type)) {
            return testParamItemMapper.selectParamNameOptions();
        } else if ("unit".equals(type)) {
            return testParamItemMapper.selectUnitOptions();
        }
        return new ArrayList<>();
    }
}
