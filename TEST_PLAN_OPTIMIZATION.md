# 测试方案配置模块优化调整说明

## 优化内容概述

本次优化解决了测试方案配置模块的以下问题：

### ✅ 已优化的问题：

1. **附件处理统一化** 
   - **问题**: 附件上传使用JSON格式，与其他模块不一致
   - **修复**: 统一使用逗号分隔的字符串格式，与材料、参数组、参数明细保持一致

2. **移除不存在的筛选项**
   - **问题**: 前端有状态、创建人等数据库中不存在的筛选项
   - **修复**: 移除状态和创建人筛选项，添加测试设备筛选项

3. **附件上传报错修复**
   - **问题**: 附件上传后编辑时无法正确显示，查看附件报错
   - **修复**: 统一附件数据格式处理，修复编辑和查看逻辑

4. **筛选项功能增强**
   - **问题**: 筛选项不支持点击显示候选项
   - **修复**: 添加焦点事件，支持点击显示所有候选项

## 修复的文件

### 数据库表结构：
- `sql/material_tables.sql` - 更新测试方案表结构

### 后端文件：
- `ruoyi-system/src/main/java/com/ruoyi/system/domain/TestPlan.java` - 实体类优化
- `ruoyi-system/src/main/java/com/ruoyi/system/mapper/TestPlanMapper.java` - 添加选项查询方法
- `ruoyi-system/src/main/resources/mapper/system/TestPlanMapper.xml` - SQL查询优化

### 前端文件：
- `ruoyi-ui/src/views/material/testPlan/index.vue` - 主要优化文件

## 具体修复内容

### 1. 数据库表结构调整

**原表结构问题：**
- 字段名不匹配
- 缺少必要字段

**修复后的表结构：**
```sql
CREATE TABLE `test_plans` (
  `test_plan_id`     INT NOT NULL AUTO_INCREMENT COMMENT '测试方案ID（主键）',
  `plan_code`        VARCHAR(50)  DEFAULT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `test_parameter`   VARCHAR(100) DEFAULT NULL COMMENT '测试参数',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_plan_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案表';
```

### 2. 前端筛选项优化

**移除的筛选项：**
- ❌ 状态筛选（数据库中不存在status字段）
- ❌ 创建人筛选（改为使用审计字段显示）

**新增的筛选项：**
- ✅ 测试设备筛选（支持自动补全和焦点显示候选项）

**优化的筛选项：**
- ✅ 方案编号：支持点击显示所有候选项
- ✅ 性能类型：支持点击显示所有候选项
- ✅ 测试设备：支持点击显示所有候选项

### 3. 附件处理统一化

**修复前的问题：**
```javascript
// 错误：直接将文件列表对象赋值
this.form.attachments = this.fileList;

// 错误：编辑时直接使用attachments
this.fileList = response.data.attachments || [];

// 错误：查看时使用JSON.parse
this.attachmentList = JSON.parse(attachments || '[]');
```

**修复后的处理：**
```javascript
// 正确：转换为逗号分隔的URL字符串
this.form.attachments = this.fileList.length > 0 
  ? this.fileList.map(file => file.url).join(',') 
  : null;

// 正确：编辑时解析字符串为文件对象
this.fileList = this.parseAttachments(response.data.attachments);

// 正确：查看时处理多种格式
handleViewAttachments(attachments) {
  // 支持逗号分隔字符串和JSON数组格式
}
```

### 4. 新增的核心方法

**parseAttachments方法：**
```javascript
parseAttachments(attachments) {
  if (!attachments) return [];
  
  if (Array.isArray(attachments)) return attachments;
  
  if (typeof attachments === 'string') {
    return attachments.split(',').filter(url => url.trim()).map((url, index) => ({
      name: url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`,
      url: url.trim(),
      uid: Date.now() + index,
      status: 'success'
    }));
  }
  
  return [];
}
```

## 验证步骤

### 1. 筛选功能验证
1. 进入测试方案管理页面
2. 点击方案编号输入框 → ✅ 应显示所有方案编号候选项
3. 点击性能类型输入框 → ✅ 应显示所有性能类型候选项
4. 点击测试设备输入框 → ✅ 应显示所有测试设备候选项
5. 输入部分内容 → ✅ 应进行模糊匹配过滤

### 2. 附件功能验证
1. 新增测试方案并上传附件 → ✅ 附件应正常上传
2. 保存后编辑该方案 → ✅ 附件应正确显示在文件列表中
3. 删除部分附件后保存 → ✅ 附件应正确更新
4. 点击查看附件 → ✅ 附件列表应正确显示，不为空
5. 点击下载附件 → ✅ 应能正常下载

### 3. 数据一致性验证
1. 检查数据库中attachments字段 → ✅ 应为逗号分隔的URL字符串
2. 与材料、参数组、参数明细的附件格式对比 → ✅ 应完全一致

## 注意事项

1. **数据库更新**：需要执行`sql/material_tables.sql`中的测试方案表创建语句
2. **数据迁移**：如果已有测试数据，需要将JSON格式的附件转换为逗号分隔格式
3. **权限配置**：确保测试方案相关的菜单权限正确配置
4. **文件上传**：确保文件上传服务正常运行

## 技术细节

### 附件数据格式统一
- **数据库存储**：`"file1.pdf,file2.jpg,file3.doc"`
- **前端显示**：`[{name, url, uid, status}, ...]`
- **API传输**：字符串格式（保存时）/ 文件对象数组（编辑时）

### 筛选项数据来源
- 方案编号：`SELECT DISTINCT plan_code FROM test_plans`
- 性能类型：`SELECT DISTINCT performance_type FROM test_plans`
- 测试设备：`SELECT DISTINCT test_equipment FROM test_plans`

优化完成后，测试方案配置模块应该与其他模块保持一致的用户体验和数据格式。
