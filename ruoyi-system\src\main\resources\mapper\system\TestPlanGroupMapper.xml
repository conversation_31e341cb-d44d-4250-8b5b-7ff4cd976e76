<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestPlanGroupMapper">
    
    <resultMap type="TestPlanGroup" id="TestPlanGroupResult">
        <result property="planGroupId"    column="plan_group_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="performanceType"    column="performance_type"    />
        <result property="performanceName"    column="performance_name"    />
        <result property="testEquipment"    column="test_equipment"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="TestPlanGroupTestParamItemResult" type="TestPlanGroup" extends="TestPlanGroupResult">
        <collection property="testParamItemList" notNullColumn="sub_test_param_id" javaType="java.util.List" resultMap="TestParamItemResult" />
    </resultMap>

    <resultMap type="TestParamItem" id="TestParamItemResult">
        <result property="testParamId"    column="sub_test_param_id"    />
        <result property="planGroupId"    column="sub_plan_group_id"    />
        <result property="paramName"    column="sub_param_name"    />
        <result property="paramValue"    column="sub_param_value"    />
        <result property="unit"    column="sub_unit"    />
        <result property="attachments"    column="sub_attachments"    />
        <result property="remark"    column="sub_remark"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
    </resultMap>

    <sql id="selectTestPlanGroupVo">
        select plan_group_id, plan_code, performance_type, performance_name, test_equipment, attachments, remark, create_by, create_time, update_by, update_time from test_plan_group
    </sql>

    <select id="selectTestPlanGroupList" parameterType="TestPlanGroup" resultMap="TestPlanGroupResult">
        <include refid="selectTestPlanGroupVo"/>
        <where>  
            <if test="planCode != null  and planCode != ''"> and plan_code like concat('%', #{planCode}, '%')</if>
            <if test="performanceType != null  and performanceType != ''"> and performance_type like concat('%', #{performanceType}, '%')</if>
            <if test="performanceName != null  and performanceName != ''"> and performance_name like concat('%', #{performanceName}, '%')</if>
            <if test="testEquipment != null  and testEquipment != ''"> and test_equipment like concat('%', #{testEquipment}, '%')</if>
        </where>
        order by plan_group_id desc
    </select>
    
    <select id="selectTestPlanGroupByPlanGroupId" parameterType="Long" resultMap="TestPlanGroupTestParamItemResult">
        select g.plan_group_id, g.plan_code, g.performance_type, g.performance_name, g.test_equipment, g.attachments, g.remark, g.create_by, g.create_time, g.update_by, g.update_time,
 t.test_param_id as sub_test_param_id, t.plan_group_id as sub_plan_group_id, t.param_name as sub_param_name, t.param_value as sub_param_value, t.unit as sub_unit, t.attachments as sub_attachments, t.remark as sub_remark, t.create_by as sub_create_by, t.create_time as sub_create_time, t.update_by as sub_update_by, t.update_time as sub_update_time
        from test_plan_group g
        left join test_param_item t on t.plan_group_id = g.plan_group_id
        where g.plan_group_id = #{planGroupId}
        order by t.test_param_id
    </select>
        
    <insert id="insertTestPlanGroup" parameterType="TestPlanGroup" useGeneratedKeys="true" keyProperty="planGroupId">
        insert into test_plan_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planCode != null">plan_code,</if>
            <if test="performanceType != null">performance_type,</if>
            <if test="performanceName != null">performance_name,</if>
            <if test="testEquipment != null">test_equipment,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planCode != null">#{planCode},</if>
            <if test="performanceType != null">#{performanceType},</if>
            <if test="performanceName != null">#{performanceName},</if>
            <if test="testEquipment != null">#{testEquipment},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTestPlanGroup" parameterType="TestPlanGroup">
        update test_plan_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="performanceType != null">performance_type = #{performanceType},</if>
            <if test="performanceName != null">performance_name = #{performanceName},</if>
            <if test="testEquipment != null">test_equipment = #{testEquipment},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where plan_group_id = #{planGroupId}
    </update>

    <delete id="deleteTestPlanGroupByPlanGroupId" parameterType="Long">
        delete from test_plan_group where plan_group_id = #{planGroupId}
    </delete>

    <delete id="deleteTestPlanGroupByPlanGroupIds" parameterType="String">
        delete from test_plan_group where plan_group_id in 
        <foreach item="planGroupId" collection="array" open="(" separator="," close=")">
            #{planGroupId}
        </foreach>
    </delete>

    <select id="selectPlanCodeOptions" resultType="String">
        SELECT DISTINCT plan_code FROM test_plan_group
        WHERE plan_code IS NOT NULL AND plan_code != ''
        ORDER BY plan_code
    </select>

    <select id="selectPerformanceTypeOptions" resultType="String">
        SELECT DISTINCT performance_type FROM test_plan_group
        WHERE performance_type IS NOT NULL AND performance_type != ''
        ORDER BY performance_type
    </select>

    <select id="selectTestEquipmentOptions" resultType="String">
        SELECT DISTINCT test_equipment FROM test_plan_group
        WHERE test_equipment IS NOT NULL AND test_equipment != ''
        ORDER BY test_equipment
    </select>

</mapper>
