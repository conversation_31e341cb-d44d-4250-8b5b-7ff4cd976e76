package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TestPlanGroup;

/**
 * 测试方案组Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface TestPlanGroupMapper 
{
    /**
     * 查询测试方案组
     * 
     * @param planGroupId 测试方案组主键
     * @return 测试方案组
     */
    public TestPlanGroup selectTestPlanGroupByPlanGroupId(Long planGroupId);

    /**
     * 查询测试方案组列表
     * 
     * @param testPlanGroup 测试方案组
     * @return 测试方案组集合
     */
    public List<TestPlanGroup> selectTestPlanGroupList(TestPlanGroup testPlanGroup);

    /**
     * 新增测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    public int insertTestPlanGroup(TestPlanGroup testPlanGroup);

    /**
     * 修改测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    public int updateTestPlanGroup(TestPlanGroup testPlanGroup);

    /**
     * 删除测试方案组
     * 
     * @param planGroupId 测试方案组主键
     * @return 结果
     */
    public int deleteTestPlanGroupByPlanGroupId(Long planGroupId);

    /**
     * 批量删除测试方案组
     * 
     * @param planGroupIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestPlanGroupByPlanGroupIds(Long[] planGroupIds);

    /**
     * 获取方案编号选项
     *
     * @return 方案编号列表
     */
    public List<String> selectPlanCodeOptions();

    /**
     * 获取性能类型选项
     *
     * @return 性能类型列表
     */
    public List<String> selectPerformanceTypeOptions();

    /**
     * 获取测试设备选项
     *
     * @return 测试设备列表
     */
    public List<String> selectTestEquipmentOptions();
}
