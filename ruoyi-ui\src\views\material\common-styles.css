/* 材料管理系统统一样式文件 */

/* ==================== 全局变量 ==================== */
:root {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --info-gradient: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  
  --text-primary: #2c3e50;
  --text-secondary: #606266;
  --text-placeholder: #909399;
  --text-disabled: #C0C4CC;
  
  --border-color: #e4e7ed;
  --border-light: #e9ecef;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 12px;
}

/* ==================== 页面布局 ==================== */
.app-container {
  padding: 20px;
  background: var(--background-light);
  min-height: calc(100vh - 84px);
}

.page-header {
  background: var(--background-white);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.page-title i {
  color: var(--primary-color);
  font-size: 20px;
}

.page-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ==================== 卡片样式 ==================== */
.enhanced-card {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: var(--primary-gradient);
  color: white;
  border-radius: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.item-count-badge {
  margin-left: 8px;
}

/* ==================== 搜索区域 ==================== */
.search-section {
  background: var(--background-light);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
  border: 1px solid var(--border-light);
}

.search-form {
  margin: 0;
}

.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 20px;
  color: white;
}

.reset-btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 20px;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  color: var(--text-secondary);
}

/* ==================== 表格样式 ==================== */
.enhanced-table {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.enhanced-table .el-table__header {
  background: var(--primary-gradient);
}

.enhanced-table .el-table__header th {
  background: transparent;
  color: white;
  font-weight: 600;
  border-bottom: none;
}

.enhanced-table .el-table__body tr:hover > td {
  background-color: #f0f9ff !important;
}

.enhanced-table .current-row {
  background-color: #e6f7ff !important;
}

.enhanced-table .current-row:hover > td {
  background-color: #e6f7ff !important;
}

.table-container {
  border-radius: var(--border-radius);
  overflow: hidden;
}

/* ==================== 表格单元格样式 ==================== */
.index-number {
  font-weight: bold;
  color: var(--primary-color);
}

/* 材料相关样式 */
.material-name-cell, .material-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.material-icon {
  color: var(--primary-color);
}

.material-name {
  font-weight: 600;
  color: var(--text-primary);
}

/* 供应商样式 */
.supplier-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.supplier-icon {
  color: #E6A23C;
}

/* 工艺参数样式 */
.process-type-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.process-icon {
  color: #67C23A;
}

.process-type {
  font-weight: 600;
  color: var(--text-primary);
}

/* 参数编号样式 */
.param-number-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-number-icon {
  color: var(--primary-color);
}

.param-number {
  font-weight: 600;
  color: var(--text-primary);
}

/* 参数名称样式 */
.param-name-cell, .test-param-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-icon, .test-param-icon {
  color: #E6A23C;
}

.param-name, .test-param-name {
  font-weight: 600;
  color: var(--text-primary);
}

/* 参数值样式 */
.param-value, .test-param-value {
  font-weight: 600;
  color: #67C23A;
  font-family: 'Courier New', monospace;
}

/* 测试方案样式 */
.plan-code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-icon {
  color: var(--primary-color);
}

.plan-code {
  font-weight: 600;
  color: var(--text-primary);
}

.performance-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.performance-icon {
  color: #E6A23C;
}

.equipment-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.equipment-icon {
  color: #67C23A;
}

/* ==================== 用户信息和时间信息 ==================== */
.user-info, .time-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.user-info i, .time-info i {
  color: var(--text-placeholder);
}

/* ==================== 附件和操作按钮 ==================== */
.attachment-btn {
  color: var(--primary-color);
  font-weight: 500;
}

.attachment-btn:hover {
  color: #66b1ff;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-btn {
  color: var(--primary-color);
  font-weight: 500;
}

.edit-btn:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.copy-btn {
  color: #67C23A;
  font-weight: 500;
}

.copy-btn:hover {
  color: #85ce61;
  background-color: #f0f9ff;
}

.delete-btn {
  color: #F56C6C;
  font-weight: 500;
}

.delete-btn:hover {
  color: #f78989;
  background-color: #fef0f0;
}

/* ==================== 通用样式 ==================== */
.empty-data {
  color: var(--text-disabled);
  font-style: italic;
}

.description-text {
  color: var(--text-secondary);
  font-size: 13px;
}

/* 指示器样式 */
.material-indicator, .param-group-indicator, .plan-group-indicator {
  margin-left: 10px;
}

.material-indicator .el-tag, 
.param-group-indicator .el-tag, 
.plan-group-indicator .el-tag {
  border-radius: var(--border-radius-large);
  padding: 0 8px;
  font-size: 12px;
}

/* ==================== 统一按钮样式 ==================== */
.el-button--primary {
  background: var(--primary-gradient) !important;
  border: none !important;
}

.el-button--success {
  background: var(--success-gradient) !important;
  border: none !important;
}

.el-button--info {
  background: var(--info-gradient) !important;
  border: none !important;
}

.el-button--warning {
  background: var(--warning-gradient) !important;
  border: none !important;
}

.el-button--danger {
  background: var(--danger-gradient) !important;
  border: none !important;
}
