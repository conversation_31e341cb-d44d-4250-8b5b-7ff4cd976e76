{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue?vue&type=style&index=0&id=76a7d25c&scoped=true&lang=css", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue", "mtime": 1754285271290}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753339847609}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753339890164}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753339854740}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7DQp9DQoNCi8qIOmhtemdouWktOmDqOagt+W8jyAqLw0KLnBhZ2UtaGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCn0NCg0KLnBhZ2UtdGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDI0cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnBhZ2UtdGl0bGUgaSB7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMjhweDsNCn0NCg0KLnBhZ2UtZGVzY3JpcHRpb24gcCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLyog5aKe5by65Y2h54mH5qC35byPICovDQouZW5oYW5jZWQtY2FyZCB7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQp9DQoNCi5lbmhhbmNlZC1jYXJkOmhvdmVyIHsNCiAgYm94LXNoYWRvdzogMCA4cHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMTIpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQp9DQoNCi8qIOWNoeeJh+WktOmDqOagt+W8jyAqLw0KLmNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAwOw0KfQ0KDQouaGVhZGVyLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEycHg7DQp9DQoNCi5oZWFkZXItbGVmdCBpIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMThweDsNCn0NCg0KLmhlYWRlci10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQouaXRlbS1jb3VudC1iYWRnZSB7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQp9DQoNCi5oZWFkZXItcmlnaHQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCn0NCg0KLmhlYWRlci1yaWdodCAuZWwtYnV0dG9uIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiDmkJzntKLljLrln5/moLflvI8gKi8NCi5zZWFyY2gtc2VjdGlvbiB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnNlYXJjaC1mb3JtIC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5zZWFyY2gtZm9ybSAuZWwtYXV0b2NvbXBsZXRlIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQouc2VhcmNoLWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlcjogbm9uZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQoucmVzZXQtYnRuIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiDooajmoLzlrrnlmajmoLflvI8gKi8NCi50YWJsZS1jb250YWluZXIgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA1KTsNCn0NCg0KLyog5aKe5by66KGo5qC85qC35byPICovDQouZW5oYW5jZWQtdGFibGUgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB0aCB7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50Ow0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5lbmhhbmNlZC10YWJsZSAuZWwtdGFibGVfX2JvZHkgdHI6aG92ZXIgPiB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZmYgIWltcG9ydGFudDsNCn0NCg0KLmVuaGFuY2VkLXRhYmxlIC5jdXJyZW50LXJvdyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmYgIWltcG9ydGFudDsNCn0NCg0KLmVuaGFuY2VkLXRhYmxlIC5jdXJyZW50LXJvdzpob3ZlciA+IHRkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2U2ZjdmZiAhaW1wb3J0YW50Ow0KfQ0KDQovKiDooajmoLzljZXlhYPmoLzmoLflvI8gKi8NCi5pbmRleC1udW1iZXIgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5wbGFuLWNvZGUtY2VsbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQoucGxhbi1pY29uIHsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5wbGFuLWNvZGUgew0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLnBlcmZvcm1hbmNlLW5hbWUtY2VsbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQoucGVyZm9ybWFuY2UtaWNvbiB7DQogIGNvbG9yOiAjRTZBMjNDOw0KfQ0KDQouZXF1aXBtZW50LWNlbGwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmVxdWlwbWVudC1pY29uIHsNCiAgY29sb3I6ICM2N0MyM0E7DQp9DQoNCi51c2VyLWluZm8sIC50aW1lLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDVweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQoudXNlci1pbmZvIGksIC50aW1lLWluZm8gaSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouYXR0YWNobWVudC1idG4gew0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmF0dGFjaG1lbnQtYnRuOmhvdmVyIHsNCiAgY29sb3I6ICM2NmIxZmY7DQp9DQoNCi5lbXB0eS1kYXRhIHsNCiAgY29sb3I6ICNDMEM0Q0M7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLyog5rWL6K+V5Y+C5pWw5piO57uG5qC35byPICovDQoudGVzdC1wYXJhbS1uYW1lLWNlbGwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLnRlc3QtcGFyYW0taWNvbiB7DQogIGNvbG9yOiAjRTZBMjNDOw0KfQ0KDQoudGVzdC1wYXJhbS1uYW1lIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICMyYzNlNTA7DQp9DQoNCi50ZXN0LXBhcmFtLXZhbHVlIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM2N0MyM0E7DQp9DQoNCi8qIOaMh+ekuuWZqOagt+W8jyAqLw0KLnBsYW4tZ3JvdXAtaW5kaWNhdG9yIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQp9DQoNCi5wbGFuLWdyb3VwLWluZGljYXRvciAuZWwtdGFnIHsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMCA4cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLyog5pON5L2c5oyJ6ZKu5qC35byPICovDQouYWN0aW9uLWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi5lZGl0LWJ0biB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouZWRpdC1idG46aG92ZXIgew0KICBjb2xvcjogIzY2YjFmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2VjZjVmZjsNCn0NCg0KLmNvcHktYnRuIHsNCiAgY29sb3I6ICM2N0MyM0E7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5jb3B5LWJ0bjpob3ZlciB7DQogIGNvbG9yOiAjODVjZTYxOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KfQ0KDQouZGVsZXRlLWJ0biB7DQogIGNvbG9yOiAjRjU2QzZDOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouZGVsZXRlLWJ0bjpob3ZlciB7DQogIGNvbG9yOiAjZjc4OTg5Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmVmMGYwOw0KfQ0KDQouZGlhbG9nLWZvb3RlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmVsLXVwbG9hZF9fdGlwIHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWFyZ2luLXRvcDogN3B4Ow0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuYXBwLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMTBweDsNCiAgfQ0KDQogIC5wYWdlLWhlYWRlciB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KDQogIC5oZWFkZXItcmlnaHQgew0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgfQ0KDQogIC5zZWFyY2gtZm9ybSB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgfQ0KDQogIC5zZWFyY2gtZm9ybSAuZWwtZm9ybS1pdGVtIHsNCiAgICBtYXJnaW4tcmlnaHQ6IDA7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCg0KLyog57uf5LiA5oyJ6ZKu5qC35byPICovDQouZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0tc3VjY2VzcyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1NmFiMmYgMCUsICNhOGU2Y2YgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5lbC1idXR0b24tLWluZm8gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTdhMmI4IDAlLCAjMTM4NDk2IDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtYnV0dG9uLS13YXJuaW5nIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLmVsLWJ1dHRvbi0tZGFuZ2VyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI2YiAwJSwgI2VlNWEyNCAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqoCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/testPlan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span>测试方案配置管理</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理测试方案组和测试参数的二层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击测试方案组行查看对应的测试参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 测试方案组表格 -->\r\n    <el-card class=\"plan-group-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"header-title\">测试方案组管理</span>\r\n          <el-badge :value=\"planGroupTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddPlanGroup\">\r\n            <span>新增方案组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"planGroupMultiple\" @click=\"handleBatchDeletePlanGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试方案组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"planGroupQueryParams\" ref=\"planGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.planCode\"\r\n              :fetch-suggestions=\"queryPlanCodeSuggestions\"\r\n              placeholder=\"请输入方案编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handlePlanCodeSelect\"\r\n              @focus=\"handlePlanCodeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-document\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.performanceType\"\r\n              :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n              placeholder=\"请输入性能类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handlePerformanceTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-lightning\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.testEquipment\"\r\n              :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n              placeholder=\"请输入测试设备\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleTestEquipmentFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-cpu\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handlePlanGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetPlanGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试方案组表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"planGroupLoading\"\r\n          :data=\"planGroupList\"\r\n          style=\"width: 100%\"\r\n          @selection-change=\"handlePlanGroupSelectionChange\"\r\n          @row-click=\"handlePlanGroupRowClick\"\r\n          highlight-current-row\r\n          :row-class-name=\"getPlanGroupRowClassName\"\r\n          ref=\"planGroupTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试方案数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (planGroupQueryParams.pageNum - 1) * planGroupQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"planCode\" label=\"方案编号\" min-width=\"160\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"plan-code-cell\">\r\n                <i class=\"el-icon-document plan-icon\"></i>\r\n                <span class=\"plan-code\">{{ scope.row.planCode }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceType\" label=\"性能类型\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" :type=\"getPerformanceTypeTag(scope.row.performanceType)\">\r\n                {{ scope.row.performanceType }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceName\" label=\"性能名称\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"performance-name-cell\">\r\n                <i class=\"el-icon-lightning performance-icon\"></i>\r\n                <span>{{ scope.row.performanceName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"testEquipment\" label=\"测试设备\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"equipment-cell\">\r\n                <i class=\"el-icon-cpu equipment-icon\"></i>\r\n                <span>{{ scope.row.testEquipment || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewPlanGroupAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleEditPlanGroup(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleCopyPlanGroup(scope.row)\" class=\"copy-btn\">\r\n                  <i class=\"el-icon-copy-document\"></i>\r\n                  复制\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDeletePlanGroup(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"planGroupTotal > 0\"\r\n        :total=\"planGroupTotal\"\r\n        :page.sync=\"planGroupQueryParams.pageNum\"\r\n        :limit.sync=\"planGroupQueryParams.pageSize\"\r\n        @pagination=\"getPlanGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试参数明细表格 -->\r\n    <el-card class=\"test-param-card enhanced-card\" v-show=\"currentPlanGroup\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">测试参数明细</span>\r\n          <div class=\"plan-group-indicator\" v-if=\"currentPlanGroup\">\r\n            <el-tag type=\"warning\" size=\"small\">\r\n              <i class=\"el-icon-document\"></i>\r\n              {{ currentPlanGroup.planCode }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"testParamTotal\" class=\"item-count-badge\" type=\"warning\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>新增参数</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"testParamMultiple || !currentPlanGroup\" @click=\"handleBatchDeleteTestParam\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试参数明细查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"testParamQueryParams\" ref=\"testParamQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.paramName\"\r\n              :fetch-suggestions=\"queryParamNameSuggestions\"\r\n              placeholder=\"请输入参数名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-data-line\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数单位\" prop=\"unit\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.unit\"\r\n              :fetch-suggestions=\"queryUnitSuggestions\"\r\n              placeholder=\"请输入参数单位\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleUnitFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-price-tag\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleTestParamQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetTestParamQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试参数明细表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"testParamLoading\"\r\n          :data=\"testParamList\"\r\n          @selection-change=\"handleTestParamSelectionChange\"\r\n          @row-click=\"handleTestParamRowClick\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getTestParamRowClassName\"\r\n          ref=\"testParamTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试参数数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (testParamQueryParams.pageNum - 1) * testParamQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"test-param-name-cell\">\r\n                <i class=\"el-icon-data-line test-param-icon\"></i>\r\n                <span class=\"test-param-name\">{{ scope.row.paramName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"test-param-value\" v-if=\"scope.row.paramValue !== null\">{{ scope.row.paramValue }}</span>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"success\" v-if=\"scope.row.unit\">{{ scope.row.unit }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewTestParamAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditTestParam(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteTestParam(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"testParamTotal > 0\"\r\n        :total=\"testParamTotal\"\r\n        :page.sync=\"testParamQueryParams.pageNum\"\r\n        :limit.sync=\"testParamQueryParams.pageSize\"\r\n        @pagination=\"getTestParamList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试方案组对话框 -->\r\n    <el-dialog :title=\"planGroupTitle\" :visible.sync=\"planGroupOpen\" width=\"800px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"planGroupForm\" :model=\"planGroupForm\" :rules=\"planGroupRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n              <el-input v-model=\"planGroupForm.planCode\" placeholder=\"请输入方案编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n              <el-input v-model=\"planGroupForm.performanceType\" placeholder=\"请输入性能类型\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能名称\" prop=\"performanceName\">\r\n              <el-input v-model=\"planGroupForm.performanceName\" placeholder=\"请输入性能名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n              <el-input v-model=\"planGroupForm.testEquipment\" placeholder=\"请输入测试设备\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"planGroupUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"planGroupFileList\"\r\n            :on-success=\"handlePlanGroupUploadSuccess\"\r\n            :on-remove=\"handlePlanGroupUploadRemove\"\r\n            :before-upload=\"beforePlanGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"planGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPlanGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPlanGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 测试参数明细对话框 -->\r\n    <el-dialog :title=\"testParamTitle\" :visible.sync=\"testParamOpen\" width=\"600px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"testParamForm\" :model=\"testParamForm\" :rules=\"testParamRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"testParamForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"testParamForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"testParamForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"testParamUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"testParamFileList\"\r\n            :on-success=\"handleTestParamUploadSuccess\"\r\n            :on-remove=\"handleTestParamUploadRemove\"\r\n            :before-upload=\"beforeTestParamUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"testParamForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitTestParamForm\">确 定</el-button>\r\n        <el-button @click=\"cancelTestParam\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestPlanGroup, getTestPlanGroup, delTestPlanGroup, addTestPlanGroup, updateTestPlanGroup,\r\n  exportTestPlanGroup, getTestPlanGroupOptions\r\n} from \"@/api/material/testPlanGroup\";\r\nimport {\r\n  listTestParamItem, getTestParamItem, delTestParamItem, addTestParamItem, updateTestParamItem,\r\n  exportTestParamItem, getTestParamItemOptions, listByPlanGroupId\r\n} from \"@/api/material/testParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestPlan\",\r\n  data() {\r\n    return {\r\n      // 测试方案组相关\r\n      planGroupLoading: false,\r\n      planGroupList: [],\r\n      planGroupTotal: 0,\r\n      planGroupOpen: false,\r\n      planGroupTitle: \"\",\r\n      planGroupForm: {},\r\n      planGroupFileList: [],\r\n      planGroupIds: [],\r\n      planGroupSingle: true,\r\n      planGroupMultiple: true,\r\n      planGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planCode: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      planGroupRules: {\r\n        planCode: [\r\n          { required: true, message: \"方案编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        performanceType: [\r\n          { required: true, message: \"性能类型不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 测试参数明细相关\r\n      testParamLoading: false,\r\n      testParamList: [],\r\n      testParamTotal: 0,\r\n      testParamOpen: false,\r\n      testParamTitle: \"\",\r\n      testParamForm: {},\r\n      testParamFileList: [],\r\n      testParamIds: [],\r\n      testParamSingle: true,\r\n      testParamMultiple: true,\r\n      testParamQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planGroupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      testParamRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 当前选中的测试方案组\r\n      currentPlanGroup: null,\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      planCodeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      unitSuggestions: [],\r\n\r\n      // 上传相关\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getPlanGroupList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 获取性能类型标签颜色 */\r\n    getPerformanceTypeTag(type) {\r\n      const typeMap = {\r\n        '力学性能': 'success',\r\n        '电学性能': 'primary',\r\n        '热学性能': 'warning',\r\n        '光学性能': 'info',\r\n        '化学性能': 'danger',\r\n        '物理性能': ''\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 查询测试方案组列表 */\r\n    getPlanGroupList() {\r\n      this.planGroupLoading = true;\r\n      listTestPlanGroup(this.planGroupQueryParams).then(response => {\r\n        this.planGroupList = response.rows;\r\n        this.planGroupTotal = response.total;\r\n        this.planGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询测试参数明细列表 */\r\n    getTestParamList() {\r\n      if (!this.currentPlanGroup) return;\r\n\r\n      this.testParamLoading = true;\r\n      this.testParamQueryParams.planGroupId = this.currentPlanGroup.planGroupId;\r\n      listTestParamItem(this.testParamQueryParams).then(response => {\r\n        this.testParamList = response.rows;\r\n        this.testParamTotal = response.total;\r\n        this.testParamLoading = false;\r\n\r\n        // 更新参数筛选选项\r\n        this.$nextTick(() => {\r\n          this.updateParamFilterOptions();\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取方案编号建议\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取性能类型建议\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取测试设备建议\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数名称建议\r\n      getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数单位建议\r\n      getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 搜索建议方法 */\r\n    queryPlanCodeSuggestions(queryString, cb) {\r\n      let suggestions = this.planCodeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.planCodeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 焦点事件 */\r\n    handlePlanCodeFocus() {\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handlePerformanceTypeFocus() {\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleTestEquipmentFocus() {\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleParamNameFocus() {\r\n      // 只获取当前选中测试方案组下的参数名称选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数名称\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数名称\r\n        getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleUnitFocus() {\r\n      // 只获取当前选中测试方案组下的参数单位选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数单位\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数单位\r\n        getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 测试方案组相关方法 */\r\n    handlePlanGroupSelectionChange(selection) {\r\n      this.planGroupIds = selection.map(item => item.planGroupId);\r\n      this.planGroupSingle = selection.length !== 1;\r\n      this.planGroupMultiple = !selection.length;\r\n    },\r\n\r\n    handlePlanGroupRowClick(row) {\r\n      this.currentPlanGroup = row;\r\n      this.$refs.planGroupTable.toggleRowSelection(row);\r\n\r\n      // 清空测试参数筛选条件\r\n      this.testParamQueryParams.paramName = null;\r\n      this.testParamQueryParams.unit = null;\r\n      this.resetForm(\"testParamQueryForm\");\r\n\r\n      // 加载测试参数列表\r\n      this.getTestParamList();\r\n\r\n      // 延迟更新筛选选项，确保testParamList已加载\r\n      this.$nextTick(() => {\r\n        this.updateParamFilterOptions();\r\n      });\r\n    },\r\n\r\n    getPlanGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentPlanGroup && row.planGroupId === this.currentPlanGroup.planGroupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    handlePlanGroupQuery() {\r\n      this.planGroupQueryParams.pageNum = 1;\r\n      this.getPlanGroupList();\r\n    },\r\n\r\n    resetPlanGroupQuery() {\r\n      this.resetForm(\"planGroupQueryForm\");\r\n      this.handlePlanGroupQuery();\r\n    },\r\n\r\n    handleAddPlanGroup() {\r\n      this.resetPlanGroupForm();\r\n      this.planGroupOpen = true;\r\n      this.planGroupTitle = \"添加测试方案组\";\r\n    },\r\n\r\n    handleEditPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId || this.planGroupIds;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"修改测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleCopyPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.planGroupForm.planGroupId = null;\r\n        this.planGroupForm.planCode = this.planGroupForm.planCode + \"_copy\";\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"复制测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleDeletePlanGroup(row) {\r\n      const planGroupIds = row.planGroupId || this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除测试方案组编号为\"' + row.planCode + '\"的数据项？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        if (this.currentPlanGroup && this.currentPlanGroup.planGroupId === row.planGroupId) {\r\n          this.currentPlanGroup = null;\r\n          this.testParamList = [];\r\n        }\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeletePlanGroup() {\r\n      const planGroupIds = this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + planGroupIds.length + '条数据？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        this.currentPlanGroup = null;\r\n        this.testParamList = [];\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportPlanGroup() {\r\n      this.download('material/testPlanGroup/export', {\r\n        ...this.planGroupQueryParams\r\n      }, `test_plan_group_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 更新参数筛选选项 */\r\n    updateParamFilterOptions() {\r\n      if (this.currentPlanGroup && this.testParamList.length > 0) {\r\n        // 从当前测试参数列表中提取唯一的参数名称和单位\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      }\r\n    },\r\n\r\n    /** 测试参数明细相关方法 */\r\n    handleTestParamSelectionChange(selection) {\r\n      this.testParamIds = selection.map(item => item.testParamId);\r\n      this.testParamSingle = selection.length !== 1;\r\n      this.testParamMultiple = !selection.length;\r\n    },\r\n\r\n    handleTestParamRowClick(row) {\r\n      this.$refs.testParamTable.toggleRowSelection(row);\r\n    },\r\n\r\n    handleTestParamQuery() {\r\n      this.testParamQueryParams.pageNum = 1;\r\n      this.getTestParamList();\r\n    },\r\n\r\n    resetTestParamQuery() {\r\n      this.resetForm(\"testParamQueryForm\");\r\n      this.handleTestParamQuery();\r\n    },\r\n\r\n    handleAddTestParam() {\r\n      this.resetTestParamForm();\r\n      this.testParamOpen = true;\r\n      this.testParamTitle = \"添加测试参数明细\";\r\n    },\r\n\r\n    handleEditTestParam(row) {\r\n      this.resetTestParamForm();\r\n      const testParamId = row.testParamId || this.testParamIds;\r\n      getTestParamItem(testParamId).then(response => {\r\n        this.testParamForm = response.data;\r\n        this.parseTestParamAttachments();\r\n        this.testParamOpen = true;\r\n        this.testParamTitle = \"修改测试参数明细\";\r\n      });\r\n    },\r\n\r\n    handleDeleteTestParam(row) {\r\n      const testParamIds = row.testParamId || this.testParamIds;\r\n      this.$modal.confirm('是否确认删除参数名称为\"' + row.paramName + '\"的数据项？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeleteTestParam() {\r\n      const testParamIds = this.testParamIds;\r\n      this.$modal.confirm('是否确认删除选中的' + testParamIds.length + '条数据？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportTestParam() {\r\n      this.download('material/testParamItem/export', {\r\n        ...this.testParamQueryParams\r\n      }, `test_param_item_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 表单相关方法 */\r\n    resetPlanGroupForm() {\r\n      this.planGroupForm = {\r\n        planGroupId: null,\r\n        planCode: null,\r\n        performanceType: null,\r\n        performanceName: null,\r\n        testEquipment: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.planGroupFileList = [];\r\n      this.resetForm(\"planGroupForm\");\r\n    },\r\n\r\n    resetTestParamForm() {\r\n      this.testParamForm = {\r\n        testParamId: null,\r\n        planGroupId: this.currentPlanGroup ? this.currentPlanGroup.planGroupId : null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.testParamFileList = [];\r\n      this.resetForm(\"testParamForm\");\r\n    },\r\n\r\n    submitPlanGroupForm() {\r\n      this.$refs[\"planGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.planGroupForm.attachments = this.planGroupFileList.map(file => file.url).join(',');\r\n          if (this.planGroupForm.planGroupId != null) {\r\n            updateTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          } else {\r\n            addTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    submitTestParamForm() {\r\n      this.$refs[\"testParamForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.testParamForm.attachments = this.testParamFileList.map(file => file.url).join(',');\r\n          if (this.testParamForm.testParamId != null) {\r\n            updateTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          } else {\r\n            addTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    cancelPlanGroup() {\r\n      this.planGroupOpen = false;\r\n      this.resetPlanGroupForm();\r\n    },\r\n\r\n    cancelTestParam() {\r\n      this.testParamOpen = false;\r\n      this.resetTestParamForm();\r\n    },\r\n\r\n    /** 附件相关方法 */\r\n    parsePlanGroupAttachments() {\r\n      if (this.planGroupForm.attachments) {\r\n        const urls = this.planGroupForm.attachments.split(',');\r\n        this.planGroupFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    parseTestParamAttachments() {\r\n      if (this.testParamForm.attachments) {\r\n        const urls = this.testParamForm.attachments.split(',');\r\n        this.testParamFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    handlePlanGroupUploadSuccess(response, file) {\r\n      this.planGroupFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handleTestParamUploadSuccess(response, file) {\r\n      this.testParamFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handlePlanGroupUploadRemove(file) {\r\n      const index = this.planGroupFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.planGroupFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    handleTestParamUploadRemove(file) {\r\n      const index = this.testParamFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.testParamFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    beforePlanGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    beforeTestParamUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    handleViewPlanGroupAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    handleViewTestParamAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    viewAttachments(attachments) {\r\n      if (!attachments) return;\r\n      const urls = attachments.split(',');\r\n      this.attachmentList = urls.map(url => ({\r\n        name: url.substring(url.lastIndexOf('/') + 1),\r\n        url: url\r\n      }));\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    handlePlanCodeSelect(item) {\r\n      this.planGroupQueryParams.planCode = item.value;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.plan-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.performance-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.performance-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.equipment-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.equipment-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 测试参数明细样式 */\r\n.test-param-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.test-param-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.test-param-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.test-param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n}\r\n\r\n/* 指示器样式 */\r\n.plan-group-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.plan-group-indicator .el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.copy-btn {\r\n  color: #67C23A;\r\n  font-weight: 500;\r\n}\r\n\r\n.copy-btn:hover {\r\n  color: #85ce61;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}