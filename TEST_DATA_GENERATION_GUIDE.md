# 测试数据生成指南

## 概述

本指南提供了完整的测试数据生成方案，基于新的数据录入结构创建丰富的测试数据，用于验证系统功能和进行演示。

## 数据结构说明

### 新的数据录入结构
- **测试结果表 (test_results)**：使用 `plan_group_id` 和 `test_param_id` 替代原来的 `test_plan_id`
- **测试方案组 (test_plan_group)**：包含测试方案的基本信息
- **测试参数明细 (test_param_item)**：包含具体的测试参数信息
- **工艺参数组 (process_param_group)**：材料的工艺参数组
- **工艺参数明细 (process_param_item)**：具体的工艺参数

## 生成的测试数据

### 1. 材料数据 (10种材料)
- **碳纤维复合材料**：东丽工业 T700-12K、中复神鹰 SYT49-12K、威海光威 GW-T300
- **玻璃纤维复合材料**：巨石集团 E-Glass-2400、重庆国际 C-Glass-1200
- **芳纶纤维复合材料**：杜邦公司 Kevlar-49
- **树脂基体材料**：环氧树脂、聚酰亚胺树脂、酚醛树脂、不饱和聚酯树脂

### 2. 工艺参数组 (15个参数组)
- **预浸料制备**：树脂含量、固化温度、固化时间、压力
- **热压成型**：成型温度、成型压力、保压时间、升温速率
- **后固化处理**：后固化温度、后固化时间、升温速率
- **拉挤成型**：拉挤速度、模具温度、纤维含量、牵引力
- **缠绕成型**：缠绕角度、缠绕张力、固化温度、转速
- **编织预成型**：编织密度、编织角度、张力控制
- **RTM成型**：注射压力、注射温度、固化温度、固化时间
- **模压成型**：模压温度、模压压力、保压时间
- **手糊成型**：层间时间、固化温度、固化时间
- **自动铺放**：铺放速度、压实压力、铺放角度
- **真空浸渍**：真空度、浸渍温度、浸渍时间
- **溶液浸渍**：溶液浓度、浸渍温度、干燥温度
- **压制成型**：压制温度、压制压力、保压时间

### 3. 测试方案组 (12个测试方案)
- **力学性能测试**：
  - TP-MECH-001：拉伸性能测试（拉伸强度、拉伸模量、断裂伸长率、泊松比）
  - TP-MECH-002：弯曲性能测试（弯曲强度、弯曲模量、最大弯曲应变）
  - TP-MECH-003：冲击性能测试（冲击强度、冲击韧性）
  - TP-MECH-004：疲劳性能测试（疲劳强度、疲劳寿命）

- **热学性能测试**：
  - TP-THER-001：热变形温度测试（热变形温度、载荷）
  - TP-THER-002：玻璃化转变温度（玻璃化转变温度、升温速率）
  - TP-THER-003：热膨胀系数测试（线性热膨胀系数、测试温度范围）

- **电学性能测试**：
  - TP-ELEC-001：介电性能测试（介电常数、介电损耗、测试频率）
  - TP-ELEC-002：绝缘电阻测试（体积电阻率、表面电阻率）

- **物理性能测试**：
  - TP-PHYS-001：密度测试（密度、测试温度）
  - TP-PHYS-002：吸水率测试（24小时吸水率、平衡吸水率）

- **表面性能测试**：
  - TP-SURF-001：表面粗糙度测试（算术平均粗糙度Ra、最大高度粗糙度Rz）

### 4. 测试结果数据 (80+条记录)
- **多批次数据**：模拟不同时间的测试批次，体现时间趋势
- **多供应商数据**：不同供应商的材料性能对比
- **多参数数据**：涵盖力学、热学、电学、物理等多种性能参数
- **真实偏差**：供应商数据与实际测试数据存在合理偏差

## 执行步骤

### 步骤1：准备数据库
```sql
-- 确保数据库连接正常
USE your_database_name;

-- 检查表结构是否已更新
DESCRIBE test_results;
```

### 步骤2：执行基础数据生成
```sql
-- 执行基础数据生成脚本
source sql/test_data_generation.sql;
```

### 步骤3：执行测试结果数据生成
```sql
-- 执行测试结果数据生成脚本
source sql/test_results_data.sql;
```

### 步骤4：验证数据完整性
```sql
-- 检查数据生成结果
SELECT COUNT(*) FROM materials;
SELECT COUNT(*) FROM process_param_group;
SELECT COUNT(*) FROM process_param_item;
SELECT COUNT(*) FROM test_plan_group;
SELECT COUNT(*) FROM test_param_item;
SELECT COUNT(*) FROM test_results;
```

## 数据特点

### 1. 真实性
- 基于实际复合材料行业的参数和性能指标
- 参数数值符合工程实际情况
- 供应商数据与测试数据存在合理偏差

### 2. 完整性
- 涵盖完整的材料-工艺-测试链条
- 包含多种材料类型和工艺方法
- 测试方案覆盖主要性能指标

### 3. 多样性
- 10种不同材料，6个不同供应商
- 15种工艺参数组，12种测试方案
- 多批次、多时间点的测试数据

### 4. 关联性
- 数据之间具有完整的关联关系
- 支持多维度对比分析
- 便于验证级联选择功能

## 验证功能

### 1. 数据录入功能验证
- **级联选择**：测试方案组 → 测试参数选择
- **参数展示**：工艺参数和测试方案参数信息展示
- **数据保存**：新的数据结构保存和查询

### 2. 趋势对比功能验证
- **材料对比**：不同材料的性能对比
- **供应商对比**：供应商数据准确率分析
- **参数编号对比**：工艺参数组的性能分析
- **工艺类型对比**：不同工艺的稳定性分析
- **时间趋势**：性能随时间的变化趋势

### 3. 筛选功能验证
- **智能筛选**：根据选择动态更新筛选选项
- **多条件筛选**：材料、供应商、工艺类型等组合筛选
- **搜索建议**：自动完成和搜索建议功能

## 数据统计

### 预期数据量
- **材料**：10种
- **工艺参数组**：15个
- **工艺参数明细**：60+个
- **测试方案组**：12个
- **测试参数明细**：24个
- **测试结果**：80+条

### 对比维度数据分布
- **材料对比**：每种材料3-15条测试记录
- **供应商对比**：每个供应商5-25条测试记录
- **测试方案对比**：每个方案3-20条测试记录
- **时间趋势**：跨越14天的时间序列数据

## 注意事项

### 1. 数据一致性
- 确保外键关联正确
- 验证数据类型和格式
- 检查必填字段完整性

### 2. 性能考虑
- 大量数据插入可能需要时间
- 建议在测试环境先执行
- 可根据需要调整数据量

### 3. 扩展性
- 可基于现有模板添加更多数据
- 支持自定义材料和测试方案
- 便于后续功能扩展

## 故障排除

### 常见问题1：外键约束错误
**解决方法**：
1. 检查关联表是否存在数据
2. 验证外键字段值的正确性
3. 确认表结构已正确更新

### 常见问题2：数据类型不匹配
**解决方法**：
1. 检查字段数据类型定义
2. 验证插入数据的格式
3. 必要时进行数据类型转换

### 常见问题3：重复数据插入
**解决方法**：
1. 清理现有测试数据
2. 检查唯一约束设置
3. 使用INSERT IGNORE或ON DUPLICATE KEY UPDATE

## 总结

通过执行这些测试数据生成脚本，您将获得：
- ✅ 完整的材料-工艺-测试数据链
- ✅ 丰富的多维度对比数据
- ✅ 真实的工程参数和性能指标
- ✅ 完善的关联关系和数据完整性
- ✅ 充足的功能验证和演示数据

这些数据将充分支持系统的各项功能验证和用户演示需求。
