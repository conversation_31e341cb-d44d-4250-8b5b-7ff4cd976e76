{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue", "mtime": 1754278483446}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0TWF0ZXJpYWwsIGdldE1hdGVyaWFsLCBkZWxNYXRlcmlhbCwgYWRkTWF0ZXJpYWwsIHVwZGF0ZU1hdGVyaWFsLA0KICBleHBvcnRNYXRlcmlhbCwgZ2V0TWF0ZXJpYWxPcHRpb25zDQp9IGZyb20gIkAvYXBpL21hdGVyaWFsL21hdGVyaWFsIjsNCmltcG9ydCB7DQogIGxpc3RQcm9jZXNzUGFyYW1Hcm91cCwgZ2V0UHJvY2Vzc1BhcmFtR3JvdXAsIGRlbFByb2Nlc3NQYXJhbUdyb3VwLA0KICBhZGRQcm9jZXNzUGFyYW1Hcm91cCwgdXBkYXRlUHJvY2Vzc1BhcmFtR3JvdXAsIGxpc3RCeU1hdGVyaWFsSWQsDQogIGV4cG9ydFByb2Nlc3NQYXJhbUdyb3VwLCBnZXRQcm9jZXNzUGFyYW1Hcm91cE9wdGlvbnMsIGV4cG9ydENvbXBsZXRlRGF0YQ0KfSBmcm9tICJAL2FwaS9tYXRlcmlhbC9wcm9jZXNzUGFyYW1Hcm91cCI7DQppbXBvcnQgew0KICBsaXN0UHJvY2Vzc1BhcmFtSXRlbSwgZ2V0UHJvY2Vzc1BhcmFtSXRlbSwgZGVsUHJvY2Vzc1BhcmFtSXRlbSwNCiAgYWRkUHJvY2Vzc1BhcmFtSXRlbSwgdXBkYXRlUHJvY2Vzc1BhcmFtSXRlbSwgbGlzdEJ5R3JvdXBJZCwNCiAgZXhwb3J0UHJvY2Vzc1BhcmFtSXRlbSwgZ2V0UHJvY2Vzc1BhcmFtSXRlbU9wdGlvbnMNCn0gZnJvbSAiQC9hcGkvbWF0ZXJpYWwvcHJvY2Vzc1BhcmFtSXRlbSI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJNYXRlcmlhbENvbmZpZyIsDQogIGRpcmVjdGl2ZXM6IHsNCiAgICAvLyDmi5bmi73mjIfku6QNCiAgICBkcmFnOiB7DQogICAgICBiaW5kKGVsKSB7DQogICAgICAgIGNvbnN0IGRpYWxvZ0hlYWRlckVsID0gZWwucXVlcnlTZWxlY3RvcignLmVsLWRpYWxvZ19faGVhZGVyJyk7DQogICAgICAgIGNvbnN0IGRyYWdEb20gPSBlbC5xdWVyeVNlbGVjdG9yKCcuZWwtZGlhbG9nJyk7DQogICAgICAgIGRpYWxvZ0hlYWRlckVsLnN0eWxlLmN1cnNvciA9ICdtb3ZlJzsNCg0KICAgICAgICAvLyDojrflj5bljp/mnInlsZ7mgKcgaWUgZG9t5YWD57SgLmN1cnJlbnRTdHlsZSDngavni5DosLfmrYwgd2luZG93LmdldENvbXB1dGVkU3R5bGUoZG9t5YWD57SgLCBudWxsKTsNCiAgICAgICAgY29uc3Qgc3R5ID0gZHJhZ0RvbS5jdXJyZW50U3R5bGUgfHwgd2luZG93LmdldENvbXB1dGVkU3R5bGUoZHJhZ0RvbSwgbnVsbCk7DQoNCiAgICAgICAgZGlhbG9nSGVhZGVyRWwub25tb3VzZWRvd24gPSAoZSkgPT4gew0KICAgICAgICAgIC8vIOm8oOagh+aMieS4i++8jOiuoeeul+W9k+WJjeWFg+e0oOi3neemu+WPr+inhuWMuueahOi3neemuw0KICAgICAgICAgIGNvbnN0IGRpc1ggPSBlLmNsaWVudFggLSBkaWFsb2dIZWFkZXJFbC5vZmZzZXRMZWZ0Ow0KICAgICAgICAgIGNvbnN0IGRpc1kgPSBlLmNsaWVudFkgLSBkaWFsb2dIZWFkZXJFbC5vZmZzZXRUb3A7DQoNCiAgICAgICAgICAvLyDojrflj5bliLDnmoTlgLzluKZweCDmraPliJnljLnphY3mm7/mjaINCiAgICAgICAgICBsZXQgc3R5TCwgc3R5VDsNCg0KICAgICAgICAgIC8vIOazqOaEj+WcqGll5LitIOesrOS4gOasoeiOt+WPluWIsOeahOWAvOS4uue7hOS7tuiHquW4pjUwJSDnp7vliqjkuYvlkI7otYvlgLzkuLpweA0KICAgICAgICAgIGlmIChzdHkubGVmdC5pbmNsdWRlcygnJScpKSB7DQogICAgICAgICAgICBzdHlMID0gK2RvY3VtZW50LmJvZHkuY2xpZW50V2lkdGggKiAoK3N0eS5sZWZ0LnJlcGxhY2UoL1wlL2csICcnKSAvIDEwMCk7DQogICAgICAgICAgICBzdHlUID0gK2RvY3VtZW50LmJvZHkuY2xpZW50SGVpZ2h0ICogKCtzdHkudG9wLnJlcGxhY2UoL1wlL2csICcnKSAvIDEwMCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHN0eUwgPSArc3R5LmxlZnQucmVwbGFjZSgvcHgvZywgJycpOw0KICAgICAgICAgICAgc3R5VCA9ICtzdHkudG9wLnJlcGxhY2UoL3B4L2csICcnKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBkb2N1bWVudC5vbm1vdXNlbW92ZSA9IGZ1bmN0aW9uIChlKSB7DQogICAgICAgICAgICAvLyDpgJrov4fkuovku7blp5TmiZjvvIzorqHnrpfnp7vliqjnmoTot53nprsNCiAgICAgICAgICAgIGNvbnN0IGwgPSBlLmNsaWVudFggLSBkaXNYOw0KICAgICAgICAgICAgY29uc3QgdCA9IGUuY2xpZW50WSAtIGRpc1k7DQoNCiAgICAgICAgICAgIC8vIOenu+WKqOW9k+WJjeWFg+e0oA0KICAgICAgICAgICAgZHJhZ0RvbS5zdHlsZS5sZWZ0ID0gYCR7bCArIHN0eUx9cHhgOw0KICAgICAgICAgICAgZHJhZ0RvbS5zdHlsZS50b3AgPSBgJHt0ICsgc3R5VH1weGA7DQoNCiAgICAgICAgICAgIC8vIOWwhuatpOaXtueahOS9jee9ruS8oOWHuuWOuw0KICAgICAgICAgICAgLy8gYmluZGluZy52YWx1ZSh7eDplLnBhZ2VYLHk6ZS5wYWdlWX0pDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIGRvY3VtZW50Lm9ubW91c2V1cCA9IGZ1bmN0aW9uIChlKSB7DQogICAgICAgICAgICBkb2N1bWVudC5vbm1vdXNlbW92ZSA9IG51bGw7DQogICAgICAgICAgICBkb2N1bWVudC5vbm1vdXNldXAgPSBudWxsOw0KICAgICAgICAgIH07DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOS4iuS8oOebuOWFsw0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkIiwNCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsNCiAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKQ0KICAgICAgfSwNCg0KICAgICAgLy8g5p2Q5paZ55u45YWzDQogICAgICBtYXRlcmlhbExvYWRpbmc6IHRydWUsDQogICAgICBtYXRlcmlhbExpc3Q6IFtdLA0KICAgICAgbWF0ZXJpYWxUb3RhbDogMCwNCiAgICAgIGN1cnJlbnRNYXRlcmlhbDogbnVsbCwNCiAgICAgIG1hdGVyaWFsT3BlbjogZmFsc2UsDQogICAgICBtYXRlcmlhbFRpdGxlOiAiIiwNCiAgICAgIG1hdGVyaWFsRm9ybToge30sDQogICAgICBtYXRlcmlhbEZpbGVMaXN0OiBbXSwNCiAgICAgIG1hdGVyaWFsSWRzOiBbXSwNCiAgICAgIG1hdGVyaWFsU2luZ2xlOiB0cnVlLA0KICAgICAgbWF0ZXJpYWxNdWx0aXBsZTogdHJ1ZSwNCiAgICAgIG1hdGVyaWFsUXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBtYXRlcmlhbE5hbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyTmFtZTogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxNb2RlbDogbnVsbA0KICAgICAgfSwNCiAgICAgIG1hdGVyaWFsUnVsZXM6IHsNCiAgICAgICAgbWF0ZXJpYWxOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuadkOaWmeWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQoNCiAgICAgIC8vIOW3peiJuuWPguaVsOe7hOebuOWFsw0KICAgICAgcGFyYW1Hcm91cExvYWRpbmc6IGZhbHNlLA0KICAgICAgcGFyYW1Hcm91cExpc3Q6IFtdLA0KICAgICAgcGFyYW1Hcm91cFRvdGFsOiAwLA0KICAgICAgY3VycmVudFBhcmFtR3JvdXA6IG51bGwsDQogICAgICBwYXJhbUdyb3VwT3BlbjogZmFsc2UsDQogICAgICBwYXJhbUdyb3VwVGl0bGU6ICIiLA0KICAgICAgcGFyYW1Hcm91cEZvcm06IHt9LA0KICAgICAgcGFyYW1Hcm91cEZpbGVMaXN0OiBbXSwNCiAgICAgIHBhcmFtR3JvdXBJZHM6IFtdLA0KICAgICAgcGFyYW1Hcm91cFNpbmdsZTogdHJ1ZSwNCiAgICAgIHBhcmFtR3JvdXBNdWx0aXBsZTogdHJ1ZSwNCiAgICAgIHBhcmFtR3JvdXBRdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsDQogICAgICAgIHByb2Nlc3NUeXBlOiBudWxsLA0KICAgICAgICBwYXJhbU51bWJlcjogbnVsbA0KICAgICAgfSwNCiAgICAgIHBhcmFtR3JvdXBSdWxlczogew0KICAgICAgICBwcm9jZXNzVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlt6XoibrnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwYXJhbU51bWJlcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj4LmlbDnvJblj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyDlj4LmlbDmmI7nu4bnm7jlhbMNCiAgICAgIHBhcmFtSXRlbUxvYWRpbmc6IGZhbHNlLA0KICAgICAgcGFyYW1JdGVtTGlzdDogW10sDQogICAgICBwYXJhbUl0ZW1Ub3RhbDogMCwNCiAgICAgIHBhcmFtSXRlbU9wZW46IGZhbHNlLA0KICAgICAgcGFyYW1JdGVtVGl0bGU6ICIiLA0KICAgICAgcGFyYW1JdGVtRm9ybToge30sDQogICAgICBwYXJhbUl0ZW1GaWxlTGlzdDogW10sDQogICAgICBwYXJhbUl0ZW1JZHM6IFtdLA0KICAgICAgcGFyYW1JdGVtU2luZ2xlOiB0cnVlLA0KICAgICAgcGFyYW1JdGVtTXVsdGlwbGU6IHRydWUsDQogICAgICBwYXJhbUl0ZW1RdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGdyb3VwSWQ6IG51bGwsDQogICAgICAgIHBhcmFtTmFtZTogbnVsbCwNCiAgICAgICAgdW5pdDogbnVsbA0KICAgICAgfSwNCiAgICAgIHBhcmFtSXRlbVJ1bGVzOiB7DQogICAgICAgIHBhcmFtTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj4LmlbDlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyDpmYTku7bmn6XnnIsNCiAgICAgIGF0dGFjaG1lbnREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGF0dGFjaG1lbnRMaXN0OiBbXSwNCg0KICAgICAgLy8g5pCc57Si5bu66K6u5pWw5o2uDQogICAgICBtYXRlcmlhbE5hbWVTdWdnZXN0aW9uczogW10sDQogICAgICBzdXBwbGllclN1Z2dlc3Rpb25zOiBbXSwNCiAgICAgIG1hdGVyaWFsTW9kZWxTdWdnZXN0aW9uczogW10sDQogICAgICBwcm9jZXNzVHlwZVN1Z2dlc3Rpb25zOiBbXSwNCiAgICAgIHBhcmFtTmFtZVN1Z2dlc3Rpb25zOiBbXSwNCiAgICAgIHBhcmFtTnVtYmVyU3VnZ2VzdGlvbnM6IFtdLA0KICAgICAgdW5pdFN1Z2dlc3Rpb25zOiBbXQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICB0aGlzLmxvYWRTdWdnZXN0aW9ucygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWKoOi9veaQnOe0ouW7uuiuruaVsOaNriAqLw0KICAgIGxvYWRTdWdnZXN0aW9ucygpIHsNCiAgICAgIC8vIOiOt+WPluadkOaWmeWQjeensOW7uuiurg0KICAgICAgZ2V0TWF0ZXJpYWxPcHRpb25zKHsgdHlwZTogJ21hdGVyaWFsTmFtZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxOYW1lU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCg0KICAgICAgLy8g6I635Y+W5L6b5bqU5ZWG5bu66K6uDQogICAgICBnZXRNYXRlcmlhbE9wdGlvbnMoeyB0eXBlOiAnc3VwcGxpZXJOYW1lJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdXBwbGllclN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQoNCiAgICAgIC8vIOiOt+WPluadkOaWmeWei+WPt+W7uuiurg0KICAgICAgZ2V0TWF0ZXJpYWxPcHRpb25zKHsgdHlwZTogJ21hdGVyaWFsTW9kZWwnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLm1hdGVyaWFsTW9kZWxTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KDQogICAgICAvLyDojrflj5blt6Xoibrnsbvlnovlu7rorq4NCiAgICAgIGdldFByb2Nlc3NQYXJhbUdyb3VwT3B0aW9ucyh7IHR5cGU6ICdwcm9jZXNzVHlwZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHJvY2Vzc1R5cGVTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KDQogICAgICAvLyDojrflj5blj4LmlbDnvJblj7flu7rorq4NCiAgICAgIGdldFByb2Nlc3NQYXJhbUdyb3VwT3B0aW9ucyh7IHR5cGU6ICdwYXJhbU51bWJlcicgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KDQogICAgICAvLyDojrflj5blj4LmlbDlkI3np7Dlu7rorq4NCiAgICAgIGdldFByb2Nlc3NQYXJhbUl0ZW1PcHRpb25zKHsgdHlwZTogJ3BhcmFtTmFtZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1OYW1lU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCg0KICAgICAgLy8g6I635Y+W5Y+C5pWw5Y2V5L2N5bu66K6uDQogICAgICBnZXRQcm9jZXNzUGFyYW1JdGVtT3B0aW9ucyh7IHR5cGU6ICd1bml0JyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy51bml0U3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmeWQjeensOaQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5TWF0ZXJpYWxOYW1lU3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLm1hdGVyaWFsTmFtZVN1Z2dlc3Rpb25zOw0KICAgICAgaWYgKHF1ZXJ5U3RyaW5nKSB7DQogICAgICAgIHN1Z2dlc3Rpb25zID0gdGhpcy5tYXRlcmlhbE5hbWVTdWdnZXN0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyk7DQogICAgfSwNCg0KICAgIC8qKiDkvpvlupTllYbmkJzntKLlu7rorq4gKi8NCiAgICBxdWVyeVN1cHBsaWVyU3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLnN1cHBsaWVyU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnN1cHBsaWVyU3VnZ2VzdGlvbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiBpdGVtLnZhbHVlLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihxdWVyeVN0cmluZy50b0xvd2VyQ2FzZSgpKSAhPT0gLTE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY2Ioc3VnZ2VzdGlvbnMpOw0KICAgIH0sDQoNCiAgICAvKiog5p2Q5paZ5Z6L5Y+35pCc57Si5bu66K6uICovDQogICAgcXVlcnlNYXRlcmlhbE1vZGVsU3VnZ2VzdGlvbnMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgc3VnZ2VzdGlvbnMgPSB0aGlzLm1hdGVyaWFsTW9kZWxTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMubWF0ZXJpYWxNb2RlbFN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgLyoqIOW3peiJuuexu+Wei+aQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5UHJvY2Vzc1R5cGVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucHJvY2Vzc1R5cGVTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMucHJvY2Vzc1R5cGVTdWdnZXN0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyk7DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDlkI3np7DmkJzntKLlu7rorq4gKi8NCiAgICAvKiog5Y+C5pWw5ZCN56ew5pCc57Si5bu66K6uICovDQogICAgcXVlcnlQYXJhbU5hbWVTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGFyYW1OYW1lU3VnZ2VzdGlvbnM7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc3VnZ2VzdGlvbnMgPSB0aGlzLnBhcmFtTmFtZVN1Z2dlc3Rpb25zLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZS50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGNiKHN1Z2dlc3Rpb25zKTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe8luWPt+aQnOe0ouW7uuiuriAqLw0KICAgIHF1ZXJ5UGFyYW1OdW1iZXJTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9uczsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzdWdnZXN0aW9ucyA9IHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKHF1ZXJ5U3RyaW5nLnRvTG93ZXJDYXNlKCkpICE9PSAtMTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBjYihzdWdnZXN0aW9ucyk7DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDljZXkvY3mkJzntKLlu7rorq4gKi8NCiAgICBxdWVyeVVuaXRTdWdnZXN0aW9ucyhxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGxldCBzdWdnZXN0aW9ucyA9IHRoaXMudW5pdFN1Z2dlc3Rpb25zOw0KICAgICAgaWYgKHF1ZXJ5U3RyaW5nKSB7DQogICAgICAgIHN1Z2dlc3Rpb25zID0gdGhpcy51bml0U3VnZ2VzdGlvbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiBpdGVtLnZhbHVlLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihxdWVyeVN0cmluZy50b0xvd2VyQ2FzZSgpKSAhPT0gLTE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY2Ioc3VnZ2VzdGlvbnMpOw0KICAgIH0sDQoNCiAgICAvKiog5p2Q5paZ5ZCN56ew6YCJ5oup5LqL5Lu2ICovDQogICAgaGFuZGxlTWF0ZXJpYWxOYW1lU2VsZWN0KGl0ZW0pIHsNCiAgICAgIHRoaXMubWF0ZXJpYWxRdWVyeVBhcmFtcy5tYXRlcmlhbE5hbWUgPSBpdGVtLnZhbHVlOw0KICAgICAgLy8g56e76Zmk6Ieq5Yqo5pCc57Si77yM6K6p55So5oi35omL5Yqo54K55Ye75pCc57Si5oyJ6ZKuDQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnlkI3np7DnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVNYXRlcmlhbE5hbWVGb2N1cygpIHsNCiAgICAgIC8vIOmHjeaWsOWKoOi9veadkOaWmeWQjeensOW7uuiurg0KICAgICAgZ2V0TWF0ZXJpYWxPcHRpb25zKHsgdHlwZTogJ21hdGVyaWFsTmFtZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxOYW1lU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOS+m+W6lOWVhueEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZVN1cHBsaWVyRm9jdXMoKSB7DQogICAgICAvLyDph43mlrDliqDovb3kvpvlupTllYblu7rorq4NCiAgICAgIGdldE1hdGVyaWFsT3B0aW9ucyh7IHR5cGU6ICdzdXBwbGllck5hbWUnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN1cHBsaWVyU3VnZ2VzdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmeWei+WPt+eEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZU1hdGVyaWFsTW9kZWxGb2N1cygpIHsNCiAgICAgIC8vIOmHjeaWsOWKoOi9veadkOaWmeWei+WPt+W7uuiurg0KICAgICAgZ2V0TWF0ZXJpYWxPcHRpb25zKHsgdHlwZTogJ21hdGVyaWFsTW9kZWwnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLm1hdGVyaWFsTW9kZWxTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5bel6Im657G75Z6L54Sm54K55LqL5Lu2ICovDQogICAgaGFuZGxlUHJvY2Vzc1R5cGVGb2N1cygpIHsNCiAgICAgIC8vIOWfuuS6juW9k+WJjemAieS4reeahOadkOaWmeWKoOi9veW3peiJuuexu+Wei+W7uuiurg0KICAgICAgaWYgKHRoaXMuY3VycmVudE1hdGVyaWFsKSB7DQogICAgICAgIC8vIOS7juW9k+WJjeadkOaWmeeahOWPguaVsOe7hOS4reiOt+WPluW3peiJuuexu+Wei+mAiemhuQ0KICAgICAgICBjb25zdCBwcm9jZXNzVHlwZXMgPSBbLi4ubmV3IFNldCh0aGlzLnBhcmFtR3JvdXBMaXN0Lm1hcChpdGVtID0+IGl0ZW0ucHJvY2Vzc1R5cGUpLmZpbHRlcihCb29sZWFuKSldOw0KICAgICAgICB0aGlzLnByb2Nlc3NUeXBlU3VnZ2VzdGlvbnMgPSBwcm9jZXNzVHlwZXMubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6YCJ5Lit5p2Q5paZ77yM5Yqg6L295omA5pyJ5bel6Im657G75Z6LDQogICAgICAgIGdldFByb2Nlc3NQYXJhbUdyb3VwT3B0aW9ucyh7IHR5cGU6ICdwcm9jZXNzVHlwZScgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzVHlwZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe8luWPt+eEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZVBhcmFtTnVtYmVyRm9jdXMoKSB7DQogICAgICAvLyDln7rkuo7lvZPliY3pgInkuK3nmoTmnZDmlpnliqDovb3lj4LmlbDnvJblj7flu7rorq4NCiAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXRlcmlhbCkgew0KICAgICAgICAvLyDku47lvZPliY3mnZDmlpnnmoTlj4LmlbDnu4TkuK3ojrflj5blj4LmlbDnvJblj7fpgInpobkNCiAgICAgICAgY29uc3QgcGFyYW1OdW1iZXJzID0gWy4uLm5ldyBTZXQodGhpcy5wYXJhbUdyb3VwTGlzdC5tYXAoaXRlbSA9PiBpdGVtLnBhcmFtTnVtYmVyKS5maWx0ZXIoQm9vbGVhbikpXTsNCiAgICAgICAgdGhpcy5wYXJhbU51bWJlclN1Z2dlc3Rpb25zID0gcGFyYW1OdW1iZXJzLm1hcChpdGVtID0+ICh7IHZhbHVlOiBpdGVtIH0pKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOayoeaciemAieS4readkOaWme+8jOWKoOi9veaJgOacieWPguaVsOe8luWPtw0KICAgICAgICBnZXRQcm9jZXNzUGFyYW1Hcm91cE9wdGlvbnMoeyB0eXBlOiAncGFyYW1OdW1iZXInIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMucGFyYW1OdW1iZXJTdWdnZXN0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDlkI3np7DnhKbngrnkuovku7YgKi8NCiAgICBoYW5kbGVQYXJhbU5hbWVGb2N1cygpIHsNCiAgICAgIC8vIOWfuuS6juW9k+WJjemAieS4reeahOWPguaVsOe7hOWKoOi9veWPguaVsOWQjeensOW7uuiurg0KICAgICAgaWYgKHRoaXMuY3VycmVudFBhcmFtR3JvdXApIHsNCiAgICAgICAgLy8g5LuO5b2T5YmN5Y+C5pWw57uE55qE5Y+C5pWw5piO57uG5Lit6I635Y+W5Y+C5pWw5ZCN56ew6YCJ6aG5DQogICAgICAgIGNvbnN0IHBhcmFtTmFtZXMgPSBbLi4ubmV3IFNldCh0aGlzLnBhcmFtSXRlbUxpc3QubWFwKGl0ZW0gPT4gaXRlbS5wYXJhbU5hbWUpLmZpbHRlcihCb29sZWFuKSldOw0KICAgICAgICB0aGlzLnBhcmFtTmFtZVN1Z2dlc3Rpb25zID0gcGFyYW1OYW1lcy5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInpgInkuK3lj4LmlbDnu4TvvIzliqDovb3miYDmnInlj4LmlbDlkI3np7ANCiAgICAgICAgZ2V0UHJvY2Vzc1BhcmFtSXRlbU9wdGlvbnMoeyB0eXBlOiAncGFyYW1OYW1lJyB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLnBhcmFtTmFtZVN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOWNleS9jeeEpueCueS6i+S7tiAqLw0KICAgIGhhbmRsZVVuaXRGb2N1cygpIHsNCiAgICAgIC8vIOWfuuS6juW9k+WJjemAieS4reeahOWPguaVsOe7hOWKoOi9veWPguaVsOWNleS9jeW7uuiurg0KICAgICAgaWYgKHRoaXMuY3VycmVudFBhcmFtR3JvdXApIHsNCiAgICAgICAgLy8g5LuO5b2T5YmN5Y+C5pWw57uE55qE5Y+C5pWw5piO57uG5Lit6I635Y+W5Y+C5pWw5Y2V5L2N6YCJ6aG5DQogICAgICAgIGNvbnN0IHVuaXRzID0gWy4uLm5ldyBTZXQodGhpcy5wYXJhbUl0ZW1MaXN0Lm1hcChpdGVtID0+IGl0ZW0udW5pdCkuZmlsdGVyKEJvb2xlYW4pKV07DQogICAgICAgIHRoaXMudW5pdFN1Z2dlc3Rpb25zID0gdW5pdHMubWFwKGl0ZW0gPT4gKHsgdmFsdWU6IGl0ZW0gfSkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6YCJ5Lit5Y+C5pWw57uE77yM5Yqg6L295omA5pyJ5Y+C5pWw5Y2V5L2NDQogICAgICAgIGdldFByb2Nlc3NQYXJhbUl0ZW1PcHRpb25zKHsgdHlwZTogJ3VuaXQnIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMudW5pdFN1Z2dlc3Rpb25zID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiAoeyB2YWx1ZTogaXRlbSB9KSk7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOino+aekOmZhOS7tuaVsOaNriAqLw0KICAgIHBhcnNlQXR0YWNobWVudHMoYXR0YWNobWVudHMpIHsNCiAgICAgIGlmICghYXR0YWNobWVudHMpIHsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzlt7Lnu4/mmK/mlbDnu4TvvIznm7TmjqXov5Tlm54NCiAgICAgIGlmIChBcnJheS5pc0FycmF5KGF0dGFjaG1lbnRzKSkgew0KICAgICAgICByZXR1cm4gYXR0YWNobWVudHM7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYr+Wtl+espuS4su+8jOaMiemAl+WPt+WIhuWJsuW5tui9rOaNouS4uuaWh+S7tuWvueixoQ0KICAgICAgaWYgKHR5cGVvZiBhdHRhY2htZW50cyA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgcmV0dXJuIGF0dGFjaG1lbnRzLnNwbGl0KCcsJykuZmlsdGVyKHVybCA9PiB1cmwudHJpbSgpKS5tYXAoKHVybCwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHVybC5zdWJzdHJpbmcodXJsLmxhc3RJbmRleE9mKCcvJykgKyAxKSB8fCBg6ZmE5Lu2JHtpbmRleCArIDF9YDsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICB1cmw6IHVybC50cmltKCksDQogICAgICAgICAgICB1aWQ6IERhdGUubm93KCkgKyBpbmRleCwNCiAgICAgICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnDQogICAgICAgICAgfTsNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBbXTsNCiAgICB9LA0KDQogICAgLyoqIOafpeivouadkOaWmeWIl+ihqCAqLw0KICAgIGdldE1hdGVyaWFsTGlzdCgpIHsNCiAgICAgIHRoaXMubWF0ZXJpYWxMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RNYXRlcmlhbCh0aGlzLm1hdGVyaWFsUXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLm1hdGVyaWFsTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMubWF0ZXJpYWxUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLm1hdGVyaWFsTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnmn6Xor6IgKi8NCiAgICBoYW5kbGVNYXRlcmlhbFF1ZXJ5KCkgew0KICAgICAgdGhpcy5tYXRlcmlhbFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruadkOaWmeafpeivoiAqLw0KICAgIHJlc2V0TWF0ZXJpYWxRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJtYXRlcmlhbFF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVNYXRlcmlhbFF1ZXJ5KCk7DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnooYzngrnlh7vkuovku7YgKi8NCiAgICBoYW5kbGVNYXRlcmlhbENsaWNrKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50TWF0ZXJpYWwgPSByb3c7DQogICAgICB0aGlzLnBhcmFtR3JvdXBRdWVyeVBhcmFtcy5tYXRlcmlhbElkID0gcm93Lm1hdGVyaWFsSWQ7DQogICAgICB0aGlzLmdldFBhcmFtR3JvdXBMaXN0KCk7DQogICAgICB0aGlzLnBhcmFtSXRlbUxpc3QgPSBbXTsNCiAgICAgIHRoaXMucGFyYW1JdGVtVG90YWwgPSAwOw0KICAgICAgdGhpcy5jdXJyZW50UGFyYW1Hcm91cCA9IG51bGw7DQogICAgICAvLyDlkIzml7bpgInkuK3or6XooYwNCiAgICAgIHRoaXMuJHJlZnMubWF0ZXJpYWxUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93KTsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmemAieaLqeWPmOWMluS6i+S7tiAqLw0KICAgIGhhbmRsZU1hdGVyaWFsU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5tYXRlcmlhbElkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsSWQpOw0KICAgICAgdGhpcy5tYXRlcmlhbFNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm1hdGVyaWFsTXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KDQogICAgLyoqIOadkOaWmeihjOagt+W8jyAqLw0KICAgIGdldE1hdGVyaWFsUm93Q2xhc3NOYW1lKHtyb3csIHJvd0luZGV4fSkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudE1hdGVyaWFsICYmIHJvdy5tYXRlcmlhbElkID09PSB0aGlzLmN1cnJlbnRNYXRlcmlhbC5tYXRlcmlhbElkKSB7DQogICAgICAgIHJldHVybiAnY3VycmVudC1yb3cnOw0KICAgICAgfQ0KICAgICAgcmV0dXJuICcnOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5bel6Im65Y+C5pWw57uE5YiX6KGoICovDQogICAgZ2V0UGFyYW1Hcm91cExpc3QoKSB7DQogICAgICBpZiAoIXRoaXMuY3VycmVudE1hdGVyaWFsKSByZXR1cm47DQogICAgICB0aGlzLnBhcmFtR3JvdXBMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RQcm9jZXNzUGFyYW1Hcm91cCh0aGlzLnBhcmFtR3JvdXBRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1Hcm91cExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnBhcmFtR3JvdXBUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLnBhcmFtR3JvdXBMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe7hOafpeivoiAqLw0KICAgIGhhbmRsZVBhcmFtR3JvdXBRdWVyeSgpIHsNCiAgICAgIHRoaXMucGFyYW1Hcm91cFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRQYXJhbUdyb3VwTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5Y+C5pWw57uE5p+l6K+iICovDQogICAgcmVzZXRQYXJhbUdyb3VwUXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicGFyYW1Hcm91cFF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVQYXJhbUdyb3VwUXVlcnkoKTsNCiAgICB9LA0KDQogICAgLyoqIOW3peiJuuWPguaVsOe7hOihjOeCueWHu+S6i+S7tiAqLw0KICAgIGhhbmRsZVBhcmFtR3JvdXBDbGljayhyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhcmFtR3JvdXAgPSByb3c7DQogICAgICB0aGlzLnBhcmFtSXRlbVF1ZXJ5UGFyYW1zLmdyb3VwSWQgPSByb3cuZ3JvdXBJZDsNCiAgICAgIHRoaXMuZ2V0UGFyYW1JdGVtTGlzdCgpOw0KICAgICAgLy8g5ZCM5pe26YCJ5Lit6K+l6KGMDQogICAgICB0aGlzLiRyZWZzLnBhcmFtR3JvdXBUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93KTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe7hOmAieaLqeWPmOWMluS6i+S7tiAqLw0KICAgIGhhbmRsZVBhcmFtR3JvdXBTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnBhcmFtR3JvdXBJZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5ncm91cElkKTsNCiAgICAgIHRoaXMucGFyYW1Hcm91cFNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLnBhcmFtR3JvdXBNdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQoNCiAgICAvKiog5Y+C5pWw5piO57uG6KGM54K55Ye75LqL5Lu2ICovDQogICAgaGFuZGxlUGFyYW1JdGVtUm93Q2xpY2socm93KSB7DQogICAgICB0aGlzLiRyZWZzLnBhcmFtSXRlbVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihyb3cpOw0KICAgIH0sDQoNCiAgICAvKiog5Y+C5pWw5piO57uG6YCJ5oup5Y+Y5YyW5LqL5Lu2ICovDQogICAgaGFuZGxlUGFyYW1JdGVtU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5wYXJhbUl0ZW1JZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pdGVtSWQpOw0KICAgICAgdGhpcy5wYXJhbUl0ZW1TaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5wYXJhbUl0ZW1NdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQoNCiAgICAvKiog5Y+C5pWw57uE6KGM5qC35byPICovDQogICAgZ2V0UGFyYW1Hcm91cFJvd0NsYXNzTmFtZSh7cm93LCByb3dJbmRleH0pIHsNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRQYXJhbUdyb3VwICYmIHJvdy5ncm91cElkID09PSB0aGlzLmN1cnJlbnRQYXJhbUdyb3VwLmdyb3VwSWQpIHsNCiAgICAgICAgcmV0dXJuICdjdXJyZW50LXJvdyc7DQogICAgICB9DQogICAgICByZXR1cm4gJyc7DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6Llj4LmlbDmmI7nu4bliJfooaggKi8NCiAgICBnZXRQYXJhbUl0ZW1MaXN0KCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRQYXJhbUdyb3VwKSByZXR1cm47DQogICAgICB0aGlzLnBhcmFtSXRlbUxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFByb2Nlc3NQYXJhbUl0ZW0odGhpcy5wYXJhbUl0ZW1RdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFyYW1JdGVtTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMucGFyYW1JdGVtVG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5wYXJhbUl0ZW1Mb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOaYjue7huafpeivoiAqLw0KICAgIGhhbmRsZVBhcmFtSXRlbVF1ZXJ5KCkgew0KICAgICAgdGhpcy5wYXJhbUl0ZW1RdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0UGFyYW1JdGVtTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5Y+C5pWw5piO57uG5p+l6K+iICovDQogICAgcmVzZXRQYXJhbUl0ZW1RdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJwYXJhbUl0ZW1RdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUGFyYW1JdGVtUXVlcnkoKTsNCiAgICB9LA0KDQogICAgLyoqIOaWsOWinuadkOaWmSAqLw0KICAgIGhhbmRsZUFkZE1hdGVyaWFsKCkgew0KICAgICAgdGhpcy5yZXNldE1hdGVyaWFsRm9ybSgpOw0KICAgICAgdGhpcy5tYXRlcmlhbE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5tYXRlcmlhbFRpdGxlID0gIua3u+WKoOadkOaWmeS/oeaBryI7DQogICAgfSwNCg0KICAgIC8qKiDkv67mlLnmnZDmlpkgKi8NCiAgICBoYW5kbGVFZGl0TWF0ZXJpYWwocm93KSB7DQogICAgICB0aGlzLnJlc2V0TWF0ZXJpYWxGb3JtKCk7DQogICAgICBjb25zdCBtYXRlcmlhbElkID0gcm93Lm1hdGVyaWFsSWQ7DQogICAgICBnZXRNYXRlcmlhbChtYXRlcmlhbElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbEZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDlpITnkIbpmYTku7bmlbDmja4NCiAgICAgICAgdGhpcy5tYXRlcmlhbEZpbGVMaXN0ID0gdGhpcy5wYXJzZUF0dGFjaG1lbnRzKHJlc3BvbnNlLmRhdGEuYXR0YWNobWVudHMpOw0KICAgICAgICB0aGlzLm1hdGVyaWFsT3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMubWF0ZXJpYWxUaXRsZSA9ICLkv67mlLnmnZDmlpnkv6Hmga8iOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTmnZDmlpnooajljZUgKi8NCiAgICBzdWJtaXRNYXRlcmlhbEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJtYXRlcmlhbEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOWwhuaWh+S7tuWIl+ihqOi9rOaNouS4uumAl+WPt+WIhumalOeahFVSTOWtl+espuS4sg0KICAgICAgICAgIHRoaXMubWF0ZXJpYWxGb3JtLmF0dGFjaG1lbnRzID0gdGhpcy5tYXRlcmlhbEZpbGVMaXN0Lmxlbmd0aCA+IDANCiAgICAgICAgICAgID8gdGhpcy5tYXRlcmlhbEZpbGVMaXN0Lm1hcChmaWxlID0+IGZpbGUudXJsKS5qb2luKCcsJykNCiAgICAgICAgICAgIDogbnVsbDsNCg0KICAgICAgICAgIC8vIOiuvue9ruWIm+W7uuS6uuWSjOabtOaWsOS6ug0KICAgICAgICAgIGlmICh0aGlzLm1hdGVyaWFsRm9ybS5tYXRlcmlhbElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIC8vIOabtOaWsOaTjeS9nO+8jOiuvue9ruabtOaWsOS6ug0KICAgICAgICAgICAgdGhpcy5tYXRlcmlhbEZvcm0udXBkYXRlQnkgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWU7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaWsOWinuaTjeS9nO+8jOiuvue9ruWIm+W7uuS6ug0KICAgICAgICAgICAgdGhpcy5tYXRlcmlhbEZvcm0uY3JlYXRlQnkgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKHRoaXMubWF0ZXJpYWxGb3JtLm1hdGVyaWFsSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlTWF0ZXJpYWwodGhpcy5tYXRlcmlhbEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5tYXRlcmlhbE9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRNYXRlcmlhbCh0aGlzLm1hdGVyaWFsRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm1hdGVyaWFsT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldE1hdGVyaWFsTGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWPlua2iOadkOaWmeaTjeS9nCAqLw0KICAgIGNhbmNlbE1hdGVyaWFsKCkgew0KICAgICAgdGhpcy5tYXRlcmlhbE9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXRNYXRlcmlhbEZvcm0oKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruadkOaWmeihqOWNlSAqLw0KICAgIHJlc2V0TWF0ZXJpYWxGb3JtKCkgew0KICAgICAgdGhpcy5tYXRlcmlhbEZvcm0gPSB7DQogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsDQogICAgICAgIG1hdGVyaWFsTmFtZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJOYW1lOiBudWxsLA0KICAgICAgICBtYXRlcmlhbE1vZGVsOiBudWxsLA0KICAgICAgICBtYXRlcmlhbERlc2NyaXB0aW9uOiBudWxsLA0KICAgICAgICBhdHRhY2htZW50czogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5tYXRlcmlhbEZpbGVMaXN0ID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgibWF0ZXJpYWxGb3JtIik7DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmnZDmlpkgKi8NCiAgICBoYW5kbGVEZWxldGVNYXRlcmlhbChyb3cpIHsNCiAgICAgIGNvbnN0IG1hdGVyaWFsSWRzID0gcm93Lm1hdGVyaWFsSWQ7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmnZDmlpkiJyArIHJvdy5tYXRlcmlhbE5hbWUgKyAnIu+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxNYXRlcmlhbChtYXRlcmlhbElkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCg0KICAgIC8qKiDmibnph4/liKDpmaTmnZDmlpkgKi8NCiAgICBoYW5kbGVCYXRjaERlbGV0ZU1hdGVyaWFsKCkgew0KICAgICAgY29uc3QgbWF0ZXJpYWxJZHMgPSB0aGlzLm1hdGVyaWFsSWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit55qEJyArIG1hdGVyaWFsSWRzLmxlbmd0aCArICfmnaHmnZDmlpnmlbDmja7vvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsTWF0ZXJpYWwobWF0ZXJpYWxJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TWF0ZXJpYWxMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5a+85Ye65p2Q5paZICovDQogICAgaGFuZGxlRXhwb3J0TWF0ZXJpYWwoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtYXRlcmlhbC9tYXRlcmlhbC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMubWF0ZXJpYWxRdWVyeVBhcmFtcw0KICAgICAgfSwgYG1hdGVyaWFsXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKTsNCiAgICB9LA0KDQogICAgLyoqIOaVtOS9k+WvvOWHuiAqLw0KICAgIGhhbmRsZUV4cG9ydENvbXBsZXRlKCkgew0KICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5a+85Ye65pWw5o2u77yM6K+356iN5YCZLi4uIik7DQogICAgICB0aGlzLmRvd25sb2FkKCdtYXRlcmlhbC9tYXRlcmlhbC9leHBvcnRDb21wbGV0ZScsIHsNCiAgICAgICAgLi4udGhpcy5tYXRlcmlhbFF1ZXJ5UGFyYW1zDQogICAgICB9LCBgY29tcGxldGVfZGF0YV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlr7zlh7rmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlh7rlpLHotKU6ICIgKyAoZXJyb3IubWVzc2FnZSB8fCAi5pyq55+l6ZSZ6K+vIikpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmlrDlop7lt6Xoibrlj4LmlbDnu4QgKi8NCiAgICBoYW5kbGVBZGRQYXJhbUdyb3VwKCkgew0KICAgICAgdGhpcy5yZXNldFBhcmFtR3JvdXBGb3JtKCk7DQogICAgICB0aGlzLnBhcmFtR3JvdXBGb3JtLm1hdGVyaWFsSWQgPSB0aGlzLmN1cnJlbnRNYXRlcmlhbC5tYXRlcmlhbElkOw0KICAgICAgdGhpcy5wYXJhbUdyb3VwT3BlbiA9IHRydWU7DQogICAgICB0aGlzLnBhcmFtR3JvdXBUaXRsZSA9ICLmt7vliqDlt6Xoibrlj4LmlbDnu4QiOw0KICAgIH0sDQoNCiAgICAvKiog5L+u5pS55bel6Im65Y+C5pWw57uEICovDQogICAgaGFuZGxlRWRpdFBhcmFtR3JvdXAocm93KSB7DQogICAgICB0aGlzLnJlc2V0UGFyYW1Hcm91cEZvcm0oKTsNCiAgICAgIGNvbnN0IGdyb3VwSWQgPSByb3cuZ3JvdXBJZDsNCiAgICAgIGdldFByb2Nlc3NQYXJhbUdyb3VwKGdyb3VwSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBhcmFtR3JvdXBGb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g5aSE55CG6ZmE5Lu25pWw5o2uDQogICAgICAgIHRoaXMucGFyYW1Hcm91cEZpbGVMaXN0ID0gdGhpcy5wYXJzZUF0dGFjaG1lbnRzKHJlc3BvbnNlLmRhdGEuYXR0YWNobWVudHMpOw0KICAgICAgICB0aGlzLnBhcmFtR3JvdXBPcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5wYXJhbUdyb3VwVGl0bGUgPSAi5L+u5pS55bel6Im65Y+C5pWw57uEIjsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk5bel6Im65Y+C5pWw57uE6KGo5Y2VICovDQogICAgc3VibWl0UGFyYW1Hcm91cEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJwYXJhbUdyb3VwRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g5bCG5paH5Lu25YiX6KGo6L2s5o2i5Li66YCX5Y+35YiG6ZqU55qEVVJM5a2X56ym5LiyDQogICAgICAgICAgdGhpcy5wYXJhbUdyb3VwRm9ybS5hdHRhY2htZW50cyA9IHRoaXMucGFyYW1Hcm91cEZpbGVMaXN0Lmxlbmd0aCA+IDANCiAgICAgICAgICAgID8gdGhpcy5wYXJhbUdyb3VwRmlsZUxpc3QubWFwKGZpbGUgPT4gZmlsZS51cmwpLmpvaW4oJywnKQ0KICAgICAgICAgICAgOiBudWxsOw0KDQogICAgICAgICAgLy8g6K6+572u5Yib5bu65Lq65ZKM5pu05paw5Lq6DQogICAgICAgICAgaWYgKHRoaXMucGFyYW1Hcm91cEZvcm0uZ3JvdXBJZCAhPSBudWxsKSB7DQogICAgICAgICAgICAvLyDmm7TmlrDmk43kvZzvvIzorr7nva7mm7TmlrDkuroNCiAgICAgICAgICAgIHRoaXMucGFyYW1Hcm91cEZvcm0udXBkYXRlQnkgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWU7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaWsOWinuaTjeS9nO+8jOiuvue9ruWIm+W7uuS6ug0KICAgICAgICAgICAgdGhpcy5wYXJhbUdyb3VwRm9ybS5jcmVhdGVCeSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAodGhpcy5wYXJhbUdyb3VwRm9ybS5ncm91cElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVByb2Nlc3NQYXJhbUdyb3VwKHRoaXMucGFyYW1Hcm91cEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5wYXJhbUdyb3VwT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldFBhcmFtR3JvdXBMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkUHJvY2Vzc1BhcmFtR3JvdXAodGhpcy5wYXJhbUdyb3VwRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLnBhcmFtR3JvdXBPcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0UGFyYW1Hcm91cExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlj5bmtojlt6Xoibrlj4LmlbDnu4Tmk43kvZwgKi8NCiAgICBjYW5jZWxQYXJhbUdyb3VwKCkgew0KICAgICAgdGhpcy5wYXJhbUdyb3VwT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldFBhcmFtR3JvdXBGb3JtKCk7DQogICAgfSwNCg0KICAgIC8qKiDph43nva7lt6Xoibrlj4LmlbDnu4TooajljZUgKi8NCiAgICByZXNldFBhcmFtR3JvdXBGb3JtKCkgew0KICAgICAgdGhpcy5wYXJhbUdyb3VwRm9ybSA9IHsNCiAgICAgICAgZ3JvdXBJZDogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxJZDogbnVsbCwNCiAgICAgICAgcHJvY2Vzc1R5cGU6IG51bGwsDQogICAgICAgIHBhcmFtTnVtYmVyOiBudWxsLA0KICAgICAgICBhdHRhY2htZW50czogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5wYXJhbUdyb3VwRmlsZUxpc3QgPSBbXTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJwYXJhbUdyb3VwRm9ybSIpOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5bel6Im65Y+C5pWw57uEICovDQogICAgaGFuZGxlRGVsZXRlUGFyYW1Hcm91cChyb3cpIHsNCiAgICAgIGNvbnN0IGdyb3VwSWRzID0gcm93Lmdyb3VwSWQ7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlj4LmlbDnu4QiJyArIHJvdy5wYXJhbU51bWJlciArICci77yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFByb2Nlc3NQYXJhbUdyb3VwKGdyb3VwSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldFBhcmFtR3JvdXBMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5om56YeP5Yig6Zmk5Y+C5pWw57uEICovDQogICAgaGFuZGxlQmF0Y2hEZWxldGVQYXJhbUdyb3VwKCkgew0KICAgICAgY29uc3QgZ3JvdXBJZHMgPSB0aGlzLnBhcmFtR3JvdXBJZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoQnICsgZ3JvdXBJZHMubGVuZ3RoICsgJ+adoeWPguaVsOe7hOaVsOaNru+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxQcm9jZXNzUGFyYW1Hcm91cChncm91cElkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRQYXJhbUdyb3VwTGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOWvvOWHuuW3peiJuuWPguaVsOe7hCAqLw0KICAgIGhhbmRsZUV4cG9ydFBhcmFtR3JvdXAoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtYXRlcmlhbC9wcm9jZXNzUGFyYW1Hcm91cC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucGFyYW1Hcm91cFF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcGFyYW1fZ3JvdXBfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApOw0KICAgIH0sDQoNCiAgICAvKiog5paw5aKe5Y+C5pWw5piO57uGICovDQogICAgaGFuZGxlQWRkUGFyYW1JdGVtKCkgew0KICAgICAgdGhpcy5yZXNldFBhcmFtSXRlbUZvcm0oKTsNCiAgICAgIHRoaXMucGFyYW1JdGVtRm9ybS5ncm91cElkID0gdGhpcy5jdXJyZW50UGFyYW1Hcm91cC5ncm91cElkOw0KICAgICAgdGhpcy5wYXJhbUl0ZW1PcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMucGFyYW1JdGVtVGl0bGUgPSAi5re75Yqg5Y+C5pWw5piO57uGIjsNCiAgICB9LA0KDQogICAgLyoqIOS/ruaUueWPguaVsOaYjue7hiAqLw0KICAgIGhhbmRsZUVkaXRQYXJhbUl0ZW0ocm93KSB7DQogICAgICB0aGlzLnJlc2V0UGFyYW1JdGVtRm9ybSgpOw0KICAgICAgY29uc3QgaXRlbUlkID0gcm93Lml0ZW1JZDsNCiAgICAgIGdldFByb2Nlc3NQYXJhbUl0ZW0oaXRlbUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5wYXJhbUl0ZW1Gb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g5aSE55CG6ZmE5Lu25pWw5o2uDQogICAgICAgIHRoaXMucGFyYW1JdGVtRmlsZUxpc3QgPSB0aGlzLnBhcnNlQXR0YWNobWVudHMocmVzcG9uc2UuZGF0YS5hdHRhY2htZW50cyk7DQogICAgICAgIHRoaXMucGFyYW1JdGVtT3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMucGFyYW1JdGVtVGl0bGUgPSAi5L+u5pS55Y+C5pWw5piO57uGIjsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk5Y+C5pWw5piO57uG6KGo5Y2VICovDQogICAgc3VibWl0UGFyYW1JdGVtRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbInBhcmFtSXRlbUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOWwhuaWh+S7tuWIl+ihqOi9rOaNouS4uumAl+WPt+WIhumalOeahFVSTOWtl+espuS4sg0KICAgICAgICAgIHRoaXMucGFyYW1JdGVtRm9ybS5hdHRhY2htZW50cyA9IHRoaXMucGFyYW1JdGVtRmlsZUxpc3QubGVuZ3RoID4gMA0KICAgICAgICAgICAgPyB0aGlzLnBhcmFtSXRlbUZpbGVMaXN0Lm1hcChmaWxlID0+IGZpbGUudXJsKS5qb2luKCcsJykNCiAgICAgICAgICAgIDogbnVsbDsNCg0KICAgICAgICAgIC8vIOiuvue9ruWIm+W7uuS6uuWSjOabtOaWsOS6ug0KICAgICAgICAgIGlmICh0aGlzLnBhcmFtSXRlbUZvcm0uaXRlbUlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIC8vIOabtOaWsOaTjeS9nO+8jOiuvue9ruabtOaWsOS6ug0KICAgICAgICAgICAgdGhpcy5wYXJhbUl0ZW1Gb3JtLnVwZGF0ZUJ5ID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmlrDlop7mk43kvZzvvIzorr7nva7liJvlu7rkuroNCiAgICAgICAgICAgIHRoaXMucGFyYW1JdGVtRm9ybS5jcmVhdGVCeSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAodGhpcy5wYXJhbUl0ZW1Gb3JtLml0ZW1JZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVQcm9jZXNzUGFyYW1JdGVtKHRoaXMucGFyYW1JdGVtRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLnBhcmFtSXRlbU9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRQYXJhbUl0ZW1MaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkUHJvY2Vzc1BhcmFtSXRlbSh0aGlzLnBhcmFtSXRlbUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5wYXJhbUl0ZW1PcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0UGFyYW1JdGVtTGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWPlua2iOWPguaVsOaYjue7huaTjeS9nCAqLw0KICAgIGNhbmNlbFBhcmFtSXRlbSgpIHsNCiAgICAgIHRoaXMucGFyYW1JdGVtT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldFBhcmFtSXRlbUZvcm0oKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruWPguaVsOaYjue7huihqOWNlSAqLw0KICAgIHJlc2V0UGFyYW1JdGVtRm9ybSgpIHsNCiAgICAgIHRoaXMucGFyYW1JdGVtRm9ybSA9IHsNCiAgICAgICAgaXRlbUlkOiBudWxsLA0KICAgICAgICBncm91cElkOiBudWxsLA0KICAgICAgICBwYXJhbU5hbWU6IG51bGwsDQogICAgICAgIHBhcmFtVmFsdWU6IG51bGwsDQogICAgICAgIHVuaXQ6IG51bGwsDQogICAgICAgIGF0dGFjaG1lbnRzOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnBhcmFtSXRlbUZpbGVMaXN0ID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicGFyYW1JdGVtRm9ybSIpOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5Y+C5pWw5piO57uGICovDQogICAgaGFuZGxlRGVsZXRlUGFyYW1JdGVtKHJvdykgew0KICAgICAgY29uc3QgaXRlbUlkcyA9IHJvdy5pdGVtSWQ7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlj4LmlbAiJyArIHJvdy5wYXJhbU5hbWUgKyAnIu+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxQcm9jZXNzUGFyYW1JdGVtKGl0ZW1JZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0UGFyYW1JdGVtTGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOaJuemHj+WIoOmZpOWPguaVsOaYjue7hiAqLw0KICAgIGhhbmRsZUJhdGNoRGVsZXRlUGFyYW1JdGVtKCkgew0KICAgICAgY29uc3QgaXRlbUlkcyA9IHRoaXMucGFyYW1JdGVtSWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit55qEJyArIGl0ZW1JZHMubGVuZ3RoICsgJ+adoeWPguaVsOaYjue7huaVsOaNru+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxQcm9jZXNzUGFyYW1JdGVtKGl0ZW1JZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0UGFyYW1JdGVtTGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOWvvOWHuuWPguaVsOaYjue7hiAqLw0KICAgIGhhbmRsZUV4cG9ydFBhcmFtSXRlbSgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21hdGVyaWFsL3Byb2Nlc3NQYXJhbUl0ZW0vZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnBhcmFtSXRlbVF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcGFyYW1faXRlbV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCk7DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnpmYTku7bkuIrkvKDmiJDlip8gKi8NCiAgICBoYW5kbGVNYXRlcmlhbFVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxGaWxlTGlzdCA9IGZpbGVMaXN0Lm1hcChpdGVtID0+ICh7DQogICAgICAgICAgbmFtZTogaXRlbS5uYW1lLA0KICAgICAgICAgIHVybDogaXRlbS5yZXNwb25zZSA/IGl0ZW0ucmVzcG9uc2UudXJsIDogaXRlbS51cmwsDQogICAgICAgICAgc2l6ZTogdGhpcy5mb3JtYXRGaWxlU2l6ZShpdGVtLnNpemUgfHwgaXRlbS5yYXc/LnNpemUpLA0KICAgICAgICAgIHVpZDogaXRlbS51aWQsDQogICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgfSkpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAi5LiK5Lyg5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmnZDmlpnpmYTku7bnp7vpmaQgKi8NCiAgICBoYW5kbGVNYXRlcmlhbEZpbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmnZDmlpnpmYTku7bnp7vpmaTlm57osIPvvJonLCB7IGZpbGUsIGZpbGVMaXN0IH0pOw0KICAgICAgLy8g56Gu5L+dZmlsZUxpc3TmmK/mlbDnu4TvvIzlubbkuJTmraPnoa7lpITnkIbnqbrmlbDnu4TnmoTmg4XlhrUNCiAgICAgIGlmIChBcnJheS5pc0FycmF5KGZpbGVMaXN0KSkgew0KICAgICAgICB0aGlzLm1hdGVyaWFsRmlsZUxpc3QgPSBmaWxlTGlzdC5tYXAoaXRlbSA9PiAoew0KICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgICB1cmw6IGl0ZW0ucmVzcG9uc2UgPyBpdGVtLnJlc3BvbnNlLnVybCA6IGl0ZW0udXJsLA0KICAgICAgICAgIHNpemU6IHRoaXMuZm9ybWF0RmlsZVNpemUoaXRlbS5zaXplIHx8IGl0ZW0ucmF3Py5zaXplKSwNCiAgICAgICAgICB1aWQ6IGl0ZW0udWlkLA0KICAgICAgICAgIHN0YXR1czogaXRlbS5zdGF0dXMgfHwgJ3N1Y2Nlc3MnDQogICAgICAgIH0pKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2ZpbGVMaXN05LiN5piv5pWw57uE77yaJywgZmlsZUxpc3QpOw0KICAgICAgICB0aGlzLm1hdGVyaWFsRmlsZUxpc3QgPSBbXTsNCiAgICAgIH0NCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIumZhOS7tuWIoOmZpOaIkOWKnyIpOw0KICAgIH0sDQoNCiAgICAvKiog5p2Q5paZ6ZmE5Lu25LiK5Lyg5YmN5qOA5p+lICovDQogICAgYmVmb3JlTWF0ZXJpYWxVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTA7DQogICAgICBpZiAoIWlzTHQxME0pIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzTHQxME07DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDnu4TpmYTku7bkuIrkvKDmiJDlip8gKi8NCiAgICBoYW5kbGVQYXJhbUdyb3VwVXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy5wYXJhbUdyb3VwRmlsZUxpc3QgPSBmaWxlTGlzdC5tYXAoaXRlbSA9PiAoew0KICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgICB1cmw6IGl0ZW0ucmVzcG9uc2UgPyBpdGVtLnJlc3BvbnNlLnVybCA6IGl0ZW0udXJsLA0KICAgICAgICAgIHNpemU6IHRoaXMuZm9ybWF0RmlsZVNpemUoaXRlbS5zaXplIHx8IGl0ZW0ucmF3Py5zaXplKSwNCiAgICAgICAgICB1aWQ6IGl0ZW0udWlkLA0KICAgICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnDQogICAgICAgIH0pKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5LiK5Lyg5oiQ5YqfIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihyZXNwb25zZS5tc2cgfHwgIuS4iuS8oOWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5Y+C5pWw57uE6ZmE5Lu256e76ZmkICovDQogICAgaGFuZGxlUGFyYW1Hcm91cEZpbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMucGFyYW1Hcm91cEZpbGVMaXN0ID0gZmlsZUxpc3QubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgbmFtZTogaXRlbS5uYW1lLA0KICAgICAgICB1cmw6IGl0ZW0ucmVzcG9uc2UgPyBpdGVtLnJlc3BvbnNlLnVybCA6IGl0ZW0udXJsLA0KICAgICAgICBzaXplOiB0aGlzLmZvcm1hdEZpbGVTaXplKGl0ZW0uc2l6ZSB8fCBpdGVtLnJhdz8uc2l6ZSksDQogICAgICAgIHVpZDogaXRlbS51aWQsDQogICAgICAgIHN0YXR1czogaXRlbS5zdGF0dXMgfHwgJ3N1Y2Nlc3MnDQogICAgICB9KSk7DQogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLpmYTku7bliKDpmaTmiJDlip8iKTsNCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOe7hOmZhOS7tuS4iuS8oOWJjeajgOafpSAqLw0KICAgIGJlZm9yZVBhcmFtR3JvdXBVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTA7DQogICAgICBpZiAoIWlzTHQxME0pIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzTHQxME07DQogICAgfSwNCg0KICAgIC8qKiDlj4LmlbDmmI7nu4bpmYTku7bkuIrkvKDmiJDlip8gKi8NCiAgICBoYW5kbGVQYXJhbUl0ZW1VcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICB0aGlzLnBhcmFtSXRlbUZpbGVMaXN0ID0gZmlsZUxpc3QubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsDQogICAgICAgICAgdXJsOiBpdGVtLnJlc3BvbnNlID8gaXRlbS5yZXNwb25zZS51cmwgOiBpdGVtLnVybCwNCiAgICAgICAgICBzaXplOiB0aGlzLmZvcm1hdEZpbGVTaXplKGl0ZW0uc2l6ZSB8fCBpdGVtLnJhdz8uc2l6ZSksDQogICAgICAgICAgdWlkOiBpdGVtLnVpZCwNCiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJw0KICAgICAgICB9KSk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS4iuS8oOaIkOWKnyIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnIHx8ICLkuIrkvKDlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWPguaVsOaYjue7humZhOS7tuenu+mZpCAqLw0KICAgIGhhbmRsZVBhcmFtSXRlbUZpbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMucGFyYW1JdGVtRmlsZUxpc3QgPSBmaWxlTGlzdC5tYXAoaXRlbSA9PiAoew0KICAgICAgICBuYW1lOiBpdGVtLm5hbWUsDQogICAgICAgIHVybDogaXRlbS5yZXNwb25zZSA/IGl0ZW0ucmVzcG9uc2UudXJsIDogaXRlbS51cmwsDQogICAgICAgIHNpemU6IHRoaXMuZm9ybWF0RmlsZVNpemUoaXRlbS5zaXplIHx8IGl0ZW0ucmF3Py5zaXplKSwNCiAgICAgICAgdWlkOiBpdGVtLnVpZCwNCiAgICAgICAgc3RhdHVzOiBpdGVtLnN0YXR1cyB8fCAnc3VjY2VzcycNCiAgICAgIH0pKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIumZhOS7tuWIoOmZpOaIkOWKnyIpOw0KICAgIH0sDQoNCiAgICAvKiog5Y+C5pWw5piO57uG6ZmE5Lu25LiK5Lyg5YmN5qOA5p+lICovDQogICAgYmVmb3JlUGFyYW1JdGVtVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOw0KICAgICAgaWYgKCFpc0x0MTBNKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBpc0x0MTBNOw0KICAgIH0sDQoNCiAgICAvKiog5p+l55yL6ZmE5Lu2ICovDQogICAgaGFuZGxlVmlld0F0dGFjaG1lbnRzKGF0dGFjaG1lbnRzKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAodHlwZW9mIGF0dGFjaG1lbnRzID09PSAnc3RyaW5nJykgew0KICAgICAgICAgIC8vIOWmguaenOaYr+mAl+WPt+WIhumalOeahFVSTOWtl+espuS4su+8jOi9rOaNouS4uuWvueixoeaVsOe7hA0KICAgICAgICAgIGlmIChhdHRhY2htZW50cy5pbmNsdWRlcygnLCcpIHx8IChhdHRhY2htZW50cyAmJiAhYXR0YWNobWVudHMuc3RhcnRzV2l0aCgnWycpKSkgew0KICAgICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IGF0dGFjaG1lbnRzLnNwbGl0KCcsJykuZmlsdGVyKHVybCA9PiB1cmwudHJpbSgpKS5tYXAoKHVybCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB1cmwuc3Vic3RyaW5nKHVybC5sYXN0SW5kZXhPZignLycpICsgMSkgfHwgYOmZhOS7tiR7aW5kZXggKyAxfWA7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiB1cmwudHJpbSgpDQogICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5bCd6K+V6Kej5p6QSlNPTuagvOW8jw0KICAgICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IEpTT04ucGFyc2UoYXR0YWNobWVudHMgfHwgJ1tdJyk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoYXR0YWNobWVudHMpKSB7DQogICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IGF0dGFjaG1lbnRzOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYXR0YWNobWVudExpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAvLyDlpoLmnpxKU09O6Kej5p6Q5aSx6LSl77yM5bCd6K+V5L2c5Li66YCX5Y+35YiG6ZqU55qE5a2X56ym5Liy5aSE55CGDQogICAgICAgIGlmICh0eXBlb2YgYXR0YWNobWVudHMgPT09ICdzdHJpbmcnICYmIGF0dGFjaG1lbnRzLnRyaW0oKSkgew0KICAgICAgICAgIHRoaXMuYXR0YWNobWVudExpc3QgPSBhdHRhY2htZW50cy5zcGxpdCgnLCcpLmZpbHRlcih1cmwgPT4gdXJsLnRyaW0oKSkubWFwKCh1cmwsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHVybC5zdWJzdHJpbmcodXJsLmxhc3RJbmRleE9mKCcvJykgKyAxKSB8fCBg6ZmE5Lu2JHtpbmRleCArIDF9YDsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICB1cmw6IHVybC50cmltKCkNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5hdHRhY2htZW50TGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmF0dGFjaG1lbnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOS4i+i9vemZhOS7tiAqLw0KICAgIGRvd25sb2FkQXR0YWNobWVudCh1cmwsIG5hbWUpIHsNCiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7DQogICAgICBsaW5rLmhyZWYgPSB1cmw7DQogICAgICBsaW5rLmRvd25sb2FkID0gbmFtZTsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluaWh+S7tuWkp+WwjyAqLw0KICAgIGZvcm1hdEZpbGVTaXplKHNpemUpIHsNCiAgICAgIGlmICghc2l6ZSkgcmV0dXJuICcwIEInOw0KICAgICAgaWYgKHNpemUgPCAxMDI0KSB7DQogICAgICAgIHJldHVybiBzaXplICsgJyBCJzsNCiAgICAgIH0gZWxzZSBpZiAoc2l6ZSA8IDEwMjQgKiAxMDI0KSB7DQogICAgICAgIHJldHVybiAoc2l6ZSAvIDEwMjQpLnRvRml4ZWQoMikgKyAnIEtCJzsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAoc2l6ZSAvIDEwMjQgLyAxMDI0KS50b0ZpeGVkKDIpICsgJyBNQic7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/material/config", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-setting\"></i>\r\n        <span>材料及工艺参数配置</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理材料信息、工艺参数组和参数明细的三层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击材料行查看工艺参数组，点击参数组行查看参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 材料信息表格 -->\r\n    <el-card class=\"material-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-box\"></i>\r\n          <span class=\"header-title\">材料信息管理</span>\r\n          <el-badge :value=\"materialTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddMaterial\">\r\n            <span>新增材料</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"materialMultiple\" @click=\"handleBatchDeleteMaterial\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportComplete\" class=\"export-complete-btn\">\r\n            <span>整体导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 材料查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"materialQueryParams\" ref=\"materialQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialName\"\r\n              :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n              placeholder=\"请输入材料名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handleMaterialNameSelect\"\r\n              @focus=\"handleMaterialNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-search\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.supplierName\"\r\n              :fetch-suggestions=\"querySupplierSuggestions\"\r\n              placeholder=\"请输入供应商名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleSupplierFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-office-building\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialModel\"\r\n              :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n              placeholder=\"请输入材料型号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleMaterialModelFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-goods\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleMaterialQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetMaterialQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"materialLoading\"\r\n          :data=\"materialList\"\r\n          @row-click=\"handleMaterialClick\"\r\n          @selection-change=\"handleMaterialSelectionChange\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getMaterialRowClassName\"\r\n          ref=\"materialTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载材料数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (materialQueryParams.pageNum - 1) * materialQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"材料名称\" min-width=\"140\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"material-name-cell\">\r\n                <i class=\"el-icon-box material-icon\"></i>\r\n                <span class=\"material-name\">{{ scope.row.materialName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"supplierName\" label=\"供应商\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"supplier-cell\">\r\n                <i class=\"el-icon-office-building supplier-icon\"></i>\r\n                <span>{{ scope.row.supplierName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialModel\" label=\"材料型号\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"info\" v-if=\"scope.row.materialModel\">{{ scope.row.materialModel }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialDescription\" label=\"材料描述\" min-width=\"180\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.materialDescription\" class=\"description-text\">{{ scope.row.materialDescription }}</span>\r\n              <span v-else class=\"empty-data\">暂无描述</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditMaterial(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteMaterial(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"materialTotal > 0\"\r\n        :total=\"materialTotal\"\r\n        :page.sync=\"materialQueryParams.pageNum\"\r\n        :limit.sync=\"materialQueryParams.pageSize\"\r\n        @pagination=\"getMaterialList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 工艺参数组表格 -->\r\n    <el-card class=\"param-group-card enhanced-card\" style=\"margin-bottom: 20px;\" v-show=\"currentMaterial\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-setting\"></i>\r\n          <span class=\"header-title\">工艺参数组</span>\r\n          <div class=\"material-indicator\" v-if=\"currentMaterial\">\r\n            <el-tag type=\"success\" size=\"small\">\r\n              <i class=\"el-icon-box\"></i>\r\n              {{ currentMaterial.materialName }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"paramGroupTotal\" class=\"item-count-badge\" type=\"success\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>新增参数组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"paramGroupMultiple || !currentMaterial\" @click=\"handleBatchDeleteParamGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"paramGroupQueryParams\" ref=\"paramGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.processType\"\r\n              :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n              placeholder=\"请输入工艺类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleProcessTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-setting\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.paramNumber\"\r\n              :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n              placeholder=\"请输入参数编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNumberFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-tickets\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleParamGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetParamGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table\r\n        v-loading=\"paramGroupLoading\"\r\n        :data=\"paramGroupList\"\r\n        @row-click=\"handleParamGroupClick\"\r\n        @selection-change=\"handleParamGroupSelectionChange\"\r\n        highlight-current-row\r\n        style=\"width: 100%\"\r\n        :row-class-name=\"getParamGroupRowClassName\"\r\n        ref=\"paramGroupTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column prop=\"processType\" label=\"工艺类型\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column prop=\"paramNumber\" label=\"参数编号\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" />\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\" />\r\n        <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click.stop=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditParamGroup(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click.stop=\"handleDeleteParamGroup(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"paramGroupTotal > 0\"\r\n        :total=\"paramGroupTotal\"\r\n        :page.sync=\"paramGroupQueryParams.pageNum\"\r\n        :limit.sync=\"paramGroupQueryParams.pageSize\"\r\n        @pagination=\"getParamGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 参数明细表格 -->\r\n    <el-card class=\"box-card\" v-show=\"currentParamGroup\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-weight: bold; font-size: 16px;\">参数明细 - {{ currentParamGroup ? currentParamGroup.paramNumber : '' }}</span>\r\n        <div style=\"float: right;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddParamItem\" :disabled=\"!currentParamGroup\">新增</el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" :disabled=\"paramItemMultiple || !currentParamGroup\" @click=\"handleBatchDeleteParamItem\">批量删除</el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExportParamItem\" :disabled=\"!currentParamGroup\">导出</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数明细查询条件 -->\r\n      <el-form :model=\"paramItemQueryParams\" ref=\"paramItemQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" style=\"margin-bottom: 15px;\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-autocomplete\r\n            v-model=\"paramItemQueryParams.paramName\"\r\n            :fetch-suggestions=\"queryParamNameSuggestions\"\r\n            placeholder=\"请输入参数名称\"\r\n            clearable\r\n            style=\"width: 200px;\"\r\n            @focus=\"handleParamNameFocus\"\r\n            :trigger-on-focus=\"true\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"参数单位\" prop=\"unit\">\r\n          <el-autocomplete\r\n            v-model=\"paramItemQueryParams.unit\"\r\n            :fetch-suggestions=\"queryUnitSuggestions\"\r\n            placeholder=\"请输入参数单位\"\r\n            clearable\r\n            style=\"width: 200px;\"\r\n            @focus=\"handleUnitFocus\"\r\n            :trigger-on-focus=\"true\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleParamItemQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetParamItemQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <el-table\r\n        v-loading=\"paramItemLoading\"\r\n        :data=\"paramItemList\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleParamItemSelectionChange\"\r\n        @row-click=\"handleParamItemRowClick\"\r\n        ref=\"paramItemTable\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip />\r\n        <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.paramValue !== null ? scope.row.paramValue : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"unit\" label=\"单位\" width=\"80\" />\r\n        <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" />\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\" />\r\n        <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\"scope.row.attachments\" size=\"mini\" type=\"text\" @click.stop=\"handleViewAttachments(scope.row.attachments)\">查看</el-button>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"handleEditParamItem(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"text\" style=\"color: #f56c6c\" @click=\"handleDeleteParamItem(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"paramItemTotal > 0\"\r\n        :total=\"paramItemTotal\"\r\n        :page.sync=\"paramItemQueryParams.pageNum\"\r\n        :limit.sync=\"paramItemQueryParams.pageSize\"\r\n        @pagination=\"getParamItemList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 材料信息对话框 -->\r\n    <el-dialog :title=\"materialTitle\" :visible.sync=\"materialOpen\" width=\"800px\" append-to-body v-drag>\r\n      <el-form ref=\"materialForm\" :model=\"materialForm\" :rules=\"materialRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n              <el-input v-model=\"materialForm.materialName\" placeholder=\"请输入材料名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"supplierName\">\r\n              <el-input v-model=\"materialForm.supplierName\" placeholder=\"请输入供应商名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n              <el-input v-model=\"materialForm.materialModel\" placeholder=\"请输入材料型号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"材料描述\">\r\n          <el-input v-model=\"materialForm.materialDescription\" type=\"textarea\" placeholder=\"请输入材料描述\" :rows=\"3\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"materialUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"materialFileList\"\r\n            :on-success=\"handleMaterialUploadSuccess\"\r\n            :on-remove=\"handleMaterialFileRemove\"\r\n            :before-upload=\"beforeMaterialUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"materialForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitMaterialForm\">确 定</el-button>\r\n        <el-button @click=\"cancelMaterial\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工艺参数组对话框 -->\r\n    <el-dialog :title=\"paramGroupTitle\" :visible.sync=\"paramGroupOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramGroupForm\" :model=\"paramGroupForm\" :rules=\"paramGroupRules\" label-width=\"100px\">\r\n        <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n          <el-input v-model=\"paramGroupForm.processType\" placeholder=\"请输入工艺类型\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n          <el-input v-model=\"paramGroupForm.paramNumber\" placeholder=\"请输入参数编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramGroupUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramGroupFileList\"\r\n            :on-success=\"handleParamGroupUploadSuccess\"\r\n            :on-remove=\"handleParamGroupFileRemove\"\r\n            :before-upload=\"beforeParamGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数明细对话框 -->\r\n    <el-dialog :title=\"paramItemTitle\" :visible.sync=\"paramItemOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramItemForm\" :model=\"paramItemForm\" :rules=\"paramItemRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"paramItemForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"paramItemForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"paramItemForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramItemUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramItemFileList\"\r\n            :on-success=\"handleParamItemUploadSuccess\"\r\n            :on-remove=\"handleParamItemFileRemove\"\r\n            :before-upload=\"beforeParamItemUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramItemForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamItemForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamItem\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial,\r\n  exportMaterial, getMaterialOptions\r\n} from \"@/api/material/material\";\r\nimport {\r\n  listProcessParamGroup, getProcessParamGroup, delProcessParamGroup,\r\n  addProcessParamGroup, updateProcessParamGroup, listByMaterialId,\r\n  exportProcessParamGroup, getProcessParamGroupOptions, exportCompleteData\r\n} from \"@/api/material/processParamGroup\";\r\nimport {\r\n  listProcessParamItem, getProcessParamItem, delProcessParamItem,\r\n  addProcessParamItem, updateProcessParamItem, listByGroupId,\r\n  exportProcessParamItem, getProcessParamItemOptions\r\n} from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\n\r\nexport default {\r\n  name: \"MaterialConfig\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n\r\n      // 材料相关\r\n      materialLoading: true,\r\n      materialList: [],\r\n      materialTotal: 0,\r\n      currentMaterial: null,\r\n      materialOpen: false,\r\n      materialTitle: \"\",\r\n      materialForm: {},\r\n      materialFileList: [],\r\n      materialIds: [],\r\n      materialSingle: true,\r\n      materialMultiple: true,\r\n      materialQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null\r\n      },\r\n      materialRules: {\r\n        materialName: [\r\n          { required: true, message: \"材料名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 工艺参数组相关\r\n      paramGroupLoading: false,\r\n      paramGroupList: [],\r\n      paramGroupTotal: 0,\r\n      currentParamGroup: null,\r\n      paramGroupOpen: false,\r\n      paramGroupTitle: \"\",\r\n      paramGroupForm: {},\r\n      paramGroupFileList: [],\r\n      paramGroupIds: [],\r\n      paramGroupSingle: true,\r\n      paramGroupMultiple: true,\r\n      paramGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null\r\n      },\r\n      paramGroupRules: {\r\n        processType: [\r\n          { required: true, message: \"工艺类型不能为空\", trigger: \"blur\" }\r\n        ],\r\n        paramNumber: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 参数明细相关\r\n      paramItemLoading: false,\r\n      paramItemList: [],\r\n      paramItemTotal: 0,\r\n      paramItemOpen: false,\r\n      paramItemTitle: \"\",\r\n      paramItemForm: {},\r\n      paramItemFileList: [],\r\n      paramItemIds: [],\r\n      paramItemSingle: true,\r\n      paramItemMultiple: true,\r\n      paramItemQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        groupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      paramItemRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      materialNameSuggestions: [],\r\n      supplierSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      paramNumberSuggestions: [],\r\n      unitSuggestions: []\r\n    };\r\n  },\r\n  created() {\r\n    this.getMaterialList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数编号建议\r\n      getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数名称建议\r\n      getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n        this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数单位建议\r\n      getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n        this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商搜索建议 */\r\n    querySupplierSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数名称搜索建议 */\r\n    /** 参数名称搜索建议 */\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数单位搜索建议 */\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料名称选择事件 */\r\n    handleMaterialNameSelect(item) {\r\n      this.materialQueryParams.materialName = item.value;\r\n      // 移除自动搜索，让用户手动点击搜索按钮\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      // 重新加载材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商焦点事件 */\r\n    handleSupplierFocus() {\r\n      // 重新加载供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      // 重新加载材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      // 基于当前选中的材料加载工艺类型建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取工艺类型选项\r\n        const processTypes = [...new Set(this.paramGroupList.map(item => item.processType).filter(Boolean))];\r\n        this.processTypeSuggestions = processTypes.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有工艺类型\r\n        getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n          this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      // 基于当前选中的材料加载参数编号建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取参数编号选项\r\n        const paramNumbers = [...new Set(this.paramGroupList.map(item => item.paramNumber).filter(Boolean))];\r\n        this.paramNumberSuggestions = paramNumbers.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有参数编号\r\n        getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n          this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数名称焦点事件 */\r\n    handleParamNameFocus() {\r\n      // 基于当前选中的参数组加载参数名称建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数名称选项\r\n        const paramNames = [...new Set(this.paramItemList.map(item => item.paramName).filter(Boolean))];\r\n        this.paramNameSuggestions = paramNames.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数名称\r\n        getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数单位焦点事件 */\r\n    handleUnitFocus() {\r\n      // 基于当前选中的参数组加载参数单位建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数单位选项\r\n        const units = [...new Set(this.paramItemList.map(item => item.unit).filter(Boolean))];\r\n        this.unitSuggestions = units.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数单位\r\n        getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      // 如果已经是数组，直接返回\r\n      if (Array.isArray(attachments)) {\r\n        return attachments;\r\n      }\r\n\r\n      // 如果是字符串，按逗号分割并转换为文件对象\r\n      if (typeof attachments === 'string') {\r\n        return attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n          const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n          return {\r\n            name: fileName,\r\n            url: url.trim(),\r\n            uid: Date.now() + index,\r\n            status: 'success'\r\n          };\r\n        });\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 查询材料列表 */\r\n    getMaterialList() {\r\n      this.materialLoading = true;\r\n      listMaterial(this.materialQueryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.materialTotal = response.total;\r\n        this.materialLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 材料查询 */\r\n    handleMaterialQuery() {\r\n      this.materialQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n    },\r\n\r\n    /** 重置材料查询 */\r\n    resetMaterialQuery() {\r\n      this.resetForm(\"materialQueryForm\");\r\n      this.handleMaterialQuery();\r\n    },\r\n\r\n    /** 材料行点击事件 */\r\n    handleMaterialClick(row) {\r\n      this.currentMaterial = row;\r\n      this.paramGroupQueryParams.materialId = row.materialId;\r\n      this.getParamGroupList();\r\n      this.paramItemList = [];\r\n      this.paramItemTotal = 0;\r\n      this.currentParamGroup = null;\r\n      // 同时选中该行\r\n      this.$refs.materialTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 材料选择变化事件 */\r\n    handleMaterialSelectionChange(selection) {\r\n      this.materialIds = selection.map(item => item.materialId);\r\n      this.materialSingle = selection.length !== 1;\r\n      this.materialMultiple = !selection.length;\r\n    },\r\n\r\n    /** 材料行样式 */\r\n    getMaterialRowClassName({row, rowIndex}) {\r\n      if (this.currentMaterial && row.materialId === this.currentMaterial.materialId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询工艺参数组列表 */\r\n    getParamGroupList() {\r\n      if (!this.currentMaterial) return;\r\n      this.paramGroupLoading = true;\r\n      listProcessParamGroup(this.paramGroupQueryParams).then(response => {\r\n        this.paramGroupList = response.rows;\r\n        this.paramGroupTotal = response.total;\r\n        this.paramGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数组查询 */\r\n    handleParamGroupQuery() {\r\n      this.paramGroupQueryParams.pageNum = 1;\r\n      this.getParamGroupList();\r\n    },\r\n\r\n    /** 重置参数组查询 */\r\n    resetParamGroupQuery() {\r\n      this.resetForm(\"paramGroupQueryForm\");\r\n      this.handleParamGroupQuery();\r\n    },\r\n\r\n    /** 工艺参数组行点击事件 */\r\n    handleParamGroupClick(row) {\r\n      this.currentParamGroup = row;\r\n      this.paramItemQueryParams.groupId = row.groupId;\r\n      this.getParamItemList();\r\n      // 同时选中该行\r\n      this.$refs.paramGroupTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数组选择变化事件 */\r\n    handleParamGroupSelectionChange(selection) {\r\n      this.paramGroupIds = selection.map(item => item.groupId);\r\n      this.paramGroupSingle = selection.length !== 1;\r\n      this.paramGroupMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数明细行点击事件 */\r\n    handleParamItemRowClick(row) {\r\n      this.$refs.paramItemTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数明细选择变化事件 */\r\n    handleParamItemSelectionChange(selection) {\r\n      this.paramItemIds = selection.map(item => item.itemId);\r\n      this.paramItemSingle = selection.length !== 1;\r\n      this.paramItemMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数组行样式 */\r\n    getParamGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentParamGroup && row.groupId === this.currentParamGroup.groupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询参数明细列表 */\r\n    getParamItemList() {\r\n      if (!this.currentParamGroup) return;\r\n      this.paramItemLoading = true;\r\n      listProcessParamItem(this.paramItemQueryParams).then(response => {\r\n        this.paramItemList = response.rows;\r\n        this.paramItemTotal = response.total;\r\n        this.paramItemLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数明细查询 */\r\n    handleParamItemQuery() {\r\n      this.paramItemQueryParams.pageNum = 1;\r\n      this.getParamItemList();\r\n    },\r\n\r\n    /** 重置参数明细查询 */\r\n    resetParamItemQuery() {\r\n      this.resetForm(\"paramItemQueryForm\");\r\n      this.handleParamItemQuery();\r\n    },\r\n\r\n    /** 新增材料 */\r\n    handleAddMaterial() {\r\n      this.resetMaterialForm();\r\n      this.materialOpen = true;\r\n      this.materialTitle = \"添加材料信息\";\r\n    },\r\n\r\n    /** 修改材料 */\r\n    handleEditMaterial(row) {\r\n      this.resetMaterialForm();\r\n      const materialId = row.materialId;\r\n      getMaterial(materialId).then(response => {\r\n        this.materialForm = response.data;\r\n        // 处理附件数据\r\n        this.materialFileList = this.parseAttachments(response.data.attachments);\r\n        this.materialOpen = true;\r\n        this.materialTitle = \"修改材料信息\";\r\n      });\r\n    },\r\n\r\n    /** 提交材料表单 */\r\n    submitMaterialForm() {\r\n      this.$refs[\"materialForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.materialForm.attachments = this.materialFileList.length > 0\r\n            ? this.materialFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.materialForm.materialId != null) {\r\n            // 更新操作，设置更新人\r\n            this.materialForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.materialForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.materialForm.materialId != null) {\r\n            updateMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          } else {\r\n            addMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消材料操作 */\r\n    cancelMaterial() {\r\n      this.materialOpen = false;\r\n      this.resetMaterialForm();\r\n    },\r\n\r\n    /** 重置材料表单 */\r\n    resetMaterialForm() {\r\n      this.materialForm = {\r\n        materialId: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        materialDescription: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.materialFileList = [];\r\n      this.resetForm(\"materialForm\");\r\n    },\r\n\r\n    /** 删除材料 */\r\n    handleDeleteMaterial(row) {\r\n      const materialIds = row.materialId;\r\n      this.$modal.confirm('是否确认删除材料\"' + row.materialName + '\"？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除材料 */\r\n    handleBatchDeleteMaterial() {\r\n      const materialIds = this.materialIds;\r\n      this.$modal.confirm('是否确认删除选中的' + materialIds.length + '条材料数据？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出材料 */\r\n    handleExportMaterial() {\r\n      this.download('material/material/export', {\r\n        ...this.materialQueryParams\r\n      }, `material_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 整体导出 */\r\n    handleExportComplete() {\r\n      this.$modal.loading(\"正在导出数据，请稍候...\");\r\n      this.download('material/material/exportComplete', {\r\n        ...this.materialQueryParams\r\n      }, `complete_data_${new Date().getTime()}.xlsx`).then(() => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgSuccess(\"导出成功\");\r\n      }).catch(error => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(\"导出失败: \" + (error.message || \"未知错误\"));\r\n      });\r\n    },\r\n\r\n    /** 新增工艺参数组 */\r\n    handleAddParamGroup() {\r\n      this.resetParamGroupForm();\r\n      this.paramGroupForm.materialId = this.currentMaterial.materialId;\r\n      this.paramGroupOpen = true;\r\n      this.paramGroupTitle = \"添加工艺参数组\";\r\n    },\r\n\r\n    /** 修改工艺参数组 */\r\n    handleEditParamGroup(row) {\r\n      this.resetParamGroupForm();\r\n      const groupId = row.groupId;\r\n      getProcessParamGroup(groupId).then(response => {\r\n        this.paramGroupForm = response.data;\r\n        // 处理附件数据\r\n        this.paramGroupFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramGroupOpen = true;\r\n        this.paramGroupTitle = \"修改工艺参数组\";\r\n      });\r\n    },\r\n\r\n    /** 提交工艺参数组表单 */\r\n    submitParamGroupForm() {\r\n      this.$refs[\"paramGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramGroupForm.attachments = this.paramGroupFileList.length > 0\r\n            ? this.paramGroupFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramGroupForm.groupId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramGroupForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramGroupForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramGroupForm.groupId != null) {\r\n            updateProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          } else {\r\n            addProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消工艺参数组操作 */\r\n    cancelParamGroup() {\r\n      this.paramGroupOpen = false;\r\n      this.resetParamGroupForm();\r\n    },\r\n\r\n    /** 重置工艺参数组表单 */\r\n    resetParamGroupForm() {\r\n      this.paramGroupForm = {\r\n        groupId: null,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramGroupFileList = [];\r\n      this.resetForm(\"paramGroupForm\");\r\n    },\r\n\r\n    /** 删除工艺参数组 */\r\n    handleDeleteParamGroup(row) {\r\n      const groupIds = row.groupId;\r\n      this.$modal.confirm('是否确认删除参数组\"' + row.paramNumber + '\"？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数组 */\r\n    handleBatchDeleteParamGroup() {\r\n      const groupIds = this.paramGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + groupIds.length + '条参数组数据？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出工艺参数组 */\r\n    handleExportParamGroup() {\r\n      this.download('material/processParamGroup/export', {\r\n        ...this.paramGroupQueryParams\r\n      }, `param_group_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 新增参数明细 */\r\n    handleAddParamItem() {\r\n      this.resetParamItemForm();\r\n      this.paramItemForm.groupId = this.currentParamGroup.groupId;\r\n      this.paramItemOpen = true;\r\n      this.paramItemTitle = \"添加参数明细\";\r\n    },\r\n\r\n    /** 修改参数明细 */\r\n    handleEditParamItem(row) {\r\n      this.resetParamItemForm();\r\n      const itemId = row.itemId;\r\n      getProcessParamItem(itemId).then(response => {\r\n        this.paramItemForm = response.data;\r\n        // 处理附件数据\r\n        this.paramItemFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramItemOpen = true;\r\n        this.paramItemTitle = \"修改参数明细\";\r\n      });\r\n    },\r\n\r\n    /** 提交参数明细表单 */\r\n    submitParamItemForm() {\r\n      this.$refs[\"paramItemForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramItemForm.attachments = this.paramItemFileList.length > 0\r\n            ? this.paramItemFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramItemForm.itemId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramItemForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramItemForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramItemForm.itemId != null) {\r\n            updateProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          } else {\r\n            addProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消参数明细操作 */\r\n    cancelParamItem() {\r\n      this.paramItemOpen = false;\r\n      this.resetParamItemForm();\r\n    },\r\n\r\n    /** 重置参数明细表单 */\r\n    resetParamItemForm() {\r\n      this.paramItemForm = {\r\n        itemId: null,\r\n        groupId: null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramItemFileList = [];\r\n      this.resetForm(\"paramItemForm\");\r\n    },\r\n\r\n    /** 删除参数明细 */\r\n    handleDeleteParamItem(row) {\r\n      const itemIds = row.itemId;\r\n      this.$modal.confirm('是否确认删除参数\"' + row.paramName + '\"？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数明细 */\r\n    handleBatchDeleteParamItem() {\r\n      const itemIds = this.paramItemIds;\r\n      this.$modal.confirm('是否确认删除选中的' + itemIds.length + '条参数明细数据？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出参数明细 */\r\n    handleExportParamItem() {\r\n      this.download('material/processParamItem/export', {\r\n        ...this.paramItemQueryParams\r\n      }, `param_item_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 材料附件上传成功 */\r\n    handleMaterialUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 材料附件移除 */\r\n    handleMaterialFileRemove(file, fileList) {\r\n      console.log('材料附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组，并且正确处理空数组的情况\r\n      if (Array.isArray(fileList)) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.materialFileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 材料附件上传前检查 */\r\n    beforeMaterialUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数组附件上传成功 */\r\n    handleParamGroupUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramGroupFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数组附件移除 */\r\n    handleParamGroupFileRemove(file, fileList) {\r\n      this.paramGroupFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数组附件上传前检查 */\r\n    beforeParamGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数明细附件上传成功 */\r\n    handleParamItemUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramItemFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数明细附件移除 */\r\n    handleParamItemFileRemove(file, fileList) {\r\n      this.paramItemFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数明细附件上传前检查 */\r\n    beforeParamItemUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          // 如果是逗号分隔的URL字符串，转换为对象数组\r\n          if (attachments.includes(',') || (attachments && !attachments.startsWith('['))) {\r\n            this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: url.trim()\r\n              };\r\n            });\r\n          } else {\r\n            // 尝试解析JSON格式\r\n            this.attachmentList = JSON.parse(attachments || '[]');\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments;\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      } catch (e) {\r\n        // 如果JSON解析失败，尝试作为逗号分隔的字符串处理\r\n        if (typeof attachments === 'string' && attachments.trim()) {\r\n          this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n            const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: url.trim()\r\n            };\r\n          });\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      }\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '0 B';\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.material-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-complete-btn {\r\n  background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.export-complete-btn:hover {\r\n  background: linear-gradient(135deg, #feb47b 0%, #ff7e5f 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.material-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.material-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.material-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.supplier-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.supplier-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.description-text {\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"]}]}