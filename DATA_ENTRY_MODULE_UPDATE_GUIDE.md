# 数据录入模块更新指南

## 概述

本指南详细说明了如何更新数据录入模块以适配新的测试方案结构，并实现测试方案参数的关联显示和筛选优化。

## 更新内容

### 1. 数据库结构调整

#### 1.1 test_results表结构更新
- 将 `test_plan_id` 字段替换为 `plan_group_id` 和 `test_param_id`
- 支持新的测试方案组和测试参数明细关联

#### 1.2 执行数据库更新脚本
```sql
-- 执行表结构更新脚本
source sql/update_test_results_table.sql;
```

### 2. 后端代码更新

#### 2.1 实体类更新
**文件：** `TestResult.java`
- 添加 `planGroupId` 和 `testParamId` 字段
- 移除或保留 `testPlanId` 字段（根据需要）
- 更新相应的getter/setter方法

#### 2.2 Mapper层更新
**文件：** `TestResultMapper.xml`
- 更新SQL查询，关联新的测试方案组和测试参数表
- 修改查询条件，支持新的字段筛选
- 更新插入和更新语句

#### 2.3 Service层更新
**文件：** `ITestResultService.java` 和 `TestResultServiceImpl.java`
- 添加新的选项查询方法
- 支持根据测试方案组ID获取测试参数选项

#### 2.4 Controller层更新
**文件：** `TestResultController.java`
- 添加新的API接口
- 支持测试方案组和测试参数的选项查询

### 3. 前端代码更新

#### 3.1 API接口更新
**文件：** `testResult.js`
- 添加根据测试方案组ID获取测试参数选项的接口

#### 3.2 Vue组件更新
**文件：** `ruoyi-ui/src/views/material/testResult/index.vue`

**主要更新内容：**
1. **查询条件调整**
   - 将"测试方案"改为"测试方案组"
   - 添加"测试参数"筛选条件

2. **表格列调整**
   - 添加"测试参数"列显示
   - 更新列设置选项

3. **表单字段调整**
   - 测试方案选择改为测试方案组和测试参数的级联选择
   - 添加表单验证规则

4. **详情信息增强**
   - 在参数详情中添加测试方案参数信息显示
   - 包含方案编号、性能类型、测试设备、参数名称等

5. **搜索建议优化**
   - 支持测试方案组和测试参数的自动完成
   - 级联更新搜索选项

### 4. 测试方案配置优化

#### 4.1 参数筛选优化
**文件：** `ruoyi-ui/src/views/material/testPlan/index.vue`

**优化内容：**
1. **智能筛选**
   - 参数名称筛选只显示当前选中方案组下的参数
   - 参数单位筛选只显示当前选中方案组下的单位

2. **动态更新**
   - 切换测试方案组时自动清空筛选条件
   - 实时更新筛选选项

3. **用户体验**
   - 未选择方案组时显示全部选项
   - 选择方案组后只显示相关选项

## 功能特性

### 1. 级联选择
- **测试方案组选择** → **测试参数选择**
- 选择测试方案组后，测试参数选项自动更新
- 支持搜索和自动完成

### 2. 详情信息展示
- **材料参数明细**：显示材料相关的参数信息
- **测试方案参数**：显示测试方案的详细参数信息
- 包含方案编号、性能类型、测试设备、参数数值等

### 3. 智能筛选
- **上下文相关**：筛选选项根据当前选择动态调整
- **精确匹配**：只显示相关的参数选项
- **用户友好**：避免无关选项干扰

### 4. 数据完整性
- **关联验证**：确保测试方案组和测试参数的正确关联
- **数据一致性**：保持前后端数据结构一致
- **向后兼容**：支持现有数据的平滑迁移

## 实施步骤

### 步骤1：数据库更新
```sql
-- 1. 备份现有数据
CREATE TABLE test_results_backup AS SELECT * FROM test_results;

-- 2. 执行结构更新
source sql/update_test_results_table.sql;

-- 3. 验证更新结果
DESCRIBE test_results;
```

### 步骤2：后端部署
1. 更新实体类、Mapper、Service、Controller
2. 重启后端服务
3. 验证API接口正常工作

### 步骤3：前端部署
1. 更新Vue组件和API接口文件
2. 清理浏览器缓存
3. 验证界面功能正常

### 步骤4：功能测试
1. **数据录入测试**
   - 测试测试方案组选择
   - 测试测试参数级联选择
   - 验证数据保存和查询

2. **详情显示测试**
   - 验证材料参数明细显示
   - 验证测试方案参数信息显示

3. **筛选功能测试**
   - 测试测试方案配置中的参数筛选
   - 验证筛选选项的动态更新

## 注意事项

### 1. 数据迁移
- 执行数据库更新前务必备份数据
- 根据实际数据映射关系调整迁移脚本
- 验证迁移后的数据完整性

### 2. 权限配置
确保用户具有以下权限：
- 测试方案组的查看权限
- 测试参数明细的查看权限
- 数据录入的相关权限

### 3. 性能考虑
- 级联查询可能影响性能，建议添加适当的索引
- 考虑对常用选项进行缓存
- 监控API响应时间

### 4. 用户培训
- 培训用户使用新的级联选择功能
- 说明测试方案参数信息的含义
- 指导用户使用优化后的筛选功能

## 故障排除

### 常见问题1：级联选择不工作
**可能原因：**
- 测试方案组API返回数据格式不正确
- 前端组件事件绑定问题

**解决方法：**
1. 检查API返回数据格式
2. 验证前端事件处理方法
3. 查看浏览器控制台错误信息

### 常见问题2：详情信息不显示
**可能原因：**
- 数据库关联查询失败
- 前端数据绑定问题

**解决方法：**
1. 检查数据库表关联关系
2. 验证后端SQL查询
3. 检查前端数据绑定逻辑

### 常见问题3：筛选选项不更新
**可能原因：**
- 事件触发时机问题
- 数据更新逻辑错误

**解决方法：**
1. 检查事件触发顺序
2. 验证数据更新方法
3. 使用$nextTick确保DOM更新

## 总结

通过本次更新，数据录入模块已成功适配新的测试方案结构，实现了：
- ✅ 测试方案组和测试参数的级联选择
- ✅ 测试方案参数信息的详细显示
- ✅ 智能化的参数筛选功能
- ✅ 良好的用户体验和性能表现

更新后的系统提供了更加精确和用户友好的数据录入体验，同时保持了数据的完整性和一致性。
