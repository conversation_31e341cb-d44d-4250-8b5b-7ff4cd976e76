package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 测试参数明细对象 test_param_item
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class TestParamItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试参数ID */
    private Long testParamId;

    /** 所属测试方案组ID */
    @Excel(name = "所属测试方案组ID")
    private Long planGroupId;

    /** 测试参数名称 */
    @Excel(name = "测试参数名称")
    private String paramName;

    /** 测试参数数值 */
    @Excel(name = "测试参数数值")
    private String paramValue;

    /** 参数单位 */
    @Excel(name = "参数单位")
    private String unit;

    /** 附件URL */
    private String attachments;

    /** 方案编号（关联显示用） */
    @Excel(name = "方案编号")
    private String planCode;

    /** 性能类型（关联显示用） */
    @Excel(name = "性能类型")
    private String performanceType;

    public void setTestParamId(Long testParamId) 
    {
        this.testParamId = testParamId;
    }

    public Long getTestParamId() 
    {
        return testParamId;
    }
    public void setPlanGroupId(Long planGroupId) 
    {
        this.planGroupId = planGroupId;
    }

    public Long getPlanGroupId() 
    {
        return planGroupId;
    }
    public void setParamName(String paramName) 
    {
        this.paramName = paramName;
    }

    public String getParamName() 
    {
        return paramName;
    }
    public void setParamValue(String paramValue) 
    {
        this.paramValue = paramValue;
    }

    public String getParamValue() 
    {
        return paramValue;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }

    public String getPlanCode() 
    {
        return planCode;
    }

    public void setPlanCode(String planCode) 
    {
        this.planCode = planCode;
    }

    public String getPerformanceType() 
    {
        return performanceType;
    }

    public void setPerformanceType(String performanceType) 
    {
        this.performanceType = performanceType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("testParamId", getTestParamId())
            .append("planGroupId", getPlanGroupId())
            .append("paramName", getParamName())
            .append("paramValue", getParamValue())
            .append("unit", getUnit())
            .append("attachments", getAttachments())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("planCode", getPlanCode())
            .append("performanceType", getPerformanceType())
            .toString();
    }
}
