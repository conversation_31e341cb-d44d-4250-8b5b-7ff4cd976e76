<template>
  <div class="app-container">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-setting"></i>
        <span>材料及工艺参数配置</span>
      </div>
      <div class="page-description">
        <p>📋 管理材料信息、工艺参数组和参数明细的三层级联配置系统</p>
        <el-alert
          title="使用提示：点击材料行查看工艺参数组，点击参数组行查看参数明细"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px;">
        </el-alert>
      </div>
    </div>

    <!-- 材料信息表格 -->
    <el-card class="material-card enhanced-card" style="margin-bottom: 20px;">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-box"></i>
          <span class="header-title">材料信息管理</span>
          <el-badge :value="materialTotal" class="item-count-badge" type="primary" />
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddMaterial">
            <span>新增材料</span>
          </el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="materialMultiple" @click="handleBatchDeleteMaterial">
            <span>批量删除</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="handleExportMaterial">
            <span>导出</span>
          </el-button>
          <el-button type="warning" icon="el-icon-download" size="small" @click="handleExportComplete" class="export-complete-btn">
            <span>整体导出</span>
          </el-button>
        </div>
      </div>

      <!-- 材料查询条件 -->
      <div class="search-section">
        <el-form :model="materialQueryParams" ref="materialQueryForm" size="small" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="材料名称" prop="materialName">
            <el-autocomplete
              v-model="materialQueryParams.materialName"
              :fetch-suggestions="queryMaterialNameSuggestions"
              placeholder="请输入材料名称"
              clearable
              style="width: 220px;"
              @select="handleMaterialNameSelect"
              @focus="handleMaterialNameFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-search"
            />
          </el-form-item>
          <el-form-item label="供应商" prop="supplierName">
            <el-autocomplete
              v-model="materialQueryParams.supplierName"
              :fetch-suggestions="querySupplierSuggestions"
              placeholder="请输入供应商名称"
              clearable
              style="width: 220px;"
              @focus="handleSupplierFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-office-building"
            />
          </el-form-item>
          <el-form-item label="材料型号" prop="materialModel">
            <el-autocomplete
              v-model="materialQueryParams.materialModel"
              :fetch-suggestions="queryMaterialModelSuggestions"
              placeholder="请输入材料型号"
              clearable
              style="width: 220px;"
              @focus="handleMaterialModelFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-goods"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleMaterialQuery" class="search-btn">
              <span>搜索</span>
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetMaterialQuery" class="reset-btn">
              <span>重置</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="table-container">
        <el-table
          v-loading="materialLoading"
          :data="materialList"
          @row-click="handleMaterialClick"
          @selection-change="handleMaterialSelectionChange"
          highlight-current-row
          style="width: 100%"
          :row-class-name="getMaterialRowClassName"
          ref="materialTable"
          class="enhanced-table"
          element-loading-text="正在加载材料数据..."
          element-loading-spinner="el-icon-loading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="序号" width="60" align="center">
            <template slot-scope="scope">
              <span class="index-number">{{ scope.$index + 1 + (materialQueryParams.pageNum - 1) * materialQueryParams.pageSize }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="materialName" label="材料名称" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="material-name-cell">
                <i class="el-icon-box material-icon"></i>
                <span class="material-name">{{ scope.row.materialName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="supplierName" label="供应商" min-width="130" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="supplier-cell">
                <i class="el-icon-office-building supplier-icon"></i>
                <span>{{ scope.row.supplierName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="materialModel" label="材料型号" min-width="130" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag size="small" type="info" v-if="scope.row.materialModel">{{ scope.row.materialModel }}</el-tag>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="materialDescription" label="材料描述" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.materialDescription" class="description-text">{{ scope.row.materialDescription }}</span>
              <span v-else class="empty-data">暂无描述</span>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.createBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateBy" label="更新人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.updateBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.updateTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="附件" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.attachments"
                size="mini"
                type="text"
                @click.stop="handleViewAttachments(scope.row.attachments)"
                class="attachment-btn"
              >
                <i class="el-icon-paperclip"></i>
                查看
              </el-button>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button size="mini" type="text" @click.stop="handleEditMaterial(scope.row)" class="edit-btn">
                  <i class="el-icon-edit"></i>
                  编辑
                </el-button>
                <el-button size="mini" type="text" @click.stop="handleDeleteMaterial(scope.row)" class="delete-btn">
                  <i class="el-icon-delete"></i>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="materialTotal > 0"
        :total="materialTotal"
        :page.sync="materialQueryParams.pageNum"
        :limit.sync="materialQueryParams.pageSize"
        @pagination="getMaterialList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 工艺参数组表格 -->
    <el-card class="param-group-card enhanced-card" style="margin-bottom: 20px;" v-show="currentMaterial">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-setting"></i>
          <span class="header-title">工艺参数组</span>
          <div class="material-indicator" v-if="currentMaterial">
            <el-tag type="success" size="small">
              <i class="el-icon-box"></i>
              {{ currentMaterial.materialName }}
            </el-tag>
          </div>
          <el-badge :value="paramGroupTotal" class="item-count-badge" type="success" />
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddParamGroup" :disabled="!currentMaterial">
            <span>新增参数组</span>
          </el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="paramGroupMultiple || !currentMaterial" @click="handleBatchDeleteParamGroup">
            <span>批量删除</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="handleExportParamGroup" :disabled="!currentMaterial">
            <span>导出</span>
          </el-button>
        </div>
      </div>

      <!-- 参数组查询条件 -->
      <div class="search-section">
        <el-form :model="paramGroupQueryParams" ref="paramGroupQueryForm" size="small" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="工艺类型" prop="processType">
            <el-autocomplete
              v-model="paramGroupQueryParams.processType"
              :fetch-suggestions="queryProcessTypeSuggestions"
              placeholder="请输入工艺类型"
              clearable
              style="width: 220px;"
              @focus="handleProcessTypeFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-setting"
            />
          </el-form-item>
          <el-form-item label="参数编号" prop="paramNumber">
            <el-autocomplete
              v-model="paramGroupQueryParams.paramNumber"
              :fetch-suggestions="queryParamNumberSuggestions"
              placeholder="请输入参数编号"
              clearable
              style="width: 220px;"
              @focus="handleParamNumberFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-tickets"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleParamGroupQuery" class="search-btn">
              <span>搜索</span>
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetParamGroupQuery" class="reset-btn">
              <span>重置</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        v-loading="paramGroupLoading"
        :data="paramGroupList"
        @row-click="handleParamGroupClick"
        @selection-change="handleParamGroupSelectionChange"
        highlight-current-row
        style="width: 100%"
        :row-class-name="getParamGroupRowClassName"
        ref="paramGroupTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="processType" label="工艺类型" min-width="120" show-overflow-tooltip />
        <el-table-column prop="paramNumber" label="参数编号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="更新人" width="100" />
        <el-table-column prop="updateTime" label="更新时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.attachments" size="mini" type="text" @click.stop="handleViewAttachments(scope.row.attachments)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click.stop="handleEditParamGroup(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c" @click.stop="handleDeleteParamGroup(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="paramGroupTotal > 0"
        :total="paramGroupTotal"
        :page.sync="paramGroupQueryParams.pageNum"
        :limit.sync="paramGroupQueryParams.pageSize"
        @pagination="getParamGroupList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 参数明细表格 -->
    <el-card class="box-card" v-show="currentParamGroup">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold; font-size: 16px;">参数明细 - {{ currentParamGroup ? currentParamGroup.paramNumber : '' }}</span>
        <div style="float: right;">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddParamItem" :disabled="!currentParamGroup">新增</el-button>
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="paramItemMultiple || !currentParamGroup" @click="handleBatchDeleteParamItem">批量删除</el-button>
          <el-button type="success" icon="el-icon-download" size="mini" @click="handleExportParamItem" :disabled="!currentParamGroup">导出</el-button>
        </div>
      </div>

      <!-- 参数明细查询条件 -->
      <el-form :model="paramItemQueryParams" ref="paramItemQueryForm" size="small" :inline="true" label-width="80px" style="margin-bottom: 15px;">
        <el-form-item label="参数名称" prop="paramName">
          <el-autocomplete
            v-model="paramItemQueryParams.paramName"
            :fetch-suggestions="queryParamNameSuggestions"
            placeholder="请输入参数名称"
            clearable
            style="width: 200px;"
            @focus="handleParamNameFocus"
            :trigger-on-focus="true"
          />
        </el-form-item>
        <el-form-item label="参数单位" prop="unit">
          <el-autocomplete
            v-model="paramItemQueryParams.unit"
            :fetch-suggestions="queryUnitSuggestions"
            placeholder="请输入参数单位"
            clearable
            style="width: 200px;"
            @focus="handleUnitFocus"
            :trigger-on-focus="true"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleParamItemQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetParamItemQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="paramItemLoading"
        :data="paramItemList"
        style="width: 100%"
        @selection-change="handleParamItemSelectionChange"
        @row-click="handleParamItemRowClick"
        ref="paramItemTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="paramName" label="参数名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="paramValue" label="参数数值" width="120" align="right">
          <template slot-scope="scope">
            <span>{{ scope.row.paramValue !== null ? scope.row.paramValue : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="更新人" width="100" />
        <el-table-column prop="updateTime" label="更新时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.attachments" size="mini" type="text" @click.stop="handleViewAttachments(scope.row.attachments)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEditParamItem(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c" @click="handleDeleteParamItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="paramItemTotal > 0"
        :total="paramItemTotal"
        :page.sync="paramItemQueryParams.pageNum"
        :limit.sync="paramItemQueryParams.pageSize"
        @pagination="getParamItemList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 材料信息对话框 -->
    <el-dialog :title="materialTitle" :visible.sync="materialOpen" width="800px" append-to-body v-drag>
      <el-form ref="materialForm" :model="materialForm" :rules="materialRules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="材料名称" prop="materialName">
              <el-input v-model="materialForm.materialName" placeholder="请输入材料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="materialForm.supplierName" placeholder="请输入供应商名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="材料型号" prop="materialModel">
              <el-input v-model="materialForm.materialModel" placeholder="请输入材料型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="材料描述">
          <el-input v-model="materialForm.materialDescription" type="textarea" placeholder="请输入材料描述" :rows="3" />
        </el-form-item>
        <el-form-item label="附件上传">
          <el-upload
            ref="materialUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="materialFileList"
            :on-success="handleMaterialUploadSuccess"
            :on-remove="handleMaterialFileRemove"
            :before-upload="beforeMaterialUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持多文件上传，单个文件大小不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="materialForm.remark" type="textarea" placeholder="请输入备注" :rows="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMaterialForm">确 定</el-button>
        <el-button @click="cancelMaterial">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 工艺参数组对话框 -->
    <el-dialog :title="paramGroupTitle" :visible.sync="paramGroupOpen" width="600px" append-to-body v-drag>
      <el-form ref="paramGroupForm" :model="paramGroupForm" :rules="paramGroupRules" label-width="100px">
        <el-form-item label="工艺类型" prop="processType">
          <el-input v-model="paramGroupForm.processType" placeholder="请输入工艺类型" />
        </el-form-item>
        <el-form-item label="参数编号" prop="paramNumber">
          <el-input v-model="paramGroupForm.paramNumber" placeholder="请输入参数编号" />
        </el-form-item>
        <el-form-item label="附件上传">
          <el-upload
            ref="paramGroupUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="paramGroupFileList"
            :on-success="handleParamGroupUploadSuccess"
            :on-remove="handleParamGroupFileRemove"
            :before-upload="beforeParamGroupUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持多文件上传，单个文件大小不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="paramGroupForm.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitParamGroupForm">确 定</el-button>
        <el-button @click="cancelParamGroup">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 参数明细对话框 -->
    <el-dialog :title="paramItemTitle" :visible.sync="paramItemOpen" width="600px" append-to-body v-drag>
      <el-form ref="paramItemForm" :model="paramItemForm" :rules="paramItemRules" label-width="100px">
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="paramItemForm.paramName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数数值" prop="paramValue">
              <el-input v-model="paramItemForm.paramValue" placeholder="请输入参数数值（支持文本格式）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数单位" prop="unit">
              <el-input v-model="paramItemForm.unit" placeholder="请输入参数单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件上传">
          <el-upload
            ref="paramItemUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="paramItemFileList"
            :on-success="handleParamItemUploadSuccess"
            :on-remove="handleParamItemFileRemove"
            :before-upload="beforeParamItemUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持多文件上传，单个文件大小不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="paramItemForm.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitParamItemForm">确 定</el-button>
        <el-button @click="cancelParamItem">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog title="附件列表" :visible.sync="attachmentDialogVisible" width="600px" append-to-body v-drag>
      <el-table :data="attachmentList" style="width: 100%">
        <el-table-column prop="name" label="文件名" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="downloadAttachment(scope.row.url, scope.row.name)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial,
  exportMaterial, getMaterialOptions
} from "@/api/material/material";
import {
  listProcessParamGroup, getProcessParamGroup, delProcessParamGroup,
  addProcessParamGroup, updateProcessParamGroup, listByMaterialId,
  exportProcessParamGroup, getProcessParamGroupOptions, exportCompleteData
} from "@/api/material/processParamGroup";
import {
  listProcessParamItem, getProcessParamItem, delProcessParamItem,
  addProcessParamItem, updateProcessParamItem, listByGroupId,
  exportProcessParamItem, getProcessParamItemOptions
} from "@/api/material/processParamItem";
import { getToken } from "@/utils/auth";
import axios from "axios";

export default {
  name: "MaterialConfig",
  directives: {
    // 拖拽指令
    drag: {
      bind(el) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        const dragDom = el.querySelector('.el-dialog');
        dialogHeaderEl.style.cursor = 'move';

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);

        dialogHeaderEl.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft;
          const disY = e.clientY - dialogHeaderEl.offsetTop;

          // 获取到的值带px 正则匹配替换
          let styL, styT;

          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
          if (sty.left.includes('%')) {
            styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100);
            styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100);
          } else {
            styL = +sty.left.replace(/px/g, '');
            styT = +sty.top.replace(/px/g, '');
          }

          document.onmousemove = function (e) {
            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX;
            const t = e.clientY - disY;

            // 移动当前元素
            dragDom.style.left = `${l + styL}px`;
            dragDom.style.top = `${t + styT}px`;

            // 将此时的位置传出去
            // binding.value({x:e.pageX,y:e.pageY})
          };

          document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;
          };
        }
      }
    }
  },
  data() {
    return {
      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },

      // 材料相关
      materialLoading: true,
      materialList: [],
      materialTotal: 0,
      currentMaterial: null,
      materialOpen: false,
      materialTitle: "",
      materialForm: {},
      materialFileList: [],
      materialIds: [],
      materialSingle: true,
      materialMultiple: true,
      materialQueryParams: {
        pageNum: 1,
        pageSize: 10,
        materialName: null,
        supplierName: null,
        materialModel: null
      },
      materialRules: {
        materialName: [
          { required: true, message: "材料名称不能为空", trigger: "blur" }
        ]
      },

      // 工艺参数组相关
      paramGroupLoading: false,
      paramGroupList: [],
      paramGroupTotal: 0,
      currentParamGroup: null,
      paramGroupOpen: false,
      paramGroupTitle: "",
      paramGroupForm: {},
      paramGroupFileList: [],
      paramGroupIds: [],
      paramGroupSingle: true,
      paramGroupMultiple: true,
      paramGroupQueryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        processType: null,
        paramNumber: null
      },
      paramGroupRules: {
        processType: [
          { required: true, message: "工艺类型不能为空", trigger: "blur" }
        ],
        paramNumber: [
          { required: true, message: "参数编号不能为空", trigger: "blur" }
        ]
      },

      // 参数明细相关
      paramItemLoading: false,
      paramItemList: [],
      paramItemTotal: 0,
      paramItemOpen: false,
      paramItemTitle: "",
      paramItemForm: {},
      paramItemFileList: [],
      paramItemIds: [],
      paramItemSingle: true,
      paramItemMultiple: true,
      paramItemQueryParams: {
        pageNum: 1,
        pageSize: 10,
        groupId: null,
        paramName: null,
        unit: null
      },
      paramItemRules: {
        paramName: [
          { required: true, message: "参数名称不能为空", trigger: "blur" }
        ]
      },

      // 附件查看
      attachmentDialogVisible: false,
      attachmentList: [],

      // 搜索建议数据
      materialNameSuggestions: [],
      supplierSuggestions: [],
      materialModelSuggestions: [],
      processTypeSuggestions: [],
      paramNameSuggestions: [],
      paramNumberSuggestions: [],
      unitSuggestions: []
    };
  },
  created() {
    this.getMaterialList();
    this.loadSuggestions();
  },
  methods: {
    /** 加载搜索建议数据 */
    loadSuggestions() {
      // 获取材料名称建议
      getMaterialOptions({ type: 'materialName' }).then(response => {
        this.materialNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取供应商建议
      getMaterialOptions({ type: 'supplierName' }).then(response => {
        this.supplierSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取材料型号建议
      getMaterialOptions({ type: 'materialModel' }).then(response => {
        this.materialModelSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取工艺类型建议
      getProcessParamGroupOptions({ type: 'processType' }).then(response => {
        this.processTypeSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取参数编号建议
      getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {
        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取参数名称建议
      getProcessParamItemOptions({ type: 'paramName' }).then(response => {
        this.paramNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取参数单位建议
      getProcessParamItemOptions({ type: 'unit' }).then(response => {
        this.unitSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 材料名称搜索建议 */
    queryMaterialNameSuggestions(queryString, cb) {
      let suggestions = this.materialNameSuggestions;
      if (queryString) {
        suggestions = this.materialNameSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 供应商搜索建议 */
    querySupplierSuggestions(queryString, cb) {
      let suggestions = this.supplierSuggestions;
      if (queryString) {
        suggestions = this.supplierSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 材料型号搜索建议 */
    queryMaterialModelSuggestions(queryString, cb) {
      let suggestions = this.materialModelSuggestions;
      if (queryString) {
        suggestions = this.materialModelSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 工艺类型搜索建议 */
    queryProcessTypeSuggestions(queryString, cb) {
      let suggestions = this.processTypeSuggestions;
      if (queryString) {
        suggestions = this.processTypeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 参数名称搜索建议 */
    /** 参数名称搜索建议 */
    queryParamNameSuggestions(queryString, cb) {
      let suggestions = this.paramNameSuggestions;
      if (queryString) {
        suggestions = this.paramNameSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 参数编号搜索建议 */
    queryParamNumberSuggestions(queryString, cb) {
      let suggestions = this.paramNumberSuggestions;
      if (queryString) {
        suggestions = this.paramNumberSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 参数单位搜索建议 */
    queryUnitSuggestions(queryString, cb) {
      let suggestions = this.unitSuggestions;
      if (queryString) {
        suggestions = this.unitSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 材料名称选择事件 */
    handleMaterialNameSelect(item) {
      this.materialQueryParams.materialName = item.value;
      // 移除自动搜索，让用户手动点击搜索按钮
    },

    /** 材料名称焦点事件 */
    handleMaterialNameFocus() {
      // 重新加载材料名称建议
      getMaterialOptions({ type: 'materialName' }).then(response => {
        this.materialNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 供应商焦点事件 */
    handleSupplierFocus() {
      // 重新加载供应商建议
      getMaterialOptions({ type: 'supplierName' }).then(response => {
        this.supplierSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 材料型号焦点事件 */
    handleMaterialModelFocus() {
      // 重新加载材料型号建议
      getMaterialOptions({ type: 'materialModel' }).then(response => {
        this.materialModelSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 工艺类型焦点事件 */
    handleProcessTypeFocus() {
      // 基于当前选中的材料加载工艺类型建议
      if (this.currentMaterial) {
        // 从当前材料的参数组中获取工艺类型选项
        const processTypes = [...new Set(this.paramGroupList.map(item => item.processType).filter(Boolean))];
        this.processTypeSuggestions = processTypes.map(item => ({ value: item }));
      } else {
        // 如果没有选中材料，加载所有工艺类型
        getProcessParamGroupOptions({ type: 'processType' }).then(response => {
          this.processTypeSuggestions = response.data.map(item => ({ value: item }));
        }).catch(() => {});
      }
    },

    /** 参数编号焦点事件 */
    handleParamNumberFocus() {
      // 基于当前选中的材料加载参数编号建议
      if (this.currentMaterial) {
        // 从当前材料的参数组中获取参数编号选项
        const paramNumbers = [...new Set(this.paramGroupList.map(item => item.paramNumber).filter(Boolean))];
        this.paramNumberSuggestions = paramNumbers.map(item => ({ value: item }));
      } else {
        // 如果没有选中材料，加载所有参数编号
        getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {
          this.paramNumberSuggestions = response.data.map(item => ({ value: item }));
        }).catch(() => {});
      }
    },

    /** 参数名称焦点事件 */
    handleParamNameFocus() {
      // 基于当前选中的参数组加载参数名称建议
      if (this.currentParamGroup) {
        // 从当前参数组的参数明细中获取参数名称选项
        const paramNames = [...new Set(this.paramItemList.map(item => item.paramName).filter(Boolean))];
        this.paramNameSuggestions = paramNames.map(item => ({ value: item }));
      } else {
        // 如果没有选中参数组，加载所有参数名称
        getProcessParamItemOptions({ type: 'paramName' }).then(response => {
          this.paramNameSuggestions = response.data.map(item => ({ value: item }));
        }).catch(() => {});
      }
    },

    /** 参数单位焦点事件 */
    handleUnitFocus() {
      // 基于当前选中的参数组加载参数单位建议
      if (this.currentParamGroup) {
        // 从当前参数组的参数明细中获取参数单位选项
        const units = [...new Set(this.paramItemList.map(item => item.unit).filter(Boolean))];
        this.unitSuggestions = units.map(item => ({ value: item }));
      } else {
        // 如果没有选中参数组，加载所有参数单位
        getProcessParamItemOptions({ type: 'unit' }).then(response => {
          this.unitSuggestions = response.data.map(item => ({ value: item }));
        }).catch(() => {});
      }
    },

    /** 解析附件数据 */
    parseAttachments(attachments) {
      if (!attachments) {
        return [];
      }

      // 如果已经是数组，直接返回
      if (Array.isArray(attachments)) {
        return attachments;
      }

      // 如果是字符串，按逗号分割并转换为文件对象
      if (typeof attachments === 'string') {
        return attachments.split(',').filter(url => url.trim()).map((url, index) => {
          const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;
          return {
            name: fileName,
            url: url.trim(),
            uid: Date.now() + index,
            status: 'success'
          };
        });
      }

      return [];
    },

    /** 查询材料列表 */
    getMaterialList() {
      this.materialLoading = true;
      listMaterial(this.materialQueryParams).then(response => {
        this.materialList = response.rows;
        this.materialTotal = response.total;
        this.materialLoading = false;
      });
    },

    /** 材料查询 */
    handleMaterialQuery() {
      this.materialQueryParams.pageNum = 1;
      this.getMaterialList();
    },

    /** 重置材料查询 */
    resetMaterialQuery() {
      this.resetForm("materialQueryForm");
      this.handleMaterialQuery();
    },

    /** 材料行点击事件 */
    handleMaterialClick(row) {
      this.currentMaterial = row;
      this.paramGroupQueryParams.materialId = row.materialId;
      this.getParamGroupList();
      this.paramItemList = [];
      this.paramItemTotal = 0;
      this.currentParamGroup = null;
      // 同时选中该行
      this.$refs.materialTable.toggleRowSelection(row);
    },

    /** 材料选择变化事件 */
    handleMaterialSelectionChange(selection) {
      this.materialIds = selection.map(item => item.materialId);
      this.materialSingle = selection.length !== 1;
      this.materialMultiple = !selection.length;
    },

    /** 材料行样式 */
    getMaterialRowClassName({row, rowIndex}) {
      if (this.currentMaterial && row.materialId === this.currentMaterial.materialId) {
        return 'current-row';
      }
      return '';
    },

    /** 查询工艺参数组列表 */
    getParamGroupList() {
      if (!this.currentMaterial) return;
      this.paramGroupLoading = true;
      listProcessParamGroup(this.paramGroupQueryParams).then(response => {
        this.paramGroupList = response.rows;
        this.paramGroupTotal = response.total;
        this.paramGroupLoading = false;
      });
    },

    /** 参数组查询 */
    handleParamGroupQuery() {
      this.paramGroupQueryParams.pageNum = 1;
      this.getParamGroupList();
    },

    /** 重置参数组查询 */
    resetParamGroupQuery() {
      this.resetForm("paramGroupQueryForm");
      this.handleParamGroupQuery();
    },

    /** 工艺参数组行点击事件 */
    handleParamGroupClick(row) {
      this.currentParamGroup = row;
      this.paramItemQueryParams.groupId = row.groupId;
      this.getParamItemList();
      // 同时选中该行
      this.$refs.paramGroupTable.toggleRowSelection(row);
    },

    /** 参数组选择变化事件 */
    handleParamGroupSelectionChange(selection) {
      this.paramGroupIds = selection.map(item => item.groupId);
      this.paramGroupSingle = selection.length !== 1;
      this.paramGroupMultiple = !selection.length;
    },

    /** 参数明细行点击事件 */
    handleParamItemRowClick(row) {
      this.$refs.paramItemTable.toggleRowSelection(row);
    },

    /** 参数明细选择变化事件 */
    handleParamItemSelectionChange(selection) {
      this.paramItemIds = selection.map(item => item.itemId);
      this.paramItemSingle = selection.length !== 1;
      this.paramItemMultiple = !selection.length;
    },

    /** 参数组行样式 */
    getParamGroupRowClassName({row, rowIndex}) {
      if (this.currentParamGroup && row.groupId === this.currentParamGroup.groupId) {
        return 'current-row';
      }
      return '';
    },

    /** 查询参数明细列表 */
    getParamItemList() {
      if (!this.currentParamGroup) return;
      this.paramItemLoading = true;
      listProcessParamItem(this.paramItemQueryParams).then(response => {
        this.paramItemList = response.rows;
        this.paramItemTotal = response.total;
        this.paramItemLoading = false;
      });
    },

    /** 参数明细查询 */
    handleParamItemQuery() {
      this.paramItemQueryParams.pageNum = 1;
      this.getParamItemList();
    },

    /** 重置参数明细查询 */
    resetParamItemQuery() {
      this.resetForm("paramItemQueryForm");
      this.handleParamItemQuery();
    },

    /** 新增材料 */
    handleAddMaterial() {
      this.resetMaterialForm();
      this.materialOpen = true;
      this.materialTitle = "添加材料信息";
    },

    /** 修改材料 */
    handleEditMaterial(row) {
      this.resetMaterialForm();
      const materialId = row.materialId;
      getMaterial(materialId).then(response => {
        this.materialForm = response.data;
        // 处理附件数据
        this.materialFileList = this.parseAttachments(response.data.attachments);
        this.materialOpen = true;
        this.materialTitle = "修改材料信息";
      });
    },

    /** 提交材料表单 */
    submitMaterialForm() {
      this.$refs["materialForm"].validate(valid => {
        if (valid) {
          // 将文件列表转换为逗号分隔的URL字符串
          this.materialForm.attachments = this.materialFileList.length > 0
            ? this.materialFileList.map(file => file.url).join(',')
            : null;

          // 设置创建人和更新人
          if (this.materialForm.materialId != null) {
            // 更新操作，设置更新人
            this.materialForm.updateBy = this.$store.state.user.name;
          } else {
            // 新增操作，设置创建人
            this.materialForm.createBy = this.$store.state.user.name;
          }

          if (this.materialForm.materialId != null) {
            updateMaterial(this.materialForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.materialOpen = false;
              this.getMaterialList();
            });
          } else {
            addMaterial(this.materialForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.materialOpen = false;
              this.getMaterialList();
            });
          }
        }
      });
    },

    /** 取消材料操作 */
    cancelMaterial() {
      this.materialOpen = false;
      this.resetMaterialForm();
    },

    /** 重置材料表单 */
    resetMaterialForm() {
      this.materialForm = {
        materialId: null,
        materialName: null,
        supplierName: null,
        materialModel: null,
        materialDescription: null,
        attachments: null,
        remark: null
      };
      this.materialFileList = [];
      this.resetForm("materialForm");
    },

    /** 删除材料 */
    handleDeleteMaterial(row) {
      const materialIds = row.materialId;
      this.$modal.confirm('是否确认删除材料"' + row.materialName + '"？').then(function() {
        return delMaterial(materialIds);
      }).then(() => {
        this.getMaterialList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 批量删除材料 */
    handleBatchDeleteMaterial() {
      const materialIds = this.materialIds;
      this.$modal.confirm('是否确认删除选中的' + materialIds.length + '条材料数据？').then(function() {
        return delMaterial(materialIds);
      }).then(() => {
        this.getMaterialList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出材料 */
    handleExportMaterial() {
      this.download('material/material/export', {
        ...this.materialQueryParams
      }, `material_${new Date().getTime()}.xlsx`);
    },

    /** 整体导出 */
    handleExportComplete() {
      this.$modal.loading("正在导出数据，请稍候...");
      this.download('material/material/exportComplete', {
        ...this.materialQueryParams
      }, `complete_data_${new Date().getTime()}.xlsx`).then(() => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("导出成功");
      }).catch(error => {
        this.$modal.closeLoading();
        this.$modal.msgError("导出失败: " + (error.message || "未知错误"));
      });
    },

    /** 新增工艺参数组 */
    handleAddParamGroup() {
      this.resetParamGroupForm();
      this.paramGroupForm.materialId = this.currentMaterial.materialId;
      this.paramGroupOpen = true;
      this.paramGroupTitle = "添加工艺参数组";
    },

    /** 修改工艺参数组 */
    handleEditParamGroup(row) {
      this.resetParamGroupForm();
      const groupId = row.groupId;
      getProcessParamGroup(groupId).then(response => {
        this.paramGroupForm = response.data;
        // 处理附件数据
        this.paramGroupFileList = this.parseAttachments(response.data.attachments);
        this.paramGroupOpen = true;
        this.paramGroupTitle = "修改工艺参数组";
      });
    },

    /** 提交工艺参数组表单 */
    submitParamGroupForm() {
      this.$refs["paramGroupForm"].validate(valid => {
        if (valid) {
          // 将文件列表转换为逗号分隔的URL字符串
          this.paramGroupForm.attachments = this.paramGroupFileList.length > 0
            ? this.paramGroupFileList.map(file => file.url).join(',')
            : null;

          // 设置创建人和更新人
          if (this.paramGroupForm.groupId != null) {
            // 更新操作，设置更新人
            this.paramGroupForm.updateBy = this.$store.state.user.name;
          } else {
            // 新增操作，设置创建人
            this.paramGroupForm.createBy = this.$store.state.user.name;
          }

          if (this.paramGroupForm.groupId != null) {
            updateProcessParamGroup(this.paramGroupForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.paramGroupOpen = false;
              this.getParamGroupList();
            });
          } else {
            addProcessParamGroup(this.paramGroupForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.paramGroupOpen = false;
              this.getParamGroupList();
            });
          }
        }
      });
    },

    /** 取消工艺参数组操作 */
    cancelParamGroup() {
      this.paramGroupOpen = false;
      this.resetParamGroupForm();
    },

    /** 重置工艺参数组表单 */
    resetParamGroupForm() {
      this.paramGroupForm = {
        groupId: null,
        materialId: null,
        processType: null,
        paramNumber: null,
        attachments: null,
        remark: null
      };
      this.paramGroupFileList = [];
      this.resetForm("paramGroupForm");
    },

    /** 删除工艺参数组 */
    handleDeleteParamGroup(row) {
      const groupIds = row.groupId;
      this.$modal.confirm('是否确认删除参数组"' + row.paramNumber + '"？').then(function() {
        return delProcessParamGroup(groupIds);
      }).then(() => {
        this.getParamGroupList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 批量删除参数组 */
    handleBatchDeleteParamGroup() {
      const groupIds = this.paramGroupIds;
      this.$modal.confirm('是否确认删除选中的' + groupIds.length + '条参数组数据？').then(function() {
        return delProcessParamGroup(groupIds);
      }).then(() => {
        this.getParamGroupList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出工艺参数组 */
    handleExportParamGroup() {
      this.download('material/processParamGroup/export', {
        ...this.paramGroupQueryParams
      }, `param_group_${new Date().getTime()}.xlsx`);
    },

    /** 新增参数明细 */
    handleAddParamItem() {
      this.resetParamItemForm();
      this.paramItemForm.groupId = this.currentParamGroup.groupId;
      this.paramItemOpen = true;
      this.paramItemTitle = "添加参数明细";
    },

    /** 修改参数明细 */
    handleEditParamItem(row) {
      this.resetParamItemForm();
      const itemId = row.itemId;
      getProcessParamItem(itemId).then(response => {
        this.paramItemForm = response.data;
        // 处理附件数据
        this.paramItemFileList = this.parseAttachments(response.data.attachments);
        this.paramItemOpen = true;
        this.paramItemTitle = "修改参数明细";
      });
    },

    /** 提交参数明细表单 */
    submitParamItemForm() {
      this.$refs["paramItemForm"].validate(valid => {
        if (valid) {
          // 将文件列表转换为逗号分隔的URL字符串
          this.paramItemForm.attachments = this.paramItemFileList.length > 0
            ? this.paramItemFileList.map(file => file.url).join(',')
            : null;

          // 设置创建人和更新人
          if (this.paramItemForm.itemId != null) {
            // 更新操作，设置更新人
            this.paramItemForm.updateBy = this.$store.state.user.name;
          } else {
            // 新增操作，设置创建人
            this.paramItemForm.createBy = this.$store.state.user.name;
          }

          if (this.paramItemForm.itemId != null) {
            updateProcessParamItem(this.paramItemForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.paramItemOpen = false;
              this.getParamItemList();
            });
          } else {
            addProcessParamItem(this.paramItemForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.paramItemOpen = false;
              this.getParamItemList();
            });
          }
        }
      });
    },

    /** 取消参数明细操作 */
    cancelParamItem() {
      this.paramItemOpen = false;
      this.resetParamItemForm();
    },

    /** 重置参数明细表单 */
    resetParamItemForm() {
      this.paramItemForm = {
        itemId: null,
        groupId: null,
        paramName: null,
        paramValue: null,
        unit: null,
        attachments: null,
        remark: null
      };
      this.paramItemFileList = [];
      this.resetForm("paramItemForm");
    },

    /** 删除参数明细 */
    handleDeleteParamItem(row) {
      const itemIds = row.itemId;
      this.$modal.confirm('是否确认删除参数"' + row.paramName + '"？').then(function() {
        return delProcessParamItem(itemIds);
      }).then(() => {
        this.getParamItemList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 批量删除参数明细 */
    handleBatchDeleteParamItem() {
      const itemIds = this.paramItemIds;
      this.$modal.confirm('是否确认删除选中的' + itemIds.length + '条参数明细数据？').then(function() {
        return delProcessParamItem(itemIds);
      }).then(() => {
        this.getParamItemList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出参数明细 */
    handleExportParamItem() {
      this.download('material/processParamItem/export', {
        ...this.paramItemQueryParams
      }, `param_item_${new Date().getTime()}.xlsx`);
    },

    /** 材料附件上传成功 */
    handleMaterialUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.materialFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: 'success'
        }));
        this.$modal.msgSuccess("上传成功");
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
    },

    /** 材料附件移除 */
    handleMaterialFileRemove(file, fileList) {
      console.log('材料附件移除回调：', { file, fileList });
      // 确保fileList是数组，并且正确处理空数组的情况
      if (Array.isArray(fileList)) {
        this.materialFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: item.status || 'success'
        }));
      } else {
        console.error('fileList不是数组：', fileList);
        this.materialFileList = [];
      }
      this.$modal.msgSuccess("附件删除成功");
    },

    /** 材料附件上传前检查 */
    beforeMaterialUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    /** 参数组附件上传成功 */
    handleParamGroupUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.paramGroupFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: 'success'
        }));
        this.$modal.msgSuccess("上传成功");
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
    },

    /** 参数组附件移除 */
    handleParamGroupFileRemove(file, fileList) {
      this.paramGroupFileList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.url : item.url,
        size: this.formatFileSize(item.size || item.raw?.size),
        uid: item.uid,
        status: item.status || 'success'
      }));
      this.$modal.msgSuccess("附件删除成功");
    },

    /** 参数组附件上传前检查 */
    beforeParamGroupUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    /** 参数明细附件上传成功 */
    handleParamItemUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.paramItemFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: 'success'
        }));
        this.$modal.msgSuccess("上传成功");
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
    },

    /** 参数明细附件移除 */
    handleParamItemFileRemove(file, fileList) {
      this.paramItemFileList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.url : item.url,
        size: this.formatFileSize(item.size || item.raw?.size),
        uid: item.uid,
        status: item.status || 'success'
      }));
      this.$modal.msgSuccess("附件删除成功");
    },

    /** 参数明细附件上传前检查 */
    beforeParamItemUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    /** 查看附件 */
    handleViewAttachments(attachments) {
      try {
        if (typeof attachments === 'string') {
          // 如果是逗号分隔的URL字符串，转换为对象数组
          if (attachments.includes(',') || (attachments && !attachments.startsWith('['))) {
            this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {
              const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;
              return {
                name: fileName,
                url: url.trim()
              };
            });
          } else {
            // 尝试解析JSON格式
            this.attachmentList = JSON.parse(attachments || '[]');
          }
        } else if (Array.isArray(attachments)) {
          this.attachmentList = attachments;
        } else {
          this.attachmentList = [];
        }
      } catch (e) {
        // 如果JSON解析失败，尝试作为逗号分隔的字符串处理
        if (typeof attachments === 'string' && attachments.trim()) {
          this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {
            const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;
            return {
              name: fileName,
              url: url.trim()
            };
          });
        } else {
          this.attachmentList = [];
        }
      }
      this.attachmentDialogVisible = true;
    },

    /** 下载附件 */
    downloadAttachment(url, name) {
      const link = document.createElement('a');
      link.href = url;
      link.download = name;
      link.click();
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '0 B';
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB';
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  margin-right: 10px;
  color: #409EFF;
  font-size: 28px;
}

.page-description p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

/* 增强卡片样式 */
.enhanced-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  background: white;
}

.enhanced-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left i {
  color: #409EFF;
  font-size: 18px;
}

.header-title {
  font-weight: bold;
  font-size: 16px;
  color: #2c3e50;
}

.material-indicator {
  margin-left: 10px;
}

.item-count-badge {
  margin-left: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.header-right .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.export-complete-btn {
  background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
  border: none;
  color: white;
}

.export-complete-btn:hover {
  background: linear-gradient(135deg, #feb47b 0%, #ff7e5f 100%);
}

/* 搜索区域样式 */
.search-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 10px;
}

.search-form .el-autocomplete {
  border-radius: 6px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
}

.reset-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 表格容器样式 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 增强表格样式 */
.enhanced-table {
  border-radius: 8px;
}

.enhanced-table .el-table__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.enhanced-table .el-table__header th {
  background: transparent;
  color: white;
  font-weight: 600;
  border-bottom: none;
}

.enhanced-table .el-table__body tr:hover > td {
  background-color: #f0f9ff !important;
}

.enhanced-table .current-row {
  background-color: #e6f7ff !important;
}

.enhanced-table .current-row:hover > td {
  background-color: #e6f7ff !important;
}

/* 表格单元格样式 */
.index-number {
  font-weight: bold;
  color: #409EFF;
}

.material-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.material-icon {
  color: #67C23A;
}

.material-name {
  font-weight: 600;
  color: #2c3e50;
}

.supplier-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.supplier-icon {
  color: #E6A23C;
}

.description-text {
  color: #606266;
  font-size: 13px;
}

.empty-data {
  color: #C0C4CC;
  font-style: italic;
}

.user-info, .time-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.user-info i, .time-info i {
  color: #909399;
}

.attachment-btn {
  color: #409EFF;
  font-weight: 500;
}

.attachment-btn:hover {
  color: #66b1ff;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-btn {
  color: #409EFF;
  font-weight: 500;
}

.edit-btn:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.delete-btn {
  color: #F56C6C;
  font-weight: 500;
}

.delete-btn:hover {
  color: #f78989;
  background-color: #fef0f0;
}

/* 通用样式 */
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: center;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    padding: 15px;
  }

  .header-right {
    flex-wrap: wrap;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 统一按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.el-button--success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  border: none !important;
}

.el-button--info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  border: none !important;
}

.el-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  border: none !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
}
</style>
