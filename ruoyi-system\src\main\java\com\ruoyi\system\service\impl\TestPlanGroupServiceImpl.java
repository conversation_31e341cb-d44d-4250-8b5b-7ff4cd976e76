package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.system.mapper.TestPlanGroupMapper;
import com.ruoyi.system.domain.TestPlanGroup;
import com.ruoyi.system.domain.TestParamItem;
import com.ruoyi.system.mapper.TestParamItemMapper;
import com.ruoyi.system.service.ITestPlanGroupService;

/**
 * 测试方案组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class TestPlanGroupServiceImpl implements ITestPlanGroupService 
{
    @Autowired
    private TestPlanGroupMapper testPlanGroupMapper;

    @Autowired
    private TestParamItemMapper testParamItemMapper;

    /**
     * 查询测试方案组
     * 
     * @param planGroupId 测试方案组主键
     * @return 测试方案组
     */
    @Override
    public TestPlanGroup selectTestPlanGroupByPlanGroupId(Long planGroupId)
    {
        return testPlanGroupMapper.selectTestPlanGroupByPlanGroupId(planGroupId);
    }

    /**
     * 查询测试方案组列表
     * 
     * @param testPlanGroup 测试方案组
     * @return 测试方案组
     */
    @Override
    public List<TestPlanGroup> selectTestPlanGroupList(TestPlanGroup testPlanGroup)
    {
        return testPlanGroupMapper.selectTestPlanGroupList(testPlanGroup);
    }

    /**
     * 新增测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    @Override
    public int insertTestPlanGroup(TestPlanGroup testPlanGroup)
    {
        testPlanGroup.setCreateTime(DateUtils.getNowDate());
        int rows = testPlanGroupMapper.insertTestPlanGroup(testPlanGroup);
        insertTestParamItem(testPlanGroup);
        return rows;
    }

    /**
     * 修改测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    @Override
    public int updateTestPlanGroup(TestPlanGroup testPlanGroup)
    {
        testPlanGroup.setUpdateTime(DateUtils.getNowDate());
        testParamItemMapper.deleteTestParamItemByPlanGroupId(testPlanGroup.getPlanGroupId());
        insertTestParamItem(testPlanGroup);
        return testPlanGroupMapper.updateTestPlanGroup(testPlanGroup);
    }

    /**
     * 批量删除测试方案组
     * 
     * @param planGroupIds 需要删除的测试方案组主键
     * @return 结果
     */
    @Override
    public int deleteTestPlanGroupByPlanGroupIds(Long[] planGroupIds)
    {
        for (Long planGroupId : planGroupIds)
        {
            testParamItemMapper.deleteTestParamItemByPlanGroupId(planGroupId);
        }
        return testPlanGroupMapper.deleteTestPlanGroupByPlanGroupIds(planGroupIds);
    }

    /**
     * 删除测试方案组信息
     * 
     * @param planGroupId 测试方案组主键
     * @return 结果
     */
    @Override
    public int deleteTestPlanGroupByPlanGroupId(Long planGroupId)
    {
        testParamItemMapper.deleteTestParamItemByPlanGroupId(planGroupId);
        return testPlanGroupMapper.deleteTestPlanGroupByPlanGroupId(planGroupId);
    }

    /**
     * 获取测试方案组选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectTestPlanGroupOptions(String type)
    {
        if ("planCode".equals(type)) {
            return testPlanGroupMapper.selectPlanCodeOptions();
        } else if ("performanceType".equals(type)) {
            return testPlanGroupMapper.selectPerformanceTypeOptions();
        } else if ("testEquipment".equals(type)) {
            return testPlanGroupMapper.selectTestEquipmentOptions();
        }
        return new ArrayList<>();
    }

    /**
     * 新增测试参数明细信息
     * 
     * @param testPlanGroup 测试方案组对象
     */
    public void insertTestParamItem(TestPlanGroup testPlanGroup)
    {
        List<TestParamItem> testParamItemList = testPlanGroup.getTestParamItemList();
        Long planGroupId = testPlanGroup.getPlanGroupId();
        if (testParamItemList != null && testParamItemList.size() > 0)
        {
            for (TestParamItem testParamItem : testParamItemList)
            {
                testParamItem.setPlanGroupId(planGroupId);
                testParamItemMapper.insertTestParamItem(testParamItem);
            }
        }
    }
}
