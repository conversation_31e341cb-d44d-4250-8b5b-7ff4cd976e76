# 基于实际数据库结构的修复指南

## 📋 当前数据库结构分析

根据您的 `material_tables.sql` 文件，我发现了以下实际结构：

### 现有表结构
1. **materials** - 材料基本信息表
2. **process_param_group** - 工艺参数组表
3. **process_param_item** - 工艺参数明细表
4. **test_plans** - 测试方案表（需要重构）
5. **test_results** - 测试结果表

### 关键发现
- 所有ID字段都是 `BIGINT(20)` 类型，不是 `INT`
- `process_param_item.param_value` 是 `DECIMAL(20,6)` 需要改为 `VARCHAR(100)`
- 外键约束名称与表结构不完全匹配

## 🔧 修复方案

### 执行步骤

#### 1. 备份数据库
```bash
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 2. 执行修复脚本
```sql
-- 在数据库中执行
source database_actual_fix.sql;
```

#### 3. 验证修复结果
```sql
-- 检查新表结构
DESCRIBE test_plan_group;
DESCRIBE test_param_item;

-- 检查数据迁移
SELECT COUNT(*) FROM test_plan_group;
SELECT COUNT(*) FROM test_param_item;
SELECT COUNT(*) FROM test_results;

-- 检查外键约束
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  REFERENCED_TABLE_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL;
```

## 📊 修复内容详解

### 1. 参数数值格式修改
```sql
-- 将参数数值从DECIMAL改为VARCHAR，支持更灵活的格式
ALTER TABLE `process_param_item` 
MODIFY COLUMN `param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）';
```

**支持的格式示例：**
- 数值：`180.5`
- 范围：`25-200`
- 特殊值：`标准值`、`待定`
- 复合值：`50x25x3`

### 2. 测试方案表重构

#### 原结构问题
```sql
-- 原test_plans表结构单一，扩展性差
CREATE TABLE `test_plans` (
  `test_plan_id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `plan_code` VARCHAR(50),
  `performance_type` VARCHAR(50),
  `performance_name` VARCHAR(100),
  `test_equipment` VARCHAR(100),
  `test_parameter` VARCHAR(100), -- 单一字段，无法支持多参数
  -- ...
);
```

#### 新的两层结构
```sql
-- 测试方案组表
CREATE TABLE `test_plan_group` (
  `plan_group_id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `plan_code` VARCHAR(50) NOT NULL,
  `performance_type` VARCHAR(50),
  `performance_name` VARCHAR(100),
  `test_equipment` VARCHAR(100),
  -- ...
);

-- 测试参数明细表
CREATE TABLE `test_param_item` (
  `test_param_id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `plan_group_id` BIGINT(20) NOT NULL,
  `param_name` VARCHAR(100) NOT NULL,
  `param_value` VARCHAR(100),
  `unit` VARCHAR(20),
  -- ...
);
```

### 3. 数据迁移策略

#### 自动迁移
- **基本信息**：从 `test_plans` 迁移到 `test_plan_group`
- **测试参数**：将 `test_parameter` 字段拆分为 `test_param_item` 记录
- **智能补充**：根据性能类型自动添加常用测试参数

#### 迁移示例
```sql
-- 原数据
test_plans: {
  test_plan_id: 1,
  plan_code: 'TP001',
  performance_type: '机械性能',
  performance_name: '拉伸强度测试',
  test_parameter: '拉伸速度: 2mm/min'
}

-- 迁移后
test_plan_group: {
  plan_group_id: 1,
  plan_code: 'TP001',
  performance_type: '机械性能',
  performance_name: '拉伸强度测试'
}

test_param_item: [
  {plan_group_id: 1, param_name: '测试参数', param_value: '拉伸速度: 2mm/min'},
  {plan_group_id: 1, param_name: '试样长度', param_value: '250', unit: 'mm'},
  {plan_group_id: 1, param_name: '试样宽度', param_value: '25', unit: 'mm'}
]
```

### 4. 外键约束重建

#### 完整的外键关系
```sql
-- 工艺参数组 → 材料
process_param_group.material_id → materials.material_id

-- 工艺参数明细 → 工艺参数组
process_param_item.group_id → process_param_group.group_id

-- 测试参数明细 → 测试方案组
test_param_item.plan_group_id → test_plan_group.plan_group_id

-- 测试结果 → 测试方案组
test_results.plan_group_id → test_plan_group.plan_group_id

-- 测试结果 → 工艺参数组
test_results.group_id → process_param_group.group_id
```

## 🎯 修复后的优势

### 1. 数据一致性
- 所有ID字段统一使用 `BIGINT(20)` 类型
- 参数数值支持灵活的字符串格式
- 完整的外键约束确保数据完整性

### 2. 功能扩展性
- 一个测试方案可以包含多个测试参数
- 每个参数可以独立管理（名称、数值、单位、附件、备注）
- 支持参数的增删改查操作

### 3. 用户体验
- 与工艺参数管理保持一致的操作方式
- 上下分层显示：方案组 + 参数明细
- 点击联动：选择方案组显示对应参数

### 4. 系统架构
- 统一的数据结构设计
- 清晰的业务逻辑分层
- 便于后续功能扩展

## ⚠️ 注意事项

### 执行前检查
1. **数据备份**：务必备份整个数据库
2. **应用停止**：停止所有访问数据库的应用
3. **权限确认**：确保有足够的数据库操作权限

### 执行后验证
1. **数据完整性**：检查所有表的记录数量
2. **外键约束**：验证所有外键约束正确建立
3. **功能测试**：测试前端界面的所有功能

### 回滚方案
如果修复失败，可以：
1. 从备份表恢复数据：`test_plans_backup`、`test_results_backup`
2. 删除新创建的表：`test_plan_group`、`test_param_item`
3. 重新创建原有的外键约束

## 🚀 后续工作

修复完成后，还需要：

1. **后端API开发**：
   - 创建 TestPlanGroupController、Service、Mapper
   - 创建 TestParamItemController、Service、Mapper

2. **前端界面更新**：
   - 使用新的测试方案配置界面
   - 更新数据录入模块以适配新结构

3. **趋势对比模块调整**：
   - 更新API调用以使用新的测试方案组结构
   - 修改参数明细显示逻辑

现在您可以安全地执行 `database_actual_fix.sql` 脚本来完成数据库结构的修复和升级！
