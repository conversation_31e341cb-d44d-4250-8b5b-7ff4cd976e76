# 数据录入模块修复指南

## 问题概述

您的项目中存在测试方案表结构不一致的问题：
- **数据库中**：使用新的拆分表结构（`test_plan_group` + `test_param_item`）
- **数据录入模块**：部分代码仍引用旧的单表结构（`test_plans`）

这导致了 `Table 'codebuddy.test_plans' doesn't exist` 的错误。

## 修复内容

### 1. ✅ 已修复的文件

#### 1.1 TestResultMapper.xml
**文件路径：** `ruoyi-system/src/main/resources/mapper/system/TestResultMapper.xml`

**修复内容：**
- 第185行：将 `test_plans` 改为 `test_plan_group`
- 第190行：将 `test_plans` 改为 `test_plan_group`
- 第131行：将表别名 `tp` 改为 `tpg`

#### 1.2 兼容性数据库脚本
**文件路径：** `sql/fix_data_entry_compatibility.sql`

**修复内容：**
- 创建 `test_plans` 视图，基于新的表结构
- 插入测试数据到 `test_plan_group` 和 `test_param_item`
- 创建详情查询视图 `test_result_detail_view`
- 验证数据完整性

### 2. 数据库修复步骤

#### 2.1 执行兼容性脚本
```sql
-- 连接到数据库
mysql -u root -p

-- 选择数据库
USE codebuddy;

-- 执行修复脚本
SOURCE sql/fix_data_entry_compatibility.sql;
```

#### 2.2 验证修复结果
```sql
-- 检查test_plans视图是否创建成功
SELECT COUNT(*) FROM test_plans;

-- 检查数据录入模块相关表
SELECT COUNT(*) FROM test_plan_group;
SELECT COUNT(*) FROM test_param_item;
SELECT COUNT(*) FROM test_results;

-- 验证关联查询
SELECT 
    tr.test_result_id,
    tpg.plan_code,
    tpi.param_name,
    pg.param_number,
    m.material_name
FROM test_results tr
LEFT JOIN test_plan_group tpg ON tr.plan_group_id = tpg.plan_group_id
LEFT JOIN test_param_item tpi ON tr.test_param_id = tpi.test_param_id
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
LEFT JOIN materials m ON pg.material_id = m.material_id
LIMIT 5;
```

### 3. 应用重启

修复完成后，需要重启应用以确保更改生效：

```bash
# 停止应用
# 重新启动应用
```

### 4. 功能验证

#### 4.1 数据录入页面访问
1. 登录系统
2. 进入数据录入模块
3. 确认页面能正常加载，不再出现表不存在的错误

#### 4.2 测试方案选择功能
1. 点击"新增"按钮
2. 在测试方案字段中输入内容
3. 验证是否显示候选项下拉列表
4. 选择一个测试方案，确认能正常选择

#### 4.3 参数明细显示功能
1. 选择一个参数编号
2. 验证是否显示相关的参数详情信息
3. 确认显示的信息包括：
   - 材料名称
   - 供应商名称
   - 工艺类型
   - 参数列表

#### 4.4 测试方案参数信息显示
1. 选择测试方案组和测试参数
2. 验证是否显示测试方案参数信息
3. 确认显示的信息包括：
   - 方案编号
   - 性能类型
   - 性能名称
   - 测试设备
   - 测试参数

#### 4.5 数据保存和查询
1. 填写完整的测试结果信息
2. 点击保存，确认能成功保存
3. 在列表中查询刚保存的数据
4. 点击详情按钮，确认详情信息完整显示

### 5. 常见问题排查

#### 5.1 仍然出现表不存在错误
**可能原因：**
- 数据库脚本未正确执行
- 应用未重启
- 缓存问题

**解决方法：**
1. 重新执行数据库脚本
2. 重启应用服务
3. 清除浏览器缓存

#### 5.2 测试方案选项为空
**可能原因：**
- test_plan_group表中没有数据
- 视图创建失败

**解决方法：**
1. 检查test_plan_group表是否有数据
2. 重新创建test_plans视图
3. 检查应用日志是否有错误

#### 5.3 参数明细不显示
**可能原因：**
- test_param_item表中没有数据
- 关联查询有问题

**解决方法：**
1. 检查test_param_item表是否有数据
2. 验证planGroupId关联是否正确
3. 检查前端API调用是否正常

### 6. 技术说明

#### 6.1 表结构映射关系
```
旧结构（test_plans）          新结构
├── test_plan_id         →   test_plan_group.plan_group_id
├── plan_code           →   test_plan_group.plan_code
├── performance_type    →   test_plan_group.performance_type
├── performance_name    →   test_plan_group.performance_name
├── test_equipment      →   test_plan_group.test_equipment
├── test_parameter      →   test_param_item.param_name (聚合)
└── attachments         →   test_plan_group.attachments
```

#### 6.2 视图定义
创建的 `test_plans` 视图将新表结构的数据聚合为旧表结构的格式，确保向后兼容。

#### 6.3 数据录入流程
1. 用户选择测试方案组（plan_group_id）
2. 系统显示该组下的测试参数选项（test_param_item）
3. 用户选择具体的测试参数（test_param_id）
4. 用户选择工艺参数组（group_id）
5. 系统显示相关的参数明细信息
6. 用户填写测试结果并保存

## 修复完成确认

当以下条件都满足时，表示修复成功：

1. ✅ 数据库中存在test_plans视图
2. ✅ test_plan_group和test_param_item表有数据
3. ✅ 数据录入页面能正常访问
4. ✅ 测试方案选择功能正常
5. ✅ 参数明细显示功能正常
6. ✅ 数据保存和查询功能正常
7. ✅ 详情显示功能完整

## 后续建议

1. **统一表结构**：建议将所有模块都迁移到新的表结构
2. **数据同步**：如果需要保持两套表结构，建议建立数据同步机制
3. **文档更新**：更新相关技术文档，说明新的表结构设计
4. **测试覆盖**：增加自动化测试，确保表结构变更不影响功能
