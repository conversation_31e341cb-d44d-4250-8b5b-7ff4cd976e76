-- 基于实际数据库表结构的修复脚本
-- 根据material_tables.sql中的实际表结构进行修改

-- ======== 第一步：安全检查和备份 ========
SET FOREIGN_KEY_CHECKS = 0;

-- 检查当前表结构
SELECT 'Current table structure check:' as info;
SHOW TABLES LIKE '%material%';
SHOW TABLES LIKE '%process%';
SHOW TABLES LIKE '%test%';

-- 备份现有数据
CREATE TABLE IF NOT EXISTS `test_plans_backup` AS SELECT * FROM `test_plans`;
CREATE TABLE IF NOT EXISTS `test_results_backup` AS SELECT * FROM `test_results`;

-- ======== 第二步：删除现有的外键约束 ========
-- 删除可能存在的外键约束（使用实际的约束名称）
ALTER TABLE `process_param_group` DROP FOREIGN KEY IF EXISTS `fk_group_material`;
ALTER TABLE `process_param_group` DROP FOREIGN KEY IF EXISTS `fk_ppg_material_id`;
ALTER TABLE `process_param_item` DROP FOREIGN KEY IF EXISTS `fk_item_group`;
ALTER TABLE `process_param_item` DROP FOREIGN KEY IF EXISTS `fk_ppi_group_id`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_tr_test_plan_id`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_tr_group_id`;
ALTER TABLE `test_plans` DROP FOREIGN KEY IF EXISTS `fk_tp_material_id`;

-- ======== 第三步：修改参数数值字段格式 ========
-- 将process_param_item表的param_value从DECIMAL改为VARCHAR
ALTER TABLE `process_param_item` 
MODIFY COLUMN `param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）';

-- ======== 第四步：重新设计测试方案表结构 ========

-- 4.1 创建测试方案组表（类似process_param_group，使用BIGINT保持一致性）
DROP TABLE IF EXISTS `test_plan_group`;
CREATE TABLE `test_plan_group` (
  `plan_group_id`    BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code`        VARCHAR(50)  NOT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`),
  KEY `idx_performance_type` (`performance_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案组表';

-- 4.2 创建测试参数明细表（类似process_param_item，使用BIGINT保持一致性）
DROP TABLE IF EXISTS `test_param_item`;
CREATE TABLE `test_param_item` (
  `test_param_id`   BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）',
  `plan_group_id`   BIGINT(20) NOT NULL COMMENT '所属测试方案组ID',
  `param_name`      VARCHAR(100) NOT NULL COMMENT '测试参数名称',
  `param_value`     VARCHAR(100) DEFAULT NULL COMMENT '测试参数数值（字符串格式）',
  `unit`            VARCHAR(20)   DEFAULT NULL COMMENT '参数单位',
  `attachments`     VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`          VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`       VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`       VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_param_id`),
  KEY `idx_plan_group_id` (`plan_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试参数明细表';

-- ======== 第五步：数据迁移 ========

-- 5.1 从原test_plans表迁移数据到test_plan_group表
INSERT INTO `test_plan_group` (
  `plan_group_id`, `plan_code`, `performance_type`, `performance_name`,
  `test_equipment`, `attachments`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`
)
SELECT
  `test_plan_id`, `plan_code`, `performance_type`, `performance_name`,
  `test_equipment`, `attachments`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`
FROM `test_plans`;

-- 5.2 将原test_plans表的test_parameter字段拆分为test_param_item记录
-- 如果test_parameter字段有数据，创建对应的参数明细记录
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `create_by`, `create_time`)
SELECT
  `test_plan_id` as plan_group_id,
  '测试参数' as param_name,
  `test_parameter` as param_value,
  `create_by`,
  `create_time`
FROM `test_plans`
WHERE `test_parameter` IS NOT NULL AND `test_parameter` != '';

-- 5.3 为每个测试方案组添加更详细的测试参数（基于现有数据）
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `unit`, `create_by`)
SELECT
  tpg.plan_group_id,
  CASE
    WHEN tpg.performance_type = '机械性能' THEN '试样长度'
    WHEN tpg.performance_type = '热性能' THEN '试样尺寸'
    WHEN tpg.performance_type = '电性能' THEN '试样厚度'
    ELSE '标准参数'
  END as param_name,
  CASE
    WHEN tpg.performance_type = '机械性能' THEN '250'
    WHEN tpg.performance_type = '热性能' THEN '25x25'
    WHEN tpg.performance_type = '电性能' THEN '1'
    ELSE '标准值'
  END as param_value,
  CASE
    WHEN tpg.performance_type = '机械性能' THEN 'mm'
    WHEN tpg.performance_type = '热性能' THEN 'mm'
    WHEN tpg.performance_type = '电性能' THEN 'mm'
    ELSE ''
  END as unit,
  'admin' as create_by
FROM `test_plan_group` tpg
WHERE NOT EXISTS (
  SELECT 1 FROM `test_param_item` tpi
  WHERE tpi.plan_group_id = tpg.plan_group_id
  AND tpi.param_name != '测试参数'
);

-- ======== 第六步：修改test_results表结构 ========

-- 6.1 重命名字段以保持一致性（保持BIGINT类型）
ALTER TABLE `test_results`
CHANGE COLUMN `test_plan_id` `plan_group_id` BIGINT(20) NOT NULL COMMENT '测试方案组ID';

-- 6.2 确保group_id字段类型正确
ALTER TABLE `test_results`
MODIFY COLUMN `group_id` BIGINT(20) NOT NULL COMMENT '参数组ID';

-- 6.3 清理test_results表中无效的外键数据
DELETE FROM `test_results` 
WHERE `plan_group_id` NOT IN (
    SELECT `plan_group_id` FROM `test_plan_group` WHERE `plan_group_id` IS NOT NULL
);

DELETE FROM `test_results` 
WHERE `group_id` NOT IN (
    SELECT `group_id` FROM `process_param_group` WHERE `group_id` IS NOT NULL
);

-- ======== 第七步：重新添加外键约束 ========

-- 7.1 工艺参数组 -> 材料
ALTER TABLE `process_param_group` 
ADD CONSTRAINT `fk_group_material`
  FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.2 工艺参数明细 -> 工艺参数组
ALTER TABLE `process_param_item` 
ADD CONSTRAINT `fk_item_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.3 测试参数明细 -> 测试方案组
ALTER TABLE `test_param_item` 
ADD CONSTRAINT `fk_test_param_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.4 测试结果 -> 测试方案组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.5 测试结果 -> 工艺参数组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_param_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- ======== 第八步：插入示例测试参数明细数据 ========
-- 为现有的测试方案组添加更详细的测试参数

-- 假设已有测试方案，为其添加详细的测试参数
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `unit`, `create_by`) VALUES
-- 为第一个测试方案添加参数（如果存在）
(1, '试样长度', '250', 'mm', 'admin'),
(1, '试样宽度', '25', 'mm', 'admin'),
(1, '拉伸速度', '2', 'mm/min', 'admin'),
-- 为第二个测试方案添加参数（如果存在）
(2, '试样厚度', '4', 'mm', 'admin'),
(2, '跨距', '64', 'mm', 'admin'),
(2, '加载速度', '2', 'mm/min', 'admin')
ON DUPLICATE KEY UPDATE param_name = param_name; -- 避免重复插入

-- ======== 第九步：删除原test_plans表 ========
-- 确认数据迁移成功后，删除原表
DROP TABLE IF EXISTS `test_plans`;

-- ======== 第十步：创建索引优化性能 ========
CREATE INDEX IF NOT EXISTS `idx_material_name` ON `materials` (`material_name`);
CREATE INDEX IF NOT EXISTS `idx_process_type` ON `process_param_group` (`process_type`);
CREATE INDEX IF NOT EXISTS `idx_param_number` ON `process_param_group` (`param_number`);
CREATE INDEX IF NOT EXISTS `idx_performance_type` ON `test_plan_group` (`performance_type`);
CREATE INDEX IF NOT EXISTS `idx_plan_code` ON `test_plan_group` (`plan_code`);
CREATE INDEX IF NOT EXISTS `idx_test_result_create_time` ON `test_results` (`create_time`);

SET FOREIGN_KEY_CHECKS = 1;

-- ======== 第十一步：验证修改结果 ========
SELECT 'Database structure verification:' as info;

SELECT 'test_plan_group table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_plan_group`;

SELECT 'test_param_item table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_param_item`;

SELECT 'test_results table:' as table_name;
SELECT COUNT(*) as record_count FROM `test_results`;

SELECT 'Foreign key constraints:' as info;
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
  TABLE_NAME, CONSTRAINT_NAME;

SELECT 'Modification completed successfully!' as status;
