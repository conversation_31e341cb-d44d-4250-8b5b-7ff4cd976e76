# 数据库外键约束修复指南

## 问题原因

错误 `1452 - Cannot add or update a child row: a foreign key constraint fails` 是因为：

1. `test_results` 表中存在的 `test_plan_id` 值在 `test_plans` 表中找不到对应记录
2. 在添加外键约束之前，需要确保数据的完整性和一致性

## 解决方案

### 方案一：使用修复脚本（推荐）

执行 `database_fix_foreign_key.sql` 脚本，该脚本会：

1. **安全清理数据**：删除无效的外键引用
2. **重建表结构**：创建新的测试方案组和测试参数明细表
3. **数据迁移**：将现有数据迁移到新结构
4. **添加约束**：在数据一致后添加外键约束

```sql
-- 在数据库中执行
source database_fix_foreign_key.sql;
```

### 方案二：手动步骤修复

如果需要手动执行，按以下步骤：

#### 1. 检查数据完整性
```sql
-- 检查test_results表中的无效外键
SELECT DISTINCT tr.test_plan_id 
FROM test_results tr 
LEFT JOIN test_plans tp ON tr.test_plan_id = tp.test_plan_id 
WHERE tp.test_plan_id IS NULL;
```

#### 2. 清理无效数据
```sql
-- 删除无效的test_results记录
DELETE FROM test_results 
WHERE test_plan_id NOT IN (
    SELECT test_plan_id FROM test_plans WHERE test_plan_id IS NOT NULL
);
```

#### 3. 创建测试方案组表
```sql
CREATE TABLE `test_plan_group` (
  `plan_group_id`    INT NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code`        VARCHAR(50)  NOT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案组表';
```

#### 4. 插入测试方案组数据
```sql
INSERT INTO `test_plan_group` (`plan_group_id`, `plan_code`, `performance_type`, `performance_name`, `test_equipment`, `create_by`) VALUES
(1, 'TP001', '力学性能', '拉伸强度测试', 'Instron 5985', 'admin'),
(2, 'TP002', '力学性能', '弯曲强度测试', 'Instron 5985', 'admin'),
-- ... 其他数据
```

#### 5. 修改test_results表
```sql
-- 修改字段名
ALTER TABLE `test_results` 
CHANGE COLUMN `test_plan_id` `plan_group_id` INT NOT NULL COMMENT '测试方案组ID';

-- 清理超出范围的数据
DELETE FROM `test_results` WHERE `plan_group_id` > 8 OR `plan_group_id` < 1;
```

#### 6. 添加外键约束
```sql
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;
```

## 验证修复结果

### 1. 检查外键约束
```sql
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
  TABLE_NAME;
```

### 2. 检查数据完整性
```sql
-- 检查测试方案组数据
SELECT COUNT(*) as plan_group_count FROM test_plan_group;

-- 检查测试参数明细数据
SELECT COUNT(*) as test_param_count FROM test_param_item;

-- 检查测试结果数据
SELECT COUNT(*) as test_result_count FROM test_results;
```

### 3. 测试外键约束
```sql
-- 尝试插入无效的外键数据（应该失败）
INSERT INTO test_results (plan_group_id, group_id, test_value) VALUES (999, 1, 100);
-- 应该报错：Cannot add or update a child row: a foreign key constraint fails
```

## 注意事项

1. **数据备份**：执行修复前务必备份数据库
2. **停止应用**：修复期间建议停止应用程序访问数据库
3. **测试验证**：修复后进行全面的功能测试
4. **API更新**：后端API需要相应调整以支持新的数据结构

## 常见问题

### Q: 如果仍然报外键约束错误怎么办？
A: 
1. 检查是否还有其他表引用了旧的test_plans表
2. 确认所有相关数据都已正确清理
3. 可以临时禁用外键检查：`SET FOREIGN_KEY_CHECKS = 0;`

### Q: 原有的测试方案数据会丢失吗？
A: 
不会，脚本会：
1. 创建备份表 `test_plans_backup`
2. 将数据迁移到新的测试方案组结构
3. 保留所有原有信息

### Q: 如何回滚到原来的结构？
A: 
1. 删除新创建的表
2. 从备份表恢复原有数据
3. 重新创建原有的外键约束

## 执行建议

**推荐执行顺序：**
1. 备份数据库
2. 执行 `database_fix_foreign_key.sql`
3. 验证数据完整性
4. 更新后端API
5. 测试前端功能

修复完成后，您将拥有一个更加灵活和扩展性更强的测试方案管理系统！
