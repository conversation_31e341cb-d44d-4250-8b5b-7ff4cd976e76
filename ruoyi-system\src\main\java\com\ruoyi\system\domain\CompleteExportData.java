package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;

/**
 * 完整导出数据对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class CompleteExportData
{
    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 材料型号 */
    @Excel(name = "材料型号")
    private String materialModel;

    /** 材料描述 */
    @Excel(name = "材料描述")
    private String materialDescription;

    /** 工艺类型 */
    @Excel(name = "工艺类型")
    private String processType;

    /** 参数编号 */
    @Excel(name = "参数编号")
    private String paramNumber;

    /** 参数名称 */
    @Excel(name = "参数名称")
    private String paramName;

    /** 参数数值 */
    @Excel(name = "参数数值")
    private String paramValue;

    /** 参数单位 */
    @Excel(name = "参数单位")
    private String unit;

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getMaterialModel() {
        return materialModel;
    }

    public void setMaterialModel(String materialModel) {
        this.materialModel = materialModel;
    }

    public String getMaterialDescription() {
        return materialDescription;
    }

    public void setMaterialDescription(String materialDescription) {
        this.materialDescription = materialDescription;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getParamNumber() {
        return paramNumber;
    }

    public void setParamNumber(String paramNumber) {
        this.paramNumber = paramNumber;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
