package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TestPlanGroup;

/**
 * 测试方案组Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ITestPlanGroupService 
{
    /**
     * 查询测试方案组
     * 
     * @param planGroupId 测试方案组主键
     * @return 测试方案组
     */
    public TestPlanGroup selectTestPlanGroupByPlanGroupId(Long planGroupId);

    /**
     * 查询测试方案组列表
     * 
     * @param testPlanGroup 测试方案组
     * @return 测试方案组集合
     */
    public List<TestPlanGroup> selectTestPlanGroupList(TestPlanGroup testPlanGroup);

    /**
     * 新增测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    public int insertTestPlanGroup(TestPlanGroup testPlanGroup);

    /**
     * 修改测试方案组
     * 
     * @param testPlanGroup 测试方案组
     * @return 结果
     */
    public int updateTestPlanGroup(TestPlanGroup testPlanGroup);

    /**
     * 批量删除测试方案组
     * 
     * @param planGroupIds 需要删除的测试方案组主键集合
     * @return 结果
     */
    public int deleteTestPlanGroupByPlanGroupIds(Long[] planGroupIds);

    /**
     * 删除测试方案组信息
     * 
     * @param planGroupId 测试方案组主键
     * @return 结果
     */
    public int deleteTestPlanGroupByPlanGroupId(Long planGroupId);

    /**
     * 获取测试方案组选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectTestPlanGroupOptions(String type);
}
