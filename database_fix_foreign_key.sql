-- 数据库外键约束修复脚本
-- 解决外键约束失败的问题

-- ======== 第一步：删除现有外键约束（如果存在） ========
SET FOREIGN_KEY_CHECKS = 0;

ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_plan`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_group`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_plan_group`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_param_group`;
ALTER TABLE `process_param_item` DROP FOREIGN KEY IF EXISTS `fk_item_group`;
ALTER TABLE `process_param_group` DROP FOREIGN KEY IF EXISTS `fk_group_material`;
ALTER TABLE `test_param_item` DROP FOREIGN KEY IF EXISTS `fk_test_param_group`;

SET FOREIGN_KEY_CHECKS = 1;

-- ======== 第二步：检查和清理数据 ========

-- 2.1 检查test_results表中的数据
SELECT 'test_results表中的数据统计:' as info;
SELECT COUNT(*) as total_records FROM test_results;
SELECT DISTINCT test_plan_id FROM test_results ORDER BY test_plan_id;

-- 2.2 检查test_plans表中的数据
SELECT 'test_plans表中的数据统计:' as info;
SELECT COUNT(*) as total_records FROM test_plans;
SELECT test_plan_id, plan_code FROM test_plans ORDER BY test_plan_id;

-- 2.3 清理test_results表中无效的外键数据
DELETE FROM test_results 
WHERE test_plan_id NOT IN (
    SELECT test_plan_id FROM test_plans WHERE test_plan_id IS NOT NULL
);

-- 2.4 检查materials表中的数据
SELECT 'materials表中的数据统计:' as info;
SELECT COUNT(*) as total_records FROM materials;

-- 2.5 清理process_param_group表中无效的外键数据
DELETE FROM process_param_group 
WHERE material_id NOT IN (
    SELECT material_id FROM materials WHERE material_id IS NOT NULL
);

-- 2.6 清理process_param_item表中无效的外键数据
DELETE FROM process_param_item 
WHERE group_id NOT IN (
    SELECT group_id FROM process_param_group WHERE group_id IS NOT NULL
);

-- ======== 第三步：修改参数数值字段格式 ========
ALTER TABLE `process_param_item` 
MODIFY COLUMN `param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）';

-- ======== 第四步：创建新的测试方案结构 ========

-- 4.1 删除旧的测试方案表（备份数据）
CREATE TABLE IF NOT EXISTS `test_plans_backup` AS SELECT * FROM `test_plans`;

-- 4.2 创建测试方案组表
DROP TABLE IF EXISTS `test_plan_group`;
CREATE TABLE `test_plan_group` (
  `plan_group_id`    INT NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code`        VARCHAR(50)  NOT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`),
  KEY `idx_performance_type` (`performance_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案组表';

-- 4.3 创建测试参数明细表
DROP TABLE IF EXISTS `test_param_item`;
CREATE TABLE `test_param_item` (
  `test_param_id`   INT NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）',
  `plan_group_id`   INT NOT NULL COMMENT '所属测试方案组ID',
  `param_name`      VARCHAR(100) NOT NULL COMMENT '测试参数名称',
  `param_value`     VARCHAR(100) DEFAULT NULL COMMENT '测试参数数值（字符串格式）',
  `unit`            VARCHAR(20)   DEFAULT NULL COMMENT '参数单位',
  `attachments`     VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`          VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`       VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`       VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_param_id`),
  KEY `idx_plan_group_id` (`plan_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试参数明细表';

-- ======== 第五步：插入测试方案组数据 ========
INSERT INTO `test_plan_group` (`plan_group_id`, `plan_code`, `performance_type`, `performance_name`, `test_equipment`, `create_by`) VALUES
(1, 'TP001', '力学性能', '拉伸强度测试', 'Instron 5985', 'admin'),
(2, 'TP002', '力学性能', '弯曲强度测试', 'Instron 5985', 'admin'),
(3, 'TP003', '力学性能', '冲击韧性测试', 'Charpy冲击试验机', 'admin'),
(4, 'TP004', '热学性能', '热膨胀系数测试', 'TMA热机械分析仪', 'admin'),
(5, 'TP005', '热学性能', '导热系数测试', '激光导热仪', 'admin'),
(6, 'TP006', '电学性能', '介电常数测试', '阻抗分析仪', 'admin'),
(7, 'TP007', '化学性能', '耐腐蚀性测试', '盐雾试验箱', 'admin'),
(8, 'TP008', '物理性能', '密度测试', '密度计', 'admin');

-- ======== 第六步：修改test_results表结构 ========
-- 6.1 修改字段名
ALTER TABLE `test_results` 
CHANGE COLUMN `test_plan_id` `plan_group_id` INT NOT NULL COMMENT '测试方案组ID';

-- 6.2 清理超出范围的数据
DELETE FROM `test_results` WHERE `plan_group_id` > 8 OR `plan_group_id` < 1;

-- ======== 第七步：重新添加外键约束 ========
SET FOREIGN_KEY_CHECKS = 0;

-- 7.1 工艺参数组 -> 材料
ALTER TABLE `process_param_group` 
ADD CONSTRAINT `fk_group_material`
  FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.2 工艺参数明细 -> 工艺参数组
ALTER TABLE `process_param_item` 
ADD CONSTRAINT `fk_item_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.3 测试参数明细 -> 测试方案组
ALTER TABLE `test_param_item` 
ADD CONSTRAINT `fk_test_param_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.4 测试结果 -> 测试方案组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 7.5 测试结果 -> 工艺参数组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_param_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;

-- ======== 第八步：插入测试参数明细数据 ========
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `unit`, `create_by`) VALUES
-- TP001 拉伸强度测试参数
(1, '试样长度', '250', 'mm', 'admin'),
(1, '试样宽度', '25', 'mm', 'admin'),
(1, '试样厚度', '2', 'mm', 'admin'),
(1, '拉伸速度', '2', 'mm/min', 'admin'),
(1, '预载荷', '10', 'N', 'admin'),

-- TP002 弯曲强度测试参数
(2, '试样长度', '80', 'mm', 'admin'),
(2, '试样宽度', '10', 'mm', 'admin'),
(2, '试样厚度', '4', 'mm', 'admin'),
(2, '跨距', '64', 'mm', 'admin'),
(2, '加载速度', '2', 'mm/min', 'admin'),

-- TP003 冲击韧性测试参数
(3, '试样长度', '55', 'mm', 'admin'),
(3, '试样宽度', '10', 'mm', 'admin'),
(3, '试样厚度', '10', 'mm', 'admin'),
(3, '缺口深度', '2', 'mm', 'admin'),
(3, '摆锤能量', '300', 'J', 'admin'),

-- TP004 热膨胀系数测试参数
(4, '试样长度', '25', 'mm', 'admin'),
(4, '温度范围', '25-200', '°C', 'admin'),
(4, '升温速率', '5', '°C/min', 'admin'),
(4, '保温时间', '10', 'min', 'admin'),

-- TP005 导热系数测试参数
(5, '试样直径', '12.7', 'mm', 'admin'),
(5, '试样厚度', '2', 'mm', 'admin'),
(5, '测试温度', '25', '°C', 'admin'),
(5, '激光功率', '0.1', 'W', 'admin'),

-- TP006 介电常数测试参数
(6, '试样直径', '50', 'mm', 'admin'),
(6, '试样厚度', '1', 'mm', 'admin'),
(6, '测试频率', '1000', 'Hz', 'admin'),
(6, '测试电压', '1', 'V', 'admin'),

-- TP007 耐腐蚀性测试参数
(7, '试样尺寸', '50x25x3', 'mm', 'admin'),
(7, '盐雾浓度', '5', '%', 'admin'),
(7, '测试温度', '35', '°C', 'admin'),
(7, '测试时间', '168', 'h', 'admin'),

-- TP008 密度测试参数
(8, '试样体积', '1', 'cm³', 'admin'),
(8, '测试温度', '23', '°C', 'admin'),
(8, '测试介质', '蒸馏水', '', 'admin'),
(8, '浸泡时间', '24', 'h', 'admin');

-- ======== 第九步：验证数据完整性 ========
SELECT 'test_plan_group表数据:' as info;
SELECT * FROM test_plan_group;

SELECT 'test_param_item表数据统计:' as info;
SELECT plan_group_id, COUNT(*) as param_count FROM test_param_item GROUP BY plan_group_id;

SELECT 'test_results表数据统计:' as info;
SELECT plan_group_id, COUNT(*) as result_count FROM test_results GROUP BY plan_group_id;

SELECT '外键约束验证:' as info;
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  CONSTRAINT_TYPE,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
  TABLE_NAME, CONSTRAINT_NAME;

SELECT '修复完成!' as status;
