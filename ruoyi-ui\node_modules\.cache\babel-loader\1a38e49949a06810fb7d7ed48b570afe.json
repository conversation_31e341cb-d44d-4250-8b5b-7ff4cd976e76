{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue", "mtime": 1754285158811}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\babel.config.js", "mtime": 1753339103954}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_material", "require", "_processParamGroup", "_processParamItem", "_auth", "_axios", "_interopRequireDefault", "name", "directives", "drag", "bind", "el", "dialogHeaderEl", "querySelector", "dragDom", "style", "cursor", "sty", "currentStyle", "window", "getComputedStyle", "onmousedown", "e", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "styL", "styT", "left", "includes", "document", "body", "clientWidth", "replace", "clientHeight", "top", "<PERSON><PERSON><PERSON><PERSON>", "l", "t", "concat", "onmouseup", "data", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "materialLoading", "materialList", "materialTotal", "currentMaterial", "materialOpen", "materialTitle", "materialForm", "materialFileList", "materialIds", "materialSingle", "materialMultiple", "materialQueryParams", "pageNum", "pageSize", "materialName", "supplierName", "materialModel", "materialRules", "required", "message", "trigger", "paramGroupLoading", "paramGroupList", "paramGroupTotal", "currentParamGroup", "paramGroupOpen", "paramGroupTitle", "paramGroupForm", "paramGroupFileList", "paramGroupIds", "paramGroupSingle", "paramGroupMultiple", "paramGroupQueryParams", "materialId", "processType", "paramNumber", "paramGroupRules", "paramItemLoading", "paramItemList", "paramItemTotal", "paramItemOpen", "paramItemTitle", "paramItemForm", "paramItemFileList", "paramItemIds", "paramItemSingle", "paramItemMultiple", "paramItemQueryParams", "groupId", "paramName", "unit", "paramItemRules", "attachmentDialogVisible", "attachmentList", "materialNameSuggestions", "supplierSuggestions", "materialModelSuggestions", "processTypeSuggestions", "paramNameSuggestions", "paramNumberSuggestions", "unitSuggestions", "created", "getMaterialList", "loadSuggestions", "methods", "_this", "getMaterialOptions", "type", "then", "response", "map", "item", "value", "catch", "getProcessParamGroupOptions", "getProcessParamItemOptions", "queryMaterialNameSuggestions", "queryString", "cb", "suggestions", "filter", "toLowerCase", "indexOf", "querySupplierSuggestions", "queryMaterialModelSuggestions", "queryProcessTypeSuggestions", "queryParamNameSuggestions", "queryParamNumberSuggestions", "queryUnitSuggestions", "handleMaterialNameSelect", "handleMaterialNameFocus", "_this2", "handleSupplierFocus", "_this3", "handleMaterialModelFocus", "_this4", "handleProcessTypeFocus", "_this5", "processTypes", "_toConsumableArray2", "default", "Set", "Boolean", "handleParamNumberFocus", "_this6", "paramNumbers", "handleParamNameFocus", "_this7", "paramNames", "handleUnitFocus", "_this8", "units", "parseAttachments", "attachments", "Array", "isArray", "split", "url", "trim", "index", "fileName", "substring", "lastIndexOf", "uid", "Date", "now", "status", "_this9", "listMaterial", "rows", "total", "handleMaterialQuery", "resetMaterialQuery", "resetForm", "handleMaterialClick", "row", "getParamGroupList", "$refs", "materialTable", "toggleRowSelection", "handleMaterialSelectionChange", "selection", "length", "getMaterialRowClassName", "_ref", "rowIndex", "_this0", "listProcessParamGroup", "handleParamGroupQuery", "resetParamGroupQuery", "handleParamGroupClick", "getParamItemList", "paramGroupTable", "handleParamGroupSelectionChange", "handleParamItemRowClick", "paramItemTable", "handleParamItemSelectionChange", "itemId", "getParamGroupRowClassName", "_ref2", "_this1", "listProcessParamItem", "handleParamItemQuery", "resetParamItemQuery", "handleAddMaterial", "resetMaterialForm", "handleEditMaterial", "_this10", "getMaterial", "submitMaterialForm", "_this11", "validate", "valid", "file", "join", "updateBy", "$store", "state", "user", "createBy", "updateMaterial", "$modal", "msgSuccess", "addMaterial", "cancelMaterial", "materialDescription", "remark", "handleDeleteMaterial", "_this12", "confirm", "delMaterial", "handleBatchDeleteMaterial", "_this13", "handleExportMaterial", "download", "_objectSpread2", "getTime", "handleExportComplete", "_this14", "loading", "closeLoading", "error", "msgError", "handleAddParamGroup", "resetParamGroupForm", "handleEditParamGroup", "_this15", "getProcessParamGroup", "submitParamGroupForm", "_this16", "updateProcessParamGroup", "addProcessParamGroup", "cancelParamGroup", "handleDeleteParamGroup", "_this17", "groupIds", "delProcessParamGroup", "handleBatchDeleteParamGroup", "_this18", "handleExportParamGroup", "handleAddParamItem", "resetParamItemForm", "handleEditParamItem", "_this19", "getProcessParamItem", "submitParamItemForm", "_this20", "updateProcessParamItem", "addProcessParamItem", "cancelParamItem", "paramValue", "handleDeleteParamItem", "_this21", "itemIds", "delProcessParamItem", "handleBatchDeleteParamItem", "_this22", "handleExportParamItem", "handleMaterialUploadSuccess", "fileList", "_this23", "code", "_item$raw", "size", "formatFileSize", "raw", "msg", "handleMaterialFileRemove", "_this24", "console", "log", "_item$raw2", "beforeMaterialUpload", "isLt10M", "handleParamGroupUploadSuccess", "_this25", "_item$raw3", "handleParamGroupFileRemove", "_this26", "_item$raw4", "beforeParamGroupUpload", "handleParamItemUploadSuccess", "_this27", "_item$raw5", "handleParamItemFileRemove", "_this28", "_item$raw6", "beforeParamItemUpload", "handleViewAttachments", "startsWith", "JSON", "parse", "downloadAttachment", "link", "createElement", "href", "click", "toFixed"], "sources": ["src/views/material/config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-setting\"></i>\r\n        <span>材料及工艺参数配置</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理材料信息、工艺参数组和参数明细的三层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击材料行查看工艺参数组，点击参数组行查看参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 材料信息表格 -->\r\n    <el-card class=\"material-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-box\"></i>\r\n          <span class=\"header-title\">材料信息管理</span>\r\n          <el-badge :value=\"materialTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddMaterial\">\r\n            <span>新增材料</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"materialMultiple\" @click=\"handleBatchDeleteMaterial\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportComplete\" class=\"export-complete-btn\">\r\n            <span>整体导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 材料查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"materialQueryParams\" ref=\"materialQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialName\"\r\n              :fetch-suggestions=\"queryMaterialNameSuggestions\"\r\n              placeholder=\"请输入材料名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handleMaterialNameSelect\"\r\n              @focus=\"handleMaterialNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-search\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"供应商\" prop=\"supplierName\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.supplierName\"\r\n              :fetch-suggestions=\"querySupplierSuggestions\"\r\n              placeholder=\"请输入供应商名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleSupplierFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-office-building\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n            <el-autocomplete\r\n              v-model=\"materialQueryParams.materialModel\"\r\n              :fetch-suggestions=\"queryMaterialModelSuggestions\"\r\n              placeholder=\"请输入材料型号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleMaterialModelFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-goods\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleMaterialQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetMaterialQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"materialLoading\"\r\n          :data=\"materialList\"\r\n          @row-click=\"handleMaterialClick\"\r\n          @selection-change=\"handleMaterialSelectionChange\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getMaterialRowClassName\"\r\n          ref=\"materialTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载材料数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (materialQueryParams.pageNum - 1) * materialQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"材料名称\" min-width=\"140\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"material-name-cell\">\r\n                <i class=\"el-icon-box material-icon\"></i>\r\n                <span class=\"material-name\">{{ scope.row.materialName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"supplierName\" label=\"供应商\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"supplier-cell\">\r\n                <i class=\"el-icon-office-building supplier-icon\"></i>\r\n                <span>{{ scope.row.supplierName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialModel\" label=\"材料型号\" min-width=\"130\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"info\" v-if=\"scope.row.materialModel\">{{ scope.row.materialModel }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialDescription\" label=\"材料描述\" min-width=\"180\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.materialDescription\" class=\"description-text\">{{ scope.row.materialDescription }}</span>\r\n              <span v-else class=\"empty-data\">暂无描述</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditMaterial(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteMaterial(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"materialTotal > 0\"\r\n        :total=\"materialTotal\"\r\n        :page.sync=\"materialQueryParams.pageNum\"\r\n        :limit.sync=\"materialQueryParams.pageSize\"\r\n        @pagination=\"getMaterialList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 工艺参数组表格 -->\r\n    <el-card class=\"param-group-card enhanced-card\" style=\"margin-bottom: 20px;\" v-show=\"currentMaterial\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-setting\"></i>\r\n          <span class=\"header-title\">工艺参数组</span>\r\n          <div class=\"material-indicator\" v-if=\"currentMaterial\">\r\n            <el-tag type=\"success\" size=\"small\">\r\n              <i class=\"el-icon-box\"></i>\r\n              {{ currentMaterial.materialName }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"paramGroupTotal\" class=\"item-count-badge\" type=\"success\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>新增参数组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"paramGroupMultiple || !currentMaterial\" @click=\"handleBatchDeleteParamGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportParamGroup\" :disabled=\"!currentMaterial\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"paramGroupQueryParams\" ref=\"paramGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.processType\"\r\n              :fetch-suggestions=\"queryProcessTypeSuggestions\"\r\n              placeholder=\"请输入工艺类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleProcessTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-setting\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n            <el-autocomplete\r\n              v-model=\"paramGroupQueryParams.paramNumber\"\r\n              :fetch-suggestions=\"queryParamNumberSuggestions\"\r\n              placeholder=\"请输入参数编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNumberFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-tickets\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleParamGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetParamGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"paramGroupLoading\"\r\n          :data=\"paramGroupList\"\r\n          @row-click=\"handleParamGroupClick\"\r\n          @selection-change=\"handleParamGroupSelectionChange\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getParamGroupRowClassName\"\r\n          ref=\"paramGroupTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载参数组数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (paramGroupQueryParams.pageNum - 1) * paramGroupQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"processType\" label=\"工艺类型\" min-width=\"140\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"process-type-cell\">\r\n                <i class=\"el-icon-setting process-icon\"></i>\r\n                <span class=\"process-type\">{{ scope.row.processType }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramNumber\" label=\"参数编号\" min-width=\"140\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"param-number-cell\">\r\n                <i class=\"el-icon-tickets param-number-icon\"></i>\r\n                <span class=\"param-number\">{{ scope.row.paramNumber }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditParamGroup(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteParamGroup(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"paramGroupTotal > 0\"\r\n        :total=\"paramGroupTotal\"\r\n        :page.sync=\"paramGroupQueryParams.pageNum\"\r\n        :limit.sync=\"paramGroupQueryParams.pageSize\"\r\n        @pagination=\"getParamGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 参数明细表格 -->\r\n    <el-card class=\"param-item-card enhanced-card\" v-show=\"currentParamGroup\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">参数明细</span>\r\n          <div class=\"param-group-indicator\" v-if=\"currentParamGroup\">\r\n            <el-tag type=\"warning\" size=\"small\">\r\n              <i class=\"el-icon-tickets\"></i>\r\n              {{ currentParamGroup.paramNumber }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"paramItemTotal\" class=\"item-count-badge\" type=\"warning\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddParamItem\" :disabled=\"!currentParamGroup\">\r\n            <span>新增参数</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"paramItemMultiple || !currentParamGroup\" @click=\"handleBatchDeleteParamItem\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportParamItem\" :disabled=\"!currentParamGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 参数明细查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"paramItemQueryParams\" ref=\"paramItemQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n            <el-autocomplete\r\n              v-model=\"paramItemQueryParams.paramName\"\r\n              :fetch-suggestions=\"queryParamNameSuggestions\"\r\n              placeholder=\"请输入参数名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-data-line\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数单位\" prop=\"unit\">\r\n            <el-autocomplete\r\n              v-model=\"paramItemQueryParams.unit\"\r\n              :fetch-suggestions=\"queryUnitSuggestions\"\r\n              placeholder=\"请输入参数单位\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleUnitFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-price-tag\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleParamItemQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetParamItemQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"paramItemLoading\"\r\n          :data=\"paramItemList\"\r\n          @selection-change=\"handleParamItemSelectionChange\"\r\n          @row-click=\"handleParamItemRowClick\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getParamItemRowClassName\"\r\n          ref=\"paramItemTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载参数数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (paramItemQueryParams.pageNum - 1) * paramItemQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"param-name-cell\">\r\n                <i class=\"el-icon-data-line param-icon\"></i>\r\n                <span class=\"param-name\">{{ scope.row.paramName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"param-value\" v-if=\"scope.row.paramValue !== null\">{{ scope.row.paramValue }}</span>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"success\" v-if=\"scope.row.unit\">{{ scope.row.unit }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditParamItem(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteParamItem(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"paramItemTotal > 0\"\r\n        :total=\"paramItemTotal\"\r\n        :page.sync=\"paramItemQueryParams.pageNum\"\r\n        :limit.sync=\"paramItemQueryParams.pageSize\"\r\n        @pagination=\"getParamItemList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 材料信息对话框 -->\r\n    <el-dialog :title=\"materialTitle\" :visible.sync=\"materialOpen\" width=\"800px\" append-to-body v-drag>\r\n      <el-form ref=\"materialForm\" :model=\"materialForm\" :rules=\"materialRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料名称\" prop=\"materialName\">\r\n              <el-input v-model=\"materialForm.materialName\" placeholder=\"请输入材料名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"supplierName\">\r\n              <el-input v-model=\"materialForm.supplierName\" placeholder=\"请输入供应商名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"材料型号\" prop=\"materialModel\">\r\n              <el-input v-model=\"materialForm.materialModel\" placeholder=\"请输入材料型号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"材料描述\">\r\n          <el-input v-model=\"materialForm.materialDescription\" type=\"textarea\" placeholder=\"请输入材料描述\" :rows=\"3\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"materialUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"materialFileList\"\r\n            :on-success=\"handleMaterialUploadSuccess\"\r\n            :on-remove=\"handleMaterialFileRemove\"\r\n            :before-upload=\"beforeMaterialUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"materialForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"2\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitMaterialForm\">确 定</el-button>\r\n        <el-button @click=\"cancelMaterial\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工艺参数组对话框 -->\r\n    <el-dialog :title=\"paramGroupTitle\" :visible.sync=\"paramGroupOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramGroupForm\" :model=\"paramGroupForm\" :rules=\"paramGroupRules\" label-width=\"100px\">\r\n        <el-form-item label=\"工艺类型\" prop=\"processType\">\r\n          <el-input v-model=\"paramGroupForm.processType\" placeholder=\"请输入工艺类型\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"参数编号\" prop=\"paramNumber\">\r\n          <el-input v-model=\"paramGroupForm.paramNumber\" placeholder=\"请输入参数编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramGroupUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramGroupFileList\"\r\n            :on-success=\"handleParamGroupUploadSuccess\"\r\n            :on-remove=\"handleParamGroupFileRemove\"\r\n            :before-upload=\"beforeParamGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 参数明细对话框 -->\r\n    <el-dialog :title=\"paramItemTitle\" :visible.sync=\"paramItemOpen\" width=\"600px\" append-to-body v-drag>\r\n      <el-form ref=\"paramItemForm\" :model=\"paramItemForm\" :rules=\"paramItemRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"paramItemForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"paramItemForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"paramItemForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"paramItemUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"paramItemFileList\"\r\n            :on-success=\"handleParamItemUploadSuccess\"\r\n            :on-remove=\"handleParamItemFileRemove\"\r\n            :before-upload=\"beforeParamItemUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持多文件上传，单个文件大小不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"paramItemForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitParamItemForm\">确 定</el-button>\r\n        <el-button @click=\"cancelParamItem\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body v-drag>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial,\r\n  exportMaterial, getMaterialOptions\r\n} from \"@/api/material/material\";\r\nimport {\r\n  listProcessParamGroup, getProcessParamGroup, delProcessParamGroup,\r\n  addProcessParamGroup, updateProcessParamGroup, listByMaterialId,\r\n  exportProcessParamGroup, getProcessParamGroupOptions, exportCompleteData\r\n} from \"@/api/material/processParamGroup\";\r\nimport {\r\n  listProcessParamItem, getProcessParamItem, delProcessParamItem,\r\n  addProcessParamItem, updateProcessParamItem, listByGroupId,\r\n  exportProcessParamItem, getProcessParamItemOptions\r\n} from \"@/api/material/processParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\n\r\nexport default {\r\n  name: \"MaterialConfig\",\r\n  directives: {\r\n    // 拖拽指令\r\n    drag: {\r\n      bind(el) {\r\n        const dialogHeaderEl = el.querySelector('.el-dialog__header');\r\n        const dragDom = el.querySelector('.el-dialog');\r\n        dialogHeaderEl.style.cursor = 'move';\r\n\r\n        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\r\n\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL, styT;\r\n\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (sty.left.includes('%')) {\r\n            styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\r\n            styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\r\n          } else {\r\n            styL = +sty.left.replace(/px/g, '');\r\n            styT = +sty.top.replace(/px/g, '');\r\n          }\r\n\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            const l = e.clientX - disX;\r\n            const t = e.clientY - disY;\r\n\r\n            // 移动当前元素\r\n            dragDom.style.left = `${l + styL}px`;\r\n            dragDom.style.top = `${t + styT}px`;\r\n\r\n            // 将此时的位置传出去\r\n            // binding.value({x:e.pageX,y:e.pageY})\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        }\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n\r\n      // 材料相关\r\n      materialLoading: true,\r\n      materialList: [],\r\n      materialTotal: 0,\r\n      currentMaterial: null,\r\n      materialOpen: false,\r\n      materialTitle: \"\",\r\n      materialForm: {},\r\n      materialFileList: [],\r\n      materialIds: [],\r\n      materialSingle: true,\r\n      materialMultiple: true,\r\n      materialQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null\r\n      },\r\n      materialRules: {\r\n        materialName: [\r\n          { required: true, message: \"材料名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 工艺参数组相关\r\n      paramGroupLoading: false,\r\n      paramGroupList: [],\r\n      paramGroupTotal: 0,\r\n      currentParamGroup: null,\r\n      paramGroupOpen: false,\r\n      paramGroupTitle: \"\",\r\n      paramGroupForm: {},\r\n      paramGroupFileList: [],\r\n      paramGroupIds: [],\r\n      paramGroupSingle: true,\r\n      paramGroupMultiple: true,\r\n      paramGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null\r\n      },\r\n      paramGroupRules: {\r\n        processType: [\r\n          { required: true, message: \"工艺类型不能为空\", trigger: \"blur\" }\r\n        ],\r\n        paramNumber: [\r\n          { required: true, message: \"参数编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 参数明细相关\r\n      paramItemLoading: false,\r\n      paramItemList: [],\r\n      paramItemTotal: 0,\r\n      paramItemOpen: false,\r\n      paramItemTitle: \"\",\r\n      paramItemForm: {},\r\n      paramItemFileList: [],\r\n      paramItemIds: [],\r\n      paramItemSingle: true,\r\n      paramItemMultiple: true,\r\n      paramItemQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        groupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      paramItemRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      materialNameSuggestions: [],\r\n      supplierSuggestions: [],\r\n      materialModelSuggestions: [],\r\n      processTypeSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      paramNumberSuggestions: [],\r\n      unitSuggestions: []\r\n    };\r\n  },\r\n  created() {\r\n    this.getMaterialList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取工艺类型建议\r\n      getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n        this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数编号建议\r\n      getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数名称建议\r\n      getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n        this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n\r\n      // 获取参数单位建议\r\n      getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n        this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料名称搜索建议 */\r\n    queryMaterialNameSuggestions(queryString, cb) {\r\n      let suggestions = this.materialNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 供应商搜索建议 */\r\n    querySupplierSuggestions(queryString, cb) {\r\n      let suggestions = this.supplierSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.supplierSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料型号搜索建议 */\r\n    queryMaterialModelSuggestions(queryString, cb) {\r\n      let suggestions = this.materialModelSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.materialModelSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 工艺类型搜索建议 */\r\n    queryProcessTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.processTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.processTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数名称搜索建议 */\r\n    /** 参数名称搜索建议 */\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数编号搜索建议 */\r\n    queryParamNumberSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNumberSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNumberSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 参数单位搜索建议 */\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 材料名称选择事件 */\r\n    handleMaterialNameSelect(item) {\r\n      this.materialQueryParams.materialName = item.value;\r\n      // 移除自动搜索，让用户手动点击搜索按钮\r\n    },\r\n\r\n    /** 材料名称焦点事件 */\r\n    handleMaterialNameFocus() {\r\n      // 重新加载材料名称建议\r\n      getMaterialOptions({ type: 'materialName' }).then(response => {\r\n        this.materialNameSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 供应商焦点事件 */\r\n    handleSupplierFocus() {\r\n      // 重新加载供应商建议\r\n      getMaterialOptions({ type: 'supplierName' }).then(response => {\r\n        this.supplierSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 材料型号焦点事件 */\r\n    handleMaterialModelFocus() {\r\n      // 重新加载材料型号建议\r\n      getMaterialOptions({ type: 'materialModel' }).then(response => {\r\n        this.materialModelSuggestions = response.data.map(item => ({ value: item }));\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 工艺类型焦点事件 */\r\n    handleProcessTypeFocus() {\r\n      // 基于当前选中的材料加载工艺类型建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取工艺类型选项\r\n        const processTypes = [...new Set(this.paramGroupList.map(item => item.processType).filter(Boolean))];\r\n        this.processTypeSuggestions = processTypes.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有工艺类型\r\n        getProcessParamGroupOptions({ type: 'processType' }).then(response => {\r\n          this.processTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数编号焦点事件 */\r\n    handleParamNumberFocus() {\r\n      // 基于当前选中的材料加载参数编号建议\r\n      if (this.currentMaterial) {\r\n        // 从当前材料的参数组中获取参数编号选项\r\n        const paramNumbers = [...new Set(this.paramGroupList.map(item => item.paramNumber).filter(Boolean))];\r\n        this.paramNumberSuggestions = paramNumbers.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中材料，加载所有参数编号\r\n        getProcessParamGroupOptions({ type: 'paramNumber' }).then(response => {\r\n          this.paramNumberSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数名称焦点事件 */\r\n    handleParamNameFocus() {\r\n      // 基于当前选中的参数组加载参数名称建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数名称选项\r\n        const paramNames = [...new Set(this.paramItemList.map(item => item.paramName).filter(Boolean))];\r\n        this.paramNameSuggestions = paramNames.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数名称\r\n        getProcessParamItemOptions({ type: 'paramName' }).then(response => {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 参数单位焦点事件 */\r\n    handleUnitFocus() {\r\n      // 基于当前选中的参数组加载参数单位建议\r\n      if (this.currentParamGroup) {\r\n        // 从当前参数组的参数明细中获取参数单位选项\r\n        const units = [...new Set(this.paramItemList.map(item => item.unit).filter(Boolean))];\r\n        this.unitSuggestions = units.map(item => ({ value: item }));\r\n      } else {\r\n        // 如果没有选中参数组，加载所有参数单位\r\n        getProcessParamItemOptions({ type: 'unit' }).then(response => {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }).catch(() => {});\r\n      }\r\n    },\r\n\r\n    /** 解析附件数据 */\r\n    parseAttachments(attachments) {\r\n      if (!attachments) {\r\n        return [];\r\n      }\r\n\r\n      // 如果已经是数组，直接返回\r\n      if (Array.isArray(attachments)) {\r\n        return attachments;\r\n      }\r\n\r\n      // 如果是字符串，按逗号分割并转换为文件对象\r\n      if (typeof attachments === 'string') {\r\n        return attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n          const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n          return {\r\n            name: fileName,\r\n            url: url.trim(),\r\n            uid: Date.now() + index,\r\n            status: 'success'\r\n          };\r\n        });\r\n      }\r\n\r\n      return [];\r\n    },\r\n\r\n    /** 查询材料列表 */\r\n    getMaterialList() {\r\n      this.materialLoading = true;\r\n      listMaterial(this.materialQueryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.materialTotal = response.total;\r\n        this.materialLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 材料查询 */\r\n    handleMaterialQuery() {\r\n      this.materialQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n    },\r\n\r\n    /** 重置材料查询 */\r\n    resetMaterialQuery() {\r\n      this.resetForm(\"materialQueryForm\");\r\n      this.handleMaterialQuery();\r\n    },\r\n\r\n    /** 材料行点击事件 */\r\n    handleMaterialClick(row) {\r\n      this.currentMaterial = row;\r\n      this.paramGroupQueryParams.materialId = row.materialId;\r\n      this.getParamGroupList();\r\n      this.paramItemList = [];\r\n      this.paramItemTotal = 0;\r\n      this.currentParamGroup = null;\r\n      // 同时选中该行\r\n      this.$refs.materialTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 材料选择变化事件 */\r\n    handleMaterialSelectionChange(selection) {\r\n      this.materialIds = selection.map(item => item.materialId);\r\n      this.materialSingle = selection.length !== 1;\r\n      this.materialMultiple = !selection.length;\r\n    },\r\n\r\n    /** 材料行样式 */\r\n    getMaterialRowClassName({row, rowIndex}) {\r\n      if (this.currentMaterial && row.materialId === this.currentMaterial.materialId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询工艺参数组列表 */\r\n    getParamGroupList() {\r\n      if (!this.currentMaterial) return;\r\n      this.paramGroupLoading = true;\r\n      listProcessParamGroup(this.paramGroupQueryParams).then(response => {\r\n        this.paramGroupList = response.rows;\r\n        this.paramGroupTotal = response.total;\r\n        this.paramGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数组查询 */\r\n    handleParamGroupQuery() {\r\n      this.paramGroupQueryParams.pageNum = 1;\r\n      this.getParamGroupList();\r\n    },\r\n\r\n    /** 重置参数组查询 */\r\n    resetParamGroupQuery() {\r\n      this.resetForm(\"paramGroupQueryForm\");\r\n      this.handleParamGroupQuery();\r\n    },\r\n\r\n    /** 工艺参数组行点击事件 */\r\n    handleParamGroupClick(row) {\r\n      this.currentParamGroup = row;\r\n      this.paramItemQueryParams.groupId = row.groupId;\r\n      this.getParamItemList();\r\n      // 同时选中该行\r\n      this.$refs.paramGroupTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数组选择变化事件 */\r\n    handleParamGroupSelectionChange(selection) {\r\n      this.paramGroupIds = selection.map(item => item.groupId);\r\n      this.paramGroupSingle = selection.length !== 1;\r\n      this.paramGroupMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数明细行点击事件 */\r\n    handleParamItemRowClick(row) {\r\n      this.$refs.paramItemTable.toggleRowSelection(row);\r\n    },\r\n\r\n    /** 参数明细选择变化事件 */\r\n    handleParamItemSelectionChange(selection) {\r\n      this.paramItemIds = selection.map(item => item.itemId);\r\n      this.paramItemSingle = selection.length !== 1;\r\n      this.paramItemMultiple = !selection.length;\r\n    },\r\n\r\n    /** 参数组行样式 */\r\n    getParamGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentParamGroup && row.groupId === this.currentParamGroup.groupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    /** 查询参数明细列表 */\r\n    getParamItemList() {\r\n      if (!this.currentParamGroup) return;\r\n      this.paramItemLoading = true;\r\n      listProcessParamItem(this.paramItemQueryParams).then(response => {\r\n        this.paramItemList = response.rows;\r\n        this.paramItemTotal = response.total;\r\n        this.paramItemLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 参数明细查询 */\r\n    handleParamItemQuery() {\r\n      this.paramItemQueryParams.pageNum = 1;\r\n      this.getParamItemList();\r\n    },\r\n\r\n    /** 重置参数明细查询 */\r\n    resetParamItemQuery() {\r\n      this.resetForm(\"paramItemQueryForm\");\r\n      this.handleParamItemQuery();\r\n    },\r\n\r\n    /** 新增材料 */\r\n    handleAddMaterial() {\r\n      this.resetMaterialForm();\r\n      this.materialOpen = true;\r\n      this.materialTitle = \"添加材料信息\";\r\n    },\r\n\r\n    /** 修改材料 */\r\n    handleEditMaterial(row) {\r\n      this.resetMaterialForm();\r\n      const materialId = row.materialId;\r\n      getMaterial(materialId).then(response => {\r\n        this.materialForm = response.data;\r\n        // 处理附件数据\r\n        this.materialFileList = this.parseAttachments(response.data.attachments);\r\n        this.materialOpen = true;\r\n        this.materialTitle = \"修改材料信息\";\r\n      });\r\n    },\r\n\r\n    /** 提交材料表单 */\r\n    submitMaterialForm() {\r\n      this.$refs[\"materialForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.materialForm.attachments = this.materialFileList.length > 0\r\n            ? this.materialFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.materialForm.materialId != null) {\r\n            // 更新操作，设置更新人\r\n            this.materialForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.materialForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.materialForm.materialId != null) {\r\n            updateMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          } else {\r\n            addMaterial(this.materialForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.materialOpen = false;\r\n              this.getMaterialList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消材料操作 */\r\n    cancelMaterial() {\r\n      this.materialOpen = false;\r\n      this.resetMaterialForm();\r\n    },\r\n\r\n    /** 重置材料表单 */\r\n    resetMaterialForm() {\r\n      this.materialForm = {\r\n        materialId: null,\r\n        materialName: null,\r\n        supplierName: null,\r\n        materialModel: null,\r\n        materialDescription: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.materialFileList = [];\r\n      this.resetForm(\"materialForm\");\r\n    },\r\n\r\n    /** 删除材料 */\r\n    handleDeleteMaterial(row) {\r\n      const materialIds = row.materialId;\r\n      this.$modal.confirm('是否确认删除材料\"' + row.materialName + '\"？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除材料 */\r\n    handleBatchDeleteMaterial() {\r\n      const materialIds = this.materialIds;\r\n      this.$modal.confirm('是否确认删除选中的' + materialIds.length + '条材料数据？').then(function() {\r\n        return delMaterial(materialIds);\r\n      }).then(() => {\r\n        this.getMaterialList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出材料 */\r\n    handleExportMaterial() {\r\n      this.download('material/material/export', {\r\n        ...this.materialQueryParams\r\n      }, `material_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 整体导出 */\r\n    handleExportComplete() {\r\n      this.$modal.loading(\"正在导出数据，请稍候...\");\r\n      this.download('material/material/exportComplete', {\r\n        ...this.materialQueryParams\r\n      }, `complete_data_${new Date().getTime()}.xlsx`).then(() => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgSuccess(\"导出成功\");\r\n      }).catch(error => {\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(\"导出失败: \" + (error.message || \"未知错误\"));\r\n      });\r\n    },\r\n\r\n    /** 新增工艺参数组 */\r\n    handleAddParamGroup() {\r\n      this.resetParamGroupForm();\r\n      this.paramGroupForm.materialId = this.currentMaterial.materialId;\r\n      this.paramGroupOpen = true;\r\n      this.paramGroupTitle = \"添加工艺参数组\";\r\n    },\r\n\r\n    /** 修改工艺参数组 */\r\n    handleEditParamGroup(row) {\r\n      this.resetParamGroupForm();\r\n      const groupId = row.groupId;\r\n      getProcessParamGroup(groupId).then(response => {\r\n        this.paramGroupForm = response.data;\r\n        // 处理附件数据\r\n        this.paramGroupFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramGroupOpen = true;\r\n        this.paramGroupTitle = \"修改工艺参数组\";\r\n      });\r\n    },\r\n\r\n    /** 提交工艺参数组表单 */\r\n    submitParamGroupForm() {\r\n      this.$refs[\"paramGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramGroupForm.attachments = this.paramGroupFileList.length > 0\r\n            ? this.paramGroupFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramGroupForm.groupId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramGroupForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramGroupForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramGroupForm.groupId != null) {\r\n            updateProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          } else {\r\n            addProcessParamGroup(this.paramGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramGroupOpen = false;\r\n              this.getParamGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消工艺参数组操作 */\r\n    cancelParamGroup() {\r\n      this.paramGroupOpen = false;\r\n      this.resetParamGroupForm();\r\n    },\r\n\r\n    /** 重置工艺参数组表单 */\r\n    resetParamGroupForm() {\r\n      this.paramGroupForm = {\r\n        groupId: null,\r\n        materialId: null,\r\n        processType: null,\r\n        paramNumber: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramGroupFileList = [];\r\n      this.resetForm(\"paramGroupForm\");\r\n    },\r\n\r\n    /** 删除工艺参数组 */\r\n    handleDeleteParamGroup(row) {\r\n      const groupIds = row.groupId;\r\n      this.$modal.confirm('是否确认删除参数组\"' + row.paramNumber + '\"？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数组 */\r\n    handleBatchDeleteParamGroup() {\r\n      const groupIds = this.paramGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + groupIds.length + '条参数组数据？').then(function() {\r\n        return delProcessParamGroup(groupIds);\r\n      }).then(() => {\r\n        this.getParamGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出工艺参数组 */\r\n    handleExportParamGroup() {\r\n      this.download('material/processParamGroup/export', {\r\n        ...this.paramGroupQueryParams\r\n      }, `param_group_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 新增参数明细 */\r\n    handleAddParamItem() {\r\n      this.resetParamItemForm();\r\n      this.paramItemForm.groupId = this.currentParamGroup.groupId;\r\n      this.paramItemOpen = true;\r\n      this.paramItemTitle = \"添加参数明细\";\r\n    },\r\n\r\n    /** 修改参数明细 */\r\n    handleEditParamItem(row) {\r\n      this.resetParamItemForm();\r\n      const itemId = row.itemId;\r\n      getProcessParamItem(itemId).then(response => {\r\n        this.paramItemForm = response.data;\r\n        // 处理附件数据\r\n        this.paramItemFileList = this.parseAttachments(response.data.attachments);\r\n        this.paramItemOpen = true;\r\n        this.paramItemTitle = \"修改参数明细\";\r\n      });\r\n    },\r\n\r\n    /** 提交参数明细表单 */\r\n    submitParamItemForm() {\r\n      this.$refs[\"paramItemForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 将文件列表转换为逗号分隔的URL字符串\r\n          this.paramItemForm.attachments = this.paramItemFileList.length > 0\r\n            ? this.paramItemFileList.map(file => file.url).join(',')\r\n            : null;\r\n\r\n          // 设置创建人和更新人\r\n          if (this.paramItemForm.itemId != null) {\r\n            // 更新操作，设置更新人\r\n            this.paramItemForm.updateBy = this.$store.state.user.name;\r\n          } else {\r\n            // 新增操作，设置创建人\r\n            this.paramItemForm.createBy = this.$store.state.user.name;\r\n          }\r\n\r\n          if (this.paramItemForm.itemId != null) {\r\n            updateProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          } else {\r\n            addProcessParamItem(this.paramItemForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.paramItemOpen = false;\r\n              this.getParamItemList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 取消参数明细操作 */\r\n    cancelParamItem() {\r\n      this.paramItemOpen = false;\r\n      this.resetParamItemForm();\r\n    },\r\n\r\n    /** 重置参数明细表单 */\r\n    resetParamItemForm() {\r\n      this.paramItemForm = {\r\n        itemId: null,\r\n        groupId: null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.paramItemFileList = [];\r\n      this.resetForm(\"paramItemForm\");\r\n    },\r\n\r\n    /** 删除参数明细 */\r\n    handleDeleteParamItem(row) {\r\n      const itemIds = row.itemId;\r\n      this.$modal.confirm('是否确认删除参数\"' + row.paramName + '\"？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 批量删除参数明细 */\r\n    handleBatchDeleteParamItem() {\r\n      const itemIds = this.paramItemIds;\r\n      this.$modal.confirm('是否确认删除选中的' + itemIds.length + '条参数明细数据？').then(function() {\r\n        return delProcessParamItem(itemIds);\r\n      }).then(() => {\r\n        this.getParamItemList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出参数明细 */\r\n    handleExportParamItem() {\r\n      this.download('material/processParamItem/export', {\r\n        ...this.paramItemQueryParams\r\n      }, `param_item_${new Date().getTime()}.xlsx`);\r\n    },\r\n\r\n    /** 材料附件上传成功 */\r\n    handleMaterialUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 材料附件移除 */\r\n    handleMaterialFileRemove(file, fileList) {\r\n      console.log('材料附件移除回调：', { file, fileList });\r\n      // 确保fileList是数组，并且正确处理空数组的情况\r\n      if (Array.isArray(fileList)) {\r\n        this.materialFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: item.status || 'success'\r\n        }));\r\n      } else {\r\n        console.error('fileList不是数组：', fileList);\r\n        this.materialFileList = [];\r\n      }\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 材料附件上传前检查 */\r\n    beforeMaterialUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数组附件上传成功 */\r\n    handleParamGroupUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramGroupFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数组附件移除 */\r\n    handleParamGroupFileRemove(file, fileList) {\r\n      this.paramGroupFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数组附件上传前检查 */\r\n    beforeParamGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 参数明细附件上传成功 */\r\n    handleParamItemUploadSuccess(response, file, fileList) {\r\n      if (response.code === 200) {\r\n        this.paramItemFileList = fileList.map(item => ({\r\n          name: item.name,\r\n          url: item.response ? item.response.url : item.url,\r\n          size: this.formatFileSize(item.size || item.raw?.size),\r\n          uid: item.uid,\r\n          status: 'success'\r\n        }));\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n      } else {\r\n        this.$modal.msgError(response.msg || \"上传失败\");\r\n      }\r\n    },\r\n\r\n    /** 参数明细附件移除 */\r\n    handleParamItemFileRemove(file, fileList) {\r\n      this.paramItemFileList = fileList.map(item => ({\r\n        name: item.name,\r\n        url: item.response ? item.response.url : item.url,\r\n        size: this.formatFileSize(item.size || item.raw?.size),\r\n        uid: item.uid,\r\n        status: item.status || 'success'\r\n      }));\r\n      this.$modal.msgSuccess(\"附件删除成功\");\r\n    },\r\n\r\n    /** 参数明细附件上传前检查 */\r\n    beforeParamItemUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    /** 查看附件 */\r\n    handleViewAttachments(attachments) {\r\n      try {\r\n        if (typeof attachments === 'string') {\r\n          // 如果是逗号分隔的URL字符串，转换为对象数组\r\n          if (attachments.includes(',') || (attachments && !attachments.startsWith('['))) {\r\n            this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n              const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n              return {\r\n                name: fileName,\r\n                url: url.trim()\r\n              };\r\n            });\r\n          } else {\r\n            // 尝试解析JSON格式\r\n            this.attachmentList = JSON.parse(attachments || '[]');\r\n          }\r\n        } else if (Array.isArray(attachments)) {\r\n          this.attachmentList = attachments;\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      } catch (e) {\r\n        // 如果JSON解析失败，尝试作为逗号分隔的字符串处理\r\n        if (typeof attachments === 'string' && attachments.trim()) {\r\n          this.attachmentList = attachments.split(',').filter(url => url.trim()).map((url, index) => {\r\n            const fileName = url.substring(url.lastIndexOf('/') + 1) || `附件${index + 1}`;\r\n            return {\r\n              name: fileName,\r\n              url: url.trim()\r\n            };\r\n          });\r\n        } else {\r\n          this.attachmentList = [];\r\n        }\r\n      }\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    /** 下载附件 */\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    /** 格式化文件大小 */\r\n    formatFileSize(size) {\r\n      if (!size) return '0 B';\r\n      if (size < 1024) {\r\n        return size + ' B';\r\n      } else if (size < 1024 * 1024) {\r\n        return (size / 1024).toFixed(2) + ' KB';\r\n      } else {\r\n        return (size / 1024 / 1024).toFixed(2) + ' MB';\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.material-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-complete-btn {\r\n  background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.export-complete-btn:hover {\r\n  background: linear-gradient(135deg, #feb47b 0%, #ff7e5f 100%);\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.material-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.material-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.material-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.supplier-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.supplier-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.description-text {\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 工艺参数组样式 */\r\n.process-type-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.process-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.process-type {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.param-number-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.param-number-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.param-number {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 参数明细样式 */\r\n.param-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.param-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.param-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n}\r\n\r\n/* 指示器样式 */\r\n.material-indicator, .param-group-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.material-indicator .el-tag, .param-group-indicator .el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n/* 通用样式 */\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAysBA,IAAAA,SAAA,GAAAC,OAAA;AAIA,IAAAC,kBAAA,GAAAD,OAAA;AAKA,IAAAE,iBAAA,GAAAF,OAAA;AAKA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACA;IACAC,IAAA;MACAC,IAAA,WAAAA,KAAAC,EAAA;QACA,IAAAC,cAAA,GAAAD,EAAA,CAAAE,aAAA;QACA,IAAAC,OAAA,GAAAH,EAAA,CAAAE,aAAA;QACAD,cAAA,CAAAG,KAAA,CAAAC,MAAA;;QAEA;QACA,IAAAC,GAAA,GAAAH,OAAA,CAAAI,YAAA,IAAAC,MAAA,CAAAC,gBAAA,CAAAN,OAAA;QAEAF,cAAA,CAAAS,WAAA,aAAAC,CAAA;UACA;UACA,IAAAC,IAAA,GAAAD,CAAA,CAAAE,OAAA,GAAAZ,cAAA,CAAAa,UAAA;UACA,IAAAC,IAAA,GAAAJ,CAAA,CAAAK,OAAA,GAAAf,cAAA,CAAAgB,SAAA;;UAEA;UACA,IAAAC,IAAA,EAAAC,IAAA;;UAEA;UACA,IAAAb,GAAA,CAAAc,IAAA,CAAAC,QAAA;YACAH,IAAA,IAAAI,QAAA,CAAAC,IAAA,CAAAC,WAAA,KAAAlB,GAAA,CAAAc,IAAA,CAAAK,OAAA;YACAN,IAAA,IAAAG,QAAA,CAAAC,IAAA,CAAAG,YAAA,KAAApB,GAAA,CAAAqB,GAAA,CAAAF,OAAA;UACA;YACAP,IAAA,IAAAZ,GAAA,CAAAc,IAAA,CAAAK,OAAA;YACAN,IAAA,IAAAb,GAAA,CAAAqB,GAAA,CAAAF,OAAA;UACA;UAEAH,QAAA,CAAAM,WAAA,aAAAjB,CAAA;YACA;YACA,IAAAkB,CAAA,GAAAlB,CAAA,CAAAE,OAAA,GAAAD,IAAA;YACA,IAAAkB,CAAA,GAAAnB,CAAA,CAAAK,OAAA,GAAAD,IAAA;;YAEA;YACAZ,OAAA,CAAAC,KAAA,CAAAgB,IAAA,MAAAW,MAAA,CAAAF,CAAA,GAAAX,IAAA;YACAf,OAAA,CAAAC,KAAA,CAAAuB,GAAA,MAAAI,MAAA,CAAAD,CAAA,GAAAX,IAAA;;YAEA;YACA;UACA;UAEAG,QAAA,CAAAU,SAAA,aAAArB,CAAA;YACAW,QAAA,CAAAM,WAAA;YACAN,QAAA,CAAAU,SAAA;UACA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MAEA;MACAC,eAAA;MACAC,YAAA;MACAC,aAAA;MACAC,eAAA;MACAC,YAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,mBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;MACA;MACAC,aAAA;QACAH,YAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAC,iBAAA;MACAC,cAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,cAAA;MACAC,eAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,qBAAA;QACApB,OAAA;QACAC,QAAA;QACAoB,UAAA;QACAC,WAAA;QACAC,WAAA;MACA;MACAC,eAAA;QACAF,WAAA,GACA;UAAAhB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAe,WAAA,GACA;UAAAjB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAiB,gBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,oBAAA;QACAnC,OAAA;QACAC,QAAA;QACAmC,OAAA;QACAC,SAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAF,SAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAgC,uBAAA;MACAC,cAAA;MAEA;MACAC,uBAAA;MACAC,mBAAA;MACAC,wBAAA;MACAC,sBAAA;MACAC,oBAAA;MACAC,sBAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA,eACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAE,KAAA;MACA;MACA,IAAAC,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAX,uBAAA,GAAAe,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAV,mBAAA,GAAAc,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAP,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAT,wBAAA,GAAAa,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAC,8CAAA;QAAAP,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAR,sBAAA,GAAAY,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAC,8CAAA;QAAAP,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAN,sBAAA,GAAAU,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAE,4CAAA;QAAAR,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAP,oBAAA,GAAAW,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;;MAEA;MACA,IAAAE,4CAAA;QAAAR,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAL,eAAA,GAAAS,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAG,4BAAA,WAAAA,6BAAAC,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAzB,uBAAA;MACA,IAAAuB,WAAA;QACAE,WAAA,QAAAzB,uBAAA,CAAA0B,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,cACAI,wBAAA,WAAAA,yBAAAN,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAxB,mBAAA;MACA,IAAAsB,WAAA;QACAE,WAAA,QAAAxB,mBAAA,CAAAyB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,eACAK,6BAAA,WAAAA,8BAAAP,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAvB,wBAAA;MACA,IAAAqB,WAAA;QACAE,WAAA,QAAAvB,wBAAA,CAAAwB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,eACAM,2BAAA,WAAAA,4BAAAR,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAtB,sBAAA;MACA,IAAAoB,WAAA;QACAE,WAAA,QAAAtB,sBAAA,CAAAuB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA;IACA;IACAO,yBAAA,WAAAA,0BAAAT,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAArB,oBAAA;MACA,IAAAmB,WAAA;QACAE,WAAA,QAAArB,oBAAA,CAAAsB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,eACAQ,2BAAA,WAAAA,4BAAAV,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAApB,sBAAA;MACA,IAAAkB,WAAA;QACAE,WAAA,QAAApB,sBAAA,CAAAqB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,eACAS,oBAAA,WAAAA,qBAAAX,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAnB,eAAA;MACA,IAAAiB,WAAA;QACAE,WAAA,QAAAnB,eAAA,CAAAoB,MAAA,WAAAT,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAS,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,eACAU,wBAAA,WAAAA,yBAAAlB,IAAA;MACA,KAAA5D,mBAAA,CAAAG,YAAA,GAAAyD,IAAA,CAAAC,KAAA;MACA;IACA;IAEA,eACAkB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAzB,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAArC,uBAAA,GAAAe,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,cACAmB,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAA3B,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAwB,MAAA,CAAAtC,mBAAA,GAAAc,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAqB,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAA7B,4BAAA;QAAAC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA0B,MAAA,CAAAvC,wBAAA,GAAAa,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA,GAAAE,KAAA;IACA;IAEA,eACAuB,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA9F,eAAA;QACA;QACA,IAAA+F,YAAA,OAAAC,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA/E,cAAA,CAAAgD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAArC,WAAA;QAAA,GAAA8C,MAAA,CAAAsB,OAAA;QACA,KAAA7C,sBAAA,GAAAyC,YAAA,CAAA5B,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA;QACA;QACA,IAAAG,8CAAA;UAAAP,IAAA;QAAA,GAAAC,IAAA,WAAAC,QAAA;UACA4B,MAAA,CAAAxC,sBAAA,GAAAY,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA,GAAAE,KAAA;MACA;IACA;IAEA,eACA8B,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAArG,eAAA;QACA;QACA,IAAAsG,YAAA,OAAAN,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA/E,cAAA,CAAAgD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAApC,WAAA;QAAA,GAAA6C,MAAA,CAAAsB,OAAA;QACA,KAAA3C,sBAAA,GAAA8C,YAAA,CAAAnC,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA;QACA;QACA,IAAAG,8CAAA;UAAAP,IAAA;QAAA,GAAAC,IAAA,WAAAC,QAAA;UACAmC,MAAA,CAAA7C,sBAAA,GAAAU,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA,GAAAE,KAAA;MACA;IACA;IAEA,eACAiC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAnF,iBAAA;QACA;QACA,IAAAoF,UAAA,OAAAT,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA/D,aAAA,CAAAgC,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAtB,SAAA;QAAA,GAAA+B,MAAA,CAAAsB,OAAA;QACA,KAAA5C,oBAAA,GAAAkD,UAAA,CAAAtC,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA;QACA;QACA,IAAAI,4CAAA;UAAAR,IAAA;QAAA,GAAAC,IAAA,WAAAC,QAAA;UACAsC,MAAA,CAAAjD,oBAAA,GAAAW,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA,GAAAE,KAAA;MACA;IACA;IAEA,eACAoC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAtF,iBAAA;QACA;QACA,IAAAuF,KAAA,OAAAZ,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA/D,aAAA,CAAAgC,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAArB,IAAA;QAAA,GAAA8B,MAAA,CAAAsB,OAAA;QACA,KAAA1C,eAAA,GAAAmD,KAAA,CAAAzC,GAAA,WAAAC,IAAA;UAAA;YAAAC,KAAA,EAAAD;UAAA;QAAA;MACA;QACA;QACA,IAAAI,4CAAA;UAAAR,IAAA;QAAA,GAAAC,IAAA,WAAAC,QAAA;UACAyC,MAAA,CAAAlD,eAAA,GAAAS,QAAA,CAAA7E,IAAA,CAAA8E,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA,GAAAE,KAAA;MACA;IACA;IAEA,aACAuC,gBAAA,WAAAA,iBAAAC,WAAA;MACA,KAAAA,WAAA;QACA;MACA;;MAEA;MACA,IAAAC,KAAA,CAAAC,OAAA,CAAAF,WAAA;QACA,OAAAA,WAAA;MACA;;MAEA;MACA,WAAAA,WAAA;QACA,OAAAA,WAAA,CAAAG,KAAA,MAAApC,MAAA,WAAAqC,GAAA;UAAA,OAAAA,GAAA,CAAAC,IAAA;QAAA,GAAAhD,GAAA,WAAA+C,GAAA,EAAAE,KAAA;UACA,IAAAC,QAAA,GAAAH,GAAA,CAAAI,SAAA,CAAAJ,GAAA,CAAAK,WAAA,6BAAApI,MAAA,CAAAiI,KAAA;UACA;YACApK,IAAA,EAAAqK,QAAA;YACAH,GAAA,EAAAA,GAAA,CAAAC,IAAA;YACAK,GAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAN,KAAA;YACAO,MAAA;UACA;QACA;MACA;MAEA;IACA;IAEA,aACAhE,eAAA,WAAAA,gBAAA;MAAA,IAAAiE,MAAA;MACA,KAAA/H,eAAA;MACA,IAAAgI,sBAAA,OAAArH,mBAAA,EAAAyD,IAAA,WAAAC,QAAA;QACA0D,MAAA,CAAA9H,YAAA,GAAAoE,QAAA,CAAA4D,IAAA;QACAF,MAAA,CAAA7H,aAAA,GAAAmE,QAAA,CAAA6D,KAAA;QACAH,MAAA,CAAA/H,eAAA;MACA;IACA;IAEA,WACAmI,mBAAA,WAAAA,oBAAA;MACA,KAAAxH,mBAAA,CAAAC,OAAA;MACA,KAAAkD,eAAA;IACA;IAEA,aACAsE,kBAAA,WAAAA,mBAAA;MACA,KAAAC,SAAA;MACA,KAAAF,mBAAA;IACA;IAEA,cACAG,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAApI,eAAA,GAAAoI,GAAA;MACA,KAAAvG,qBAAA,CAAAC,UAAA,GAAAsG,GAAA,CAAAtG,UAAA;MACA,KAAAuG,iBAAA;MACA,KAAAlG,aAAA;MACA,KAAAC,cAAA;MACA,KAAAf,iBAAA;MACA;MACA,KAAAiH,KAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAJ,GAAA;IACA;IAEA,eACAK,6BAAA,WAAAA,8BAAAC,SAAA;MACA,KAAArI,WAAA,GAAAqI,SAAA,CAAAvE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtC,UAAA;MAAA;MACA,KAAAxB,cAAA,GAAAoI,SAAA,CAAAC,MAAA;MACA,KAAApI,gBAAA,IAAAmI,SAAA,CAAAC,MAAA;IACA;IAEA,YACAC,uBAAA,WAAAA,wBAAAC,IAAA;MAAA,IAAAT,GAAA,GAAAS,IAAA,CAAAT,GAAA;QAAAU,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,SAAA9I,eAAA,IAAAoI,GAAA,CAAAtG,UAAA,UAAA9B,eAAA,CAAA8B,UAAA;QACA;MACA;MACA;IACA;IAEA,gBACAuG,iBAAA,WAAAA,kBAAA;MAAA,IAAAU,MAAA;MACA,UAAA/I,eAAA;MACA,KAAAkB,iBAAA;MACA,IAAA8H,wCAAA,OAAAnH,qBAAA,EAAAoC,IAAA,WAAAC,QAAA;QACA6E,MAAA,CAAA5H,cAAA,GAAA+C,QAAA,CAAA4D,IAAA;QACAiB,MAAA,CAAA3H,eAAA,GAAA8C,QAAA,CAAA6D,KAAA;QACAgB,MAAA,CAAA7H,iBAAA;MACA;IACA;IAEA,YACA+H,qBAAA,WAAAA,sBAAA;MACA,KAAApH,qBAAA,CAAApB,OAAA;MACA,KAAA4H,iBAAA;IACA;IAEA,cACAa,oBAAA,WAAAA,qBAAA;MACA,KAAAhB,SAAA;MACA,KAAAe,qBAAA;IACA;IAEA,iBACAE,qBAAA,WAAAA,sBAAAf,GAAA;MACA,KAAA/G,iBAAA,GAAA+G,GAAA;MACA,KAAAxF,oBAAA,CAAAC,OAAA,GAAAuF,GAAA,CAAAvF,OAAA;MACA,KAAAuG,gBAAA;MACA;MACA,KAAAd,KAAA,CAAAe,eAAA,CAAAb,kBAAA,CAAAJ,GAAA;IACA;IAEA,gBACAkB,+BAAA,WAAAA,gCAAAZ,SAAA;MACA,KAAAhH,aAAA,GAAAgH,SAAA,CAAAvE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvB,OAAA;MAAA;MACA,KAAAlB,gBAAA,GAAA+G,SAAA,CAAAC,MAAA;MACA,KAAA/G,kBAAA,IAAA8G,SAAA,CAAAC,MAAA;IACA;IAEA,gBACAY,uBAAA,WAAAA,wBAAAnB,GAAA;MACA,KAAAE,KAAA,CAAAkB,cAAA,CAAAhB,kBAAA,CAAAJ,GAAA;IACA;IAEA,iBACAqB,8BAAA,WAAAA,+BAAAf,SAAA;MACA,KAAAjG,YAAA,GAAAiG,SAAA,CAAAvE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAsF,MAAA;MAAA;MACA,KAAAhH,eAAA,GAAAgG,SAAA,CAAAC,MAAA;MACA,KAAAhG,iBAAA,IAAA+F,SAAA,CAAAC,MAAA;IACA;IAEA,aACAgB,yBAAA,WAAAA,0BAAAC,KAAA;MAAA,IAAAxB,GAAA,GAAAwB,KAAA,CAAAxB,GAAA;QAAAU,QAAA,GAAAc,KAAA,CAAAd,QAAA;MACA,SAAAzH,iBAAA,IAAA+G,GAAA,CAAAvF,OAAA,UAAAxB,iBAAA,CAAAwB,OAAA;QACA;MACA;MACA;IACA;IAEA,eACAuG,gBAAA,WAAAA,iBAAA;MAAA,IAAAS,MAAA;MACA,UAAAxI,iBAAA;MACA,KAAAa,gBAAA;MACA,IAAA4H,sCAAA,OAAAlH,oBAAA,EAAAqB,IAAA,WAAAC,QAAA;QACA2F,MAAA,CAAA1H,aAAA,GAAA+B,QAAA,CAAA4D,IAAA;QACA+B,MAAA,CAAAzH,cAAA,GAAA8B,QAAA,CAAA6D,KAAA;QACA8B,MAAA,CAAA3H,gBAAA;MACA;IACA;IAEA,aACA6H,oBAAA,WAAAA,qBAAA;MACA,KAAAnH,oBAAA,CAAAnC,OAAA;MACA,KAAA2I,gBAAA;IACA;IAEA,eACAY,mBAAA,WAAAA,oBAAA;MACA,KAAA9B,SAAA;MACA,KAAA6B,oBAAA;IACA;IAEA,WACAE,iBAAA,WAAAA,kBAAA;MACA,KAAAC,iBAAA;MACA,KAAAjK,YAAA;MACA,KAAAC,aAAA;IACA;IAEA,WACAiK,kBAAA,WAAAA,mBAAA/B,GAAA;MAAA,IAAAgC,OAAA;MACA,KAAAF,iBAAA;MACA,IAAApI,UAAA,GAAAsG,GAAA,CAAAtG,UAAA;MACA,IAAAuI,qBAAA,EAAAvI,UAAA,EAAAmC,IAAA,WAAAC,QAAA;QACAkG,OAAA,CAAAjK,YAAA,GAAA+D,QAAA,CAAA7E,IAAA;QACA;QACA+K,OAAA,CAAAhK,gBAAA,GAAAgK,OAAA,CAAAvD,gBAAA,CAAA3C,QAAA,CAAA7E,IAAA,CAAAyH,WAAA;QACAsD,OAAA,CAAAnK,YAAA;QACAmK,OAAA,CAAAlK,aAAA;MACA;IACA;IAEA,aACAoK,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAjC,KAAA,iBAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAF,OAAA,CAAApK,YAAA,CAAA2G,WAAA,GAAAyD,OAAA,CAAAnK,gBAAA,CAAAuI,MAAA,OACA4B,OAAA,CAAAnK,gBAAA,CAAA+D,GAAA,WAAAuG,IAAA;YAAA,OAAAA,IAAA,CAAAxD,GAAA;UAAA,GAAAyD,IAAA,QACA;;UAEA;UACA,IAAAJ,OAAA,CAAApK,YAAA,CAAA2B,UAAA;YACA;YACAyI,OAAA,CAAApK,YAAA,CAAAyK,QAAA,GAAAL,OAAA,CAAAM,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;YACA;YACAuN,OAAA,CAAApK,YAAA,CAAA6K,QAAA,GAAAT,OAAA,CAAAM,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;UAEA,IAAAuN,OAAA,CAAApK,YAAA,CAAA2B,UAAA;YACA,IAAAmJ,wBAAA,EAAAV,OAAA,CAAApK,YAAA,EAAA8D,IAAA,WAAAC,QAAA;cACAqG,OAAA,CAAAW,MAAA,CAAAC,UAAA;cACAZ,OAAA,CAAAtK,YAAA;cACAsK,OAAA,CAAA5G,eAAA;YACA;UACA;YACA,IAAAyH,qBAAA,EAAAb,OAAA,CAAApK,YAAA,EAAA8D,IAAA,WAAAC,QAAA;cACAqG,OAAA,CAAAW,MAAA,CAAAC,UAAA;cACAZ,OAAA,CAAAtK,YAAA;cACAsK,OAAA,CAAA5G,eAAA;YACA;UACA;QACA;MACA;IACA;IAEA,aACA0H,cAAA,WAAAA,eAAA;MACA,KAAApL,YAAA;MACA,KAAAiK,iBAAA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MACA,KAAA/J,YAAA;QACA2B,UAAA;QACAnB,YAAA;QACAC,YAAA;QACAC,aAAA;QACAyK,mBAAA;QACAxE,WAAA;QACAyE,MAAA;MACA;MACA,KAAAnL,gBAAA;MACA,KAAA8H,SAAA;IACA;IAEA,WACAsD,oBAAA,WAAAA,qBAAApD,GAAA;MAAA,IAAAqD,OAAA;MACA,IAAApL,WAAA,GAAA+H,GAAA,CAAAtG,UAAA;MACA,KAAAoJ,MAAA,CAAAQ,OAAA,eAAAtD,GAAA,CAAAzH,YAAA,SAAAsD,IAAA;QACA,WAAA0H,qBAAA,EAAAtL,WAAA;MACA,GAAA4D,IAAA;QACAwH,OAAA,CAAA9H,eAAA;QACA8H,OAAA,CAAAP,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,aACAsH,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,OAAA;MACA,IAAAxL,WAAA,QAAAA,WAAA;MACA,KAAA6K,MAAA,CAAAQ,OAAA,eAAArL,WAAA,CAAAsI,MAAA,aAAA1E,IAAA;QACA,WAAA0H,qBAAA,EAAAtL,WAAA;MACA,GAAA4D,IAAA;QACA4H,OAAA,CAAAlI,eAAA;QACAkI,OAAA,CAAAX,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,WACAwH,oBAAA,WAAAA,qBAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAA/F,OAAA,MACA,KAAAzF,mBAAA,eAAArB,MAAA,CACA,IAAAsI,IAAA,GAAAwE,OAAA;IACA;IAEA,WACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAAjB,MAAA,CAAAkB,OAAA;MACA,KAAAL,QAAA,yCAAAC,cAAA,CAAA/F,OAAA,MACA,KAAAzF,mBAAA,oBAAArB,MAAA,CACA,IAAAsI,IAAA,GAAAwE,OAAA,cAAAhI,IAAA;QACAkI,OAAA,CAAAjB,MAAA,CAAAmB,YAAA;QACAF,OAAA,CAAAjB,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA,WAAAgI,KAAA;QACAH,OAAA,CAAAjB,MAAA,CAAAmB,YAAA;QACAF,OAAA,CAAAjB,MAAA,CAAAqB,QAAA,aAAAD,KAAA,CAAAtL,OAAA;MACA;IACA;IAEA,cACAwL,mBAAA,WAAAA,oBAAA;MACA,KAAAC,mBAAA;MACA,KAAAjL,cAAA,CAAAM,UAAA,QAAA9B,eAAA,CAAA8B,UAAA;MACA,KAAAR,cAAA;MACA,KAAAC,eAAA;IACA;IAEA,cACAmL,oBAAA,WAAAA,qBAAAtE,GAAA;MAAA,IAAAuE,OAAA;MACA,KAAAF,mBAAA;MACA,IAAA5J,OAAA,GAAAuF,GAAA,CAAAvF,OAAA;MACA,IAAA+J,uCAAA,EAAA/J,OAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAyI,OAAA,CAAAnL,cAAA,GAAA0C,QAAA,CAAA7E,IAAA;QACA;QACAsN,OAAA,CAAAlL,kBAAA,GAAAkL,OAAA,CAAA9F,gBAAA,CAAA3C,QAAA,CAAA7E,IAAA,CAAAyH,WAAA;QACA6F,OAAA,CAAArL,cAAA;QACAqL,OAAA,CAAApL,eAAA;MACA;IACA;IAEA,gBACAsL,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAAxE,KAAA,mBAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAqC,OAAA,CAAAtL,cAAA,CAAAsF,WAAA,GAAAgG,OAAA,CAAArL,kBAAA,CAAAkH,MAAA,OACAmE,OAAA,CAAArL,kBAAA,CAAA0C,GAAA,WAAAuG,IAAA;YAAA,OAAAA,IAAA,CAAAxD,GAAA;UAAA,GAAAyD,IAAA,QACA;;UAEA;UACA,IAAAmC,OAAA,CAAAtL,cAAA,CAAAqB,OAAA;YACA;YACAiK,OAAA,CAAAtL,cAAA,CAAAoJ,QAAA,GAAAkC,OAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;YACA;YACA8P,OAAA,CAAAtL,cAAA,CAAAwJ,QAAA,GAAA8B,OAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;UAEA,IAAA8P,OAAA,CAAAtL,cAAA,CAAAqB,OAAA;YACA,IAAAkK,0CAAA,EAAAD,OAAA,CAAAtL,cAAA,EAAAyC,IAAA,WAAAC,QAAA;cACA4I,OAAA,CAAA5B,MAAA,CAAAC,UAAA;cACA2B,OAAA,CAAAxL,cAAA;cACAwL,OAAA,CAAAzE,iBAAA;YACA;UACA;YACA,IAAA2E,uCAAA,EAAAF,OAAA,CAAAtL,cAAA,EAAAyC,IAAA,WAAAC,QAAA;cACA4I,OAAA,CAAA5B,MAAA,CAAAC,UAAA;cACA2B,OAAA,CAAAxL,cAAA;cACAwL,OAAA,CAAAzE,iBAAA;YACA;UACA;QACA;MACA;IACA;IAEA,gBACA4E,gBAAA,WAAAA,iBAAA;MACA,KAAA3L,cAAA;MACA,KAAAmL,mBAAA;IACA;IAEA,gBACAA,mBAAA,WAAAA,oBAAA;MACA,KAAAjL,cAAA;QACAqB,OAAA;QACAf,UAAA;QACAC,WAAA;QACAC,WAAA;QACA8E,WAAA;QACAyE,MAAA;MACA;MACA,KAAA9J,kBAAA;MACA,KAAAyG,SAAA;IACA;IAEA,cACAgF,sBAAA,WAAAA,uBAAA9E,GAAA;MAAA,IAAA+E,OAAA;MACA,IAAAC,QAAA,GAAAhF,GAAA,CAAAvF,OAAA;MACA,KAAAqI,MAAA,CAAAQ,OAAA,gBAAAtD,GAAA,CAAApG,WAAA,SAAAiC,IAAA;QACA,WAAAoJ,uCAAA,EAAAD,QAAA;MACA,GAAAnJ,IAAA;QACAkJ,OAAA,CAAA9E,iBAAA;QACA8E,OAAA,CAAAjC,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,cACAgJ,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA,IAAAH,QAAA,QAAA1L,aAAA;MACA,KAAAwJ,MAAA,CAAAQ,OAAA,eAAA0B,QAAA,CAAAzE,MAAA,cAAA1E,IAAA;QACA,WAAAoJ,uCAAA,EAAAD,QAAA;MACA,GAAAnJ,IAAA;QACAsJ,OAAA,CAAAlF,iBAAA;QACAkF,OAAA,CAAArC,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,cACAkJ,sBAAA,WAAAA,uBAAA;MACA,KAAAzB,QAAA,0CAAAC,cAAA,CAAA/F,OAAA,MACA,KAAApE,qBAAA,kBAAA1C,MAAA,CACA,IAAAsI,IAAA,GAAAwE,OAAA;IACA;IAEA,aACAwB,kBAAA,WAAAA,mBAAA;MACA,KAAAC,kBAAA;MACA,KAAAnL,aAAA,CAAAM,OAAA,QAAAxB,iBAAA,CAAAwB,OAAA;MACA,KAAAR,aAAA;MACA,KAAAC,cAAA;IACA;IAEA,aACAqL,mBAAA,WAAAA,oBAAAvF,GAAA;MAAA,IAAAwF,OAAA;MACA,KAAAF,kBAAA;MACA,IAAAhE,MAAA,GAAAtB,GAAA,CAAAsB,MAAA;MACA,IAAAmE,qCAAA,EAAAnE,MAAA,EAAAzF,IAAA,WAAAC,QAAA;QACA0J,OAAA,CAAArL,aAAA,GAAA2B,QAAA,CAAA7E,IAAA;QACA;QACAuO,OAAA,CAAApL,iBAAA,GAAAoL,OAAA,CAAA/G,gBAAA,CAAA3C,QAAA,CAAA7E,IAAA,CAAAyH,WAAA;QACA8G,OAAA,CAAAvL,aAAA;QACAuL,OAAA,CAAAtL,cAAA;MACA;IACA;IAEA,eACAwL,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAzF,KAAA,kBAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAsD,OAAA,CAAAxL,aAAA,CAAAuE,WAAA,GAAAiH,OAAA,CAAAvL,iBAAA,CAAAmG,MAAA,OACAoF,OAAA,CAAAvL,iBAAA,CAAA2B,GAAA,WAAAuG,IAAA;YAAA,OAAAA,IAAA,CAAAxD,GAAA;UAAA,GAAAyD,IAAA,QACA;;UAEA;UACA,IAAAoD,OAAA,CAAAxL,aAAA,CAAAmH,MAAA;YACA;YACAqE,OAAA,CAAAxL,aAAA,CAAAqI,QAAA,GAAAmD,OAAA,CAAAlD,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;YACA;YACA+Q,OAAA,CAAAxL,aAAA,CAAAyI,QAAA,GAAA+C,OAAA,CAAAlD,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/N,IAAA;UACA;UAEA,IAAA+Q,OAAA,CAAAxL,aAAA,CAAAmH,MAAA;YACA,IAAAsE,wCAAA,EAAAD,OAAA,CAAAxL,aAAA,EAAA0B,IAAA,WAAAC,QAAA;cACA6J,OAAA,CAAA7C,MAAA,CAAAC,UAAA;cACA4C,OAAA,CAAA1L,aAAA;cACA0L,OAAA,CAAA3E,gBAAA;YACA;UACA;YACA,IAAA6E,qCAAA,EAAAF,OAAA,CAAAxL,aAAA,EAAA0B,IAAA,WAAAC,QAAA;cACA6J,OAAA,CAAA7C,MAAA,CAAAC,UAAA;cACA4C,OAAA,CAAA1L,aAAA;cACA0L,OAAA,CAAA3E,gBAAA;YACA;UACA;QACA;MACA;IACA;IAEA,eACA8E,eAAA,WAAAA,gBAAA;MACA,KAAA7L,aAAA;MACA,KAAAqL,kBAAA;IACA;IAEA,eACAA,kBAAA,WAAAA,mBAAA;MACA,KAAAnL,aAAA;QACAmH,MAAA;QACA7G,OAAA;QACAC,SAAA;QACAqL,UAAA;QACApL,IAAA;QACA+D,WAAA;QACAyE,MAAA;MACA;MACA,KAAA/I,iBAAA;MACA,KAAA0F,SAAA;IACA;IAEA,aACAkG,qBAAA,WAAAA,sBAAAhG,GAAA;MAAA,IAAAiG,OAAA;MACA,IAAAC,OAAA,GAAAlG,GAAA,CAAAsB,MAAA;MACA,KAAAwB,MAAA,CAAAQ,OAAA,eAAAtD,GAAA,CAAAtF,SAAA,SAAAmB,IAAA;QACA,WAAAsK,qCAAA,EAAAD,OAAA;MACA,GAAArK,IAAA;QACAoK,OAAA,CAAAjF,gBAAA;QACAiF,OAAA,CAAAnD,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,eACAkK,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,IAAAH,OAAA,QAAA7L,YAAA;MACA,KAAAyI,MAAA,CAAAQ,OAAA,eAAA4C,OAAA,CAAA3F,MAAA,eAAA1E,IAAA;QACA,WAAAsK,qCAAA,EAAAD,OAAA;MACA,GAAArK,IAAA;QACAwK,OAAA,CAAArF,gBAAA;QACAqF,OAAA,CAAAvD,MAAA,CAAAC,UAAA;MACA,GAAA7G,KAAA;IACA;IAEA,aACAoK,qBAAA,WAAAA,sBAAA;MACA,KAAA3C,QAAA,yCAAAC,cAAA,CAAA/F,OAAA,MACA,KAAArD,oBAAA,iBAAAzD,MAAA,CACA,IAAAsI,IAAA,GAAAwE,OAAA;IACA;IAEA,eACA0C,2BAAA,WAAAA,4BAAAzK,QAAA,EAAAwG,IAAA,EAAAkE,QAAA;MAAA,IAAAC,OAAA;MACA,IAAA3K,QAAA,CAAA4K,IAAA;QACA,KAAA1O,gBAAA,GAAAwO,QAAA,CAAAzK,GAAA,WAAAC,IAAA;UAAA,IAAA2K,SAAA;UAAA;YACA/R,IAAA,EAAAoH,IAAA,CAAApH,IAAA;YACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;YACA8H,IAAA,EAAAH,OAAA,CAAAI,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAD,SAAA,GAAA3K,IAAA,CAAA8K,GAAA,cAAAH,SAAA,uBAAAA,SAAA,CAAAC,IAAA;YACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;YACAG,MAAA;UACA;QAAA;QACA,KAAAuD,MAAA,CAAAC,UAAA;MACA;QACA,KAAAD,MAAA,CAAAqB,QAAA,CAAArI,QAAA,CAAAiL,GAAA;MACA;IACA;IAEA,aACAC,wBAAA,WAAAA,yBAAA1E,IAAA,EAAAkE,QAAA;MAAA,IAAAS,OAAA;MACAC,OAAA,CAAAC,GAAA;QAAA7E,IAAA,EAAAA,IAAA;QAAAkE,QAAA,EAAAA;MAAA;MACA;MACA,IAAA7H,KAAA,CAAAC,OAAA,CAAA4H,QAAA;QACA,KAAAxO,gBAAA,GAAAwO,QAAA,CAAAzK,GAAA,WAAAC,IAAA;UAAA,IAAAoL,UAAA;UAAA;YACAxS,IAAA,EAAAoH,IAAA,CAAApH,IAAA;YACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;YACA8H,IAAA,EAAAK,OAAA,CAAAJ,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAQ,UAAA,GAAApL,IAAA,CAAA8K,GAAA,cAAAM,UAAA,uBAAAA,UAAA,CAAAR,IAAA;YACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;YACAG,MAAA,EAAAvD,IAAA,CAAAuD,MAAA;UACA;QAAA;MACA;QACA2H,OAAA,CAAAhD,KAAA,kBAAAsC,QAAA;QACA,KAAAxO,gBAAA;MACA;MACA,KAAA8K,MAAA,CAAAC,UAAA;IACA;IAEA,gBACAsE,oBAAA,WAAAA,qBAAA/E,IAAA;MACA,IAAAgF,OAAA,GAAAhF,IAAA,CAAAsE,IAAA;MACA,KAAAU,OAAA;QACA,KAAAxE,MAAA,CAAAqB,QAAA;MACA;MACA,OAAAmD,OAAA;IACA;IAEA,gBACAC,6BAAA,WAAAA,8BAAAzL,QAAA,EAAAwG,IAAA,EAAAkE,QAAA;MAAA,IAAAgB,OAAA;MACA,IAAA1L,QAAA,CAAA4K,IAAA;QACA,KAAArN,kBAAA,GAAAmN,QAAA,CAAAzK,GAAA,WAAAC,IAAA;UAAA,IAAAyL,UAAA;UAAA;YACA7S,IAAA,EAAAoH,IAAA,CAAApH,IAAA;YACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;YACA8H,IAAA,EAAAY,OAAA,CAAAX,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAa,UAAA,GAAAzL,IAAA,CAAA8K,GAAA,cAAAW,UAAA,uBAAAA,UAAA,CAAAb,IAAA;YACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;YACAG,MAAA;UACA;QAAA;QACA,KAAAuD,MAAA,CAAAC,UAAA;MACA;QACA,KAAAD,MAAA,CAAAqB,QAAA,CAAArI,QAAA,CAAAiL,GAAA;MACA;IACA;IAEA,cACAW,0BAAA,WAAAA,2BAAApF,IAAA,EAAAkE,QAAA;MAAA,IAAAmB,OAAA;MACA,KAAAtO,kBAAA,GAAAmN,QAAA,CAAAzK,GAAA,WAAAC,IAAA;QAAA,IAAA4L,UAAA;QAAA;UACAhT,IAAA,EAAAoH,IAAA,CAAApH,IAAA;UACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;UACA8H,IAAA,EAAAe,OAAA,CAAAd,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAgB,UAAA,GAAA5L,IAAA,CAAA8K,GAAA,cAAAc,UAAA,uBAAAA,UAAA,CAAAhB,IAAA;UACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;UACAG,MAAA,EAAAvD,IAAA,CAAAuD,MAAA;QACA;MAAA;MACA,KAAAuD,MAAA,CAAAC,UAAA;IACA;IAEA,iBACA8E,sBAAA,WAAAA,uBAAAvF,IAAA;MACA,IAAAgF,OAAA,GAAAhF,IAAA,CAAAsE,IAAA;MACA,KAAAU,OAAA;QACA,KAAAxE,MAAA,CAAAqB,QAAA;MACA;MACA,OAAAmD,OAAA;IACA;IAEA,iBACAQ,4BAAA,WAAAA,6BAAAhM,QAAA,EAAAwG,IAAA,EAAAkE,QAAA;MAAA,IAAAuB,OAAA;MACA,IAAAjM,QAAA,CAAA4K,IAAA;QACA,KAAAtM,iBAAA,GAAAoM,QAAA,CAAAzK,GAAA,WAAAC,IAAA;UAAA,IAAAgM,UAAA;UAAA;YACApT,IAAA,EAAAoH,IAAA,CAAApH,IAAA;YACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;YACA8H,IAAA,EAAAmB,OAAA,CAAAlB,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAoB,UAAA,GAAAhM,IAAA,CAAA8K,GAAA,cAAAkB,UAAA,uBAAAA,UAAA,CAAApB,IAAA;YACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;YACAG,MAAA;UACA;QAAA;QACA,KAAAuD,MAAA,CAAAC,UAAA;MACA;QACA,KAAAD,MAAA,CAAAqB,QAAA,CAAArI,QAAA,CAAAiL,GAAA;MACA;IACA;IAEA,eACAkB,yBAAA,WAAAA,0BAAA3F,IAAA,EAAAkE,QAAA;MAAA,IAAA0B,OAAA;MACA,KAAA9N,iBAAA,GAAAoM,QAAA,CAAAzK,GAAA,WAAAC,IAAA;QAAA,IAAAmM,UAAA;QAAA;UACAvT,IAAA,EAAAoH,IAAA,CAAApH,IAAA;UACAkK,GAAA,EAAA9C,IAAA,CAAAF,QAAA,GAAAE,IAAA,CAAAF,QAAA,CAAAgD,GAAA,GAAA9C,IAAA,CAAA8C,GAAA;UACA8H,IAAA,EAAAsB,OAAA,CAAArB,cAAA,CAAA7K,IAAA,CAAA4K,IAAA,MAAAuB,UAAA,GAAAnM,IAAA,CAAA8K,GAAA,cAAAqB,UAAA,uBAAAA,UAAA,CAAAvB,IAAA;UACAxH,GAAA,EAAApD,IAAA,CAAAoD,GAAA;UACAG,MAAA,EAAAvD,IAAA,CAAAuD,MAAA;QACA;MAAA;MACA,KAAAuD,MAAA,CAAAC,UAAA;IACA;IAEA,kBACAqF,qBAAA,WAAAA,sBAAA9F,IAAA;MACA,IAAAgF,OAAA,GAAAhF,IAAA,CAAAsE,IAAA;MACA,KAAAU,OAAA;QACA,KAAAxE,MAAA,CAAAqB,QAAA;MACA;MACA,OAAAmD,OAAA;IACA;IAEA,WACAe,qBAAA,WAAAA,sBAAA3J,WAAA;MACA;QACA,WAAAA,WAAA;UACA;UACA,IAAAA,WAAA,CAAArI,QAAA,SAAAqI,WAAA,KAAAA,WAAA,CAAA4J,UAAA;YACA,KAAAxN,cAAA,GAAA4D,WAAA,CAAAG,KAAA,MAAApC,MAAA,WAAAqC,GAAA;cAAA,OAAAA,GAAA,CAAAC,IAAA;YAAA,GAAAhD,GAAA,WAAA+C,GAAA,EAAAE,KAAA;cACA,IAAAC,QAAA,GAAAH,GAAA,CAAAI,SAAA,CAAAJ,GAAA,CAAAK,WAAA,6BAAApI,MAAA,CAAAiI,KAAA;cACA;gBACApK,IAAA,EAAAqK,QAAA;gBACAH,GAAA,EAAAA,GAAA,CAAAC,IAAA;cACA;YACA;UACA;YACA;YACA,KAAAjE,cAAA,GAAAyN,IAAA,CAAAC,KAAA,CAAA9J,WAAA;UACA;QACA,WAAAC,KAAA,CAAAC,OAAA,CAAAF,WAAA;UACA,KAAA5D,cAAA,GAAA4D,WAAA;QACA;UACA,KAAA5D,cAAA;QACA;MACA,SAAAnF,CAAA;QACA;QACA,WAAA+I,WAAA,iBAAAA,WAAA,CAAAK,IAAA;UACA,KAAAjE,cAAA,GAAA4D,WAAA,CAAAG,KAAA,MAAApC,MAAA,WAAAqC,GAAA;YAAA,OAAAA,GAAA,CAAAC,IAAA;UAAA,GAAAhD,GAAA,WAAA+C,GAAA,EAAAE,KAAA;YACA,IAAAC,QAAA,GAAAH,GAAA,CAAAI,SAAA,CAAAJ,GAAA,CAAAK,WAAA,6BAAApI,MAAA,CAAAiI,KAAA;YACA;cACApK,IAAA,EAAAqK,QAAA;cACAH,GAAA,EAAAA,GAAA,CAAAC,IAAA;YACA;UACA;QACA;UACA,KAAAjE,cAAA;QACA;MACA;MACA,KAAAD,uBAAA;IACA;IAEA,WACA4N,kBAAA,WAAAA,mBAAA3J,GAAA,EAAAlK,IAAA;MACA,IAAA8T,IAAA,GAAApS,QAAA,CAAAqS,aAAA;MACAD,IAAA,CAAAE,IAAA,GAAA9J,GAAA;MACA4J,IAAA,CAAA/E,QAAA,GAAA/O,IAAA;MACA8T,IAAA,CAAAG,KAAA;IACA;IAEA,cACAhC,cAAA,WAAAA,eAAAD,IAAA;MACA,KAAAA,IAAA;MACA,IAAAA,IAAA;QACA,OAAAA,IAAA;MACA,WAAAA,IAAA;QACA,QAAAA,IAAA,SAAAkC,OAAA;MACA;QACA,QAAAlC,IAAA,gBAAAkC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}