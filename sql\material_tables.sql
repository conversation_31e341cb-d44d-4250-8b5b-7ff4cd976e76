-- 材料管理相关数据库表创建脚本（使用用户提供的表结构）

-- ----------------------------
-- 1、材料信息表
-- ----------------------------
DROP TABLE IF EXISTS materials;
CREATE TABLE `materials` (
  `material_id`          INT NOT NULL AUTO_INCREMENT COMMENT '材料ID（主键）',
  `material_name`        VARCHAR(100) NOT NULL COMMENT '材料名称',
  `supplier_name`        VARCHAR(100) DEFAULT NULL COMMENT '供应商名称',
  `material_model`       VARCHAR(100) DEFAULT NULL COMMENT '材料型号',
  `material_description` TEXT COMMENT '材料描述',
  `attachments`          VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`               VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`            VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`          DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`            VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`          DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`material_id`),
  KEY `idx_material_name` (`material_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='材料基本信息表';

-- ----------------------------
-- 2、工艺参数组表
-- ----------------------------
DROP TABLE IF EXISTS process_param_group;
CREATE TABLE `process_param_group` (
  `group_id`     INT NOT NULL AUTO_INCREMENT COMMENT '参数组ID（主键）',
  `material_id`  INT NOT NULL COMMENT '所属材料ID',
  `process_type` VARCHAR(50)  NOT NULL COMMENT '工艺类型',
  `param_number` VARCHAR(50)  NOT NULL COMMENT '参数编号',
  `attachments`  VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`       VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`    VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`  DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`    VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`  DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`group_id`),
  UNIQUE KEY `uk_mat_num` (`material_id`,`param_number`),
  KEY `idx_process_type` (`process_type`),
  CONSTRAINT `fk_group_material`
    FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺参数组（工艺类型+参数编号）';

-- ----------------------------
-- 3、工艺参数明细表
-- ----------------------------
DROP TABLE IF EXISTS process_param_item;
CREATE TABLE `process_param_item` (
  `item_id`     INT NOT NULL AUTO_INCREMENT COMMENT '参数明细ID（主键）',
  `group_id`    INT NOT NULL COMMENT '所属参数组ID',
  `param_name`  VARCHAR(100) NOT NULL COMMENT '参数名称',
  `param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）',
  `unit`        VARCHAR(20)   DEFAULT NULL COMMENT '参数单位',
  `attachments` VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`      VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`   VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`   VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`item_id`),
  KEY `idx_group_id` (`group_id`),
  CONSTRAINT `fk_item_group`
    FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺参数明细';

-- ----------------------------
-- 4、测试方案表
-- ----------------------------
DROP TABLE IF EXISTS test_plans;
CREATE TABLE `test_plans` (
  `test_plan_id`     INT NOT NULL AUTO_INCREMENT COMMENT '测试方案ID（主键）',
  `plan_code`        VARCHAR(50)  DEFAULT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `test_parameter`   VARCHAR(100) DEFAULT NULL COMMENT '测试参数',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_plan_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案表';

-- ----------------------------
-- 5、测试结果表
-- ----------------------------
DROP TABLE IF EXISTS test_results;
CREATE TABLE test_results (
  test_result_id        BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '测试结果ID',
  test_plan_id          BIGINT(20)      NOT NULL                   COMMENT '测试方案ID',
  group_id              BIGINT(20)      NOT NULL                   COMMENT '参数组ID',
  supplier_datasheet_val VARCHAR(100)                              COMMENT '供应商Datasheet值',
  test_value            DECIMAL(18,6)                              COMMENT '实际测试值',
  unit                  VARCHAR(50)                                COMMENT '单位',
  test_status           CHAR(1)         DEFAULT '0'                COMMENT '测试状态（0待测试 1测试中 2已完成 3异常）',
  test_date             DATE                                       COMMENT '测试日期',
  test_person           VARCHAR(100)                               COMMENT '测试人员',
  test_condition        TEXT                                       COMMENT '测试条件',
  result_description    TEXT                                       COMMENT '测试结果说明',
  attachments           TEXT                                       COMMENT '附件信息(JSON格式)',
  remark                VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  create_by             VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time           DATETIME                                   COMMENT '创建时间',
  update_by             VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time           DATETIME                                   COMMENT '更新时间',
  PRIMARY KEY (test_result_id),
  INDEX idx_test_plan_id (test_plan_id),
  INDEX idx_group_id (group_id),
  INDEX idx_test_status (test_status),
  INDEX idx_test_date (test_date)
) ENGINE=INNODB AUTO_INCREMENT=1 COMMENT = '测试结果表';

-- ----------------------------
-- 添加外键约束
-- ----------------------------
ALTER TABLE process_param_groups ADD CONSTRAINT fk_ppg_material_id
  FOREIGN KEY (material_id) REFERENCES materials(material_id) ON DELETE CASCADE;

ALTER TABLE process_param_items ADD CONSTRAINT fk_ppi_group_id
  FOREIGN KEY (group_id) REFERENCES process_param_groups(group_id) ON DELETE CASCADE;

ALTER TABLE test_plans ADD CONSTRAINT fk_tp_material_id
  FOREIGN KEY (material_id) REFERENCES materials(material_id) ON DELETE CASCADE;

ALTER TABLE test_results ADD CONSTRAINT fk_tr_test_plan_id
  FOREIGN KEY (test_plan_id) REFERENCES test_plans(plan_id) ON DELETE CASCADE;

ALTER TABLE test_results ADD CONSTRAINT fk_tr_group_id
  FOREIGN KEY (group_id) REFERENCES process_param_groups(group_id) ON DELETE CASCADE;

-- ----------------------------
-- 插入测试数据
-- ----------------------------
INSERT INTO materials (material_name, supplier_name, material_model, material_description, create_by, create_time) VALUES
('铝合金6061', '供应商A', 'AL6061-T6', '高强度铝合金材料', 'admin', NOW()),
('不锈钢304', '供应商B', 'SS304-2B', '耐腐蚀不锈钢材料', 'admin', NOW()),
('碳钢Q235', '供应商C', 'Q235B', '普通碳素结构钢', 'admin', NOW());

INSERT INTO process_param_groups (material_id, process_type, param_number, create_by, create_time) VALUES
(1, '热处理', 'HT001', 'admin', NOW()),
(1, '机械加工', 'MC001', 'admin', NOW()),
(2, '热处理', 'HT002', 'admin', NOW()),
(2, '表面处理', 'ST001', 'admin', NOW()),
(3, '热轧', 'HR001', 'admin', NOW());

INSERT INTO process_param_items (group_id, param_name, param_value, unit, create_by, create_time) VALUES
(1, '加热温度', 520.0, '℃', 'admin', NOW()),
(1, '保温时间', 2.0, 'h', 'admin', NOW()),
(1, '冷却速度', 10.0, '℃/min', 'admin', NOW()),
(2, '切削速度', 150.0, 'm/min', 'admin', NOW()),
(2, '进给量', 0.2, 'mm/r', 'admin', NOW()),
(3, '淬火温度', 1050.0, '℃', 'admin', NOW()),
(3, '回火温度', 200.0, '℃', 'admin', NOW()),
(4, '酸洗时间', 30.0, 'min', 'admin', NOW()),
(4, '钝化时间', 15.0, 'min', 'admin', NOW()),
(5, '轧制温度', 1200.0, '℃', 'admin', NOW()),
(5, '轧制压力', 500.0, 'MPa', 'admin', NOW());
