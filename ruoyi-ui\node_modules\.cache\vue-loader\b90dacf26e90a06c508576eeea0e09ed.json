{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue?vue&type=template&id=0e44e783&scoped=true", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue", "mtime": 1754278483446}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753339890378}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}