{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue?vue&type=template&id=0e44e783&scoped=true", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\config\\index.vue", "mtime": 1754285158811}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753339890378}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}