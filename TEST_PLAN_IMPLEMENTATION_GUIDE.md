# 测试方案配置模块实施指南

## 概述

本指南详细说明了如何实施新的测试方案配置模块，该模块采用上下级联的设计模式：
- **上方**：测试方案组管理（类似材料组）
- **下方**：测试参数明细管理（类似参数明细）

## 实施步骤

### 1. 数据库准备

#### 1.1 确认表结构
确保数据库中存在以下表（基于codebuddy.sql）：
- `test_plan_group` - 测试方案组表
- `test_param_item` - 测试参数明细表

#### 1.2 插入测试数据
```sql
-- 执行测试数据脚本
source sql/test_plan_group_test_data.sql;
```

### 2. 后端代码部署

#### 2.1 实体类
- `TestPlanGroup.java` - 测试方案组实体
- `TestParamItem.java` - 测试参数明细实体

#### 2.2 Mapper层
- `TestPlanGroupMapper.java` - 测试方案组Mapper接口
- `TestPlanGroupMapper.xml` - 测试方案组SQL映射
- `TestParamItemMapper.java` - 测试参数明细Mapper接口  
- `TestParamItemMapper.xml` - 测试参数明细SQL映射

#### 2.3 Service层
- `ITestPlanGroupService.java` - 测试方案组Service接口
- `TestPlanGroupServiceImpl.java` - 测试方案组Service实现
- `ITestParamItemService.java` - 测试参数明细Service接口
- `TestParamItemServiceImpl.java` - 测试参数明细Service实现

#### 2.4 Controller层
- `TestPlanGroupController.java` - 测试方案组控制器
- `TestParamItemController.java` - 测试参数明细控制器

### 3. 前端代码部署

#### 3.1 API接口文件
- `ruoyi-ui/src/api/material/testPlanGroup.js` - 测试方案组API
- `ruoyi-ui/src/api/material/testParamItem.js` - 测试参数明细API

#### 3.2 Vue组件
- `ruoyi-ui/src/views/material/testPlan/index.vue` - 主界面组件

### 4. 权限配置

#### 4.1 菜单权限
确保用户具有以下权限：
- `material:testPlanGroup:list` - 查看测试方案组
- `material:testPlanGroup:add` - 新增测试方案组
- `material:testPlanGroup:edit` - 编辑测试方案组
- `material:testPlanGroup:remove` - 删除测试方案组
- `material:testPlanGroup:export` - 导出测试方案组
- `material:testParamItem:list` - 查看测试参数明细
- `material:testParamItem:add` - 新增测试参数明细
- `material:testParamItem:edit` - 编辑测试参数明细
- `material:testParamItem:remove` - 删除测试参数明细
- `material:testParamItem:export` - 导出测试参数明细

#### 4.2 菜单配置
在系统管理 -> 菜单管理中配置测试方案配置菜单，路由指向：
```
/material/testPlan
```

### 5. 功能特性

#### 5.1 测试方案组管理
- ✅ 增删改查操作
- ✅ 批量删除
- ✅ 数据导出
- ✅ 搜索过滤（方案编号、性能类型、测试设备）
- ✅ 自动完成输入
- ✅ 附件上传和查看
- ✅ 行点击选择

#### 5.2 测试参数明细管理
- ✅ 增删改查操作
- ✅ 批量删除
- ✅ 数据导出
- ✅ 搜索过滤（参数名称、参数单位）
- ✅ 自动完成输入
- ✅ 附件上传和查看
- ✅ 字符串格式参数数值支持

#### 5.3 上下级联功能
- ✅ 点击测试方案组自动加载对应参数明细
- ✅ 选中行高亮显示
- ✅ 参数明细标题显示当前方案编号
- ✅ 未选择方案组时参数明细操作按钮禁用

### 6. 使用说明

#### 6.1 基本操作流程
1. **查看测试方案组**：进入页面后自动加载测试方案组列表
2. **选择方案组**：点击任意测试方案组行，下方自动显示该方案的参数明细
3. **管理方案组**：使用上方按钮进行新增、编辑、删除、导出操作
4. **管理参数明细**：选择方案组后，使用下方按钮管理参数明细

#### 6.2 搜索功能
- **自动完成**：输入框支持自动完成，显示历史数据选项
- **模糊搜索**：支持部分匹配搜索
- **实时更新**：搜索选项会根据数据库实时更新

#### 6.3 附件管理
- **支持格式**：jpg、png、gif、pdf、doc、docx、xls、xlsx
- **文件大小**：单个文件不超过10MB
- **多文件上传**：每个记录最多5个附件
- **附件查看**：点击"查看"按钮可查看和下载附件

### 7. 技术特点

#### 7.1 参数数值字符串支持
- 支持复杂格式：如"≥500"、"10±0.5"、"150-200"等
- 灵活录入：不限制为纯数字格式
- 向后兼容：保持原有数据显示功能

#### 7.2 响应式设计
- 表格自适应宽度
- 移动端友好
- 操作按钮合理布局

#### 7.3 性能优化
- 分页加载
- 按需查询
- 缓存搜索选项

### 8. 故障排除

#### 8.1 常见问题

**问题1：页面无法打开**
- 检查菜单路由配置是否正确
- 确认用户是否有相应权限
- 查看浏览器控制台错误信息

**问题2：API接口404错误**
- 确认后端Controller是否正确部署
- 检查@RequestMapping路径配置
- 验证权限注解是否正确

**问题3：数据不显示**
- 检查数据库表是否存在
- 确认测试数据是否插入成功
- 查看后端日志错误信息

**问题4：搜索建议不显示**
- 确认数据库中有相应数据
- 检查API接口是否正常返回
- 验证前端选项加载逻辑

#### 8.2 调试方法
1. **前端调试**：使用浏览器开发者工具查看Network和Console
2. **后端调试**：查看应用日志文件
3. **数据库调试**：直接执行SQL验证数据

### 9. 扩展建议

#### 9.1 功能扩展
- 添加测试方案模板功能
- 支持参数明细批量导入
- 增加测试结果记录功能
- 添加测试报告生成功能

#### 9.2 性能优化
- 实现数据缓存机制
- 添加搜索索引
- 优化大数据量查询

### 10. 维护说明

#### 10.1 定期维护
- 定期清理无效附件文件
- 备份重要测试方案数据
- 监控系统性能指标

#### 10.2 数据备份
```sql
-- 备份测试方案组数据
CREATE TABLE test_plan_group_backup AS SELECT * FROM test_plan_group;

-- 备份测试参数明细数据  
CREATE TABLE test_param_item_backup AS SELECT * FROM test_param_item;
```

## 总结

新的测试方案配置模块提供了完整的上下级联管理功能，支持灵活的参数数值录入，具有良好的用户体验和扩展性。按照本指南实施后，用户可以高效地管理测试方案和相关参数明细。
