# 测试方案配置问题修复验证指南

## 修复的问题

### ✅ 问题1：筛选项点击后无法显示对应候选项
**原因分析：**
- 测试方案模块的候选项实现方式与材料配置模块不一致
- 后端使用复杂的TestPlan对象参数，而不是简单的type参数
- 前端参数传递方式不正确

**修复内容：**
- **参照材料配置模块的实现方式**，使用`type`参数来区分不同选项类型
- 重构后端Controller、Service、Mapper，简化参数传递
- 修改前端调用方式，使用`{ type: 'planCode' }`等参数格式
- 添加了完善的错误处理和调试信息

### ✅ 问题2：附件功能报错
**原因分析：**
- 附件数据解析时可能出现JSON解析错误："Unexpected token 'h', "http://loc"... is not valid JSON"
- `fileList.map is not a function`错误说明fileList不是数组
- 上传成功后的数据处理逻辑有问题

**修复内容：**
- 改进了附件数据解析逻辑，支持多种数据格式
- 添加了数组类型检查，防止map函数错误
- 完善了查看附件功能的错误处理

## 修复的文件

### 后端文件：
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/material/TestPlanController.java` - 修改Controller接口
- `ruoyi-system/src/main/java/com/ruoyi/system/service/ITestPlanService.java` - 修改Service接口
- `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/TestPlanServiceImpl.java` - 修改Service实现
- `ruoyi-system/src/main/java/com/ruoyi/system/mapper/TestPlanMapper.java` - 修改Mapper接口
- `ruoyi-system/src/main/resources/mapper/system/TestPlanMapper.xml` - 重构选项查询SQL

### 前端文件：
- `ruoyi-ui/src/views/material/testPlan/index.vue` - 修改参数传递方式

### 测试数据：
- `sql/test_plan_test_data.sql` - 测试数据脚本

## 验证步骤

### 1. 筛选项候选项显示验证

**步骤：**
1. 进入测试方案管理页面
2. 点击"方案编号"输入框
3. 点击"性能类型"输入框
4. 点击"测试设备"输入框

**预期结果：**
- ✅ 每个输入框点击后应显示对应的候选项下拉列表
- ✅ 候选项应该是从数据库中实际查询的数据
- ✅ 输入部分内容时应进行模糊匹配过滤
- ✅ 不再出现一直转圈的问题

**调试方法：**
- 打开浏览器开发者工具
- 查看Network标签页，确认API请求是否正常发送
- 检查请求URL：`/material/testPlan/options?planCode=all`等

### 2. 附件查看功能验证

**步骤：**
1. 确保数据库中有带附件的测试方案数据
2. 在测试方案列表中找到有附件的记录
3. 点击"查看"按钮

**预期结果：**
- ✅ 点击查看按钮应弹出附件列表对话框
- ✅ 对话框中应显示附件文件名和下载按钮
- ✅ 点击下载按钮应能下载文件

**调试方法：**
- 打开浏览器控制台
- 查看是否有"查看附件被调用"的日志
- 检查"解析后的附件列表"日志内容

### 3. 编辑时附件显示验证

**步骤：**
1. 新增一个测试方案并上传1-2个附件
2. 保存后点击"编辑"按钮
3. 检查编辑对话框中的附件列表

**预期结果：**
- ✅ 编辑对话框应显示之前上传的附件
- ✅ 附件应显示正确的文件名
- ✅ 应能删除现有附件
- ✅ 应能添加新附件

**调试方法：**
- 查看控制台"编辑获取的数据"日志
- 检查"编辑时解析的文件列表"日志
- 确认fileList数组不为空

## 测试数据准备

### 执行测试数据脚本：
```sql
-- 连接数据库
mysql -u root -p

-- 选择数据库
USE your_database_name;

-- 执行测试数据脚本
SOURCE sql/test_plan_test_data.sql;
```

### 验证测试数据：
```sql
-- 查看插入的测试数据
SELECT plan_code, performance_type, test_equipment, 
       CASE WHEN attachments IS NULL THEN '无' ELSE '有' END as 附件
FROM test_plans;

-- 查看附件数据格式
SELECT plan_code, attachments FROM test_plans WHERE attachments IS NOT NULL;
```

## 常见问题排查

### 问题1：筛选项仍然无候选项
**排查步骤：**
1. 检查数据库中是否有测试数据
2. 检查Network请求是否返回200状态
3. 检查返回的数据格式是否正确
4. 确认前端参数传递是否正确

### 问题2：查看附件仍无反应
**排查步骤：**
1. 检查控制台是否有JavaScript错误
2. 确认数据库中attachments字段有值
3. 检查"查看附件被调用"日志是否出现
4. 确认附件数据格式是否为逗号分隔的URL

### 问题3：编辑时附件仍不显示
**排查步骤：**
1. 检查"编辑获取的数据"日志中的attachments字段
2. 确认parseAttachments方法是否正确执行
3. 检查fileList是否被正确赋值
4. 确认el-upload组件的file-list绑定是否正确

## 调试开关

**开启调试模式：**
代码中已添加console.log调试信息，可以通过浏览器控制台查看：
- 筛选项API调用情况
- 附件数据解析过程
- 编辑时数据加载情况

**关闭调试模式：**
修复完成后，可以删除或注释掉console.log语句。

## 预期的控制台日志

**正常情况下应该看到：**
```
解析附件数据： "http://example.com/file1.pdf,http://example.com/file2.jpg"
分割后的URL列表： ["http://example.com/file1.pdf", "http://example.com/file2.jpg"]
编辑获取的数据： {testPlanId: 1, planCode: "TP001", ...}
编辑时解析的文件列表： [{name: "file1.pdf", url: "http://example.com/file1.pdf", ...}]
查看附件被调用，附件数据： "http://example.com/file1.pdf,http://example.com/file2.jpg"
解析后的附件列表： [{name: "file1.pdf", url: "http://example.com/file1.pdf", ...}]
```

修复完成后，测试方案配置的所有功能应该能够正常工作。
