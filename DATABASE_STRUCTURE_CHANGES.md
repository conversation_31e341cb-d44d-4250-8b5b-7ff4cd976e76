# 数据库结构修改总结

## 修改概述

根据要求对数据库结构进行了以下主要修改：

1. **参数明细数值格式调整**：将参数数值从DECIMAL改为VARCHAR字符串格式
2. **测试方案表重新设计**：采用类似参数组和参数明细的两层结构
3. **重新添加外键约束**：确保数据完整性和关联性

## 详细修改内容

### 1. 工艺参数明细表修改

**修改前：**
```sql
`param_value` DECIMAL(20,6) DEFAULT NULL COMMENT '参数数值'
```

**修改后：**
```sql
`param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）'
```

**原因：** 支持更灵活的数值格式，包括范围值、特殊标记等

### 2. 测试方案表重新设计

#### 2.1 原测试方案表结构问题
- 单一表结构，无法支持一个方案下多个测试参数
- 测试参数字段单一，扩展性差
- 与工艺参数结构不一致，用户体验不统一

#### 2.2 新的两层结构设计

**测试方案组表 (test_plan_group)**
```sql
CREATE TABLE `test_plan_group` (
  `plan_group_id`    INT NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code`        VARCHAR(50)  NOT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案组表';
```

**测试参数明细表 (test_param_item)**
```sql
CREATE TABLE `test_param_item` (
  `test_param_id`   INT NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）',
  `plan_group_id`   INT NOT NULL COMMENT '所属测试方案组ID',
  `param_name`      VARCHAR(100) NOT NULL COMMENT '测试参数名称',
  `param_value`     VARCHAR(100) DEFAULT NULL COMMENT '测试参数数值（字符串格式）',
  `unit`            VARCHAR(20)   DEFAULT NULL COMMENT '参数单位',
  `attachments`     VARCHAR(2000) DEFAULT NULL COMMENT '附件URL',
  `remark`          VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`       VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`       VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_param_id`),
  KEY `idx_plan_group_id` (`plan_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试参数明细表';
```

### 3. 外键约束重新添加

```sql
-- 工艺参数组 -> 材料
ALTER TABLE `process_param_group` 
ADD CONSTRAINT `fk_group_material`
  FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 工艺参数明细 -> 工艺参数组
ALTER TABLE `process_param_item` 
ADD CONSTRAINT `fk_item_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 测试参数明细 -> 测试方案组
ALTER TABLE `test_param_item` 
ADD CONSTRAINT `fk_test_param_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 测试结果 -> 测试方案组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 测试结果 -> 工艺参数组
ALTER TABLE `test_results` 
ADD CONSTRAINT `fk_result_param_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;
```

### 4. 测试结果表调整

**修改：**
```sql
-- 将test_plan_id改为plan_group_id，关联新的测试方案组
ALTER TABLE `test_results` 
CHANGE COLUMN `test_plan_id` `plan_group_id` INT NOT NULL COMMENT '测试方案组ID';
```

## 新结构的优势

### 1. 一致性
- 测试方案结构与工艺参数结构保持一致
- 用户界面操作体验统一
- 数据管理方式统一

### 2. 扩展性
- 支持一个测试方案下多个测试参数
- 参数数值支持字符串格式，更灵活
- 每个参数可以独立管理附件和备注

### 3. 完整性
- 完整的外键约束确保数据一致性
- 级联删除避免孤立数据
- 索引优化查询性能

### 4. 用户体验
- 上方显示测试方案组列表
- 点击方案组后下方显示对应的测试参数明细
- 支持测试参数的增删改查操作
- 与材料配置模块操作方式完全一致

## 界面功能对应

### 测试方案组管理
- **查询筛选**：方案编号、性能类型
- **基本操作**：新增、修改、删除、批量删除
- **附件管理**：上传、查看、下载附件
- **数据导出**：支持Excel导出

### 测试参数明细管理
- **关联显示**：点击方案组后显示对应参数明细
- **参数管理**：参数名称、数值、单位的增删改查
- **附件支持**：每个参数可独立管理附件
- **批量操作**：支持批量删除参数

## 数据迁移注意事项

1. **备份现有数据**：执行修改前务必备份原有测试方案数据
2. **数据转换**：需要将原有测试方案数据转换为新的两层结构
3. **API更新**：后端API需要相应调整以支持新的数据结构
4. **前端适配**：数据录入模块需要适配新的测试方案组结构

## 执行步骤

1. **执行SQL脚本**：运行 `database_structure_update.sql`
2. **更新后端API**：创建测试方案组和测试参数明细的Controller、Service、Mapper
3. **替换前端页面**：使用新的测试方案配置页面 `newIndex.vue`
4. **更新数据录入**：修改数据录入模块以适配新结构
5. **测试验证**：全面测试新功能的正确性

通过这些修改，测试方案配置将具备与材料配置完全一致的用户体验，同时提供更强的扩展性和灵活性。
