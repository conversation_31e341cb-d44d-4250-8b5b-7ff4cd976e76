{"remainingRequest": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\src\\views\\material\\testPlan\\index.vue", "mtime": 1754285271290}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\babel.config.js", "mtime": 1753339103954}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753339879243}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753339844054}, {"path": "F:\\IDEA Project\\RuoYi-Vue\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753339879356}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_testPlanGroup", "require", "_testParamItem", "_auth", "name", "data", "planGroupLoading", "planGroupList", "planGroupTotal", "planGroupOpen", "planGroupTitle", "planGroupForm", "planGroupFileList", "planGroupIds", "planGroupSingle", "planGroupMultiple", "planGroupQueryParams", "pageNum", "pageSize", "planCode", "performanceType", "testEquipment", "planGroupRules", "required", "message", "trigger", "testParamLoading", "testParamList", "testParamTotal", "testParamOpen", "testParamTitle", "testParamForm", "testParamFileList", "testParamIds", "testParamSingle", "testParamMultiple", "testParamQueryParams", "planGroupId", "paramName", "unit", "testParamRules", "currentPlanGroup", "attachmentDialogVisible", "attachmentList", "planCodeSuggestions", "performanceTypeSuggestions", "testEquipmentSuggestions", "paramNameSuggestions", "unitSuggestions", "uploadFileUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "created", "getPlanGroupList", "loadSuggestions", "methods", "getPerformanceTypeTag", "type", "typeMap", "_this", "listTestPlanGroup", "then", "response", "rows", "total", "getTestParamList", "_this2", "listTestParamItem", "$nextTick", "updateParamFilterOptions", "_this3", "getTestPlanGroupOptions", "Array", "isArray", "map", "item", "value", "getTestParamItemOptions", "queryPlanCodeSuggestions", "queryString", "cb", "suggestions", "filter", "toLowerCase", "indexOf", "queryPerformanceTypeSuggestions", "queryTestEquipmentSuggestions", "queryParamNameSuggestions", "queryUnitSuggestions", "handlePlanCodeFocus", "_this4", "handlePerformanceTypeFocus", "_this5", "handleTestEquipmentFocus", "_this6", "handleParamNameFocus", "_this7", "paramNames", "_toConsumableArray2", "default", "Set", "handleUnitFocus", "_this8", "units", "handlePlanGroupSelectionChange", "selection", "length", "handlePlanGroupRowClick", "row", "_this9", "$refs", "planGroupTable", "toggleRowSelection", "resetForm", "getPlanGroupRowClassName", "_ref", "rowIndex", "handlePlanGroupQuery", "resetPlanGroupQuery", "handleAddPlanGroup", "resetPlanGroupForm", "handleEditPlanGroup", "_this0", "getTestPlanGroup", "parsePlanGroupAttachments", "handleCopyPlanGroup", "_this1", "handleDeletePlanGroup", "_this10", "$modal", "confirm", "delTestPlanGroup", "msgSuccess", "catch", "handleBatchDeletePlanGroup", "_this11", "handleExportPlanGroup", "download", "_objectSpread2", "concat", "Date", "getTime", "handleTestParamSelectionChange", "testParamId", "handleTestParamRowClick", "testParamTable", "handleTestParamQuery", "resetTestParamQuery", "handleAddTestParam", "resetTestParamForm", "handleEditTestParam", "_this12", "getTestParamItem", "parseTestParamAttachments", "handleDeleteTestParam", "_this13", "delTestParamItem", "handleBatchDeleteTestParam", "_this14", "handleExportTestParam", "performanceName", "attachments", "remark", "paramValue", "submitPlanGroupForm", "_this15", "validate", "valid", "file", "url", "join", "updateTestPlanGroup", "addTestPlanGroup", "submitTestParamForm", "_this16", "updateTestParamItem", "addTestParamItem", "cancelPlanGroup", "cancelTestParam", "urls", "split", "index", "substring", "lastIndexOf", "uid", "handlePlanGroupUploadSuccess", "push", "handleTestParamUploadSuccess", "handlePlanGroupUploadRemove", "findIndex", "splice", "handleTestParamUploadRemove", "beforePlanGroupUpload", "isLt10M", "size", "msgError", "beforeTestParamUpload", "handleViewPlanGroupAttachments", "viewAttachments", "handleViewTestParamAttachments", "downloadAttachment", "link", "document", "createElement", "href", "click", "handlePlanCodeSelect"], "sources": ["src/views/material/testPlan/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题和说明 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span>测试方案配置管理</span>\r\n      </div>\r\n      <div class=\"page-description\">\r\n        <p>📋 管理测试方案组和测试参数的二层级联配置系统</p>\r\n        <el-alert\r\n          title=\"使用提示：点击测试方案组行查看对应的测试参数明细\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-top: 10px;\">\r\n        </el-alert>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 测试方案组表格 -->\r\n    <el-card class=\"plan-group-card enhanced-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"header-title\">测试方案组管理</span>\r\n          <el-badge :value=\"planGroupTotal\" class=\"item-count-badge\" type=\"primary\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddPlanGroup\">\r\n            <span>新增方案组</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"planGroupMultiple\" @click=\"handleBatchDeletePlanGroup\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试方案组查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"planGroupQueryParams\" ref=\"planGroupQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.planCode\"\r\n              :fetch-suggestions=\"queryPlanCodeSuggestions\"\r\n              placeholder=\"请输入方案编号\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @select=\"handlePlanCodeSelect\"\r\n              @focus=\"handlePlanCodeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-document\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.performanceType\"\r\n              :fetch-suggestions=\"queryPerformanceTypeSuggestions\"\r\n              placeholder=\"请输入性能类型\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handlePerformanceTypeFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-lightning\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n            <el-autocomplete\r\n              v-model=\"planGroupQueryParams.testEquipment\"\r\n              :fetch-suggestions=\"queryTestEquipmentSuggestions\"\r\n              placeholder=\"请输入测试设备\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleTestEquipmentFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-cpu\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handlePlanGroupQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetPlanGroupQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试方案组表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"planGroupLoading\"\r\n          :data=\"planGroupList\"\r\n          style=\"width: 100%\"\r\n          @selection-change=\"handlePlanGroupSelectionChange\"\r\n          @row-click=\"handlePlanGroupRowClick\"\r\n          highlight-current-row\r\n          :row-class-name=\"getPlanGroupRowClassName\"\r\n          ref=\"planGroupTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试方案数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (planGroupQueryParams.pageNum - 1) * planGroupQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"planCode\" label=\"方案编号\" min-width=\"160\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"plan-code-cell\">\r\n                <i class=\"el-icon-document plan-icon\"></i>\r\n                <span class=\"plan-code\">{{ scope.row.planCode }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceType\" label=\"性能类型\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" :type=\"getPerformanceTypeTag(scope.row.performanceType)\">\r\n                {{ scope.row.performanceType }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"performanceName\" label=\"性能名称\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"performance-name-cell\">\r\n                <i class=\"el-icon-lightning performance-icon\"></i>\r\n                <span>{{ scope.row.performanceName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"testEquipment\" label=\"测试设备\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"equipment-cell\">\r\n                <i class=\"el-icon-cpu equipment-icon\"></i>\r\n                <span>{{ scope.row.testEquipment || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewPlanGroupAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleEditPlanGroup(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleCopyPlanGroup(scope.row)\" class=\"copy-btn\">\r\n                  <i class=\"el-icon-copy-document\"></i>\r\n                  复制\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDeletePlanGroup(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"planGroupTotal > 0\"\r\n        :total=\"planGroupTotal\"\r\n        :page.sync=\"planGroupQueryParams.pageNum\"\r\n        :limit.sync=\"planGroupQueryParams.pageSize\"\r\n        @pagination=\"getPlanGroupList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试参数明细表格 -->\r\n    <el-card class=\"test-param-card enhanced-card\" v-show=\"currentPlanGroup\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <i class=\"el-icon-data-line\"></i>\r\n          <span class=\"header-title\">测试参数明细</span>\r\n          <div class=\"plan-group-indicator\" v-if=\"currentPlanGroup\">\r\n            <el-tag type=\"warning\" size=\"small\">\r\n              <i class=\"el-icon-document\"></i>\r\n              {{ currentPlanGroup.planCode }}\r\n            </el-tag>\r\n          </div>\r\n          <el-badge :value=\"testParamTotal\" class=\"item-count-badge\" type=\"warning\" />\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleAddTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>新增参数</span>\r\n          </el-button>\r\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" :disabled=\"testParamMultiple || !currentPlanGroup\" @click=\"handleBatchDeleteTestParam\">\r\n            <span>批量删除</span>\r\n          </el-button>\r\n          <el-button type=\"success\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExportTestParam\" :disabled=\"!currentPlanGroup\">\r\n            <span>导出</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试参数明细查询条件 -->\r\n      <div class=\"search-section\">\r\n        <el-form :model=\"testParamQueryParams\" ref=\"testParamQueryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\" class=\"search-form\">\r\n          <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.paramName\"\r\n              :fetch-suggestions=\"queryParamNameSuggestions\"\r\n              placeholder=\"请输入参数名称\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleParamNameFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-data-line\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"参数单位\" prop=\"unit\">\r\n            <el-autocomplete\r\n              v-model=\"testParamQueryParams.unit\"\r\n              :fetch-suggestions=\"queryUnitSuggestions\"\r\n              placeholder=\"请输入参数单位\"\r\n              clearable\r\n              style=\"width: 220px;\"\r\n              @focus=\"handleUnitFocus\"\r\n              :trigger-on-focus=\"true\"\r\n              prefix-icon=\"el-icon-price-tag\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleTestParamQuery\" class=\"search-btn\">\r\n              <span>搜索</span>\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetTestParamQuery\" class=\"reset-btn\">\r\n              <span>重置</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 测试参数明细表格 -->\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          v-loading=\"testParamLoading\"\r\n          :data=\"testParamList\"\r\n          @selection-change=\"handleTestParamSelectionChange\"\r\n          @row-click=\"handleTestParamRowClick\"\r\n          highlight-current-row\r\n          style=\"width: 100%\"\r\n          :row-class-name=\"getTestParamRowClassName\"\r\n          ref=\"testParamTable\"\r\n          class=\"enhanced-table\"\r\n          element-loading-text=\"正在加载测试参数数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"index-number\">{{ scope.$index + 1 + (testParamQueryParams.pageNum - 1) * testParamQueryParams.pageSize }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramName\" label=\"参数名称\" min-width=\"150\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"test-param-name-cell\">\r\n                <i class=\"el-icon-data-line test-param-icon\"></i>\r\n                <span class=\"test-param-name\">{{ scope.row.paramName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"paramValue\" label=\"参数数值\" width=\"120\" align=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"test-param-value\" v-if=\"scope.row.paramValue !== null\">{{ scope.row.paramValue }}</span>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"small\" type=\"success\" v-if=\"scope.row.unit\">{{ scope.row.unit }}</el-tag>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.createBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.createTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateBy\" label=\"更新人\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ scope.row.updateBy || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"附件\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-if=\"scope.row.attachments && scope.row.attachments.trim()\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click.stop=\"handleViewTestParamAttachments(scope.row.attachments)\"\r\n                class=\"attachment-btn\"\r\n              >\r\n                <i class=\"el-icon-paperclip\"></i>\r\n                查看\r\n              </el-button>\r\n              <span v-else class=\"empty-data\">-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleEditTestParam(scope.row)\" class=\"edit-btn\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click.stop=\"handleDeleteTestParam(scope.row)\" class=\"delete-btn\">\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"testParamTotal > 0\"\r\n        :total=\"testParamTotal\"\r\n        :page.sync=\"testParamQueryParams.pageNum\"\r\n        :limit.sync=\"testParamQueryParams.pageSize\"\r\n        @pagination=\"getTestParamList\"\r\n        style=\"margin-top: 15px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 测试方案组对话框 -->\r\n    <el-dialog :title=\"planGroupTitle\" :visible.sync=\"planGroupOpen\" width=\"800px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"planGroupForm\" :model=\"planGroupForm\" :rules=\"planGroupRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"方案编号\" prop=\"planCode\">\r\n              <el-input v-model=\"planGroupForm.planCode\" placeholder=\"请输入方案编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能类型\" prop=\"performanceType\">\r\n              <el-input v-model=\"planGroupForm.performanceType\" placeholder=\"请输入性能类型\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"性能名称\" prop=\"performanceName\">\r\n              <el-input v-model=\"planGroupForm.performanceName\" placeholder=\"请输入性能名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"测试设备\" prop=\"testEquipment\">\r\n              <el-input v-model=\"planGroupForm.testEquipment\" placeholder=\"请输入测试设备\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"planGroupUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"planGroupFileList\"\r\n            :on-success=\"handlePlanGroupUploadSuccess\"\r\n            :on-remove=\"handlePlanGroupUploadRemove\"\r\n            :before-upload=\"beforePlanGroupUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"planGroupForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPlanGroupForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPlanGroup\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 测试参数明细对话框 -->\r\n    <el-dialog :title=\"testParamTitle\" :visible.sync=\"testParamOpen\" width=\"600px\" append-to-body v-dialogDrag>\r\n      <el-form ref=\"testParamForm\" :model=\"testParamForm\" :rules=\"testParamRules\" label-width=\"100px\">\r\n        <el-form-item label=\"参数名称\" prop=\"paramName\">\r\n          <el-input v-model=\"testParamForm.paramName\" placeholder=\"请输入参数名称\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数数值\" prop=\"paramValue\">\r\n              <el-input v-model=\"testParamForm.paramValue\" placeholder=\"请输入参数数值（支持文本格式）\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"参数单位\" prop=\"unit\">\r\n              <el-input v-model=\"testParamForm.unit\" placeholder=\"请输入参数单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"附件上传\">\r\n          <el-upload\r\n            ref=\"testParamUpload\"\r\n            :limit=\"5\"\r\n            accept=\".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx\"\r\n            :action=\"uploadFileUrl\"\r\n            :headers=\"uploadHeaders\"\r\n            :file-list=\"testParamFileList\"\r\n            :on-success=\"handleTestParamUploadSuccess\"\r\n            :on-remove=\"handleTestParamUploadRemove\"\r\n            :before-upload=\"beforeTestParamUpload\"\r\n            multiple\r\n          >\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"testParamForm.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitTestParamForm\">确 定</el-button>\r\n        <el-button @click=\"cancelTestParam\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 附件查看对话框 -->\r\n    <el-dialog title=\"附件列表\" :visible.sync=\"attachmentDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"attachmentList\" style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"文件名\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadAttachment(scope.row.url, scope.row.name)\">下载</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listTestPlanGroup, getTestPlanGroup, delTestPlanGroup, addTestPlanGroup, updateTestPlanGroup,\r\n  exportTestPlanGroup, getTestPlanGroupOptions\r\n} from \"@/api/material/testPlanGroup\";\r\nimport {\r\n  listTestParamItem, getTestParamItem, delTestParamItem, addTestParamItem, updateTestParamItem,\r\n  exportTestParamItem, getTestParamItemOptions, listByPlanGroupId\r\n} from \"@/api/material/testParamItem\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"TestPlan\",\r\n  data() {\r\n    return {\r\n      // 测试方案组相关\r\n      planGroupLoading: false,\r\n      planGroupList: [],\r\n      planGroupTotal: 0,\r\n      planGroupOpen: false,\r\n      planGroupTitle: \"\",\r\n      planGroupForm: {},\r\n      planGroupFileList: [],\r\n      planGroupIds: [],\r\n      planGroupSingle: true,\r\n      planGroupMultiple: true,\r\n      planGroupQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planCode: null,\r\n        performanceType: null,\r\n        testEquipment: null\r\n      },\r\n      planGroupRules: {\r\n        planCode: [\r\n          { required: true, message: \"方案编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        performanceType: [\r\n          { required: true, message: \"性能类型不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 测试参数明细相关\r\n      testParamLoading: false,\r\n      testParamList: [],\r\n      testParamTotal: 0,\r\n      testParamOpen: false,\r\n      testParamTitle: \"\",\r\n      testParamForm: {},\r\n      testParamFileList: [],\r\n      testParamIds: [],\r\n      testParamSingle: true,\r\n      testParamMultiple: true,\r\n      testParamQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planGroupId: null,\r\n        paramName: null,\r\n        unit: null\r\n      },\r\n      testParamRules: {\r\n        paramName: [\r\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n\r\n      // 当前选中的测试方案组\r\n      currentPlanGroup: null,\r\n\r\n      // 附件查看\r\n      attachmentDialogVisible: false,\r\n      attachmentList: [],\r\n\r\n      // 搜索建议数据\r\n      planCodeSuggestions: [],\r\n      performanceTypeSuggestions: [],\r\n      testEquipmentSuggestions: [],\r\n      paramNameSuggestions: [],\r\n      unitSuggestions: [],\r\n\r\n      // 上传相关\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getPlanGroupList();\r\n    this.loadSuggestions();\r\n  },\r\n  methods: {\r\n    /** 获取性能类型标签颜色 */\r\n    getPerformanceTypeTag(type) {\r\n      const typeMap = {\r\n        '力学性能': 'success',\r\n        '电学性能': 'primary',\r\n        '热学性能': 'warning',\r\n        '光学性能': 'info',\r\n        '化学性能': 'danger',\r\n        '物理性能': ''\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 查询测试方案组列表 */\r\n    getPlanGroupList() {\r\n      this.planGroupLoading = true;\r\n      listTestPlanGroup(this.planGroupQueryParams).then(response => {\r\n        this.planGroupList = response.rows;\r\n        this.planGroupTotal = response.total;\r\n        this.planGroupLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询测试参数明细列表 */\r\n    getTestParamList() {\r\n      if (!this.currentPlanGroup) return;\r\n\r\n      this.testParamLoading = true;\r\n      this.testParamQueryParams.planGroupId = this.currentPlanGroup.planGroupId;\r\n      listTestParamItem(this.testParamQueryParams).then(response => {\r\n        this.testParamList = response.rows;\r\n        this.testParamTotal = response.total;\r\n        this.testParamLoading = false;\r\n\r\n        // 更新参数筛选选项\r\n        this.$nextTick(() => {\r\n          this.updateParamFilterOptions();\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 加载搜索建议数据 */\r\n    loadSuggestions() {\r\n      // 获取方案编号建议\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取性能类型建议\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取测试设备建议\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数名称建议\r\n      getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n\r\n      // 获取参数单位建议\r\n      getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 搜索建议方法 */\r\n    queryPlanCodeSuggestions(queryString, cb) {\r\n      let suggestions = this.planCodeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.planCodeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryPerformanceTypeSuggestions(queryString, cb) {\r\n      let suggestions = this.performanceTypeSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.performanceTypeSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryTestEquipmentSuggestions(queryString, cb) {\r\n      let suggestions = this.testEquipmentSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.testEquipmentSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryParamNameSuggestions(queryString, cb) {\r\n      let suggestions = this.paramNameSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.paramNameSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    queryUnitSuggestions(queryString, cb) {\r\n      let suggestions = this.unitSuggestions;\r\n      if (queryString) {\r\n        suggestions = this.unitSuggestions.filter(item => {\r\n          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;\r\n        });\r\n      }\r\n      cb(suggestions);\r\n    },\r\n\r\n    /** 焦点事件 */\r\n    handlePlanCodeFocus() {\r\n      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.planCodeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handlePerformanceTypeFocus() {\r\n      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleTestEquipmentFocus() {\r\n      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {\r\n        if (response.data && Array.isArray(response.data)) {\r\n          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));\r\n        }\r\n      });\r\n    },\r\n\r\n    handleParamNameFocus() {\r\n      // 只获取当前选中测试方案组下的参数名称选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数名称\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数名称\r\n        getTestParamItemOptions({ type: 'paramName' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.paramNameSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleUnitFocus() {\r\n      // 只获取当前选中测试方案组下的参数单位选项\r\n      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {\r\n        // 从当前显示的测试参数列表中提取参数单位\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      } else {\r\n        // 如果没有选中方案组，获取所有参数单位\r\n        getTestParamItemOptions({ type: 'unit' }).then(response => {\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.unitSuggestions = response.data.map(item => ({ value: item }));\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 测试方案组相关方法 */\r\n    handlePlanGroupSelectionChange(selection) {\r\n      this.planGroupIds = selection.map(item => item.planGroupId);\r\n      this.planGroupSingle = selection.length !== 1;\r\n      this.planGroupMultiple = !selection.length;\r\n    },\r\n\r\n    handlePlanGroupRowClick(row) {\r\n      this.currentPlanGroup = row;\r\n      this.$refs.planGroupTable.toggleRowSelection(row);\r\n\r\n      // 清空测试参数筛选条件\r\n      this.testParamQueryParams.paramName = null;\r\n      this.testParamQueryParams.unit = null;\r\n      this.resetForm(\"testParamQueryForm\");\r\n\r\n      // 加载测试参数列表\r\n      this.getTestParamList();\r\n\r\n      // 延迟更新筛选选项，确保testParamList已加载\r\n      this.$nextTick(() => {\r\n        this.updateParamFilterOptions();\r\n      });\r\n    },\r\n\r\n    getPlanGroupRowClassName({row, rowIndex}) {\r\n      if (this.currentPlanGroup && row.planGroupId === this.currentPlanGroup.planGroupId) {\r\n        return 'current-row';\r\n      }\r\n      return '';\r\n    },\r\n\r\n    handlePlanGroupQuery() {\r\n      this.planGroupQueryParams.pageNum = 1;\r\n      this.getPlanGroupList();\r\n    },\r\n\r\n    resetPlanGroupQuery() {\r\n      this.resetForm(\"planGroupQueryForm\");\r\n      this.handlePlanGroupQuery();\r\n    },\r\n\r\n    handleAddPlanGroup() {\r\n      this.resetPlanGroupForm();\r\n      this.planGroupOpen = true;\r\n      this.planGroupTitle = \"添加测试方案组\";\r\n    },\r\n\r\n    handleEditPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId || this.planGroupIds;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"修改测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleCopyPlanGroup(row) {\r\n      this.resetPlanGroupForm();\r\n      const planGroupId = row.planGroupId;\r\n      getTestPlanGroup(planGroupId).then(response => {\r\n        this.planGroupForm = response.data;\r\n        this.planGroupForm.planGroupId = null;\r\n        this.planGroupForm.planCode = this.planGroupForm.planCode + \"_copy\";\r\n        this.parsePlanGroupAttachments();\r\n        this.planGroupOpen = true;\r\n        this.planGroupTitle = \"复制测试方案组\";\r\n      });\r\n    },\r\n\r\n    handleDeletePlanGroup(row) {\r\n      const planGroupIds = row.planGroupId || this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除测试方案组编号为\"' + row.planCode + '\"的数据项？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        if (this.currentPlanGroup && this.currentPlanGroup.planGroupId === row.planGroupId) {\r\n          this.currentPlanGroup = null;\r\n          this.testParamList = [];\r\n        }\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeletePlanGroup() {\r\n      const planGroupIds = this.planGroupIds;\r\n      this.$modal.confirm('是否确认删除选中的' + planGroupIds.length + '条数据？').then(function() {\r\n        return delTestPlanGroup(planGroupIds);\r\n      }).then(() => {\r\n        this.getPlanGroupList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        this.currentPlanGroup = null;\r\n        this.testParamList = [];\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportPlanGroup() {\r\n      this.download('material/testPlanGroup/export', {\r\n        ...this.planGroupQueryParams\r\n      }, `test_plan_group_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 更新参数筛选选项 */\r\n    updateParamFilterOptions() {\r\n      if (this.currentPlanGroup && this.testParamList.length > 0) {\r\n        // 从当前测试参数列表中提取唯一的参数名称和单位\r\n        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];\r\n        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];\r\n\r\n        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));\r\n        this.unitSuggestions = units.map(unit => ({ value: unit }));\r\n      }\r\n    },\r\n\r\n    /** 测试参数明细相关方法 */\r\n    handleTestParamSelectionChange(selection) {\r\n      this.testParamIds = selection.map(item => item.testParamId);\r\n      this.testParamSingle = selection.length !== 1;\r\n      this.testParamMultiple = !selection.length;\r\n    },\r\n\r\n    handleTestParamRowClick(row) {\r\n      this.$refs.testParamTable.toggleRowSelection(row);\r\n    },\r\n\r\n    handleTestParamQuery() {\r\n      this.testParamQueryParams.pageNum = 1;\r\n      this.getTestParamList();\r\n    },\r\n\r\n    resetTestParamQuery() {\r\n      this.resetForm(\"testParamQueryForm\");\r\n      this.handleTestParamQuery();\r\n    },\r\n\r\n    handleAddTestParam() {\r\n      this.resetTestParamForm();\r\n      this.testParamOpen = true;\r\n      this.testParamTitle = \"添加测试参数明细\";\r\n    },\r\n\r\n    handleEditTestParam(row) {\r\n      this.resetTestParamForm();\r\n      const testParamId = row.testParamId || this.testParamIds;\r\n      getTestParamItem(testParamId).then(response => {\r\n        this.testParamForm = response.data;\r\n        this.parseTestParamAttachments();\r\n        this.testParamOpen = true;\r\n        this.testParamTitle = \"修改测试参数明细\";\r\n      });\r\n    },\r\n\r\n    handleDeleteTestParam(row) {\r\n      const testParamIds = row.testParamId || this.testParamIds;\r\n      this.$modal.confirm('是否确认删除参数名称为\"' + row.paramName + '\"的数据项？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleBatchDeleteTestParam() {\r\n      const testParamIds = this.testParamIds;\r\n      this.$modal.confirm('是否确认删除选中的' + testParamIds.length + '条数据？').then(function() {\r\n        return delTestParamItem(testParamIds);\r\n      }).then(() => {\r\n        this.getTestParamList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    handleExportTestParam() {\r\n      this.download('material/testParamItem/export', {\r\n        ...this.testParamQueryParams\r\n      }, `test_param_item_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 表单相关方法 */\r\n    resetPlanGroupForm() {\r\n      this.planGroupForm = {\r\n        planGroupId: null,\r\n        planCode: null,\r\n        performanceType: null,\r\n        performanceName: null,\r\n        testEquipment: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.planGroupFileList = [];\r\n      this.resetForm(\"planGroupForm\");\r\n    },\r\n\r\n    resetTestParamForm() {\r\n      this.testParamForm = {\r\n        testParamId: null,\r\n        planGroupId: this.currentPlanGroup ? this.currentPlanGroup.planGroupId : null,\r\n        paramName: null,\r\n        paramValue: null,\r\n        unit: null,\r\n        attachments: null,\r\n        remark: null\r\n      };\r\n      this.testParamFileList = [];\r\n      this.resetForm(\"testParamForm\");\r\n    },\r\n\r\n    submitPlanGroupForm() {\r\n      this.$refs[\"planGroupForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.planGroupForm.attachments = this.planGroupFileList.map(file => file.url).join(',');\r\n          if (this.planGroupForm.planGroupId != null) {\r\n            updateTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          } else {\r\n            addTestPlanGroup(this.planGroupForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.planGroupOpen = false;\r\n              this.getPlanGroupList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    submitTestParamForm() {\r\n      this.$refs[\"testParamForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.testParamForm.attachments = this.testParamFileList.map(file => file.url).join(',');\r\n          if (this.testParamForm.testParamId != null) {\r\n            updateTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          } else {\r\n            addTestParamItem(this.testParamForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.testParamOpen = false;\r\n              this.getTestParamList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    cancelPlanGroup() {\r\n      this.planGroupOpen = false;\r\n      this.resetPlanGroupForm();\r\n    },\r\n\r\n    cancelTestParam() {\r\n      this.testParamOpen = false;\r\n      this.resetTestParamForm();\r\n    },\r\n\r\n    /** 附件相关方法 */\r\n    parsePlanGroupAttachments() {\r\n      if (this.planGroupForm.attachments) {\r\n        const urls = this.planGroupForm.attachments.split(',');\r\n        this.planGroupFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    parseTestParamAttachments() {\r\n      if (this.testParamForm.attachments) {\r\n        const urls = this.testParamForm.attachments.split(',');\r\n        this.testParamFileList = urls.map((url, index) => ({\r\n          name: url.substring(url.lastIndexOf('/') + 1),\r\n          url: url,\r\n          uid: index\r\n        }));\r\n      }\r\n    },\r\n\r\n    handlePlanGroupUploadSuccess(response, file) {\r\n      this.planGroupFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handleTestParamUploadSuccess(response, file) {\r\n      this.testParamFileList.push({\r\n        name: file.name,\r\n        url: response.url,\r\n        uid: file.uid\r\n      });\r\n    },\r\n\r\n    handlePlanGroupUploadRemove(file) {\r\n      const index = this.planGroupFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.planGroupFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    handleTestParamUploadRemove(file) {\r\n      const index = this.testParamFileList.findIndex(item => item.uid === file.uid);\r\n      if (index > -1) {\r\n        this.testParamFileList.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    beforePlanGroupUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    beforeTestParamUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n\r\n    handleViewPlanGroupAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    handleViewTestParamAttachments(attachments) {\r\n      this.viewAttachments(attachments);\r\n    },\r\n\r\n    viewAttachments(attachments) {\r\n      if (!attachments) return;\r\n      const urls = attachments.split(',');\r\n      this.attachmentList = urls.map(url => ({\r\n        name: url.substring(url.lastIndexOf('/') + 1),\r\n        url: url\r\n      }));\r\n      this.attachmentDialogVisible = true;\r\n    },\r\n\r\n    downloadAttachment(url, name) {\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = name;\r\n      link.click();\r\n    },\r\n\r\n    handlePlanCodeSelect(item) {\r\n      this.planGroupQueryParams.planCode = item.value;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n  font-size: 28px;\r\n}\r\n\r\n.page-description p {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 增强卡片样式 */\r\n.enhanced-card {\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e4e7ed;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.enhanced-card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left i {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n}\r\n\r\n.header-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.item-count-badge {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.header-right .el-button {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.search-form .el-autocomplete {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 增强表格样式 */\r\n.enhanced-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-table .el-table__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.enhanced-table .el-table__header th {\r\n  background: transparent;\r\n  color: white;\r\n  font-weight: 600;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-table .el-table__body tr:hover > td {\r\n  background-color: #f0f9ff !important;\r\n}\r\n\r\n.enhanced-table .current-row {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n.enhanced-table .current-row:hover > td {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.index-number {\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.plan-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.plan-code {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.performance-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.performance-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.equipment-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.equipment-icon {\r\n  color: #67C23A;\r\n}\r\n\r\n.user-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.user-info i, .time-info i {\r\n  color: #909399;\r\n}\r\n\r\n.attachment-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.attachment-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.empty-data {\r\n  color: #C0C4CC;\r\n  font-style: italic;\r\n}\r\n\r\n/* 测试参数明细样式 */\r\n.test-param-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.test-param-icon {\r\n  color: #E6A23C;\r\n}\r\n\r\n.test-param-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.test-param-value {\r\n  font-weight: 600;\r\n  color: #67C23A;\r\n}\r\n\r\n/* 指示器样式 */\r\n.plan-group-indicator {\r\n  margin-left: 10px;\r\n}\r\n\r\n.plan-group-indicator .el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #66b1ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.copy-btn {\r\n  color: #67C23A;\r\n  font-weight: 500;\r\n}\r\n\r\n.copy-btn:hover {\r\n  color: #85ce61;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C;\r\n  font-weight: 500;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #f78989;\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 统一按钮样式 */\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--success {\r\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--info {\r\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--warning {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-button--danger {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAogBA,IAAAA,cAAA,GAAAC,OAAA;AAIA,IAAAC,cAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,oBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,eAAA;QACAC,aAAA;MACA;MACAC,cAAA;QACAH,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,eAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAC,gBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,oBAAA;QACAnB,OAAA;QACAC,QAAA;QACAmB,WAAA;QACAC,SAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAF,SAAA,GACA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAgB,gBAAA;MAEA;MACAC,uBAAA;MACAC,cAAA;MAEA;MACAC,mBAAA;MACAC,0BAAA;MACAC,wBAAA;MACAC,oBAAA;MACAC,eAAA;MAEA;MACAC,aAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA,iBACAC,qBAAA,WAAAA,sBAAAC,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA,gBACAJ,gBAAA,WAAAA,iBAAA;MAAA,IAAAM,KAAA;MACA,KAAAzD,gBAAA;MACA,IAAA0D,gCAAA,OAAAhD,oBAAA,EAAAiD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxD,aAAA,GAAA2D,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvD,cAAA,GAAA0D,QAAA,CAAAE,KAAA;QACAL,KAAA,CAAAzD,gBAAA;MACA;IACA;IAEA,iBACA+D,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,UAAA7B,gBAAA;MAEA,KAAAf,gBAAA;MACA,KAAAU,oBAAA,CAAAC,WAAA,QAAAI,gBAAA,CAAAJ,WAAA;MACA,IAAAkC,gCAAA,OAAAnC,oBAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAA3C,aAAA,GAAAuC,QAAA,CAAAC,IAAA;QACAG,MAAA,CAAA1C,cAAA,GAAAsC,QAAA,CAAAE,KAAA;QACAE,MAAA,CAAA5C,gBAAA;;QAEA;QACA4C,MAAA,CAAAE,SAAA;UACAF,MAAA,CAAAG,wBAAA;QACA;MACA;IACA;IAEA,eACAf,eAAA,WAAAA,gBAAA;MAAA,IAAAgB,MAAA;MACA;MACA,IAAAC,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAqE,MAAA,CAAA9B,mBAAA,GAAAsB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;;MAEA;MACA,IAAAJ,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAqE,MAAA,CAAA7B,0BAAA,GAAAqB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;;MAEA;MACA,IAAAJ,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAqE,MAAA,CAAA5B,wBAAA,GAAAoB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;;MAEA;MACA,IAAAE,sCAAA;QAAApB,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAqE,MAAA,CAAA3B,oBAAA,GAAAmB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;;MAEA;MACA,IAAAE,sCAAA;QAAApB,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAqE,MAAA,CAAA1B,eAAA,GAAAkB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;IACA;IAEA,aACAG,wBAAA,WAAAA,yBAAAC,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAzC,mBAAA;MACA,IAAAuC,WAAA;QACAE,WAAA,QAAAzC,mBAAA,CAAA0C,MAAA,WAAAP,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAO,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEAI,+BAAA,WAAAA,gCAAAN,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAxC,0BAAA;MACA,IAAAsC,WAAA;QACAE,WAAA,QAAAxC,0BAAA,CAAAyC,MAAA,WAAAP,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAO,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEAK,6BAAA,WAAAA,8BAAAP,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAvC,wBAAA;MACA,IAAAqC,WAAA;QACAE,WAAA,QAAAvC,wBAAA,CAAAwC,MAAA,WAAAP,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAO,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEAM,yBAAA,WAAAA,0BAAAR,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAtC,oBAAA;MACA,IAAAoC,WAAA;QACAE,WAAA,QAAAtC,oBAAA,CAAAuC,MAAA,WAAAP,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAO,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEAO,oBAAA,WAAAA,qBAAAT,WAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAArC,eAAA;MACA,IAAAmC,WAAA;QACAE,WAAA,QAAArC,eAAA,CAAAsC,MAAA,WAAAP,IAAA;UACA,OAAAA,IAAA,CAAAC,KAAA,CAAAO,WAAA,GAAAC,OAAA,CAAAL,WAAA,CAAAI,WAAA;QACA;MACA;MACAH,EAAA,CAAAC,WAAA;IACA;IAEA,WACAQ,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAnB,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACAyF,MAAA,CAAAlD,mBAAA,GAAAsB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;IACA;IAEAgB,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,MAAA;MACA,IAAArB,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACA2F,MAAA,CAAAnD,0BAAA,GAAAqB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;IACA;IAEAkB,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA,IAAAvB,sCAAA;QAAAd,IAAA;MAAA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;UACA6F,MAAA,CAAApD,wBAAA,GAAAoB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;YAAA;cAAAC,KAAA,EAAAD;YAAA;UAAA;QACA;MACA;IACA;IAEAoB,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA3D,gBAAA,SAAAA,gBAAA,CAAAJ,WAAA;QACA;QACA,IAAAgE,UAAA,OAAAC,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA7E,aAAA,CAAAmD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAzC,SAAA;QAAA,GAAAgD,MAAA,WAAAlF,IAAA;UAAA,OAAAA,IAAA;QAAA;QACA,KAAA2C,oBAAA,GAAAsD,UAAA,CAAAvB,GAAA,WAAA1E,IAAA;UAAA;YAAA4E,KAAA,EAAA5E;UAAA;QAAA;MACA;QACA;QACA,IAAA6E,sCAAA;UAAApB,IAAA;QAAA,GAAAI,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;YACA+F,MAAA,CAAArD,oBAAA,GAAAmB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;cAAA;gBAAAC,KAAA,EAAAD;cAAA;YAAA;UACA;QACA;MACA;IACA;IAEA0B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAjE,gBAAA,SAAAA,gBAAA,CAAAJ,WAAA;QACA;QACA,IAAAsE,KAAA,OAAAL,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA7E,aAAA,CAAAmD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAxC,IAAA;QAAA,GAAA+C,MAAA,WAAA/C,IAAA;UAAA,OAAAA,IAAA;QAAA;QACA,KAAAS,eAAA,GAAA2D,KAAA,CAAA7B,GAAA,WAAAvC,IAAA;UAAA;YAAAyC,KAAA,EAAAzC;UAAA;QAAA;MACA;QACA;QACA,IAAA0C,sCAAA;UAAApB,IAAA;QAAA,GAAAI,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAA7D,IAAA,IAAAuE,KAAA,CAAAC,OAAA,CAAAX,QAAA,CAAA7D,IAAA;YACAqG,MAAA,CAAA1D,eAAA,GAAAkB,QAAA,CAAA7D,IAAA,CAAAyE,GAAA,WAAAC,IAAA;cAAA;gBAAAC,KAAA,EAAAD;cAAA;YAAA;UACA;QACA;MACA;IACA;IAEA,gBACA6B,8BAAA,WAAAA,+BAAAC,SAAA;MACA,KAAAhG,YAAA,GAAAgG,SAAA,CAAA/B,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1C,WAAA;MAAA;MACA,KAAAvB,eAAA,GAAA+F,SAAA,CAAAC,MAAA;MACA,KAAA/F,iBAAA,IAAA8F,SAAA,CAAAC,MAAA;IACA;IAEAC,uBAAA,WAAAA,wBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAxE,gBAAA,GAAAuE,GAAA;MACA,KAAAE,KAAA,CAAAC,cAAA,CAAAC,kBAAA,CAAAJ,GAAA;;MAEA;MACA,KAAA5E,oBAAA,CAAAE,SAAA;MACA,KAAAF,oBAAA,CAAAG,IAAA;MACA,KAAA8E,SAAA;;MAEA;MACA,KAAAhD,gBAAA;;MAEA;MACA,KAAAG,SAAA;QACAyC,MAAA,CAAAxC,wBAAA;MACA;IACA;IAEA6C,wBAAA,WAAAA,yBAAAC,IAAA;MAAA,IAAAP,GAAA,GAAAO,IAAA,CAAAP,GAAA;QAAAQ,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,SAAA/E,gBAAA,IAAAuE,GAAA,CAAA3E,WAAA,UAAAI,gBAAA,CAAAJ,WAAA;QACA;MACA;MACA;IACA;IAEAoF,oBAAA,WAAAA,qBAAA;MACA,KAAAzG,oBAAA,CAAAC,OAAA;MACA,KAAAwC,gBAAA;IACA;IAEAiE,mBAAA,WAAAA,oBAAA;MACA,KAAAL,SAAA;MACA,KAAAI,oBAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAA;MACA,KAAAC,kBAAA;MACA,KAAAnH,aAAA;MACA,KAAAC,cAAA;IACA;IAEAmH,mBAAA,WAAAA,oBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,KAAAF,kBAAA;MACA,IAAAvF,WAAA,GAAA2E,GAAA,CAAA3E,WAAA,SAAAxB,YAAA;MACA,IAAAkH,+BAAA,EAAA1F,WAAA,EAAA4B,IAAA,WAAAC,QAAA;QACA4D,MAAA,CAAAnH,aAAA,GAAAuD,QAAA,CAAA7D,IAAA;QACAyH,MAAA,CAAAE,yBAAA;QACAF,MAAA,CAAArH,aAAA;QACAqH,MAAA,CAAApH,cAAA;MACA;IACA;IAEAuH,mBAAA,WAAAA,oBAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAAN,kBAAA;MACA,IAAAvF,WAAA,GAAA2E,GAAA,CAAA3E,WAAA;MACA,IAAA0F,+BAAA,EAAA1F,WAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAgE,MAAA,CAAAvH,aAAA,GAAAuD,QAAA,CAAA7D,IAAA;QACA6H,MAAA,CAAAvH,aAAA,CAAA0B,WAAA;QACA6F,MAAA,CAAAvH,aAAA,CAAAQ,QAAA,GAAA+G,MAAA,CAAAvH,aAAA,CAAAQ,QAAA;QACA+G,MAAA,CAAAF,yBAAA;QACAE,MAAA,CAAAzH,aAAA;QACAyH,MAAA,CAAAxH,cAAA;MACA;IACA;IAEAyH,qBAAA,WAAAA,sBAAAnB,GAAA;MAAA,IAAAoB,OAAA;MACA,IAAAvH,YAAA,GAAAmG,GAAA,CAAA3E,WAAA,SAAAxB,YAAA;MACA,KAAAwH,MAAA,CAAAC,OAAA,qBAAAtB,GAAA,CAAA7F,QAAA,aAAA8C,IAAA;QACA,WAAAsE,+BAAA,EAAA1H,YAAA;MACA,GAAAoD,IAAA;QACAmE,OAAA,CAAA3E,gBAAA;QACA2E,OAAA,CAAAC,MAAA,CAAAG,UAAA;QACA,IAAAJ,OAAA,CAAA3F,gBAAA,IAAA2F,OAAA,CAAA3F,gBAAA,CAAAJ,WAAA,KAAA2E,GAAA,CAAA3E,WAAA;UACA+F,OAAA,CAAA3F,gBAAA;UACA2F,OAAA,CAAAzG,aAAA;QACA;MACA,GAAA8G,KAAA;IACA;IAEAC,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,IAAA9H,YAAA,QAAAA,YAAA;MACA,KAAAwH,MAAA,CAAAC,OAAA,eAAAzH,YAAA,CAAAiG,MAAA,WAAA7C,IAAA;QACA,WAAAsE,+BAAA,EAAA1H,YAAA;MACA,GAAAoD,IAAA;QACA0E,OAAA,CAAAlF,gBAAA;QACAkF,OAAA,CAAAN,MAAA,CAAAG,UAAA;QACAG,OAAA,CAAAlG,gBAAA;QACAkG,OAAA,CAAAhH,aAAA;MACA,GAAA8G,KAAA;IACA;IAEAG,qBAAA,WAAAA,sBAAA;MACA,KAAAC,QAAA,sCAAAC,cAAA,CAAAvC,OAAA,MACA,KAAAvF,oBAAA,sBAAA+H,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEA,eACAxE,wBAAA,WAAAA,yBAAA;MACA,SAAAhC,gBAAA,SAAAd,aAAA,CAAAmF,MAAA;QACA;QACA,IAAAT,UAAA,OAAAC,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA7E,aAAA,CAAAmD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAzC,SAAA;QAAA,GAAAgD,MAAA,WAAAlF,IAAA;UAAA,OAAAA,IAAA;QAAA;QACA,IAAAuG,KAAA,OAAAL,mBAAA,CAAAC,OAAA,MAAAC,GAAA,MAAA7E,aAAA,CAAAmD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAxC,IAAA;QAAA,GAAA+C,MAAA,WAAA/C,IAAA;UAAA,OAAAA,IAAA;QAAA;QAEA,KAAAQ,oBAAA,GAAAsD,UAAA,CAAAvB,GAAA,WAAA1E,IAAA;UAAA;YAAA4E,KAAA,EAAA5E;UAAA;QAAA;QACA,KAAA4C,eAAA,GAAA2D,KAAA,CAAA7B,GAAA,WAAAvC,IAAA;UAAA;YAAAyC,KAAA,EAAAzC;UAAA;QAAA;MACA;IACA;IAEA,iBACA2G,8BAAA,WAAAA,+BAAArC,SAAA;MACA,KAAA5E,YAAA,GAAA4E,SAAA,CAAA/B,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAoE,WAAA;MAAA;MACA,KAAAjH,eAAA,GAAA2E,SAAA,CAAAC,MAAA;MACA,KAAA3E,iBAAA,IAAA0E,SAAA,CAAAC,MAAA;IACA;IAEAsC,uBAAA,WAAAA,wBAAApC,GAAA;MACA,KAAAE,KAAA,CAAAmC,cAAA,CAAAjC,kBAAA,CAAAJ,GAAA;IACA;IAEAsC,oBAAA,WAAAA,qBAAA;MACA,KAAAlH,oBAAA,CAAAnB,OAAA;MACA,KAAAoD,gBAAA;IACA;IAEAkF,mBAAA,WAAAA,oBAAA;MACA,KAAAlC,SAAA;MACA,KAAAiC,oBAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAA;MACA,KAAAC,kBAAA;MACA,KAAA5H,aAAA;MACA,KAAAC,cAAA;IACA;IAEA4H,mBAAA,WAAAA,oBAAA1C,GAAA;MAAA,IAAA2C,OAAA;MACA,KAAAF,kBAAA;MACA,IAAAN,WAAA,GAAAnC,GAAA,CAAAmC,WAAA,SAAAlH,YAAA;MACA,IAAA2H,+BAAA,EAAAT,WAAA,EAAAlF,IAAA,WAAAC,QAAA;QACAyF,OAAA,CAAA5H,aAAA,GAAAmC,QAAA,CAAA7D,IAAA;QACAsJ,OAAA,CAAAE,yBAAA;QACAF,OAAA,CAAA9H,aAAA;QACA8H,OAAA,CAAA7H,cAAA;MACA;IACA;IAEAgI,qBAAA,WAAAA,sBAAA9C,GAAA;MAAA,IAAA+C,OAAA;MACA,IAAA9H,YAAA,GAAA+E,GAAA,CAAAmC,WAAA,SAAAlH,YAAA;MACA,KAAAoG,MAAA,CAAAC,OAAA,kBAAAtB,GAAA,CAAA1E,SAAA,aAAA2B,IAAA;QACA,WAAA+F,+BAAA,EAAA/H,YAAA;MACA,GAAAgC,IAAA;QACA8F,OAAA,CAAA1F,gBAAA;QACA0F,OAAA,CAAA1B,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IAEAwB,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,IAAAjI,YAAA,QAAAA,YAAA;MACA,KAAAoG,MAAA,CAAAC,OAAA,eAAArG,YAAA,CAAA6E,MAAA,WAAA7C,IAAA;QACA,WAAA+F,+BAAA,EAAA/H,YAAA;MACA,GAAAgC,IAAA;QACAiG,OAAA,CAAA7F,gBAAA;QACA6F,OAAA,CAAA7B,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IAEA0B,qBAAA,WAAAA,sBAAA;MACA,KAAAtB,QAAA,sCAAAC,cAAA,CAAAvC,OAAA,MACA,KAAAnE,oBAAA,sBAAA2G,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEA,aACArB,kBAAA,WAAAA,mBAAA;MACA,KAAAjH,aAAA;QACA0B,WAAA;QACAlB,QAAA;QACAC,eAAA;QACAgJ,eAAA;QACA/I,aAAA;QACAgJ,WAAA;QACAC,MAAA;MACA;MACA,KAAA1J,iBAAA;MACA,KAAAyG,SAAA;IACA;IAEAoC,kBAAA,WAAAA,mBAAA;MACA,KAAA1H,aAAA;QACAoH,WAAA;QACA9G,WAAA,OAAAI,gBAAA,QAAAA,gBAAA,CAAAJ,WAAA;QACAC,SAAA;QACAiI,UAAA;QACAhI,IAAA;QACA8H,WAAA;QACAC,MAAA;MACA;MACA,KAAAtI,iBAAA;MACA,KAAAqF,SAAA;IACA;IAEAmD,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAvD,KAAA,kBAAAwD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAA9J,aAAA,CAAA0J,WAAA,GAAAI,OAAA,CAAA7J,iBAAA,CAAAkE,GAAA,WAAA8F,IAAA;YAAA,OAAAA,IAAA,CAAAC,GAAA;UAAA,GAAAC,IAAA;UACA,IAAAL,OAAA,CAAA9J,aAAA,CAAA0B,WAAA;YACA,IAAA0I,kCAAA,EAAAN,OAAA,CAAA9J,aAAA,EAAAsD,IAAA,WAAAC,QAAA;cACAuG,OAAA,CAAApC,MAAA,CAAAG,UAAA;cACAiC,OAAA,CAAAhK,aAAA;cACAgK,OAAA,CAAAhH,gBAAA;YACA;UACA;YACA,IAAAuH,+BAAA,EAAAP,OAAA,CAAA9J,aAAA,EAAAsD,IAAA,WAAAC,QAAA;cACAuG,OAAA,CAAApC,MAAA,CAAAG,UAAA;cACAiC,OAAA,CAAAhK,aAAA;cACAgK,OAAA,CAAAhH,gBAAA;YACA;UACA;QACA;MACA;IACA;IAEAwH,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhE,KAAA,kBAAAwD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAO,OAAA,CAAAnJ,aAAA,CAAAsI,WAAA,GAAAa,OAAA,CAAAlJ,iBAAA,CAAA8C,GAAA,WAAA8F,IAAA;YAAA,OAAAA,IAAA,CAAAC,GAAA;UAAA,GAAAC,IAAA;UACA,IAAAI,OAAA,CAAAnJ,aAAA,CAAAoH,WAAA;YACA,IAAAgC,kCAAA,EAAAD,OAAA,CAAAnJ,aAAA,EAAAkC,IAAA,WAAAC,QAAA;cACAgH,OAAA,CAAA7C,MAAA,CAAAG,UAAA;cACA0C,OAAA,CAAArJ,aAAA;cACAqJ,OAAA,CAAA7G,gBAAA;YACA;UACA;YACA,IAAA+G,+BAAA,EAAAF,OAAA,CAAAnJ,aAAA,EAAAkC,IAAA,WAAAC,QAAA;cACAgH,OAAA,CAAA7C,MAAA,CAAAG,UAAA;cACA0C,OAAA,CAAArJ,aAAA;cACAqJ,OAAA,CAAA7G,gBAAA;YACA;UACA;QACA;MACA;IACA;IAEAgH,eAAA,WAAAA,gBAAA;MACA,KAAA5K,aAAA;MACA,KAAAmH,kBAAA;IACA;IAEA0D,eAAA,WAAAA,gBAAA;MACA,KAAAzJ,aAAA;MACA,KAAA4H,kBAAA;IACA;IAEA,aACAzB,yBAAA,WAAAA,0BAAA;MACA,SAAArH,aAAA,CAAA0J,WAAA;QACA,IAAAkB,IAAA,QAAA5K,aAAA,CAAA0J,WAAA,CAAAmB,KAAA;QACA,KAAA5K,iBAAA,GAAA2K,IAAA,CAAAzG,GAAA,WAAA+F,GAAA,EAAAY,KAAA;UAAA;YACArL,IAAA,EAAAyK,GAAA,CAAAa,SAAA,CAAAb,GAAA,CAAAc,WAAA;YACAd,GAAA,EAAAA,GAAA;YACAe,GAAA,EAAAH;UACA;QAAA;MACA;IACA;IAEA5B,yBAAA,WAAAA,0BAAA;MACA,SAAA9H,aAAA,CAAAsI,WAAA;QACA,IAAAkB,IAAA,QAAAxJ,aAAA,CAAAsI,WAAA,CAAAmB,KAAA;QACA,KAAAxJ,iBAAA,GAAAuJ,IAAA,CAAAzG,GAAA,WAAA+F,GAAA,EAAAY,KAAA;UAAA;YACArL,IAAA,EAAAyK,GAAA,CAAAa,SAAA,CAAAb,GAAA,CAAAc,WAAA;YACAd,GAAA,EAAAA,GAAA;YACAe,GAAA,EAAAH;UACA;QAAA;MACA;IACA;IAEAI,4BAAA,WAAAA,6BAAA3H,QAAA,EAAA0G,IAAA;MACA,KAAAhK,iBAAA,CAAAkL,IAAA;QACA1L,IAAA,EAAAwK,IAAA,CAAAxK,IAAA;QACAyK,GAAA,EAAA3G,QAAA,CAAA2G,GAAA;QACAe,GAAA,EAAAhB,IAAA,CAAAgB;MACA;IACA;IAEAG,4BAAA,WAAAA,6BAAA7H,QAAA,EAAA0G,IAAA;MACA,KAAA5I,iBAAA,CAAA8J,IAAA;QACA1L,IAAA,EAAAwK,IAAA,CAAAxK,IAAA;QACAyK,GAAA,EAAA3G,QAAA,CAAA2G,GAAA;QACAe,GAAA,EAAAhB,IAAA,CAAAgB;MACA;IACA;IAEAI,2BAAA,WAAAA,4BAAApB,IAAA;MACA,IAAAa,KAAA,QAAA7K,iBAAA,CAAAqL,SAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAA6G,GAAA,KAAAhB,IAAA,CAAAgB,GAAA;MAAA;MACA,IAAAH,KAAA;QACA,KAAA7K,iBAAA,CAAAsL,MAAA,CAAAT,KAAA;MACA;IACA;IAEAU,2BAAA,WAAAA,4BAAAvB,IAAA;MACA,IAAAa,KAAA,QAAAzJ,iBAAA,CAAAiK,SAAA,WAAAlH,IAAA;QAAA,OAAAA,IAAA,CAAA6G,GAAA,KAAAhB,IAAA,CAAAgB,GAAA;MAAA;MACA,IAAAH,KAAA;QACA,KAAAzJ,iBAAA,CAAAkK,MAAA,CAAAT,KAAA;MACA;IACA;IAEAW,qBAAA,WAAAA,sBAAAxB,IAAA;MACA,IAAAyB,OAAA,GAAAzB,IAAA,CAAA0B,IAAA;MACA,KAAAD,OAAA;QACA,KAAAhE,MAAA,CAAAkE,QAAA;MACA;MACA,OAAAF,OAAA;IACA;IAEAG,qBAAA,WAAAA,sBAAA5B,IAAA;MACA,IAAAyB,OAAA,GAAAzB,IAAA,CAAA0B,IAAA;MACA,KAAAD,OAAA;QACA,KAAAhE,MAAA,CAAAkE,QAAA;MACA;MACA,OAAAF,OAAA;IACA;IAEAI,8BAAA,WAAAA,+BAAApC,WAAA;MACA,KAAAqC,eAAA,CAAArC,WAAA;IACA;IAEAsC,8BAAA,WAAAA,+BAAAtC,WAAA;MACA,KAAAqC,eAAA,CAAArC,WAAA;IACA;IAEAqC,eAAA,WAAAA,gBAAArC,WAAA;MACA,KAAAA,WAAA;MACA,IAAAkB,IAAA,GAAAlB,WAAA,CAAAmB,KAAA;MACA,KAAA7I,cAAA,GAAA4I,IAAA,CAAAzG,GAAA,WAAA+F,GAAA;QAAA;UACAzK,IAAA,EAAAyK,GAAA,CAAAa,SAAA,CAAAb,GAAA,CAAAc,WAAA;UACAd,GAAA,EAAAA;QACA;MAAA;MACA,KAAAnI,uBAAA;IACA;IAEAkK,kBAAA,WAAAA,mBAAA/B,GAAA,EAAAzK,IAAA;MACA,IAAAyM,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAnC,GAAA;MACAgC,IAAA,CAAAhE,QAAA,GAAAzI,IAAA;MACAyM,IAAA,CAAAI,KAAA;IACA;IAEAC,oBAAA,WAAAA,qBAAAnI,IAAA;MACA,KAAA/D,oBAAA,CAAAG,QAAA,GAAA4D,IAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}