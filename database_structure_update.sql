-- 数据库结构更新脚本
-- 1. 修改参数明细表的参数数值为字符串格式
-- 2. 重新设计测试方案表结构，类似参数组和参数明细的方式
-- 3. 重新添加所有外键约束

-- ======== 删除现有外键约束（如果存在） ========
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_plan`;
ALTER TABLE `test_results` DROP FOREIGN KEY IF EXISTS `fk_result_group`;
ALTER TABLE `process_param_item` DROP FOREIGN KEY IF EXISTS `fk_item_group`;
ALTER TABLE `process_param_group` DROP FOREIGN KEY IF EXISTS `fk_group_material`;

-- ======== 1. 修改工艺参数明细表 - 参数数值改为字符串 ========
ALTER TABLE `process_param_item` 
MODIFY COLUMN `param_value` VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）';

-- ======== 2. 重新设计测试方案表结构 ========

-- 2.1 备份现有测试方案数据（可选）
-- CREATE TABLE `test_plans_backup` AS SELECT * FROM `test_plans`;

-- 2.2 删除现有测试方案表
DROP TABLE IF EXISTS `test_plans`;

-- 2.3 创建新的测试方案组表（类似工艺参数组）
CREATE TABLE `test_plan_group` (
  `plan_group_id`    INT NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code`        VARCHAR(50)  NOT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`),
  KEY `idx_performance_type` (`performance_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案组表';

-- 2.4 创建测试参数明细表（类似工艺参数明细）
CREATE TABLE `test_param_item` (
  `test_param_id`   INT NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）',
  `plan_group_id`   INT NOT NULL COMMENT '所属测试方案组ID',
  `param_name`      VARCHAR(100) NOT NULL COMMENT '测试参数名称',
  `param_value`     VARCHAR(100) DEFAULT NULL COMMENT '测试参数数值（字符串格式）',
  `unit`            VARCHAR(20)   DEFAULT NULL COMMENT '参数单位',
  `attachments`     VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`          VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`       VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`       VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`     DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_param_id`),
  KEY `idx_plan_group_id` (`plan_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试参数明细表';

-- ======== 3. 处理测试结果表的数据迁移 ========

-- 3.1 先备份测试结果表（可选）
-- CREATE TABLE `test_results_backup` AS SELECT * FROM `test_results`;

-- 3.2 检查并清理无效的外键数据
-- 删除在test_plans表中不存在对应记录的test_results数据
DELETE FROM `test_results`
WHERE `test_plan_id` NOT IN (
  SELECT `test_plan_id` FROM `test_plans` WHERE `test_plan_id` IS NOT NULL
);

-- 3.3 修改测试结果表，关联新的测试方案组
ALTER TABLE `test_results`
CHANGE COLUMN `test_plan_id` `plan_group_id` INT NOT NULL COMMENT '测试方案组ID';

-- ======== 4. 插入测试方案组数据（必须在添加外键约束之前） ========

-- 4.1 插入测试方案组数据
INSERT INTO `test_plan_group` (`plan_group_id`, `plan_code`, `performance_type`, `performance_name`, `test_equipment`, `create_by`) VALUES
(1, 'TP001', '力学性能', '拉伸强度测试', 'Instron 5985', 'admin'),
(2, 'TP002', '力学性能', '弯曲强度测试', 'Instron 5985', 'admin'),
(3, 'TP003', '力学性能', '冲击韧性测试', 'Charpy冲击试验机', 'admin'),
(4, 'TP004', '热学性能', '热膨胀系数测试', 'TMA热机械分析仪', 'admin'),
(5, 'TP005', '热学性能', '导热系数测试', '激光导热仪', 'admin'),
(6, 'TP006', '电学性能', '介电常数测试', '阻抗分析仪', 'admin'),
(7, 'TP007', '化学性能', '耐腐蚀性测试', '盐雾试验箱', 'admin'),
(8, 'TP008', '物理性能', '密度测试', '密度计', 'admin');

-- 4.2 更新测试结果表中的plan_group_id，确保数据一致性
-- 将原来的test_plan_id值直接映射到plan_group_id（假设ID是连续的）
-- 如果原来的test_plan_id超出范围，则删除这些记录
DELETE FROM `test_results` WHERE `plan_group_id` > 8 OR `plan_group_id` < 1;

-- ======== 5. 重新添加所有外键约束 ========

-- 5.1 工艺参数组 -> 材料
ALTER TABLE `process_param_group`
ADD CONSTRAINT `fk_group_material`
  FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 5.2 工艺参数明细 -> 工艺参数组
ALTER TABLE `process_param_item`
ADD CONSTRAINT `fk_item_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 5.3 测试参数明细 -> 测试方案组
ALTER TABLE `test_param_item`
ADD CONSTRAINT `fk_test_param_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 5.4 测试结果 -> 测试方案组（现在数据已经一致）
ALTER TABLE `test_results`
ADD CONSTRAINT `fk_result_plan_group`
  FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- 5.5 测试结果 -> 工艺参数组
ALTER TABLE `test_results`
ADD CONSTRAINT `fk_result_param_group`
  FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`)
  ON DELETE CASCADE ON UPDATE CASCADE;

-- ======== 6. 创建索引优化查询性能 ========
CREATE INDEX IF NOT EXISTS `idx_material_name` ON `materials` (`material_name`);
CREATE INDEX IF NOT EXISTS `idx_process_type` ON `process_param_group` (`process_type`);
CREATE INDEX IF NOT EXISTS `idx_param_number` ON `process_param_group` (`param_number`);
CREATE INDEX IF NOT EXISTS `idx_performance_type` ON `test_plan_group` (`performance_type`);
CREATE INDEX IF NOT EXISTS `idx_plan_code` ON `test_plan_group` (`plan_code`);
CREATE INDEX IF NOT EXISTS `idx_test_result_create_time` ON `test_results` (`create_time`);

-- ======== 7. 插入测试参数明细数据 ========
INSERT INTO `test_param_item` (`plan_group_id`, `param_name`, `param_value`, `unit`, `create_by`) VALUES
-- TP001 拉伸强度测试参数
(1, '试样长度', '250', 'mm', 'admin'),
(1, '试样宽度', '25', 'mm', 'admin'),
(1, '试样厚度', '2', 'mm', 'admin'),
(1, '拉伸速度', '2', 'mm/min', 'admin'),
(1, '预载荷', '10', 'N', 'admin'),

-- TP002 弯曲强度测试参数
(2, '试样长度', '80', 'mm', 'admin'),
(2, '试样宽度', '10', 'mm', 'admin'),
(2, '试样厚度', '4', 'mm', 'admin'),
(2, '跨距', '64', 'mm', 'admin'),
(2, '加载速度', '2', 'mm/min', 'admin'),

-- TP003 冲击韧性测试参数
(3, '试样长度', '55', 'mm', 'admin'),
(3, '试样宽度', '10', 'mm', 'admin'),
(3, '试样厚度', '10', 'mm', 'admin'),
(3, '缺口深度', '2', 'mm', 'admin'),
(3, '摆锤能量', '300', 'J', 'admin'),

-- TP004 热膨胀系数测试参数
(4, '试样长度', '25', 'mm', 'admin'),
(4, '温度范围', '25-200', '°C', 'admin'),
(4, '升温速率', '5', '°C/min', 'admin'),
(4, '保温时间', '10', 'min', 'admin'),

-- TP005 导热系数测试参数
(5, '试样直径', '12.7', 'mm', 'admin'),
(5, '试样厚度', '2', 'mm', 'admin'),
(5, '测试温度', '25', '°C', 'admin'),
(5, '激光功率', '0.1', 'W', 'admin'),

-- TP006 介电常数测试参数
(6, '试样直径', '50', 'mm', 'admin'),
(6, '试样厚度', '1', 'mm', 'admin'),
(6, '测试频率', '1000', 'Hz', 'admin'),
(6, '测试电压', '1', 'V', 'admin'),

-- TP007 耐腐蚀性测试参数
(7, '试样尺寸', '50x25x3', 'mm', 'admin'),
(7, '盐雾浓度', '5', '%', 'admin'),
(7, '测试温度', '35', '°C', 'admin'),
(7, '测试时间', '168', 'h', 'admin'),

-- TP008 密度测试参数
(8, '试样体积', '1', 'cm³', 'admin'),
(8, '测试温度', '23', '°C', 'admin'),
(8, '测试介质', '蒸馏水', '', 'admin'),
(8, '浸泡时间', '24', 'h', 'admin');

-- ======== 8. 数据验证查询 ========
-- 验证外键约束是否正确建立
SELECT 
  TABLE_NAME,
  CONSTRAINT_NAME,
  CONSTRAINT_TYPE,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM 
  INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
  TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY 
  TABLE_NAME, CONSTRAINT_NAME;
